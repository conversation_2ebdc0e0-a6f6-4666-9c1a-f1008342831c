=== Popup Maker - Boost Sales, Conversions, Optins, Subscribers with the Ultimate WP Popups Builder ===
Contributors: danieliser, codeatlantic
Author URI: https://wppopupmaker.com/?utm_campaign=readme&utm_medium=referral&utm_source=readme-header&utm_content=author-url
Plugin URI: https://wppopupmaker.com/?utm_campaign=readme&utm_medium=referral&utm_source=readme-header&utm_content=plugin-url
Donate link:
Tags:  marketing, popup, popups, optin, conversion
Requires at least: 4.9
Tested up to: 6.8.1
Requires PHP: 5.6
Stable tag: 1.20.5
License: GPLv2 or later
License URI:  http://www.gnu.org/licenses/gpl-2.0.html

Want to boost sales & marketing efforts? Using popups are a great way to increase conversions! Use your favorite forms & builder, keep your data.

== Description ==

<h3>📈 Drive More Sales, Leads & Email List Opt-Ins Using Popups</h3>

**Popup Maker™**, rated as the **[best WordPress popup plugin](https://wppopupmaker.com/conversion-optimization/best-wordpress-popup-plugins/)** by our community of 780,000+ users, empowers you to create stunning popups, modals, and overlays in minutes. Whether you're a beginner or a pro, our versatile toolkit makes it easy to boost your WordPress site's engagement.

Transform your WordPress site into a conversion powerhouse! Create targeted campaigns using popups, exit-intent forms, slide-ins, and smart banners to skyrocket your revenue and build your email list faster than ever.

> **We switched to Popup Maker for our popups and couldn't be happier.**
>
> "We recommend it to all our clients too. Popup Maker is an essential piece of our website for maximizing growing our email list, increasing sales conversions, and guiding users with strategic calls to action. Great support too."
>
> ~ **Chris Badgett, LifterLMS CEO**

<h4>🔥 Ready to get started?</h4>

👉 [Visit Our Website](https://wppopupmaker.com/?utm_campaign=readme&utm_medium=referral&utm_source=readme-description&utm_content=main-site-link) - Explore all features & pricing
👉 [Join Our Conversion Optimization Community](https://wppopupmaker.com/community/?utm_campaign=readme&utm_medium=referral&utm_source=readme-description&utm_content=community) - Share strategies & learn from experts
👉 [Read Our Guides](https://wppopupmaker.com/docs/category/using-popup-maker/?utm_campaign=readme&utm_medium=referral&utm_source=readme-description&utm_content=user-guide) - Learn popup best practices
👉 [Get Help](https://wppopupmaker.com/support/?utm_campaign=readme&utm_medium=referral&utm_source=readme-description&utm_content=support) - World-class support when you need it

<h3>🎬 See What's Possible with Popup Maker</h3>

https://www.youtube.com/watch?v=PomnMganMVM

[Stunning examples of what you can create in Popup Maker **(View Video)**](https://www.youtube.com/watch?v=PomnMganMVM)

<h3>🛠️ Ultimate WordPress Popup Builder: Every Type of Conversion Popup at Your Fingertips</h3>

With Popup Maker, you have the ultimate popup builder toolkit at your disposal, create any type of popup in minutes:

* Email opt-in popups,
* Exit-Intent popups.
* Lead generation popups,
* Subscription list popups
* Content upgrade popups,
* Ecommerce popups,
* Contact form popups,
* Coronavirus/COVID-19 styleannouncements,
* [EU cookie notices](https://ninjaforms.com/blog/eu-cookie-notices-ninja-forms/),
* Popups to greet visitors from ProductHunt (requires [premium Advanced Targeting Conditions feature](https://wppopupmaker.com/extensions/advanced-targeting-conditions/?utm_campaign=readme&utm_medium=referral&utm_source=readme-description&utm_content=example-popups&utm_term=greet-producthunt)),
* WooCommerce upsell popups (requires [premium WooCommerce feature](https://wppopupmaker.com/extensions/woocommerce-pro/?utm_campaign=readme&utm_medium=referral&utm_source=readme-description&utm_content=example-popups&utm_term=woocommerce-upsells)),
* WooCommerce cross-sell popups (requires [premium WooCommerce feature](https://wppopupmaker.com/extensions/woocommerce-pro/?utm_campaign=readme&utm_medium=referral&utm_source=readme-description&utm_content=example-popups&utm_term=woocommerce-crosssells)),
* & more.

> **Even the free version is great**
> "Does exactly what it says and is easy to use. Free version allows full control of where popup appears, how it looks and setting conditions for popup reappearing across website."
> ~[@rsb1234](https://wordpress.org/support/topic/even-the-free-version-is-great-2/)

<h3>🔑 Trigger Popups with Ease: Multiple Ways to Open Your Popups</h3>

Popup Maker offers a variety of triggers to open your popups:

* Open automatically (with optional delay)
* Click button (or any other element) to open
* Form submission (open a popup when a form is submitted)
* Open when someone is about to leave your site (requires [premium Exit Intent feature](https://wppopupmaker.com/extensions/exit-intent-popups/?utm_campaign=readme&utm_medium=referral&utm_source=readme-description&utm_content=triggers&utm_term=exit-intent))
* Open when someone has scrolled down your page (requires [premium Scroll Trigger feature](https://wppopupmaker.com/extensions/scroll-triggered-popups/?utm_campaign=readme&utm_medium=referral&utm_source=readme-description&utm_content=triggers&utm_term=scroll))

<h3>🎯 Smart Popup Targeting: Deliver the Perfect Message to Every Visitor</h3>

Never show irrelevant popups again! Our powerful targeting system ensures your messages reach exactly who needs to see them, when they need to see them. From basic page targeting to advanced user behavior conditions, you have complete control over your popup's visibility.

<h4>Free Targeting Options:</h4>

* Target specific posts, pages, or custom post types
* Target by post categories or tags, & custom taxonomies
* Front page vs blog page targeting

<h4>Premium Targeting Features:</h4>

* Advanced user behavior targeting
* Show on specific URLs or URL patterns
* Show/hide based on user roles
* Geolocation targeting
* Browser & OS targeting
* Time & date scheduling
* Page scroll depth targeting
* User login status
* Previous popup interactions
* Ecommerce purchasing & cart rules
* Advanced custom taxonomy targeting
* Referrer source targeting


<h3>🔑 Integrate with Your Favorite Form Plugins</h3>

Popup Maker integrates with all of the most popular form plugins to allow you to open a popup when a form is submitted or close the popup when a form inside the popup is submitted. Popup Maker integrates with:

<h4>Form Plugin Integrations:</h4>

* Ninja Forms
* Gravity Forms
* Contact Form 7 (CF7)
* WPForms
* WSForm
* Fluent Forms
* Mailchimp for WordPress (MC4WP)
* Formidable Forms
* and more!

<h4>Email Marketing & CRM Integrations:</h4>

Already created a form in your email marketing service? Using our free version, you can copy and paste any form created by most list building platforms, including but not limited to:

* MailChimp
* AWeber
* InfusionSoft
* GetResponse
* Convertkit
* Constant Contact
* Mail Poet
* Mad Mimi
* FluentCRM
* Hubspot
* Emma
* and more!

<h3>⚙️ Advanced Popup Controls: Customize Every Aspect of Your Popups</h3>

Take complete control of your popups with our powerful customization features:

* **Visual Popup Editor** - Design beautiful popups with our intuitive drag & drop editor. Control sizing, positioning, animations, and more
* **Smart Display Rules** - Set cookie-based display frequency to prevent popup fatigue and improve user experience
* **Custom Animations** - Choose from slide, fade, and other engaging entrance/exit animations
* **Mobile-First Design** - Every popup is fully responsive and looks great on all devices
* **Custom Positioning** - Place your popups exactly where you want them - centered, corners, or custom positions
* **Cookie Controls** - Fine-tune when and how often visitors see your popups with advanced cookie settings

<h3>👍 Trusted by many people just like you!</h3>

Popup Maker is used on over 780,000 websites and has received over 4,200 5-star reviews just like this one:

> **Great plugin, everything I needed**
> "Really nice plugin, simple to use, responsive, a good catch !" ~[@lemmmy](https://wordpress.org/support/topic/great-plugin-everything-i-needed/)

<h3>🛠️ Enhance Your WordPress Popups Using Our Premium Features</h3>

Need even more features? Popup Maker has over 20 premium features to supercharge your conversion rates:

* [Exit Intent](https://wppopupmaker.com/extensions/exit-intent-popups/?utm_campaign=readme&utm_medium=referral&utm_source=readme-description&utm_content=premium-features&utm_term=exit-intent) - Catch abandoning visitors before they leave with perfectly timed offers
* [Popup Analytics](https://wppopupmaker.com/extensions/popup-analytics/?utm_campaign=readme&utm_medium=referral&utm_source=readme-description&utm_content=premium-features&utm_term=popup-analytics) - Track views, conversions, and optimize your popup performance with detailed insights
* [Advanced Targeting Conditions](https://wppopupmaker.com/extensions/advanced-targeting-conditions/?utm_campaign=readme&utm_medium=referral&utm_source=readme-description&utm_content=premium-features&utm_term=advanced-targeting-conditions) - Show popups based on user behavior, referral source, geolocation, and more
* [Forced Interaction](https://wppopupmaker.com/extensions/forced-interaction/?utm_campaign=readme&utm_medium=referral&utm_source=readme-description&utm_content=premium-features&utm_term=forced-interaction) - Ensure critical messages are seen with popups that require acknowledgment
* [Scheduling](https://wppopupmaker.com/extensions/scheduling/?utm_campaign=readme&utm_medium=referral&utm_source=readme-description&utm_content=premium-features&utm_term=scheduling) - Display time-sensitive offers and content automatically at the perfect moment
* And [many more premium features](https://wppopupmaker.com/extensions/?utm_campaign=readme&utm_medium=referral&utm_source=readme-description&utm_content=premium-features&utm_term=all-extensions) to help you maximize conversions!

<h3>🤝 Join Our Growing Community</h3>

We believe in making Popup Maker better together! Here's how you can get involved:

* 📚 [Read Our Documentation](https://wppopupmaker.com/docs/?utm_campaign=readme&utm_medium=referral&utm_source=readme-description&utm_content=questions&utm_term=documentation) - Comprehensive guides and tutorials
* 💬 [Get Premium Support](https://wppopupmaker.com/support/?utm_campaign=readme&utm_medium=referral&utm_source=readme-description&utm_content=questions&utm_term=support) - Our team is here to help
* 🌟 [Join Our Community](https://wppopupmaker.com/community/?utm_campaign=readme&utm_medium=referral&utm_source=readme-description&utm_content=community) - Share strategies & learn from experts
* 💻 [Contribute on GitHub](https://github.com/PopupMaker/Popup-Maker) - Help improve the code
* 🌍 [Help with Translations](https://translate.wordpress.org/projects/wp-plugins/popup-maker) - Make the **best WordPress popup plugin** accessible in your language
* ⭐ [Leave a Review](https://wordpress.org/support/plugin/popup-maker/reviews/#new-post) - Share your experience with others


<h3>Created by Code Atlantic</h3>

Popup Maker is built by the [Code Atlantic](https://code-atlantic.com) team. We create high-quality WordPress plugins that help you grow.

Check out some of our most popular plugins:

* [Content Control](https://contentcontrolplugin.com/) - Restrict Access to Pages and Posts
* [User Menus](https://wordpress.org/plugins/user-menus/) - Show Or Hide Menu Items For Different Users


== Frequently Asked Questions ==

= What are some ways I can use popups to grow my email list? =
People who use Popup Maker to grow their email list have found many creative ways to do so. Some of the most common popups include:

* Exit-intent popups - Asking for an opt-in when someone goes to leave your site, sometimes for a discount or PDF
* Content upgrade popups - Provide more value from your blog posts by giving visitors more resources in exchange for their email address within an optin popup
* White paper popups - Provide industry reports or other white papers using forms within a popup

Even better, Popup Maker integrates with all the most popular form plugins so you can easily embed a form you have already created right into your popups!

= What are some ways popups can increase my WooCommerce store's sales and revenues? =
Many people don't consider popups when trying to find ways to increase conversion rates and revenue on their WooCommerce site but they are actually very effective.

Some popups we have seen work well for ecommerce sites include:

* Cross-sell popups - recommending a companion product when viewing or adding a product to their cart
* Upsell popups - recommending a higher-tier product when viewing or adding a product to their cart
* Cart abandonment popups - offering discounts or support when someone is about to leave the checkout page

= Where is your documentation? =
You can find our documentation over on [our docs site](https://wppopupmaker.com/docs/?utm_campaign=readme&utm_medium=referral&utm_source=readme-faqs&utm_content=documentation)

= How do I open a popup? =
Using "triggers", you can customize what makes each popup open. Review [our triggers documentation here](https://wppopupmaker.com/docs/controlling-popups/using-triggers-and-cookies-together/?utm_campaign=readme&utm_medium=referral&utm_source=readme-faqs&utm_content=open-a-popup)

= How do I stop popups from opening repeatedly? =
Using "cookies", you can set up how long until the popup opens again, if ever. Review [our cookies documentation](https://wppopupmaker.com/docs/controlling-popups/cookies-settings-and-features/?utm_campaign=readme&utm_medium=referral&utm_source=readme-faqs&utm_content=stop-opening-repeatedly)

= What do I do if I want a popup to show only on a certain page/post/etc? =
Using "conditions", you can specify where the popup will be shown. Check out [our conditions documentation](https://wppopupmaker.com/docs/controlling-popups/conditions/?utm_campaign=readme&utm_medium=referral&utm_source=readme-faqs&utm_content=target-certain-pages)

= Why aren't my popups opening/working? =
There are several common causes for this, check [this guide for help](https://wppopupmaker.com/docs/problem-solving/troubleshooting-your-first-popup/?utm_campaign=readme&utm_medium=referral&utm_source=readme-faqs&utm_content=popup-wont-open) resolving it.

== Screenshots ==

1. Example popup using our built-in Ninja Forms integration
2. Example popup
3. Example popup using our WooCommerce extension to make a popup appear when a product is added to their cart.
4. Create and edit an infinite number of unique popups to get any job done.
5. Use our popup editor to customize every facet of your popup completely.
6. Add triggers to your popups to determine what causes it to open. Our free triggers include: Click Open and Auto Open.
7. Choose from many conditions to target exactly who will (and will not) see your popups.
8. Prevent your popups from being annoying to users by using cookies to disable them once they have been viewed.
9. Create and edit an unlimited number of popup themes for every situation.
10. Use the theme editor to choose from over 60 options and theme every element of your popup: Background Overlay, Popup Container, Close Button, Google Fonts, and much more.

== Changelog ==

For the latest updates and release information:

* Join our [community](https://wppopupmaker.com/community/) for important release announcements and discussions
* Visit our [changelog](https://wppopupmaker.com/changelog/) for detailed version history
* View our [complete development changelog](https://github.com/PopupMaker/Popup-Maker/blob/master/CHANGELOG.md)

= 1.20.5 - 2025-05-30 =

-   Security: Fix potential XSS for custom HTML based popups.


= 1.20.4 - 2025-01-29 =

- Tweak: Updated documentation links to use new docs site.
- Fix: Popup title aria-labelledby attribute was being double-quoted. Thanks  to the [Equalize Digital](https://equalizedigital.com/) team.
