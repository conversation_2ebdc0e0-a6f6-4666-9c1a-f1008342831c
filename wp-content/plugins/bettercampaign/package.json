{"name": "bettercampaign", "version": "1.0.0", "main": "dev/index.tsx", "author": "<PERSON>", "scripts": {"start": "wp-scripts start", "build": "wp-scripts build", "pot": "wp i18n make-pot . languages/bettercampaign.pot"}, "devDependencies": {"@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@wordpress/scripts": "^27.0.0", "autoprefixer": "^10.4.16", "css-loader": "^7.1.2", "postcss": "^8.4.31", "sass": "^1.69.0", "sass-loader": "^13.3.0", "style-loader": "^4.0.0", "tailwindcss": "^3.3.5", "ts-loader": "^9.4.0", "typescript": "^5.0.0"}, "dependencies": {"@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-toggle": "^1.0.3", "@radix-ui/react-tooltip": "^1.0.7", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "lucide-react": "^0.292.0", "tailwind-merge": "^2.0.0", "tailwindcss-animate": "^1.0.7"}}