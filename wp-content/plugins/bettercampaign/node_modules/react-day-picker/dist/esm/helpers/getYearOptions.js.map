{"version": 3, "file": "getYearOptions.js", "sourceRoot": "", "sources": ["../../../src/helpers/getYearOptions.ts"], "names": [], "mappings": "AAIA;;;;;;;;;;;;GAYG;AACH,MAAM,UAAU,cAAc,CAC5B,QAA0B,EAC1B,MAAwB,EACxB,UAAkD,EAClD,OAAgB;IAEhB,IAAI,CAAC,QAAQ;QAAE,OAAO,SAAS,CAAC;IAChC,IAAI,CAAC,MAAM;QAAE,OAAO,SAAS,CAAC;IAC9B,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,GACvE,OAAO,CAAC;IACV,MAAM,YAAY,GAAG,WAAW,CAAC,QAAQ,CAAC,CAAC;IAC3C,MAAM,WAAW,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC;IACtC,MAAM,KAAK,GAAW,EAAE,CAAC;IAEzB,IAAI,IAAI,GAAG,YAAY,CAAC;IACxB,OAAO,QAAQ,CAAC,IAAI,EAAE,WAAW,CAAC,IAAI,UAAU,CAAC,IAAI,EAAE,WAAW,CAAC,EAAE,CAAC;QACpE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACjB,IAAI,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED,OAAO,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;QACxB,MAAM,KAAK,GAAG,UAAU,CAAC,kBAAkB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAC3D,OAAO;YACL,KAAK,EAAE,OAAO,CAAC,IAAI,CAAC;YACpB,KAAK;YACL,QAAQ,EAAE,KAAK;SAChB,CAAC;IACJ,CAAC,CAAC,CAAC;AACL,CAAC"}