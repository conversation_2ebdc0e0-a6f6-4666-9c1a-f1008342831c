{"version": 3, "file": "BrowserRunner.js", "sourceRoot": "", "sources": ["../../../../src/node/BrowserRunner.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EAAE,KAAK,EAAE,MAAM,oBAAoB,CAAC;AAE3C,OAAO,KAAK,YAAY,MAAM,eAAe,CAAC;AAC9C,OAAO,KAAK,EAAE,MAAM,IAAI,CAAC;AACzB,OAAO,KAAK,IAAI,MAAM,MAAM,CAAC;AAC7B,OAAO,KAAK,QAAQ,MAAM,UAAU,CAAC;AACrC,OAAO,YAAY,MAAM,QAAQ,CAAC;AAClC,OAAO,EAAE,SAAS,EAAE,MAAM,MAAM,CAAC;AAEjC,OAAO,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAC7C,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,qBAAqB,CAAC;AAEzD,OAAO,EAAE,UAAU,EAAE,MAAM,yBAAyB,CAAC;AACrD,OAAO,EAAE,sBAAsB,IAAI,kBAAkB,EAAE,MAAM,mCAAmC,CAAC;AACjG,OAAO,EAAE,aAAa,EAAE,MAAM,oBAAoB,CAAC;AAEnD,OAAO,EAAE,YAAY,EAAE,MAAM,qBAAqB,CAAC;AAEnD,MAAM,iBAAiB,GAAG,SAAS,CAAC,YAAY,CAAC,CAAC;AAClD,MAAM,WAAW,GAAG,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;AACzC,MAAM,WAAW,GAAG,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;AAEzC,MAAM,aAAa,GAAG,KAAK,CAAC,oBAAoB,CAAC,CAAC;AAElD,MAAM,yBAAyB,GAAG;;;6EAG2C,CAAC;AAE9E,MAAM,OAAO,aAAa;IAcxB,YACE,OAAgB,EAChB,cAAsB,EACtB,gBAA0B,EAC1B,WAAmB,EACnB,iBAA2B;QAZ7B,SAAI,GAAG,IAAI,CAAC;QACZ,eAAU,GAAG,IAAI,CAAC;QAEV,YAAO,GAAG,IAAI,CAAC;QACf,eAAU,GAAG,EAAE,CAAC;QAUtB,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxB,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC;QACtC,IAAI,CAAC,iBAAiB,GAAG,gBAAgB,CAAC;QAC1C,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;QAChC,IAAI,CAAC,kBAAkB,GAAG,iBAAiB,CAAC;IAC9C,CAAC;IAED,KAAK,CAAC,OAAsB;QAC1B,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,GACpE,OAAO,CAAC;QACV,IAAI,KAA+B,CAAC;QACpC,IAAI,IAAI,EAAE;YACR,IAAI,MAAM;gBAAE,KAAK,GAAG,CAAC,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;;gBAC1D,KAAK,GAAG,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;SAC7D;aAAM;YACL,IAAI,MAAM;gBAAE,KAAK,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;;gBACxC,KAAK,GAAG,CAAC,MAAM,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;SACzC;QACD,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,2CAA2C,CAAC,CAAC;QAChE,aAAa,CACX,WAAW,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CACtE,CAAC;QACF,IAAI,CAAC,IAAI,GAAG,YAAY,CAAC,KAAK,CAC5B,IAAI,CAAC,eAAe,EACpB,IAAI,CAAC,iBAAiB,EACtB;YACE,mEAAmE;YACnE,kEAAkE;YAClE,gDAAgD;YAChD,2EAA2E;YAC3E,QAAQ,EAAE,OAAO,CAAC,QAAQ,KAAK,OAAO;YACtC,GAAG;YACH,KAAK;SACN,CACF,CAAC;QACF,IAAI,MAAM,EAAE;YACV,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YACtC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;SACvC;QACD,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;QACrB,IAAI,CAAC,eAAe,GAAG,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrD,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,IAAI,EAAE;gBAChC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;gBACpB,6BAA6B;gBAC7B,IAAI,IAAI,CAAC,kBAAkB,EAAE;oBAC3B,IAAI;wBACF,MAAM,iBAAiB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;wBAC3C,OAAO,EAAE,CAAC;qBACX;oBAAC,OAAO,KAAK,EAAE;wBACd,UAAU,CAAC,KAAK,CAAC,CAAC;wBAClB,MAAM,CAAC,KAAK,CAAC,CAAC;qBACf;iBACF;qBAAM;oBACL,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,EAAE;wBAC/B,IAAI;4BACF,8DAA8D;4BAC9D,8DAA8D;4BAC9D,MAAM,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC,CAAC;4BAE3D,MAAM,eAAe,GAAG,IAAI,CAAC,IAAI,CAC/B,IAAI,CAAC,YAAY,EACjB,oBAAoB,CACrB,CAAC;4BACF,IAAI,EAAE,CAAC,UAAU,CAAC,eAAe,CAAC,EAAE;gCAClC,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;gCAC3D,MAAM,WAAW,CAAC,SAAS,CAAC,CAAC;gCAC7B,MAAM,WAAW,CAAC,eAAe,EAAE,SAAS,CAAC,CAAC;6BAC/C;yBACF;wBAAC,OAAO,KAAK,EAAE;4BACd,UAAU,CAAC,KAAK,CAAC,CAAC;4BAClB,MAAM,CAAC,KAAK,CAAC,CAAC;yBACf;qBACF;oBAED,OAAO,EAAE,CAAC;iBACX;YACH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,UAAU,GAAG;YAChB,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SAC/D,CAAC;QACF,IAAI,YAAY;YACd,IAAI,CAAC,UAAU,CAAC,IAAI,CAClB,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,QAAQ,EAAE,GAAG,EAAE;gBAC9C,IAAI,CAAC,IAAI,EAAE,CAAC;gBACZ,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACpB,CAAC,CAAC,CACH,CAAC;QACJ,IAAI,aAAa;YACf,IAAI,CAAC,UAAU,CAAC,IAAI,CAClB,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CACnE,CAAC;QACJ,IAAI,YAAY;YACd,IAAI,CAAC,UAAU,CAAC,IAAI,CAClB,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAClE,CAAC;IACN,CAAC;IAED,KAAK;QACH,IAAI,IAAI,CAAC,OAAO;YAAE,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;QAC3C,IAAI,IAAI,CAAC,kBAAkB,EAAE;YAC3B,IAAI,CAAC,IAAI,EAAE,CAAC;SACb;aAAM,IAAI,IAAI,CAAC,UAAU,EAAE;YAC1B,0CAA0C;YAC1C,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;gBACpD,UAAU,CAAC,KAAK,CAAC,CAAC;gBAClB,IAAI,CAAC,IAAI,EAAE,CAAC;YACd,CAAC,CAAC,CAAC;SACJ;QACD,+EAA+E;QAC/E,2EAA2E;QAC3E,MAAM,CAAC,oBAAoB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC7C,OAAO,IAAI,CAAC,eAAe,CAAC;IAC9B,CAAC;IAED,IAAI;QACF,8EAA8E;QAC9E,uEAAuE;QACvE,uEAAuE;QACvE,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;YAC1D,IAAI;gBACF,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO,EAAE;oBAChC,YAAY,CAAC,IAAI,CAAC,iBAAiB,IAAI,CAAC,IAAI,CAAC,GAAG,QAAQ,EAAE,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;iBACrE;qBAAM;oBACL,2EAA2E;oBAC3E,gEAAgE;oBAChE,MAAM,cAAc,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;oBACtC,OAAO,CAAC,IAAI,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC;iBACzC;aACF;YAAC,OAAO,KAAK,EAAE;gBACd,MAAM,IAAI,KAAK,CACb,GAAG,yBAAyB,kBAAkB,KAAK,CAAC,KAAK,EAAE,CAC5D,CAAC;aACH;SACF;QAED,oEAAoE;QACpE,IAAI;YACF,IAAI,IAAI,CAAC,kBAAkB,EAAE;gBAC3B,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;aACtC;SACF;QAAC,OAAO,KAAK,EAAE,GAAE;QAElB,+EAA+E;QAC/E,2EAA2E;QAC3E,MAAM,CAAC,oBAAoB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IAC/C,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,OAKrB;QACC,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,iBAAiB,EAAE,GAAG,OAAO,CAAC;QAChE,IAAI,CAAC,OAAO,EAAE;YACZ,MAAM,iBAAiB,GAAG,MAAM,iBAAiB,CAC/C,IAAI,CAAC,IAAI,EACT,OAAO,EACP,iBAAiB,CAClB,CAAC;YACF,MAAM,SAAS,GAAG,MAAM,kBAAkB,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;YACrE,IAAI,CAAC,UAAU,GAAG,IAAI,UAAU,CAAC,iBAAiB,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;SACxE;aAAM;YACL,0EAA0E;YAC1E,mCAAmC;YACnC,MAAM,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC;YACtD,MAAM,SAAS,GAAG,IAAI,aAAa,CACjC,SAAkC,EAClC,QAAiC,CAClC,CAAC;YACF,IAAI,CAAC,UAAU,GAAG,IAAI,UAAU,CAAC,EAAE,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;SACzD;QACD,OAAO,IAAI,CAAC,UAAU,CAAC;IACzB,CAAC;CACF;AAED,SAAS,iBAAiB,CACxB,cAAyC,EACzC,OAAe,EACf,iBAAyB;IAEzB,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QACrC,MAAM,EAAE,GAAG,QAAQ,CAAC,eAAe,CAAC,EAAE,KAAK,EAAE,cAAc,CAAC,MAAM,EAAE,CAAC,CAAC;QACtE,IAAI,MAAM,GAAG,EAAE,CAAC;QAChB,MAAM,SAAS,GAAG;YAChB,MAAM,CAAC,gBAAgB,CAAC,EAAE,EAAE,MAAM,EAAE,MAAM,CAAC;YAC3C,MAAM,CAAC,gBAAgB,CAAC,EAAE,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,OAAO,EAAE,CAAC;YACrD,MAAM,CAAC,gBAAgB,CAAC,cAAc,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC,OAAO,EAAE,CAAC;YAChE,MAAM,CAAC,gBAAgB,CAAC,cAAc,EAAE,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE,CACzD,OAAO,CAAC,KAAK,CAAC,CACf;SACF,CAAC;QACF,MAAM,SAAS,GAAG,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAE/D;;WAEG;QACH,SAAS,OAAO,CAAC,KAAa;YAC5B,OAAO,EAAE,CAAC;YACV,MAAM,CACJ,IAAI,KAAK,CACP;gBACE,uCAAuC;oBACrC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;gBACpC,MAAM;gBACN,EAAE;gBACF,2FAA2F;gBAC3F,EAAE;aACH,CAAC,IAAI,CAAC,IAAI,CAAC,CACb,CACF,CAAC;QACJ,CAAC;QAED,SAAS,SAAS;YAChB,OAAO,EAAE,CAAC;YACV,MAAM,CACJ,IAAI,YAAY,CACd,mBAAmB,OAAO,wEAAwE,iBAAiB,yBAAyB,CAC7I,CACF,CAAC;QACJ,CAAC;QAED,SAAS,MAAM,CAAC,IAAY;YAC1B,MAAM,IAAI,IAAI,GAAG,IAAI,CAAC;YACtB,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,qCAAqC,CAAC,CAAC;YAChE,IAAI,CAAC,KAAK;gBAAE,OAAO;YACnB,OAAO,EAAE,CAAC;YACV,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC;QAED,SAAS,OAAO;YACd,IAAI,SAAS;gBAAE,YAAY,CAAC,SAAS,CAAC,CAAC;YACvC,MAAM,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAC;QACzC,CAAC;IACH,CAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAS,SAAS,CAAC,GAAW;IAC5B,IAAI;QACF,OAAO,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;KAC7B;IAAC,OAAO,KAAK,EAAE;QACd,IAAI,KAAK,IAAI,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE;YACjD,OAAO,KAAK,CAAC;SACd;aAAM;YACL,MAAM,KAAK,CAAC;SACb;KACF;AACH,CAAC"}