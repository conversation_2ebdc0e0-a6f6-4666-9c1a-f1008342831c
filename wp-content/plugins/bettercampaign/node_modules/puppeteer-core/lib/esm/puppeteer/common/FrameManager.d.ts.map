{"version": 3, "file": "FrameManager.d.ts", "sourceRoot": "", "sources": ["../../../../src/common/FrameManager.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EAAE,YAAY,EAAE,MAAM,mBAAmB,CAAC;AAGjD,OAAO,EAAE,gBAAgB,EAAyB,MAAM,uBAAuB,CAAC;AAChF,OAAO,EAEL,uBAAuB,EACxB,MAAM,uBAAuB,CAAC;AAC/B,OAAO,EAAE,QAAQ,EAAE,sBAAsB,EAAE,MAAM,eAAe,CAAC;AACjE,OAAO,EAAE,cAAc,EAAE,MAAM,qBAAqB,CAAC;AACrD,OAAO,EAAE,eAAe,EAAE,MAAM,sBAAsB,CAAC;AACvD,OAAO,EAAc,UAAU,EAAE,MAAM,iBAAiB,CAAC;AACzD,OAAO,EAAE,QAAQ,EAAE,aAAa,EAAE,MAAM,eAAe,CAAC;AACxD,OAAO,EAAE,WAAW,EAAE,MAAM,YAAY,CAAC;AACzC,OAAO,EAAE,IAAI,EAAE,MAAM,WAAW,CAAC;AACjC,OAAO,EAAE,YAAY,EAAE,MAAM,mBAAmB,CAAC;AACjD,OAAO,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AAC7C,OAAO,EACL,sBAAsB,EACtB,gBAAgB,EAChB,iBAAiB,EACjB,UAAU,EACV,oBAAoB,EACpB,iBAAiB,EAClB,MAAM,gBAAgB,CAAC;AAKxB;;;;;GAKG;AACH,eAAO,MAAM,yBAAyB;;;;;;;;;CAWrC,CAAC;AAEF;;GAEG;AACH,qBAAa,YAAa,SAAQ,YAAY;IAC5C,OAAO,EAAE,UAAU,CAAC;IACpB,OAAO,CAAC,KAAK,CAAO;IACpB,OAAO,CAAC,eAAe,CAAiB;IACxC,gBAAgB,EAAE,eAAe,CAAC;IAClC,OAAO,CAAC,OAAO,CAA4B;IAC3C,OAAO,CAAC,mBAAmB,CAAuC;IAClE,OAAO,CAAC,eAAe,CAAqB;IAC5C,OAAO,CAAC,UAAU,CAAQ;gBAGxB,MAAM,EAAE,UAAU,EAClB,IAAI,EAAE,IAAI,EACV,iBAAiB,EAAE,OAAO,EAC1B,eAAe,EAAE,eAAe;IAUlC,OAAO,CAAC,mBAAmB;IA0CrB,UAAU,CAAC,MAAM,GAAE,UAAyB,GAAG,OAAO,CAAC,IAAI,CAAC;IAgClE,cAAc,IAAI,cAAc;IAI1B,aAAa,CACjB,KAAK,EAAE,KAAK,EACZ,GAAG,EAAE,MAAM,EACX,OAAO,GAAE;QACP,OAAO,CAAC,EAAE,MAAM,CAAC;QACjB,OAAO,CAAC,EAAE,MAAM,CAAC;QACjB,SAAS,CAAC,EAAE,uBAAuB,GAAG,uBAAuB,EAAE,CAAC;KAC5D,GACL,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC;IAgDzB,sBAAsB,CAC1B,KAAK,EAAE,KAAK,EACZ,OAAO,GAAE;QACP,OAAO,CAAC,EAAE,MAAM,CAAC;QACjB,SAAS,CAAC,EAAE,uBAAuB,GAAG,uBAAuB,EAAE,CAAC;KAC5D,GACL,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC;YAiBjB,mBAAmB;YAgBnB,qBAAqB;IAWnC,iBAAiB,CAAC,KAAK,EAAE,QAAQ,CAAC,IAAI,CAAC,mBAAmB,GAAG,IAAI;IAOjE,sBAAsB,CAAC,OAAO,EAAE,MAAM,GAAG,IAAI;IAO7C,gBAAgB,CACd,OAAO,EAAE,UAAU,EACnB,SAAS,EAAE,QAAQ,CAAC,IAAI,CAAC,SAAS,GACjC,IAAI;IAgBP,IAAI,IAAI,IAAI;IAIZ,SAAS,IAAI,KAAK;IAIlB,MAAM,IAAI,KAAK,EAAE;IAIjB,KAAK,CAAC,OAAO,EAAE,MAAM,GAAG,KAAK,GAAG,IAAI;IAIpC,gBAAgB,CACd,OAAO,EAAE,UAAU,EACnB,OAAO,EAAE,MAAM,EACf,aAAa,CAAC,EAAE,MAAM,GACrB,IAAI;IAkBP,iBAAiB,CAAC,YAAY,EAAE,QAAQ,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI;IAoCpD,oBAAoB,CAAC,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAyB5E,+BAA+B,CAAC,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,GAAG,IAAI;IAQnE,gBAAgB,CACd,OAAO,EAAE,MAAM,EACf,MAAM,EAAE,QAAQ,CAAC,IAAI,CAAC,wBAAwB,GAC7C,IAAI;IAYP,0BAA0B,CACxB,cAAc,EAAE,QAAQ,CAAC,OAAO,CAAC,2BAA2B,EAC5D,OAAO,EAAE,UAAU,GAClB,IAAI;IA+BP,OAAO,CAAC,4BAA4B;IAWpC,OAAO,CAAC,2BAA2B;IAUnC,oBAAoB,CAClB,SAAS,EAAE,MAAM,EACjB,OAAO,GAAE,UAAyB,GACjC,gBAAgB;IAOnB,OAAO,CAAC,wBAAwB;CAOjC;AAED;;GAEG;AACH,MAAM,WAAW,2BAA2B;IAC1C;;;;;;;;;;;OAWG;IACH,OAAO,CAAC,EAAE,MAAM,GAAG,MAAM,CAAC;IAC1B;;;;OAIG;IACH,OAAO,CAAC,EAAE,MAAM,CAAC;CAClB;AAED;;GAEG;AACH,MAAM,WAAW,wBAAwB;IACvC;;OAEG;IACH,GAAG,CAAC,EAAE,MAAM,CAAC;IACb;;;;;OAKG;IACH,IAAI,CAAC,EAAE,MAAM,CAAC;IACd;;OAEG;IACH,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB;;OAEG;IACH,IAAI,CAAC,EAAE,MAAM,CAAC;CACf;AAED;;GAEG;AACH,MAAM,WAAW,uBAAuB;IACtC;;OAEG;IACH,GAAG,CAAC,EAAE,MAAM,CAAC;IACb;;;;;OAKG;IACH,IAAI,CAAC,EAAE,MAAM,CAAC;IACd;;OAEG;IACH,OAAO,CAAC,EAAE,MAAM,CAAC;CAClB;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAgDG;AACH,qBAAa,KAAK;IAChB;;OAEG;IACH,aAAa,EAAE,YAAY,CAAC;IAC5B,OAAO,CAAC,YAAY,CAAC,CAAQ;IAC7B;;OAEG;IACH,GAAG,EAAE,MAAM,CAAC;IAEZ,OAAO,CAAC,IAAI,CAAM;IAClB,OAAO,CAAC,SAAS,CAAS;IAC1B;;OAEG;IACH,SAAS,SAAM;IACf;;OAEG;IACH,KAAK,CAAC,EAAE,MAAM,CAAC;IAEf;;OAEG;IACH,gBAAgB,cAAqB;IACrC;;OAEG;IACH,UAAU,EAAE,QAAQ,CAAC;IACrB;;OAEG;IACH,eAAe,EAAE,QAAQ,CAAC;IAC1B;;OAEG;IACH,YAAY,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC;IACzB;;OAEG;IACH,OAAO,EAAE,UAAU,CAAC;IAEpB;;OAEG;gBAED,YAAY,EAAE,YAAY,EAC1B,WAAW,EAAE,KAAK,GAAG,IAAI,EACzB,OAAO,EAAE,MAAM,EACf,MAAM,EAAE,UAAU;IAgBpB;;OAEG;IACH,aAAa,CAAC,MAAM,EAAE,UAAU,GAAG,IAAI;IAgBvC;;;;OAIG;IACH,UAAU,IAAI,OAAO;IAIrB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAqCG;IACG,IAAI,CACR,GAAG,EAAE,MAAM,EACX,OAAO,GAAE;QACP,OAAO,CAAC,EAAE,MAAM,CAAC;QACjB,OAAO,CAAC,EAAE,MAAM,CAAC;QACjB,SAAS,CAAC,EAAE,uBAAuB,GAAG,uBAAuB,EAAE,CAAC;KAC5D,GACL,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC;IAI/B;;;;;;;;;;;;;;;;;;;;OAoBG;IACG,iBAAiB,CACrB,OAAO,GAAE;QACP,OAAO,CAAC,EAAE,MAAM,CAAC;QACjB,SAAS,CAAC,EAAE,uBAAuB,GAAG,uBAAuB,EAAE,CAAC;KAC5D,GACL,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC;IAI/B;;OAEG;IACH,MAAM,IAAI,UAAU;IAIpB;;OAEG;IACH,gBAAgB,IAAI,OAAO,CAAC,gBAAgB,CAAC;IAI7C;;;;;;;;;;;;OAYG;IACG,cAAc,CAAC,WAAW,SAAS,QAAQ,GAAG,QAAQ,EAC1D,YAAY,EAAE,gBAAgB,EAC9B,GAAG,IAAI,EAAE,sBAAsB,EAAE,GAChC,OAAO,CAAC,WAAW,CAAC;IAIvB;;;;;;;;OAQG;IACG,QAAQ,CAAC,CAAC,SAAS,UAAU,EACjC,YAAY,EAAE,CAAC,EACf,GAAG,IAAI,EAAE,sBAAsB,EAAE,GAChC,OAAO,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC;IAItD;;;;;;OAMG;IACG,CAAC,CAAC,CAAC,SAAS,OAAO,GAAG,OAAO,EACjC,QAAQ,EAAE,MAAM,GACf,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;IAInC;;;;OAIG;IACG,EAAE,CAAC,UAAU,EAAE,MAAM,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;IAItD;;;;;;;;;;;;;;;;;;OAkBG;IACG,KAAK,CAAC,UAAU,EACpB,QAAQ,EAAE,MAAM,EAChB,YAAY,EAAE,CACZ,OAAO,EAAE,OAAO,EAChB,GAAG,IAAI,EAAE,OAAO,EAAE,KACf,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC,EACrC,GAAG,IAAI,EAAE,sBAAsB,EAAE,GAChC,OAAO,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;IAIzC;;;;;;;;;;;;;;;;;;OAkBG;IACG,MAAM,CAAC,UAAU,EACrB,QAAQ,EAAE,MAAM,EAChB,YAAY,EAAE,CACZ,QAAQ,EAAE,OAAO,EAAE,EACnB,GAAG,IAAI,EAAE,OAAO,EAAE,KACf,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC,EACrC,GAAG,IAAI,EAAE,sBAAsB,EAAE,GAChC,OAAO,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;IAIzC;;;;;OAKG;IACG,EAAE,CAAC,CAAC,SAAS,OAAO,GAAG,OAAO,EAClC,QAAQ,EAAE,MAAM,GACf,OAAO,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;IAInC;;OAEG;IACG,OAAO,IAAI,OAAO,CAAC,MAAM,CAAC;IAIhC;;;;;;OAMG;IACG,UAAU,CACd,IAAI,EAAE,MAAM,EACZ,OAAO,GAAE;QACP,OAAO,CAAC,EAAE,MAAM,CAAC;QACjB,SAAS,CAAC,EAAE,uBAAuB,GAAG,uBAAuB,EAAE,CAAC;KAC5D,GACL,OAAO,CAAC,IAAI,CAAC;IAIhB;;;;;;;;;OASG;IACH,IAAI,IAAI,MAAM;IAId;;OAEG;IACH,GAAG,IAAI,MAAM;IAIb;;OAEG;IACH,WAAW,IAAI,KAAK,GAAG,IAAI;IAI3B;;OAEG;IACH,WAAW,IAAI,KAAK,EAAE;IAItB;;OAEG;IACH,UAAU,IAAI,OAAO;IAIrB;;;;;;;;OAQG;IACG,YAAY,CAChB,OAAO,EAAE,wBAAwB,GAChC,OAAO,CAAC,aAAa,CAAC;IAIzB;;;;;;;;;OASG;IACG,WAAW,CAAC,OAAO,EAAE,uBAAuB,GAAG,OAAO,CAAC,aAAa,CAAC;IAI3E;;;;;;;;;;;;;;;;;;;;;;;OAuBG;IACG,KAAK,CACT,QAAQ,EAAE,MAAM,EAChB,OAAO,GAAE;QACP,KAAK,CAAC,EAAE,MAAM,CAAC;QACf,MAAM,CAAC,EAAE,WAAW,CAAC;QACrB,UAAU,CAAC,EAAE,MAAM,CAAC;KAChB,GACL,OAAO,CAAC,IAAI,CAAC;IAIhB;;;;;;;;OAQG;IACG,KAAK,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAI5C;;;;;;;;;;OAUG;IACG,KAAK,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAI5C;;;;;;;;;;;;;;;;;;;;OAoBG;IACH,MAAM,CAAC,QAAQ,EAAE,MAAM,EAAE,GAAG,MAAM,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC;IAIhE;;;;;;;;;;;OAWG;IACG,GAAG,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAI1C;;;;;;;;;;;;;;;;;;;;;OAqBG;IACG,IAAI,CACR,QAAQ,EAAE,MAAM,EAChB,IAAI,EAAE,MAAM,EACZ,OAAO,CAAC,EAAE;QAAE,KAAK,EAAE,MAAM,CAAA;KAAE,GAC1B,OAAO,CAAC,IAAI,CAAC;IAIhB;;;;;;;;;;;;;;;;;;;;;;;;OAwBG;IACH,OAAO,CACL,2BAA2B,EAAE,MAAM,GAAG,MAAM,GAAG,QAAQ,EACvD,OAAO,GAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAM,EACrC,GAAG,IAAI,EAAE,sBAAsB,EAAE,GAChC,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC;IA2B3B;;;;;;;;;;;;;;;;;OAiBG;IACH,cAAc,CAAC,YAAY,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAMnD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAkCG;IACG,eAAe,CACnB,QAAQ,EAAE,MAAM,EAChB,OAAO,GAAE,sBAA2B,GACnC,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC;IAYhC;;;;;;;;;;;;;;OAcG;IACG,YAAY,CAChB,KAAK,EAAE,MAAM,EACb,OAAO,GAAE,sBAA2B,GACnC,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC;IAShC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAkCG;IACH,eAAe,CACb,YAAY,EAAE,QAAQ,GAAG,MAAM,EAC/B,OAAO,GAAE,2BAAgC,EACzC,GAAG,IAAI,EAAE,sBAAsB,EAAE,GAChC,OAAO,CAAC,QAAQ,CAAC;IAIpB;;OAEG;IACG,KAAK,IAAI,OAAO,CAAC,MAAM,CAAC;IAI9B;;OAEG;IACH,UAAU,CAAC,YAAY,EAAE,QAAQ,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI;IAKnD;;OAEG;IACH,wBAAwB,CAAC,GAAG,EAAE,MAAM,GAAG,IAAI;IAI3C;;OAEG;IACH,iBAAiB,CAAC,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,GAAG,IAAI;IAQvD;;OAEG;IACH,iBAAiB,IAAI,IAAI;IAKzB;;OAEG;IACH,OAAO,IAAI,IAAI;CAOhB"}