"use strict";
/**
 * Copyright 2017 Google Inc. All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the 'License');
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an 'AS IS' BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Touchscreen = exports.Mouse = exports.Keyboard = void 0;
const Bidi = __importStar(require("chromium-bidi/lib/cjs/protocol/protocol.js"));
const Input_js_1 = require("../../api/Input.js");
const getBidiKeyValue = (key) => {
    switch (key) {
        case '\r':
        case '\n':
            key = 'Enter';
            break;
    }
    // Measures the number of code points rather than UTF-16 code units.
    if ([...key].length === 1) {
        return key;
    }
    switch (key) {
        case 'Cancel':
            return '\uE001';
        case 'Help':
            return '\uE002';
        case 'Backspace':
            return '\uE003';
        case 'Tab':
            return '\uE004';
        case 'Clear':
            return '\uE005';
        case 'Enter':
            return '\uE007';
        case 'Shift':
        case 'ShiftLeft':
            return '\uE008';
        case 'Control':
        case 'ControlLeft':
            return '\uE009';
        case 'Alt':
        case 'AltLeft':
            return '\uE00A';
        case 'Pause':
            return '\uE00B';
        case 'Escape':
            return '\uE00C';
        case 'PageUp':
            return '\uE00E';
        case 'PageDown':
            return '\uE00F';
        case 'End':
            return '\uE010';
        case 'Home':
            return '\uE011';
        case 'ArrowLeft':
            return '\uE012';
        case 'ArrowUp':
            return '\uE013';
        case 'ArrowRight':
            return '\uE014';
        case 'ArrowDown':
            return '\uE015';
        case 'Insert':
            return '\uE016';
        case 'Delete':
            return '\uE017';
        case 'NumpadEqual':
            return '\uE019';
        case 'Numpad0':
            return '\uE01A';
        case 'Numpad1':
            return '\uE01B';
        case 'Numpad2':
            return '\uE01C';
        case 'Numpad3':
            return '\uE01D';
        case 'Numpad4':
            return '\uE01E';
        case 'Numpad5':
            return '\uE01F';
        case 'Numpad6':
            return '\uE020';
        case 'Numpad7':
            return '\uE021';
        case 'Numpad8':
            return '\uE022';
        case 'Numpad9':
            return '\uE023';
        case 'NumpadMultiply':
            return '\uE024';
        case 'NumpadAdd':
            return '\uE025';
        case 'NumpadSubtract':
            return '\uE027';
        case 'NumpadDecimal':
            return '\uE028';
        case 'NumpadDivide':
            return '\uE029';
        case 'F1':
            return '\uE031';
        case 'F2':
            return '\uE032';
        case 'F3':
            return '\uE033';
        case 'F4':
            return '\uE034';
        case 'F5':
            return '\uE035';
        case 'F6':
            return '\uE036';
        case 'F7':
            return '\uE037';
        case 'F8':
            return '\uE038';
        case 'F9':
            return '\uE039';
        case 'F10':
            return '\uE03A';
        case 'F11':
            return '\uE03B';
        case 'F12':
            return '\uE03C';
        case 'Meta':
        case 'MetaLeft':
            return '\uE03D';
        case 'ShiftRight':
            return '\uE050';
        case 'ControlRight':
            return '\uE051';
        case 'AltRight':
            return '\uE052';
        case 'MetaRight':
            return '\uE053';
        case 'Digit0':
            return '0';
        case 'Digit1':
            return '1';
        case 'Digit2':
            return '2';
        case 'Digit3':
            return '3';
        case 'Digit4':
            return '4';
        case 'Digit5':
            return '5';
        case 'Digit6':
            return '6';
        case 'Digit7':
            return '7';
        case 'Digit8':
            return '8';
        case 'Digit9':
            return '9';
        case 'KeyA':
            return 'a';
        case 'KeyB':
            return 'b';
        case 'KeyC':
            return 'c';
        case 'KeyD':
            return 'd';
        case 'KeyE':
            return 'e';
        case 'KeyF':
            return 'f';
        case 'KeyG':
            return 'g';
        case 'KeyH':
            return 'h';
        case 'KeyI':
            return 'i';
        case 'KeyJ':
            return 'j';
        case 'KeyK':
            return 'k';
        case 'KeyL':
            return 'l';
        case 'KeyM':
            return 'm';
        case 'KeyN':
            return 'n';
        case 'KeyO':
            return 'o';
        case 'KeyP':
            return 'p';
        case 'KeyQ':
            return 'q';
        case 'KeyR':
            return 'r';
        case 'KeyS':
            return 's';
        case 'KeyT':
            return 't';
        case 'KeyU':
            return 'u';
        case 'KeyV':
            return 'v';
        case 'KeyW':
            return 'w';
        case 'KeyX':
            return 'x';
        case 'KeyY':
            return 'y';
        case 'KeyZ':
            return 'z';
        case 'Semicolon':
            return ';';
        case 'Equal':
            return '=';
        case 'Comma':
            return ',';
        case 'Minus':
            return '-';
        case 'Period':
            return '.';
        case 'Slash':
            return '/';
        case 'Backquote':
            return '`';
        case 'BracketLeft':
            return '[';
        case 'Backslash':
            return '\\';
        case 'BracketRight':
            return ']';
        case 'Quote':
            return '"';
        default:
            throw new Error(`Unknown key: "${key}"`);
    }
};
/**
 * @internal
 */
class Keyboard extends Input_js_1.Keyboard {
    #context;
    /**
     * @internal
     */
    constructor(context) {
        super();
        this.#context = context;
    }
    async down(key, _options) {
        await this.#context.connection.send('input.performActions', {
            context: this.#context.id,
            actions: [
                {
                    type: Bidi.Input.SourceActionsType.Key,
                    id: "__puppeteer_keyboard" /* InputId.Keyboard */,
                    actions: [
                        {
                            type: Bidi.Input.ActionType.KeyDown,
                            value: getBidiKeyValue(key),
                        },
                    ],
                },
            ],
        });
    }
    async up(key) {
        await this.#context.connection.send('input.performActions', {
            context: this.#context.id,
            actions: [
                {
                    type: Bidi.Input.SourceActionsType.Key,
                    id: "__puppeteer_keyboard" /* InputId.Keyboard */,
                    actions: [
                        {
                            type: Bidi.Input.ActionType.KeyUp,
                            value: getBidiKeyValue(key),
                        },
                    ],
                },
            ],
        });
    }
    async press(key, options = {}) {
        const { delay = 0 } = options;
        const actions = [
            {
                type: Bidi.Input.ActionType.KeyDown,
                value: getBidiKeyValue(key),
            },
        ];
        if (delay > 0) {
            actions.push({
                type: Bidi.Input.ActionType.Pause,
                duration: delay,
            });
        }
        actions.push({
            type: Bidi.Input.ActionType.KeyUp,
            value: getBidiKeyValue(key),
        });
        await this.#context.connection.send('input.performActions', {
            context: this.#context.id,
            actions: [
                {
                    type: Bidi.Input.SourceActionsType.Key,
                    id: "__puppeteer_keyboard" /* InputId.Keyboard */,
                    actions,
                },
            ],
        });
    }
    async type(text, options = {}) {
        const { delay = 0 } = options;
        // This spread separates the characters into code points rather than UTF-16
        // code units.
        const values = [...text].map(getBidiKeyValue);
        const actions = [];
        if (delay <= 0) {
            for (const value of values) {
                actions.push({
                    type: Bidi.Input.ActionType.KeyDown,
                    value,
                }, {
                    type: Bidi.Input.ActionType.KeyUp,
                    value,
                });
            }
        }
        else {
            for (const value of values) {
                actions.push({
                    type: Bidi.Input.ActionType.KeyDown,
                    value,
                }, {
                    type: Bidi.Input.ActionType.Pause,
                    duration: delay,
                }, {
                    type: Bidi.Input.ActionType.KeyUp,
                    value,
                });
            }
        }
        await this.#context.connection.send('input.performActions', {
            context: this.#context.id,
            actions: [
                {
                    type: Bidi.Input.SourceActionsType.Key,
                    id: "__puppeteer_keyboard" /* InputId.Keyboard */,
                    actions,
                },
            ],
        });
    }
}
exports.Keyboard = Keyboard;
const getBidiButton = (button) => {
    switch (button) {
        case Input_js_1.MouseButton.Left:
            return 0;
        case Input_js_1.MouseButton.Middle:
            return 1;
        case Input_js_1.MouseButton.Right:
            return 2;
        case Input_js_1.MouseButton.Back:
            return 3;
        case Input_js_1.MouseButton.Forward:
            return 4;
    }
};
/**
 * @internal
 */
class Mouse extends Input_js_1.Mouse {
    #context;
    #lastMovePoint;
    /**
     * @internal
     */
    constructor(context) {
        super();
        this.#context = context;
    }
    async reset() {
        this.#lastMovePoint = undefined;
        await this.#context.connection.send('input.releaseActions', {
            context: this.#context.id,
        });
    }
    async move(x, y, options = {}) {
        this.#lastMovePoint = {
            x,
            y,
        };
        await this.#context.connection.send('input.performActions', {
            context: this.#context.id,
            actions: [
                {
                    type: Bidi.Input.SourceActionsType.Pointer,
                    id: "__puppeteer_mouse" /* InputId.Mouse */,
                    actions: [
                        {
                            type: Bidi.Input.ActionType.PointerMove,
                            x,
                            y,
                            duration: (options.steps ?? 0) * 50,
                            origin: options.origin,
                        },
                    ],
                },
            ],
        });
    }
    async down(options = {}) {
        await this.#context.connection.send('input.performActions', {
            context: this.#context.id,
            actions: [
                {
                    type: Bidi.Input.SourceActionsType.Pointer,
                    id: "__puppeteer_mouse" /* InputId.Mouse */,
                    actions: [
                        {
                            type: Bidi.Input.ActionType.PointerDown,
                            button: getBidiButton(options.button ?? Input_js_1.MouseButton.Left),
                        },
                    ],
                },
            ],
        });
    }
    async up(options = {}) {
        await this.#context.connection.send('input.performActions', {
            context: this.#context.id,
            actions: [
                {
                    type: Bidi.Input.SourceActionsType.Pointer,
                    id: "__puppeteer_mouse" /* InputId.Mouse */,
                    actions: [
                        {
                            type: Bidi.Input.ActionType.PointerUp,
                            button: getBidiButton(options.button ?? Input_js_1.MouseButton.Left),
                        },
                    ],
                },
            ],
        });
    }
    async click(x, y, options = {}) {
        const actions = [
            {
                type: Bidi.Input.ActionType.PointerMove,
                x,
                y,
                origin: options.origin,
            },
        ];
        const pointerDownAction = {
            type: Bidi.Input.ActionType.PointerDown,
            button: getBidiButton(options.button ?? Input_js_1.MouseButton.Left),
        };
        const pointerUpAction = {
            type: Bidi.Input.ActionType.PointerUp,
            button: pointerDownAction.button,
        };
        for (let i = 1; i < (options.count ?? 1); ++i) {
            actions.push(pointerDownAction, pointerUpAction);
        }
        actions.push(pointerDownAction);
        if (options.delay) {
            actions.push({
                type: Bidi.Input.ActionType.Pause,
                duration: options.delay,
            });
        }
        actions.push(pointerUpAction);
        await this.#context.connection.send('input.performActions', {
            context: this.#context.id,
            actions: [
                {
                    type: Bidi.Input.SourceActionsType.Pointer,
                    id: "__puppeteer_mouse" /* InputId.Mouse */,
                    actions,
                },
            ],
        });
    }
    async wheel(options = {}) {
        await this.#context.connection.send('input.performActions', {
            context: this.#context.id,
            actions: [
                {
                    type: Bidi.Input.SourceActionsType.Wheel,
                    id: "__puppeteer_wheel" /* InputId.Wheel */,
                    actions: [
                        {
                            type: Bidi.Input.ActionType.Scroll,
                            ...(this.#lastMovePoint ?? {
                                x: 0,
                                y: 0,
                            }),
                            deltaX: options.deltaX ?? 0,
                            deltaY: options.deltaY ?? 0,
                        },
                    ],
                },
            ],
        });
    }
}
exports.Mouse = Mouse;
/**
 * @internal
 */
class Touchscreen extends Input_js_1.Touchscreen {
    #context;
    /**
     * @internal
     */
    constructor(context) {
        super();
        this.#context = context;
    }
    async tap(x, y, options = {}) {
        await this.touchStart(x, y, options);
        await this.touchEnd();
    }
    async touchStart(x, y, options = {}) {
        await this.#context.connection.send('input.performActions', {
            context: this.#context.id,
            actions: [
                {
                    type: Bidi.Input.SourceActionsType.Pointer,
                    id: "__puppeteer_finger" /* InputId.Finger */,
                    parameters: {
                        pointerType: Bidi.Input.PointerType.Touch,
                    },
                    actions: [
                        {
                            type: Bidi.Input.ActionType.PointerMove,
                            x,
                            y,
                            origin: options.origin,
                        },
                        {
                            type: Bidi.Input.ActionType.PointerDown,
                            button: 0,
                        },
                    ],
                },
            ],
        });
    }
    async touchMove(x, y, options = {}) {
        await this.#context.connection.send('input.performActions', {
            context: this.#context.id,
            actions: [
                {
                    type: Bidi.Input.SourceActionsType.Pointer,
                    id: "__puppeteer_finger" /* InputId.Finger */,
                    parameters: {
                        pointerType: Bidi.Input.PointerType.Touch,
                    },
                    actions: [
                        {
                            type: Bidi.Input.ActionType.PointerMove,
                            x,
                            y,
                            origin: options.origin,
                        },
                    ],
                },
            ],
        });
    }
    async touchEnd() {
        await this.#context.connection.send('input.performActions', {
            context: this.#context.id,
            actions: [
                {
                    type: Bidi.Input.SourceActionsType.Pointer,
                    id: "__puppeteer_finger" /* InputId.Finger */,
                    parameters: {
                        pointerType: Bidi.Input.PointerType.Touch,
                    },
                    actions: [
                        {
                            type: Bidi.Input.ActionType.PointerUp,
                            button: 0,
                        },
                    ],
                },
            ],
        });
    }
}
exports.Touchscreen = Touchscreen;
//# sourceMappingURL=Input.js.map