{"version": 3, "file": "Browser.js", "sourceRoot": "", "sources": ["../../../../../src/common/bidi/Browser.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAMH,OAAO,EACL,OAAO,IAAI,WAAW,GAIvB,MAAM,sBAAsB,CAAC;AAK9B,OAAO,EAAC,cAAc,EAAC,MAAM,qBAAqB,CAAC;AAEnD,OAAO,EAAC,UAAU,EAAC,MAAM,YAAY,CAAC;AAEtC;;GAEG;AACH,MAAM,OAAO,OAAQ,SAAQ,WAAW;IACtC,MAAM,CAAU,gBAAgB,GAA4C;QAC1E,iBAAiB;QACjB,SAAS;QACT,KAAK;KACN,CAAC;IACF,MAAM,CAAU,kBAAkB,GAA0B;QAC1D,WAAW;QACX,2BAA2B;QAC3B,yBAAyB;QACzB,sCAAsC;QACtC,UAAU;QACV,6BAA6B;KAC9B,CAAC;IAEF,YAAY,GAAG,EAAE,CAAC;IAClB,eAAe,GAAG,EAAE,CAAC;IAErB,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,IAAa;QAC/B,IAAI,WAAW,GAAG,EAAE,CAAC;QACrB,IAAI,cAAc,GAAG,EAAE,CAAC;QAExB,mDAAmD;QACnD,IAAI;YACF,MAAM,EAAC,MAAM,EAAC,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,aAAa,EAAE;gBACzD,YAAY,EAAE;oBACZ,WAAW,EAAE;wBACX,mBAAmB,EAAE,IAAI,CAAC,iBAAiB;qBAC5C;iBACF;aACF,CAAC,CAAC;YACH,WAAW,GAAG,MAAM,CAAC,YAAY,CAAC,WAAW,IAAI,EAAE,CAAC;YACpD,cAAc,GAAG,MAAM,CAAC,YAAY,CAAC,cAAc,IAAI,EAAE,CAAC;SAC3D;QAAC,OAAO,GAAG,EAAE;YACZ,uCAAuC;YACvC,UAAU,CAAC,GAAG,CAAC,CAAC;SACjB;QAED,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,mBAAmB,EAAE;YAC9C,MAAM,EAAE,WAAW,CAAC,iBAAiB,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC;gBACzD,CAAC,CAAC,OAAO,CAAC,gBAAgB;gBAC1B,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,gBAAgB,EAAE,GAAG,OAAO,CAAC,kBAAkB,CAAC;SACjE,CAAC,CAAC;QAEH,OAAO,IAAI,OAAO,CAAC;YACjB,GAAG,IAAI;YACP,WAAW;YACX,cAAc;SACf,CAAC,CAAC;IACL,CAAC;IAED,QAAQ,CAAgB;IACxB,cAAc,CAAwB;IACtC,WAAW,CAAa;IACxB,gBAAgB,CAAkB;IAClC,eAAe,CAAiB;IAEhC,YACE,IAGC;QAED,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC;QAC7B,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,aAAa,CAAC;QACzC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC;QACnC,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,eAAe,CAAC;QAC7C,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,WAAW,CAAC;QACrC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,cAAc,CAAC;QAE3C,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,EAAE,GAAG,EAAE;YAChC,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;YAC3B,IAAI,CAAC,IAAI,wDAAmC,CAAC;QAC/C,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,eAAe,GAAG,IAAI,cAAc,CAAC,IAAI,EAAE;YAC9C,eAAe,EAAE,IAAI,CAAC,gBAAgB;YACtC,SAAS,EAAE,IAAI;SAChB,CAAC,CAAC;IACL,CAAC;IAED,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC;IAEQ,UAAU;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC;IAC9B,CAAC;IAEQ,KAAK,CAAC,KAAK;QAClB,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE;YAC3B,OAAO;SACR;QACD,iCAAiC;QACjC,oDAAoD;QACpD,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;QAC3B,MAAM,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;IACxC,CAAC;IAEQ,WAAW;QAClB,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;IAClC,CAAC;IAEQ,OAAO;QACd,OAAO,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC;IAC/B,CAAC;IAEQ,KAAK,CAAC,6BAA6B,CAC1C,QAAgC;QAEhC,sFAAsF;QACtF,OAAO,IAAI,cAAc,CAAC,IAAI,EAAE;YAC9B,eAAe,EAAE,IAAI,CAAC,gBAAgB;YACtC,SAAS,EAAE,KAAK;SACjB,CAAC,CAAC;IACL,CAAC;IAEQ,KAAK,CAAC,OAAO;QACpB,OAAO,GAAG,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;IACxD,CAAC;IAED;;;OAGG;IACM,eAAe;QACtB,sFAAsF;QACtF,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;IAChC,CAAC;IAED;;OAEG;IACM,qBAAqB;QAC5B,OAAO,IAAI,CAAC,eAAe,CAAC;IAC9B,CAAC;IAEQ,OAAO;QACd,OAAO,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;IACxC,CAAC"}