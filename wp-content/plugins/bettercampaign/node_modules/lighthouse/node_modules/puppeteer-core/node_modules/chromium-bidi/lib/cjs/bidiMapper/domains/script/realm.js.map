{"version": 3, "file": "realm.js", "sourceRoot": "", "sources": ["../../../../../src/bidiMapper/domains/script/realm.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;GAeG;;;AAIH,+DAAqD;AAKrD,kDAA6D;AAE7D,6DAAwE;AAKxE,MAAa,KAAK;IACP,aAAa,CAAe;IAC5B,uBAAuB,CAAyB;IAChD,QAAQ,CAAe;IACvB,kBAAkB,CAAkC;IACpD,mBAAmB,CAAsC;IACzD,OAAO,CAAS;IAChB,KAAK,CAAY;IACjB,UAAU,CAAa;IACvB,aAAa,CAAgB;IAC7B,gBAAgB,CAAkB;IAClC,OAAO,CAAU;IACjB,YAAY,CAAS;IAErB,OAAO,CAAY;IAE5B,YACE,YAA0B,EAC1B,sBAA8C,EAC9C,OAAqB,EACrB,iBAAkD,EAClD,kBAAuD,EACvD,MAAc,EACd,IAAe,EACf,OAA2B,EAC3B,YAAoB,EACpB,SAAqB,EACrB,YAA2B,EAC3B,MAAiB;QAEjB,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxB,IAAI,CAAC,kBAAkB,GAAG,iBAAiB,CAAC;QAC5C,IAAI,CAAC,mBAAmB,GAAG,kBAAkB,CAAC;QAC9C,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QACjC,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;QAC5B,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QAClC,IAAI,CAAC,uBAAuB,GAAG,sBAAsB,CAAC;QACtD,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QAClC,IAAI,CAAC,gBAAgB,GAAG,IAAI,oCAAe,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAEhE,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAElC,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QAEtB,IAAI,CAAC,aAAa,CAAC,aAAa,CAC9B;YACE,MAAM,EAAE,oBAAM,CAAC,UAAU,CAAC,YAAY;YACtC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE;SACtB,EACD,IAAI,CAAC,iBAAiB,CACvB,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,MAA8B;QACjD,IAAI;YACF,MAAM,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,uBAAuB,EAAE;gBACxD,QAAQ,EAAE,MAAM;aACjB,CAAC,CAAC;SACJ;QAAC,OAAO,CAAM,EAAE;YACf,mEAAmE;YACnE,0BAA0B;YAC1B,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,KAAK,IAAI,CAAC,CAAC,OAAO,KAAK,0BAA0B,CAAC,EAAE;gBACpE,MAAM,CAAC,CAAC;aACT;SACF;IACH,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,MAA8B;QACzC,yDAAyD;QACzD,IAAI,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,IAAI,CAAC,OAAO,EAAE;YACvE,OAAO;SACR;QAED,MAAM,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QAElC,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IACxD,CAAC;IAED,cAAc,CACZ,QAEqC,EACrC,eAAuC;QAEvC,MAAM,mBAAmB,GAAG,QAAQ,CAAC,MAAM,CAAC,mBAAoB,CAAC;QACjE,MAAM,SAAS,GAAG,IAAI,CAAC,oBAAoB,CAAC,mBAAmB,CAAC,CAAC;QAEjE,IAAI,QAAQ,CAAC,MAAM,CAAC,QAAQ,EAAE;YAC5B,MAAM,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC;YAC1C,IAAI,eAAe,KAAK,MAAM,EAAE;gBAC9B,sEAAsE;gBACtE,qDAAqD;gBACpD,SAAiB,CAAC,MAAM,GAAG,QAAQ,CAAC;gBACrC,2CAA2C;gBAC3C,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;aACpE;iBAAM;gBACL,qDAAqD;gBACrD,KAAK,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE,CACjD,IAAI,CAAC,OAAO,EAAE,CAAC,gBAAO,CAAC,MAAM,EAAE,KAAK,CAAC,CACtC,CAAC;aACH;SACF;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,oBAAoB,CAClB,cAAoD;QAEpD,wEAAwE;QACxE,+CAA+C;QAC/C,MAAM,MAAM,GAAG,cAAqB,CAAC;QAErC,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,0BAA0B,CAAC,EAAE;YACrD,MAAM,CAAC,UAAU,GAAG,GAAG,MAAM,CAAC,wBAAwB,EAAE,CAAC;YACzD,OAAO,MAAM,CAAC,0BAA0B,CAAC,CAAC;SAC3C;QAED,0EAA0E;QAC1E,yBAAyB;QACzB,IAAI,MAAM,CAAC,IAAI,KAAK,gBAAgB,EAAE;YACpC,OAAO,EAAC,IAAI,EAAE,QAAQ,EAAgC,CAAC;SACxD;QAED,MAAM,SAAS,GAAG,MAAM,CAAC,KAAK,CAAC;QAC/B,IAAI,SAAS,KAAK,SAAS,EAAE;YAC3B,OAAO,MAAM,CAAC;SACf;QAED,IAAI,MAAM,CAAC,IAAI,KAAK,MAAM,EAAE;YAC1B,IAAI,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,eAAe,CAAC,EAAE;gBAC7C,4EAA4E;gBAC5E,MAAM,CAAC,QAAQ,GAAG,GAAG,IAAI,CAAC,WAAW,GAAG,sCAAiB,GAAG,SAAS,CAAC,aAAa,EAAE,CAAC;gBACtF,OAAO,SAAS,CAAC,eAAe,CAAC,CAAC;aACnC;YACD,IAAI,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,UAAU,CAAC,EAAE;gBACxC,KAAK,MAAM,CAAC,IAAI,SAAS,CAAC,QAAQ,EAAE;oBAClC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAC/C,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,CACtB,CAAC;iBACH;aACF;SACF;QAED,wCAAwC;QACxC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;YAClD,KAAK,MAAM,CAAC,IAAI,SAAS,EAAE;gBACzB,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;aACxD;SACF;QACD,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;YACnD,KAAK,MAAM,CAAC,IAAI,SAAS,EAAE;gBACzB,SAAS,CAAC,CAAC,CAAC,GAAG;oBACb,IAAI,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC1C,IAAI,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;iBAC3C,CAAC;aACH;SACF;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,MAAM;QACJ,OAAO;YACL,KAAK,EAAE,IAAI,CAAC,OAAO;YACnB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,OAAO,EAAE,IAAI,CAAC,iBAAiB;YAC/B,GAAG,CAAC,IAAI,CAAC,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAC,OAAO,EAAE,IAAI,CAAC,OAAO,EAAC,CAAC;SAC/D,CAAC;IACJ,CAAC;IAED,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAED,IAAI,WAAW;QACb,OAAO,CACL,IAAI,CAAC,uBAAuB,CAAC,WAAW,CAAC,IAAI,CAAC,kBAAkB,CAAC;YAC/D,EAAE,WAAW,IAAI,SAAS,CAC7B,CAAC;IACJ,CAAC;IAED,IAAI,iBAAiB;QACnB,OAAO,IAAI,CAAC,kBAAkB,CAAC;IACjC,CAAC;IAED,IAAI,kBAAkB;QACpB,OAAO,IAAI,CAAC,mBAAmB,CAAC;IAClC,CAAC;IAED,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAED,IAAI,IAAI;QACN,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC;IAED,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,UAAU,CAAC;IACzB,CAAC;IAED,KAAK,CAAC,YAAY,CAChB,mBAA2B,EAC3B,KAA2B,EAC3B,UAAkC,EAClC,YAAqB,EACrB,eAAuC,EACvC,oBAAiD;QAEjD,MAAM,OAAO,GAAG,IAAI,CAAC,uBAAuB,CAAC,UAAU,CACrD,IAAI,CAAC,iBAAiB,CACvB,CAAC;QACF,MAAM,OAAO,CAAC,cAAc,EAAE,CAAC;QAE/B,OAAO;YACL,MAAM,EAAE,MAAM,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAC9C,IAAI,EACJ,mBAAmB,EACnB,KAAK,EACL,UAAU,EACV,YAAY,EACZ,eAAe,EACf,oBAAoB,CACrB;SACF,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,cAAc,CAClB,UAAkB,EAClB,YAAqB,EACrB,eAAuC,EACvC,oBAAiD;QAEjD,MAAM,OAAO,GAAG,IAAI,CAAC,uBAAuB,CAAC,UAAU,CACrD,IAAI,CAAC,iBAAiB,CACvB,CAAC;QACF,MAAM,OAAO,CAAC,cAAc,EAAE,CAAC;QAE/B,OAAO;YACL,MAAM,EAAE,MAAM,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAChD,IAAI,EACJ,UAAU,EACV,YAAY,EACZ,eAAe,EACf,oBAAoB,CACrB;SACF,CAAC;IACJ,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,kBAAkB,CACtB,SAAwC,EACxC,eAAuC;QAEvC,OAAO,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,CAC7C,SAAS,EACT,eAAe,EACf,IAAI,CACL,CAAC;IACJ,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,eAAe,CACnB,SAAwC;QAExC,OAAO,oCAAe,CAAC,eAAe,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;IAC1D,CAAC;IAED,MAAM;QACJ,IAAI,CAAC,aAAa,CAAC,aAAa,CAC9B;YACE,MAAM,EAAE,oBAAM,CAAC,UAAU,CAAC,cAAc;YACxC,MAAM,EAAE;gBACN,KAAK,EAAE,IAAI,CAAC,OAAO;aACpB;SACF,EACD,IAAI,CAAC,iBAAiB,CACvB,CAAC;IACJ,CAAC;CACF;AAtSD,sBAsSC"}