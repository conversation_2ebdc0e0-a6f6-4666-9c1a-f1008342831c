{"version": 3, "file": "EventManager.js", "sourceRoot": "", "sources": ["../../../../../src/bidiMapper/domains/events/EventManager.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;GAeG;;;AAEH,+DAKuC;AAEvC,wDAAgD;AAChD,8DAAsD;AACtD,yEAAiE;AACjE,gEAAwD;AAExD,qEAA6D;AAE7D,MAAM,YAAY;IACP,UAAU,GAAG,IAAI,wBAAS,EAAE,CAAC;IAC7B,UAAU,CAAyC;IACnD,MAAM,CAAgC;IAE/C,YACE,KAAoC,EACpC,SAAiD;QAEjD,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;IAC9B,CAAC;IAED,IAAI,EAAE;QACJ,OAAO,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;IAC5B,CAAC;IAED,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,UAAU,CAAC;IACzB,CAAC;IAED,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;CACF;AA6BD;;GAEG;AACH,MAAM,iBAAiB,GAA4C,IAAI,GAAG,CAAC;IACzE,CAAC,iBAAG,CAAC,UAAU,CAAC,kBAAkB,EAAE,GAAG,CAAC;CACzC,CAAC,CAAC;AAEH,MAAa,YAAY;IACvB,MAAM,CAAU,sBAAsB,GAAG,SAAS,CAAC;IACnD;;;;OAIG;IACH,mBAAmB,GAAG,IAAI,0BAAU,CAGlC,GAAG,EAAE,CAAC,IAAI,GAAG,EAAE,CAAC,CAAC;IACnB;;;OAGG;IACH,aAAa,GAAG,IAAI,GAAG,EAAgC,CAAC;IACxD;;;;OAIG;IACH,gBAAgB,GAAG,IAAI,GAAG,EAAkB,CAAC;IAC7C,oBAAoB,CAAsB;IAC1C,WAAW,CAAa;IACxB,uBAAuB,CAAU;IAEjC,YAAY,UAAsB;QAChC,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;QAC9B,IAAI,CAAC,oBAAoB,GAAG,IAAI,4CAAmB,CACjD,UAAU,CAAC,yBAAyB,EAAE,CACvC,CAAC;QACF,IAAI,CAAC,uBAAuB,GAAG,KAAK,CAAC;IACvC,CAAC;IAED,IAAI,sBAAsB;QACxB,OAAO,IAAI,CAAC,uBAAuB,CAAC;IACtC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,UAAU,CACf,SAA6B,EAC7B,eAAuD,EACvD,OAAuB;QAEvB,OAAO,IAAI,CAAC,SAAS,CAAC,EAAC,SAAS,EAAE,eAAe,EAAE,OAAO,EAAC,CAAC,CAAC;IAC/D,CAAC;IAED,aAAa,CACX,KAA2B,EAC3B,SAAiD;QAEjD,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,SAAS,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;IAC7E,CAAC;IAED,oBAAoB,CAClB,KAAoC,EACpC,SAAiD,EACjD,SAA6B;QAE7B,MAAM,YAAY,GAAG,IAAI,YAAY,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;QACxD,MAAM,cAAc,GAClB,IAAI,CAAC,oBAAoB,CAAC,4BAA4B,CACpD,SAAS,EACT,SAAS,CACV,CAAC;QACJ,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC;QAC3C,wDAAwD;QACxD,KAAK,MAAM,OAAO,IAAI,cAAc,EAAE;YACpC,IAAI,CAAC,WAAW,CAAC,mBAAmB,CAClC,4CAAmB,CAAC,iBAAiB,CAAC,KAAK,EAAE,OAAO,CAAC,CACtD,CAAC;YACF,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;SACvD;IACH,CAAC;IAED,KAAK,CAAC,SAAS,CACb,UAA8C,EAC9C,UAAsD,EACtD,OAAsB;QAEtB,6CAA6C;QAC7C,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE;YAClC,IAAI,SAAS,KAAK,IAAI,EAAE;gBACtB,0DAA0D;gBAC1D,IAAI,CAAC,WAAW,CAAC,yBAAyB,EAAE,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;aACpE;SACF;QAED,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE;YAClC,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE;gBAClC,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;gBAChD,IAAI,CAAC,oBAAoB,CAAC,SAAS,CAAC,SAAS,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;gBACnE,KAAK,MAAM,YAAY,IAAI,IAAI,CAAC,kBAAkB,CAChD,SAA+B,EAC/B,SAAS,EACT,OAAO,CACR,EAAE;oBACD,wCAAwC;oBACxC,IAAI,CAAC,WAAW,CAAC,mBAAmB,CAClC,4CAAmB,CAAC,iBAAiB,CAAC,YAAY,CAAC,KAAK,EAAE,OAAO,CAAC,CACnE,CAAC;oBACF,IAAI,CAAC,cAAc,CACjB,YAAY,EACZ,OAAO,EACP,SAA+B,CAChC,CAAC;iBACH;aACF;SACF;IACH,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,cAAc,CAClB,SAA2C,EAC3C,SAAiD;QAEjD,qEAAqE;QACrE,IAAI,SAAS,CAAC,UAAU,CAAC,YAAY,CAAC,sBAAsB,CAAC,EAAE;YAC7D,+BAA+B;YAC/B,IAAI,SAAS,KAAK,IAAI,EAAE;gBACtB,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;gBACpC,MAAM,OAAO,CAAC,GAAG,CACf,IAAI,CAAC,WAAW;qBACb,yBAAyB,EAAE;qBAC3B,cAAc,EAAE;qBAChB,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,SAAS,CAAC,mBAAmB,EAAE,CAAC,CAC7D,CAAC;aACH;iBAAM;gBACL,MAAM,IAAI,CAAC,WAAW;qBACnB,yBAAyB,EAAE;qBAC3B,UAAU,CAAC,SAAS,CAAC;qBACrB,SAAS,CAAC,mBAAmB,EAAE,CAAC;aACpC;SACF;IACH,CAAC;IAED,WAAW,CACT,UAA8C,EAC9C,UAAsD,EACtD,OAAsB;QAEtB,IAAI,CAAC,oBAAoB,CAAC,cAAc,CAAC,UAAU,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;IAC5E,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,YAA0B,EAAE,SAA6B;QACpE,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE;YACrC,6CAA6C;YAC7C,OAAO;SACR;QACD,MAAM,YAAY,GAAG,YAAY,CAAC,UAAU,CAC1C,SAAS,EACT,YAAY,CAAC,SAAS,CACvB,CAAC;QACF,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE;YACzC,IAAI,CAAC,aAAa,CAAC,GAAG,CACpB,YAAY,EACZ,IAAI,kBAAM,CAAe,iBAAiB,CAAC,GAAG,CAAC,SAAS,CAAE,CAAC,CAC5D,CAAC;SACH;QACD,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,YAAY,CAAE,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;QACxD,qEAAqE;QACrE,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;IACtE,CAAC;IAED;;OAEG;IACH,cAAc,CACZ,YAA0B,EAC1B,OAAsB,EACtB,SAA6B;QAE7B,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE;YACrC,6CAA6C;YAC7C,OAAO;SACR;QAED,MAAM,cAAc,GAAG,YAAY,CAAC,UAAU,CAC5C,SAAS,EACT,YAAY,CAAC,SAAS,EACtB,OAAO,CACR,CAAC;QACF,IAAI,CAAC,gBAAgB,CAAC,GAAG,CACvB,cAAc,EACd,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,YAAY,CAAC,EAAE,CAAC,CAC1E,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,kBAAkB,CAChB,SAA6B,EAC7B,SAAiD,EACjD,OAAsB;QAEtB,MAAM,YAAY,GAAG,YAAY,CAAC,UAAU,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;QACnE,MAAM,cAAc,GAAG,YAAY,CAAC,UAAU,CAC5C,SAAS,EACT,SAAS,EACT,OAAO,CACR,CAAC;QACF,MAAM,iBAAiB,GACrB,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC;QAEzD,MAAM,MAAM,GACV,IAAI,CAAC,aAAa;aACf,GAAG,CAAC,YAAY,CAAC;YAClB,EAAE,GAAG,EAAE;aACN,MAAM,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,EAAE,GAAG,iBAAiB,CAAC,IAAI,EAAE,CAAC;QAE/D,IAAI,SAAS,KAAK,IAAI,EAAE;YACtB,iFAAiF;YACjF,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,IAAI,EAAE,CAAC;iBACvD,MAAM,CACL,CAAC,UAAU,EAAE,EAAE;YACb,oDAAoD;YACpD,UAAU,KAAK,IAAI;gBACnB,mDAAmD;gBACnD,IAAI,CAAC,WAAW,CAAC,yBAAyB,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC,CACtE;iBACA,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAClB,IAAI,CAAC,kBAAkB,CAAC,SAAS,EAAE,UAAU,EAAE,OAAO,CAAC,CACxD;iBACA,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;SAChD;QACD,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;IAChD,CAAC;;AA3OU,oCAAY"}