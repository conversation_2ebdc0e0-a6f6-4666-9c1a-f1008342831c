{"version": 3, "file": "admin.css", "mappings": ";;;AAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;;AAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;;CAAA;;;CAAA;;AAAA;;;EAAA;EAAA;EAAA;EAAA;AAAA;;AAAA;;EAAA;AAAA;;AAAA;;;;;;;;CAAA;;AAAA;;EAAA;EAAA;EAAA;EAAA;KAAA;EAAA;EAAA;EAAA;EAAA;AAAA;;AAAA;;;CAAA;;AAAA;EAAA;EAAA;AAAA;;AAAA;;;;CAAA;;AAAA;EAAA;EAAA;EAAA;AAAA;;AAAA;;CAAA;;AAAA;EAAA;UAAA;AAAA;;AAAA;;CAAA;;AAAA;;;;;;EAAA;EAAA;AAAA;;AAAA;;CAAA;;AAAA;EAAA;EAAA;AAAA;;AAAA;;CAAA;;AAAA;;EAAA;AAAA;;AAAA;;;;;CAAA;;AAAA;;;;EAAA;EAAA;EAAA;EAAA;AAAA;;AAAA;;CAAA;;AAAA;EAAA;AAAA;;AAAA;;CAAA;;AAAA;;EAAA;EAAA;EAAA;EAAA;AAAA;;AAAA;EAAA;AAAA;;AAAA;EAAA;AAAA;;AAAA;;;;CAAA;;AAAA;EAAA;EAAA;EAAA;AAAA;;AAAA;;;;CAAA;;AAAA;;;;;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;;AAAA;;CAAA;;AAAA;;EAAA;AAAA;;AAAA;;;CAAA;;AAAA;;;;EAAA;EAAA;EAAA;AAAA;;AAAA;;CAAA;;AAAA;EAAA;AAAA;;AAAA;;CAAA;;AAAA;EAAA;AAAA;;AAAA;;CAAA;;AAAA;EAAA;AAAA;;AAAA;;CAAA;;AAAA;;EAAA;AAAA;;AAAA;;;CAAA;;AAAA;EAAA;EAAA;AAAA;;AAAA;;CAAA;;AAAA;EAAA;AAAA;;AAAA;;;CAAA;;AAAA;EAAA;EAAA;AAAA;;AAAA;;CAAA;;AAAA;EAAA;AAAA;;AAAA;;CAAA;;AAAA;;;;;;;;;;;;;EAAA;AAAA;;AAAA;EAAA;EAAA;AAAA;;AAAA;EAAA;AAAA;;AAAA;;;EAAA;EAAA;EAAA;AAAA;;AAAA;;CAAA;AAAA;EAAA;AAAA;;AAAA;;CAAA;;AAAA;EAAA;AAAA;;AAAA;;;CAAA;;AAAA;EAAA;EAAA;AAAA;;AAAA;;EAAA;EAAA;AAAA;;AAAA;;CAAA;;AAAA;;EAAA;AAAA;;AAAA;;CAAA;AAAA;EAAA;AAAA;;AAAA;;;;CAAA;;AAAA;;;;;;;;EAAA;EAAA;AAAA;;AAAA;;CAAA;;AAAA;;EAAA;EAAA;AAAA;;AAAA;AAAA;EAAA;AAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;EAAA;AAAA;EAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;EAAA;EAAA;EAAA;AAAA;AAAA;;EAAA;IAAA;EAAA;AAAA;AACA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;EAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;AAAA;AAAA;EAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;EAAA;AAAA;AAAA;EAAA;EAAA;EAAA;AAAA;AAAA;;EAAA;IAAA;IAAA;EAAA;AAAA;AAAA;;EAAA;IAAA;IAAA;EAAA;AAAA;AA0DA;AACA;EACE;AAHF;;AAMA;EACE;EACA;EACA;EACA;EACA;AAHF;;AAMA;AACA;EACE;AAHF;;AAMA;EACE;IACE;EAHF;AACF;;AA9EA;EAAA;AA8EC;;AA9ED;EAAA;AA8EC;;AA9ED;EAAA,mBA8EC;EA9ED;AA8EC;;AA9ED;EAAA;AA8EC;;AA9ED;EAAA;AA8EC;;AA9ED;EAAA;AA8EC;;AA9ED;EAAA;AA8EC;;AA9ED;EAAA;AA8EC;;AA9ED;EAAA;AA8EC;;AA9ED;EAAA;AA8EC;;AA9ED;EAAA;AA8EC;;AA9ED;EAAA;AA8EC;;AA9ED;EAAA;AA8EC;;AA9ED;EAAA;AA8EC;;AA9ED;EAAA;AA8EC;;AA9ED;EAAA,8BA8EC;EA9ED;AA8EC;;AA9ED;EAAA,2GA8EC;EA9ED,yGA8EC;EA9ED;AA8EC;;AA9ED;EAAA;AA8EC;;AA9ED;EAAA;AA8EC;;AA9ED;EAAA,8BA8EC;EA9ED;AA8EC;;AA9ED;EAAA,2GA8EC;EA9ED,yGA8EC;EA9ED;AA8EC;;AA9ED;EAAA;AA8EC;;AA9ED;EAAA;AA8EC;;AA9ED;EAAA;AA8EC;;AA9ED;EAAA;AA8EC;;AA9ED;EAAA;AA8EC;;AA9ED;EAAA;AA8EC;;AA9ED;EAAA;AA8EC;;AA9ED;EAAA;AA8EC;;AA9ED;EAAA,oBA8EC;EA9ED;AA8EC;;AA9ED;EAAA;AA8EC;;AA9ED;EAAA;AA8EC;;AA9ED;EAAA;AA8EC;;AA9ED;EAAA,oBA8EC;EA9ED;AA8EC;;AA9ED;EAAA;AA8EC;;AA9ED;EAAA,oBA8EC;EA9ED;AA8EC;;AA9ED;EAAA;AA8EC;;AA9ED;EAAA;AA8EC;;AA9ED;EAAA;AA8EC;;AA9ED;EAAA,yBA8EC;EA9ED;AA8EC;;AA9ED;EAAA,qBA8EC;EA9ED;AA8EC;;AA9ED;EAAA,qBA8EC;EA9ED;AA8EC;;AA9ED;EAAA,gDA8EC;EA9ED;AA8EC;;AA9ED;EAAA,iDA8EC;EA9ED;AA8EC;;AA9ED;EAAA;AA8EC;;AA9ED;EAAA;AA8EC;;AA9ED;EAAA;AA8EC;;AA9ED;EAAA;AA8EC;;AA9ED;EAAA,0CA8EC;EA9ED,uDA8EC;EA9ED;AA8EC;;AA9ED;EAAA;AA8EC;;AA9ED;EAAA,qBA8EC;EA9ED,yBA8EC;EA9ED,2BA8EC;EA9ED,yBA8EC;EA9ED,0BA8EC;EA9ED,+BA8EC;EA9ED;AA8EC;;AA9ED;EAAA,oBA8EC;EA9ED,yBA8EC;EA9ED,0BA8EC;EA9ED,wBA8EC;EA9ED,yBA8EC;EA9ED,8BA8EC;EA9ED;AA8EC;;AA9ED;EAAA,oBA8EC;EA9ED,yBA8EC;EA9ED,0BA8EC;EA9ED,wBA8EC;EA9ED,yBA8EC;EA9ED,8BA8EC;EA9ED;AA8EC;;AA9ED;EAAA;AA8EC;;AA9ED;EAAA;AA8EC;;AA9ED;EAAA;AA8EC;;AA9ED;;EAAA;IAAA;EA8EC;;EA9ED;IAAA;EA8EC;;EA9ED;IAAA;EA8EC;;EA9ED;IAAA;EA8EC;;EA9ED;IAAA;EA8EC;AAAA;;AA9ED;;EAAA;IAAA;EA8EC;;EA9ED;IAAA;EA8EC;AAAA,C", "sources": ["webpack://bettercampaign/./dev/bettercampaign/scss/index.scss"], "sourcesContent": ["@tailwind base;\n@tailwind components;\n@tailwind utilities;\n\n@layer base {\n  :root {\n    --background: 0 0% 100%;\n    --foreground: 222.2 84% 4.9%;\n    --card: 0 0% 100%;\n    --card-foreground: 222.2 84% 4.9%;\n    --popover: 0 0% 100%;\n    --popover-foreground: 222.2 84% 4.9%;\n    --primary: 221.2 83.2% 53.3%;\n    --primary-foreground: 210 40% 98%;\n    --secondary: 210 40% 96%;\n    --secondary-foreground: 222.2 84% 4.9%;\n    --muted: 210 40% 96%;\n    --muted-foreground: 215.4 16.3% 46.9%;\n    --accent: 210 40% 96%;\n    --accent-foreground: 222.2 84% 4.9%;\n    --destructive: 0 84.2% 60.2%;\n    --destructive-foreground: 210 40% 98%;\n    --border: 214.3 31.8% 91.4%;\n    --input: 214.3 31.8% 91.4%;\n    --ring: 221.2 83.2% 53.3%;\n    --radius: 0.5rem;\n  }\n\n  .dark {\n    --background: 222.2 84% 4.9%;\n    --foreground: 210 40% 98%;\n    --card: 222.2 84% 4.9%;\n    --card-foreground: 210 40% 98%;\n    --popover: 222.2 84% 4.9%;\n    --popover-foreground: 210 40% 98%;\n    --primary: 217.2 91.2% 59.8%;\n    --primary-foreground: 222.2 84% 4.9%;\n    --secondary: 217.2 32.6% 17.5%;\n    --secondary-foreground: 210 40% 98%;\n    --muted: 217.2 32.6% 17.5%;\n    --muted-foreground: 215 20.2% 65.1%;\n    --accent: 217.2 32.6% 17.5%;\n    --accent-foreground: 210 40% 98%;\n    --destructive: 0 62.8% 30.6%;\n    --destructive-foreground: 210 40% 98%;\n    --border: 217.2 32.6% 17.5%;\n    --input: 217.2 32.6% 17.5%;\n    --ring: 224.3 76.3% 94.1%;\n  }\n}\n\n@layer base {\n  * {\n    @apply border-border;\n  }\n  body {\n    @apply bg-background text-foreground;\n  }\n}\n\n/* BetterCampaign specific styles */\n.bettercampaign-admin {\n  font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, Oxygen-Sans, Ubuntu, Cantarell, \"Helvetica Neue\", sans-serif;\n}\n\n#bettercampaign {\n  background: #fff;\n  border-radius: 8px;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n  margin: 20px 0;\n  min-height: 500px;\n}\n\n/* WordPress admin compatibility */\n.wp-admin #bettercampaign {\n  margin: 20px 20px 0 2px;\n}\n\n@media screen and (max-width: 782px) {\n  .wp-admin #bettercampaign {\n    margin: 20px 10px 0 0;\n  }\n}\n"], "names": [], "sourceRoot": ""}