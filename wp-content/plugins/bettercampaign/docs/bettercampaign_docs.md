# BetterCampaign WordPress Plugin - Complete Specification

## Project Overview

## Database Schema

### Custom Tables

#### `wp_bc_campaign_logs`
```sql
CREATE TABLE wp_bc_campaign_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    campaign_id BIGINT NOT NULL,
    action VARCHAR(100) NOT NULL,
    message TEXT NOT NULL,
    data LONGTEXT NULL,
    status ENUM('info', 'warning', 'error', 'success') DEFAULT 'info',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_campaign_id (campaign_id),
    INDEX idx_created_at (created_at)
);
```

#### `wp_bc_campaign_assets`
```sql
CREATE TABLE wp_bc_campaign_assets (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    campaign_id BIGINT NOT NULL,
    asset_type VARCHAR(50) NOT NULL,
    asset_url VARCHAR(500) NOT NULL,
    metadata LONGTEXT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_campaign_id (campaign_id),
    INDEX idx_asset_type (asset_type)
);
```

### Required shadcn/ui Components
```bash
# Install all required components
npx shadcn-ui@latest add button card dialog form input label select textarea checkbox table badge tabs toast progress alert dropdown-menu
```

## Core Features & Implementation Specifications

### 1. Campaign Management System

#### Campaign Creation Flow
1. **User Input**: Campaign title + AI prompt via shadcn form
2. **Permission Selection**: Checkboxes for enabled features
3. **Validation**: Client-side with zod + server-side sanitization
4. **Database Storage**: Save as `better_campaign` CPT with meta
5. **Status Tracking**: Initialize as 'draft' status

#### Campaign Execution Workflow
1. **Trigger**: User clicks "Execute" button in UI
2. **Status Update**: Change to 'processing' with loading indicator
3. **n8n Webhook**: Send campaign data to configured endpoint
4. **Asset Generation**: Wait for AI-generated images + colors
5. **Feature Deployment**: Create NX bars, popups, pages based on permissions
6. **Status Update**: Change to 'active' or 'error' with notifications

### 2. Permission System

#### Available Permissions
- **nx_bar**: Create NotificationX promotional bar
- **exit_popup**: Generate Elementor exit-intent popup
- **deal_page**: Create dedicated landing page
- **price_update**: Scan and update prices sitewide
- **betterlinks_tracking**: Generate tracked short links

#### Permission Logic
- Each permission is stored as array in `_bc_permissions` meta
- UI shows checkbox grid with descriptions and previews
- Only enabled permissions trigger corresponding integrations
- Users can modify permissions and re-execute campaigns

### 3. NotificationX Integration

#### NX Bar Creation Specifications
**Requirements**: NotificationX plugin active
**Process**:
1. Check campaign permissions for 'nx_bar'
2. Create new NotificationX builder post (`nx_builder` post type)
3. Configure bar settings:
   - Position: Top bar
   - Background: AI-generated image from n8n
   - Colors: AI-generated color scheme
   - Content: Based on campaign prompt
   - Display rules: Show on all pages for 30 days
4. Store NX bar ID in campaign meta for future updates

**Generated Content Structure**:
- Headline: Campaign title
- Description: AI-enhanced promotional text
- CTA Button: Links to deal page or external URL
- Close option: User can dismiss

### 4. Elementor Pro Integration

#### Exit Intent Popup Creation
**Requirements**: Elementor Pro plugin active
**Process**:
1. Check campaign permissions for 'exit_popup'
2. Create Elementor template (`elementor_library` post type)
3. Configure popup settings:
   - Trigger: Exit intent
   - Background: AI-generated image
   - Colors: Match campaign color scheme
   - Content: Dynamically generated from prompt
4. Activate popup with display rules

**Popup Template Structure**:
- Header section with compelling headline
- Image section with AI-generated background
- Content section with offer details
- CTA button section with BetterLinks tracking
- Close button with exit tracking

### 5. Deal Page Generator

#### Page Creation Process
**Templates**: Pre-built high-converting templates
**Process**:
1. Check campaign permissions for 'deal_page'
2. Clone existing template page or create from scratch
3. Replace placeholders with campaign-specific content:
   - Headlines: AI-generated based on prompt
   - Images: Replace with generated assets
   - Colors: Apply campaign color scheme
   - CTAs: Add BetterLinks tracking
4. Use Elementor or Gutenberg based on site preference

**Content Replacement Logic**:
- Scan template for placeholder text patterns
- Replace with AI-generated alternatives
- Update image sources with new URLs
- Apply color scheme to all design elements
- Generate SEO-friendly URL slug

### 6. Automated Price Update System

#### Price Scanning Logic
**Scan Targets**:
- All published posts and pages
- Gutenberg blocks containing price patterns
- Elementor widgets with price elements
- Custom fields with price metadata
- WooCommerce products (if installed)

**Price Detection Patterns**:
- Currency symbols: $, €, £, ¥, etc.
- Price formats: $99.99, €50,00, £25.50
- Discount patterns: 50% off, Save $20
- Custom shortcodes: [price], [discount]

**Update Process**:
1. Scan all content for price patterns
2. Parse and store current prices in database
3. Apply campaign-specific pricing rules
4. Update content with new prices
5. Log all changes with before/after values
6. Create restore point for rollback capability

### 7. BetterLinks Integration

#### Link Creation & Tracking
**Requirements**: BetterLinks plugin active
**Process**:
1. Identify all CTA buttons and links in generated content
2. Create shortened links via BetterLinks API
3. Add UTM parameters:
   - Campaign: Campaign title
   - Source: bettercampaign
   - Medium: Generated content type
4. Store link IDs for tracking and analytics

**Link Management**:
- Auto-generate links for all campaign CTAs
- Track clicks and conversions
- Provide analytics dashboard
- Enable A/B testing of different URLs

### 8. AI Asset Generation (via n8n)

#### Image Generation Workflow
**HuggingFace Integration**:
1. Receive campaign prompt from WordPress
2. Generate image prompts based on campaign type
3. Call HuggingFace Stable Diffusion API
4. Generate multiple image variants:
   - NX bar background (1200x100px)
   - Popup background (800x600px)
   - Deal page hero image (1920x1080px)
5. Return image URLs to WordPress

**Color Scheme Generation**:
1. Analyze campaign prompt for mood/theme
2. Generate complementary color palette
3. Return colors in hex format:
   - Primary color (main CTA)
   - Secondary color (accents)
   - Text color (readability)
   - Background color (contrast)

#### n8n Webhook Integration

**Outbound Webhook (WordPress → n8n)**:
```json
{
  "campaign_id": 123,
  "campaign_title": "Summer Sale 2024",
  "prompt": "Create a vibrant summer sale campaign",
  "permissions": ["nx_bar", "exit_popup", "deal_page"],
  "callback_url": "https://site.com/wp-json/bettercampaign/v1/webhook/n8n",
  "callback_token": "secure_hash_token"
}
```

**Inbound Webhook (n8n → WordPress)**:
```json
{
  "campaign_id": 123,
  "status": "completed",
  "assets": {
    "nx_bar_background": "https://generated-image-url.jpg",
    "popup_background": "https://generated-image-url2.jpg",
    "hero_image": "https://generated-image-url3.jpg"
  },
  "color_scheme": {
    "primary": "#FF6B35",
    "secondary": "#004E89",
    "text": "#FFFFFF",
    "background": "#F5F5F5"
  },
  "token": "matching_callback_token"
}
```

## REST API Endpoints

### Campaign Management
- `GET /wp-json/bettercampaign/v1/campaigns` - List all campaigns
- `POST /wp-json/bettercampaign/v1/campaigns` - Create new campaign
- `GET /wp-json/bettercampaign/v1/campaigns/{id}` - Get specific campaign
- `PUT /wp-json/bettercampaign/v1/campaigns/{id}` - Update campaign
- `DELETE /wp-json/bettercampaign/v1/campaigns/{id}` - Delete campaign
- `POST /wp-json/bettercampaign/v1/campaigns/{id}/execute` - Execute campaign
- `GET /wp-json/bettercampaign/v1/campaigns/{id}/logs` - Get campaign logs

### Webhook Endpoints
- `POST /wp-json/bettercampaign/v1/webhook/n8n` - Receive n8n responses
- `GET /wp-json/bettercampaign/v1/webhook/status` - Check webhook health

### Utility Endpoints
- `GET /wp-json/bettercampaign/v1/permissions` - Get available permissions
- `GET /wp-json/bettercampaign/v1/templates` - List available templates
- `GET /wp-json/bettercampaign/v1/stats` - Get campaign statistics

## Security & Permissions

### WordPress Capabilities
- Create new capability: `manage_bettercampaigns`
- Default assignment: Administrator role
- Permission checks on all API endpoints
- Nonce verification for all forms

### API Security
- Authentication: WordPress REST API authentication
- Rate limiting: 100 requests per hour per user
- Input validation: Sanitize all inputs
- SQL injection prevention: Use prepared statements
- XSS prevention: Escape all outputs

### n8n Webhook Security
- Token-based authentication for callbacks
- IP whitelist for webhook endpoints
- Request signature verification
- Timeout handling for failed requests

## Development Phases

### Phase 1: Core Foundation (Week 1-2)
1. **Plugin Structure**: Main plugin file + class autoloading
2. **Database Setup**: Custom tables + Campaign CPT
3. **Admin Interface**: Basic WordPress admin pages
4. **REST API**: Core campaign CRUD endpoints

### Phase 2: React Frontend (Week 3-4)
1. **shadcn/ui Setup**: Component library installation
2. **Dashboard**: Campaign overview with metrics
3. **Campaign Form**: Create/edit with validation
4. **Campaign List**: Data table with actions
5. **State Management**: React Query + Zustand setup

### Phase 3: Plugin Integrations (Week 5-6)
1. **NotificationX**: Bar creation and management
2. **Elementor**: Popup template generation
3. **BetterLinks**: Link shortening and tracking
4. **Price Updates**: Content scanning and modification

### Phase 4: AI Integration (Week 7-8)
1. **n8n Workflows**: Webhook endpoints and processing
2. **Asset Generation**: Image and color scheme handling
3. **Content Enhancement**: AI-powered text generation
4. **Testing**: End-to-end campaign workflow testing

### Phase 5: Polish & Launch (Week 9-10)
1. **UI/UX Refinement**: Design improvements and animations
2. **Performance Optimization**: Caching and database optimization
3. **Documentation**: User guides and developer docs
4. **Security Audit**: Penetration testing and code review

## Testing Strategy

### Unit Testing
- PHP: PHPUnit for all classes and functions
- JavaScript: Jest for React components and utilities
- API: Automated endpoint testing with sample data

### Integration Testing
- Plugin compatibility testing with NotificationX, Elementor, BetterLinks
- WordPress multisite compatibility
- n8n workflow end-to-end testing

### User Acceptance Testing
- Campaign creation workflow testing
- Asset generation and deployment verification  
- Performance testing with large datasets
- Cross-browser compatibility testing

## Deployment & Distribution

### WordPress.org Repository
- Plugin submission to official repository
- SVN version control for releases
- Automated testing via WordPress plugin checker
- Regular security updates and compatibility testing

### Documentation
- User manual with screenshots
- Developer API documentation
- Video tutorials for complex workflows
- FAQ and troubleshooting guide

This specification provides a comprehensive roadmap for building the BetterCampaign plugin with modern tools like shadcn/ui, while maintaining WordPress best practices and ensuring seamless integration with existing plugins.bc_prompt` - Campaign description/prompt
- `_bc_status` - draft|processing|active|paused|completed|error
- `_bc_permissions` - Array of enabled features
- `_bc_color_scheme` - JSON with AI-generated colors
- `_bc_assets` - JSON with generated image URLs
- `_bc_nx_bar_id` - Reference to created NotificationX bar
- `_bc_elementor_popup_id` - Reference to Elementor popup
- `_bc_deal_page_id` - Reference to generated deal page
- `_bc_betterlinks_ids` - Array of created BetterLinks
- `_bc_price_updates` - JSON with price change history
- `_bc_n8n_workflow_status` - Current workflow status
- `_bc_created_by` - User ID who created campaign
- `_bc_last_executed` - Timestamp of last execution