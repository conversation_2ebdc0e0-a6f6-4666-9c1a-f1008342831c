# BetterCampaign Plugin Boilerplate

A clean, lightweight WordPress plugin boilerplate based on the SimpleWishlist plugin structure, featuring a minimal React-based admin panel built with shadcn/ui components.

## Features

- **Clean Architecture**: Well-organized PHP backend with proper namespacing
- **Modern React Frontend**: TypeScript-based React components with shadcn/ui
- **Minimal Dependencies**: Only essential packages, avoiding bloated libraries
- **Admin-Only Focus**: No frontend-facing scripts, purely admin dashboard
- **REST API Ready**: Built-in REST API endpoints for settings management
- **WordPress Standards**: Follows WordPress coding standards and best practices

## Structure

```
bettercampaign/
├── assets/                     # Built assets (generated)
├── dev/                        # Development source files
│   ├── bettercampaign/
│   │   ├── admin/             # Admin React components
│   │   ├── components/ui/     # shadcn/ui components
│   │   ├── lib/               # Utility functions
│   │   └── scss/              # Styles
│   └── index.tsx              # Main React entry point
├── includes/                   # PHP backend classes
│   ├── Admin/                 # Admin functionality
│   ├── Core/                  # Core functionality (REST API, etc.)
│   ├── BetterCampaign.php     # Main plugin class
│   └── GetInstance.php        # Singleton trait
├── vendor/                     # PHP dependencies
├── bettercampaign.php         # Main plugin file
├── package.json               # Node.js dependencies
├── composer.json              # PHP dependencies
├── webpack.config.js          # Build configuration
├── tailwind.config.js         # Tailwind CSS configuration
└── tsconfig.json              # TypeScript configuration
```

## Dependencies

### Essential React Packages Only
- **React & ReactDOM**: Core React libraries
- **@wordpress/scripts**: WordPress build tools
- **TypeScript**: Type safety
- **Tailwind CSS**: Utility-first CSS framework

### shadcn/ui Components
- **@radix-ui/***: Accessible UI primitives
- **class-variance-authority**: Component variants
- **clsx & tailwind-merge**: Utility functions
- **lucide-react**: Icon library

### Excluded Packages
The following packages were intentionally excluded to keep the boilerplate lightweight:
- quickbuilder
- rc-pagination
- react-datepicker
- react-select
- react-table
- react-toastify
- sort-array
- sweetalert2

## Getting Started

### 1. Install Dependencies

```bash
# Install Node.js dependencies
npm install

# Install PHP dependencies (optional - basic autoloader included)
composer install
```

### 2. Development

```bash
# Start development server with hot reload
npm run start

# Build for production
npm run build
```

### 3. Activate Plugin

1. Navigate to WordPress Admin → Plugins
2. Activate "BetterCampaign"
3. Access the admin panel via the "BetterCampaign" menu item

## Customization

### Adding New Settings

1. Update `includes/Admin/Settings.php` to add new form fields
2. Modify `dev/bettercampaign/admin/Dashboard/index.tsx` to add UI components
3. The REST API will automatically handle the new settings

### Adding New Components

1. Create new shadcn/ui components in `dev/bettercampaign/components/ui/`
2. Add new admin pages in `dev/bettercampaign/admin/`
3. Update the routing as needed

### Styling

- Main styles: `dev/bettercampaign/scss/index.scss`
- Uses Tailwind CSS with shadcn/ui design system
- CSS variables for theming support

## API Endpoints

- `GET /wp-json/bettercampaign/v1/settings` - Get settings
- `POST /wp-json/bettercampaign/v1/settings` - Save settings

## License

GPL-3.0+

## Author

MD Shakibul Islam
