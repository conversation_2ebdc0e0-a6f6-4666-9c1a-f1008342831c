<?php
/**
 * Migration file for BetterCampaign custom tables
 */

function bettercampaign_run_migrations() {
    global $wpdb;
    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');

    $charset_collate = $wpdb->get_charset_collate();

    $logs_table = $wpdb->prefix . 'bc_campaign_logs';
    $assets_table = $wpdb->prefix . 'bc_campaign_assets';

    $sql = "
    CREATE TABLE $logs_table (
        id BIGINT AUTO_INCREMENT PRIMARY KEY,
        campaign_id BIGINT NOT NULL,
        action VARCHAR(100) NOT NULL,
        message TEXT NOT NULL,
        data LONGTEXT NULL,
        status ENUM('info', 'warning', 'error', 'success') DEFAULT 'info',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_campaign_id (campaign_id),
        INDEX idx_created_at (created_at)
    ) $charset_collate;

    CREATE TABLE $assets_table (
        id BIGINT AUTO_INCREMENT PRIMARY KEY,
        campaign_id BIGINT NOT NULL,
        asset_type VARCHAR(50) NOT NULL,
        asset_url VARCHAR(500) NOT NULL,
        metadata LONGTEXT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_campaign_id (campaign_id),
        INDEX idx_asset_type (asset_type)
    ) $charset_collate;
    ";

    dbDelta($sql);
}

// Optionally, you can call this from your main plugin activation hook:
// add_action('activate_PLUGIN_FILE', 'bettercampaign_run_migrations'); 