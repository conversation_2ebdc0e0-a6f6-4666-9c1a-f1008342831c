<?php
/**
 * Admin Class File.
 *
 * @package BetterCampaign\Admin
 */

namespace BetterCampaign\Admin;

use BetterCampaign\GetInstance;
use BetterCampaign\BetterCampaign;

/**
 * Admin Class, this class is responsible for all Admin Actions
 */
class Admin {
    /**
     * Instance of Admin
     *
     * @var Admin
     */
    use GetInstance;
    
    /**
     * Assets Path and URL
     */
    const ASSET_URL  = BC_ASSETS . 'admin/';
    const ASSET_PATH = BC_ASSETS_PATH . 'admin/';
    const VIEWS_PATH = BC_INCLUDES . 'Admin/views/';

    /**
     * Initially Invoked when its initialized.
     */
    public function __construct(){
        add_action('init', [$this, 'init'], 5);
    }

    /**
     * This method is responsible for Admin Menu of BetterCampaign
     *
     * @return void
     */
    public function init(){
        add_action('admin_menu', [$this, 'menu'], 10);
        Settings::get_instance()->init();
        add_action('admin_enqueue_scripts', [$this, 'admin_enqueue_scripts']);
    }

    /**
     * This method is responsible for Admin Menu of BetterCampaign
     *
     * @return void
     */
    public function menu(){
        add_menu_page(
            __('BetterCampaign', 'bettercampaign'),
            __('BetterCampaign', 'bettercampaign'),
            'manage_options',
            'bettercampaign-dashboard',
            array( $this, 'views' ),
            'dashicons-email-alt',
            30
        );
    }

    /**
     * Admin Views
     *
     * @return void
     */
    public function views() {
        include_once self::VIEWS_PATH . 'main.views.php';
    }

    /**
     * Enqueue admin scripts and styles
     *
     * @param string $hook
     * @return void
     */
    public function admin_enqueue_scripts($hook) {
        if (strpos($hook, 'bettercampaign') === false) {
            return;
        }

        // Use dev or prod assets based on SC_DEV
        if (defined('SC_DEV') && SC_DEV) {
            $js_url = BC_DEV_ASSETS . 'admin/js/admin.js';
            $asset_file = BC_DEV_ASSETS_PATH . 'admin/js/admin.asset.php';
            $css_url = BC_DEV_ASSETS . 'admin.css'; // Enqueue dev CSS in dev mode
        } else {
            $js_url = BC_ASSETS . 'admin/js/admin.js';
            $asset_file = BC_ASSETS_PATH . 'admin/js/admin.asset.php';
            $css_url = BC_ASSETS . 'admin/css/admin.css'; // If you add CSS in prod
        }
        $asset_data = file_exists($asset_file) ? include $asset_file : ['dependencies' => [], 'version' => BC_VERSION];

        wp_enqueue_script(
            'bettercampaign-admin',
            $js_url,
            $asset_data['dependencies'],
            $asset_data['version'],
            true
        );

        if ($css_url) {
            wp_enqueue_style(
                'bettercampaign-admin',
                $css_url,
                [],
                BC_VERSION
            );
        }

        // Localize script with data for React
        $tabs = $this->get_tabs();
        wp_localize_script('bettercampaign-admin', 'bettercampaignTabs', $tabs);
    }

    /**
     * Get tabs configuration for React frontend
     *
     * @return array
     */
    public function get_tabs() {
        $tabs = Settings::get_instance()->get_form_data();
        $tabs['admin_url'] = get_admin_url();
        $tabs['assets'] = [
            'admin'  => BC_ADMIN_URL,
            'public' => BC_PUBLIC_URL,
        ];
        $tabs['version'] = BC_VERSION;
        
        return $tabs;
    }
}
