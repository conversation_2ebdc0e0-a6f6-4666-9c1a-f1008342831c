import { __ } from '@wordpress/i18n';
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/bettercampaign/components/ui/card';
import { Button } from '@/bettercampaign/components/ui/button';
import { Badge } from '@/bettercampaign/components/ui/badge';
// Removed Dialog import to fix build issues
import { Separator } from '@/bettercampaign/components/ui/separator';
import { LoadingSpinner } from '@/bettercampaign/components/ui/loading-spinner';
import { Alert, AlertDescription } from '@/bettercampaign/components/ui/alert';
import { useToast } from '@/bettercampaign/components/ui/use-toast';
import { Plus, Eye, Calendar, BarChart3, ExternalLink, RefreshCw, AlertCircle } from 'lucide-react';
import apiFetch from '@wordpress/api-fetch';
import SimpleCampaignForm from '@/bettercampaign/components/SimpleCampaignForm';
import { CampaignDetailsDrawer } from '@/bettercampaign/components/CampaignDetailsDrawer';

interface Campaign {
  id: number;
  title: string;
  prompt: string;
  status: string;
  permissions: string[];
  created_at: string;
  updated_at: string;
}

interface DashboardProps {
  settings: any;
  onSave: (values: any) => void;
}

const statusColors = {
  draft: 'secondary',
  processing: 'default',
  active: 'default',
  paused: 'secondary',
  completed: 'default',
  error: 'destructive',
} as const;

const statusLabels = {
  draft: __('Draft', 'bettercampaign'),
  processing: __('Processing', 'bettercampaign'),
  active: __('Active', 'bettercampaign'),
  paused: __('Paused', 'bettercampaign'),
  completed: __('Completed', 'bettercampaign'),
  error: __('Error', 'bettercampaign'),
};

const Dashboard: React.FC<DashboardProps> = ({ settings, onSave }) => {
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showCampaignForm, setShowCampaignForm] = useState(false);
  const [selectedCampaign, setSelectedCampaign] = useState<Campaign | null>(null);
  const [showCampaignDetails, setShowCampaignDetails] = useState(false);
  const { toast } = useToast();

  // Fetch campaigns
  const fetchCampaigns = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await apiFetch({
        path: 'bettercampaign/v1/campaigns',
        method: 'GET',
      });

      if (response && (response as any).success) {
        setCampaigns((response as any).data || []);
      } else {
        throw new Error((response as any).message || __('Failed to fetch campaigns', 'bettercampaign'));
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : __('Failed to fetch campaigns', 'bettercampaign');
      setError(errorMessage);
      toast({
        title: __('Error', 'bettercampaign'),
        description: errorMessage,
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCampaigns();
  }, []);

  const handleCreateCampaign = () => {
    setShowCampaignForm(true);
  };

  const handleViewCampaign = (campaign: Campaign) => {
    setSelectedCampaign(campaign);
    setShowCampaignDetails(true);
  };

  const handleCampaignFormSuccess = () => {
    setShowCampaignForm(false);
    fetchCampaigns(); // Refresh the list
  };

  const handleCampaignFormCancel = () => {
    setShowCampaignForm(false);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString(undefined, {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const truncateText = (text: string, maxLength: number = 100) => {
    return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
  };

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            {__('BetterCampaign', 'bettercampaign')}
          </h1>
          <p className="text-muted-foreground">
            {__('Create and manage your marketing campaigns', 'bettercampaign')}
          </p>
        </div>
        <Button onClick={handleCreateCampaign} className="gap-2">
          <Plus className="h-4 w-4" />
          {__('New Campaign', 'bettercampaign')}
        </Button>
      </div>

      <Separator />

      {/* Main Content */}
      {loading ? (
        <div className="flex items-center justify-center py-12">
          <LoadingSpinner size="lg" text={__('Loading campaigns...', 'bettercampaign')} />
        </div>
      ) : error ? (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription className="flex items-center justify-between">
            {error}
            <Button variant="outline" size="sm" onClick={fetchCampaigns}>
              <RefreshCw className="h-4 w-4 mr-2" />
              {__('Retry', 'bettercampaign')}
            </Button>
          </AlertDescription>
        </Alert>
      ) : campaigns.length === 0 ? (
        <Card className="text-center py-12">
          <CardContent>
            <div className="mx-auto w-24 h-24 bg-muted rounded-full flex items-center justify-center mb-4">
              <BarChart3 className="h-12 w-12 text-muted-foreground" />
            </div>
            <h3 className="text-lg font-semibold mb-2">
              {__('No campaigns yet', 'bettercampaign')}
            </h3>
            <p className="text-muted-foreground mb-6 max-w-sm mx-auto">
              {__('Get started by creating your first marketing campaign. Choose from various features to boost your conversions.', 'bettercampaign')}
            </p>
            <Button onClick={handleCreateCampaign} className="gap-2">
              <Plus className="h-4 w-4" />
              {__('Create First Campaign', 'bettercampaign')}
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {campaigns.map((campaign) => (
            <Card key={campaign.id} className="hover:shadow-md transition-shadow cursor-pointer group">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="space-y-1 flex-1">
                    <CardTitle className="text-lg line-clamp-1">{campaign.title}</CardTitle>
                    <div className="flex items-center gap-2">
                      <Badge variant={statusColors[campaign.status as keyof typeof statusColors] || 'secondary'}>
                        {statusLabels[campaign.status as keyof typeof statusLabels] || campaign.status}
                      </Badge>
                      <span className="text-xs text-muted-foreground">
                        #{campaign.id}
                      </span>
                    </div>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <p className="text-sm text-muted-foreground mb-4 line-clamp-2">
                  {truncateText(campaign.prompt)}
                </p>

                <div className="space-y-3">
                  <div className="flex items-center gap-2 text-xs text-muted-foreground">
                    <Calendar className="h-3 w-3" />
                    {formatDate(campaign.created_at)}
                  </div>

                  <div className="flex flex-wrap gap-1">
                    {campaign.permissions.slice(0, 3).map((permission) => (
                      <Badge key={permission} variant="outline" className="text-xs">
                        {permission.replace('_', ' ')}
                      </Badge>
                    ))}
                    {campaign.permissions.length > 3 && (
                      <Badge variant="outline" className="text-xs">
                        +{campaign.permissions.length - 3}
                      </Badge>
                    )}
                  </div>

                  <div className="flex justify-end pt-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleViewCampaign(campaign)}
                      className="gap-2 opacity-0 group-hover:opacity-100 transition-opacity"
                    >
                      <Eye className="h-3 w-3" />
                      {__('View Details', 'bettercampaign')}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Campaign Form Modal */}
      {showCampaignForm && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          {/* Backdrop */}
          <div
            className="fixed inset-0 bg-black/50 backdrop-blur-sm"
            onClick={() => setShowCampaignForm(false)}
          />

          {/* Modal Content */}
          <div className="relative bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <SimpleCampaignForm
              onSuccess={handleCampaignFormSuccess}
              onCancel={handleCampaignFormCancel}
            />
          </div>
        </div>
      )}

      {/* Campaign Details Drawer */}
      <CampaignDetailsDrawer
        campaign={selectedCampaign}
        isOpen={showCampaignDetails}
        onClose={() => setShowCampaignDetails(false)}
      />
    </div>
  );
};

export default Dashboard;
