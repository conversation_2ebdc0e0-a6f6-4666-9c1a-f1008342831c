import { __ } from '@wordpress/i18n';
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/bettercampaign/components/ui/card';
import { Badge } from '@/bettercampaign/components/ui/badge';
import { But<PERSON> } from '@/bettercampaign/components/ui/button';
import { Separator } from '@/bettercampaign/components/ui/separator';
import {
  ExternalLink,
  Calendar,
  BarChart3,
  MousePointer,
  TrendingUp,
  Users,
  Eye,
  Bell,
  Zap,
  Link,
  DollarSign,
  Sparkles,
  X
} from 'lucide-react';

interface Campaign {
  id: number;
  title: string;
  prompt: string;
  status: string;
  permissions: string[];
  created_at: string;
  updated_at: string;
}

interface CampaignDetailsDrawerProps {
  campaign: Campaign | null;
  isOpen: boolean;
  onClose: () => void;
}

const statusColors = {
  draft: 'secondary',
  processing: 'default',
  active: 'default',
  paused: 'secondary',
  completed: 'default',
  error: 'destructive',
} as const;

const statusLabels = {
  draft: __('Draft', 'bettercampaign'),
  processing: __('Processing', 'bettercampaign'),
  active: __('Active', 'bettercampaign'),
  paused: __('Paused', 'bettercampaign'),
  completed: __('Completed', 'bettercampaign'),
  error: __('Error', 'bettercampaign'),
};

const featureIcons = {
  notification_bar: Bell,
  popup: Zap,
  deal_page: ExternalLink,
  short_links: Link,
  price_tracking: DollarSign,
  ai_content: Sparkles,
};

const featureLabels = {
  notification_bar: __('Notification Bar', 'bettercampaign'),
  popup: __('Exit Intent Popup', 'bettercampaign'),
  deal_page: __('Landing Page', 'bettercampaign'),
  short_links: __('Short Links', 'bettercampaign'),
  price_tracking: __('Price Tracking', 'bettercampaign'),
  ai_content: __('AI Content', 'bettercampaign'),
};

// Mock data for demonstration
const getMockStats = (campaignId: number) => ({
  views: Math.floor(Math.random() * 10000) + 1000,
  clicks: Math.floor(Math.random() * 1000) + 100,
  conversions: Math.floor(Math.random() * 100) + 10,
  conversionRate: ((Math.floor(Math.random() * 100) + 10) / (Math.floor(Math.random() * 1000) + 100) * 100).toFixed(2),
});

const getMockLinks = (permissions: string[]) => {
  const links = [];
  
  if (permissions.includes('notification_bar')) {
    links.push({
      type: 'notification_bar',
      label: __('Notification Bar', 'bettercampaign'),
      url: '#',
      editUrl: '#',
    });
  }
  
  if (permissions.includes('popup')) {
    links.push({
      type: 'popup',
      label: __('Exit Intent Popup', 'bettercampaign'),
      url: '#',
      editUrl: '#',
    });
  }
  
  if (permissions.includes('deal_page')) {
    links.push({
      type: 'deal_page',
      label: __('Landing Page', 'bettercampaign'),
      url: '#',
      editUrl: '#',
    });
  }
  
  return links;
};

export const CampaignDetailsDrawer: React.FC<CampaignDetailsDrawerProps> = ({
  campaign,
  isOpen,
  onClose,
}) => {
  if (!campaign) return null;

  const stats = getMockStats(campaign.id);
  const links = getMockLinks(campaign.permissions);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString(undefined, {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black/50 backdrop-blur-sm"
        onClick={onClose}
      />

      {/* Modal Content */}
      <div className="relative bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="sticky top-0 bg-white border-b p-6 rounded-t-lg">
          <div className="flex items-start justify-between">
            <div className="space-y-1 flex-1">
              <h2 className="text-xl font-semibold">{campaign.title}</h2>
              <div className="flex items-center gap-2">
                <Badge variant={statusColors[campaign.status as keyof typeof statusColors] || 'secondary'}>
                  {statusLabels[campaign.status as keyof typeof statusLabels] || campaign.status}
                </Badge>
                <span className="text-sm text-muted-foreground">
                  ID: #{campaign.id}
                </span>
              </div>
              <p className="text-sm text-muted-foreground mt-2">
                {campaign.prompt}
              </p>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="ml-4"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Campaign Stats */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-4 w-4" />
                {__('Performance Stats', 'bettercampaign')}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-1">
                  <div className="flex items-center gap-2">
                    <Eye className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm text-muted-foreground">{__('Views', 'bettercampaign')}</span>
                  </div>
                  <p className="text-2xl font-bold">{stats.views.toLocaleString()}</p>
                </div>
                <div className="space-y-1">
                  <div className="flex items-center gap-2">
                    <MousePointer className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm text-muted-foreground">{__('Clicks', 'bettercampaign')}</span>
                  </div>
                  <p className="text-2xl font-bold">{stats.clicks.toLocaleString()}</p>
                </div>
                <div className="space-y-1">
                  <div className="flex items-center gap-2">
                    <TrendingUp className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm text-muted-foreground">{__('Conversions', 'bettercampaign')}</span>
                  </div>
                  <p className="text-2xl font-bold">{stats.conversions.toLocaleString()}</p>
                </div>
                <div className="space-y-1">
                  <div className="flex items-center gap-2">
                    <Users className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm text-muted-foreground">{__('Conv. Rate', 'bettercampaign')}</span>
                  </div>
                  <p className="text-2xl font-bold">{stats.conversionRate}%</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Campaign Features */}
          <Card>
            <CardHeader>
              <CardTitle>{__('Active Features', 'bettercampaign')}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {campaign.permissions.map((permission) => {
                  const Icon = featureIcons[permission as keyof typeof featureIcons] || Sparkles;
                  const label = featureLabels[permission as keyof typeof featureLabels] || permission;
                  
                  return (
                    <div key={permission} className="flex items-center gap-3 p-2 rounded-lg bg-muted/50">
                      <Icon className="h-4 w-4 text-primary" />
                      <span className="font-medium">{label}</span>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>

          {/* Reference Links */}
          {links.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>{__('Campaign Assets', 'bettercampaign')}</CardTitle>
                <CardDescription>
                  {__('View and manage your campaign components', 'bettercampaign')}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {links.map((link, index) => {
                    const Icon = featureIcons[link.type as keyof typeof featureIcons] || ExternalLink;
                    
                    return (
                      <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center gap-3">
                          <Icon className="h-4 w-4 text-muted-foreground" />
                          <span className="font-medium">{link.label}</span>
                        </div>
                        <div className="flex gap-2">
                          <Button size="sm" variant="outline" asChild>
                            <a href={link.url} target="_blank" rel="noopener noreferrer">
                              <Eye className="h-3 w-3 mr-1" />
                              {__('View', 'bettercampaign')}
                            </a>
                          </Button>
                          <Button size="sm" variant="outline" asChild>
                            <a href={link.editUrl} target="_blank" rel="noopener noreferrer">
                              <ExternalLink className="h-3 w-3 mr-1" />
                              {__('Edit', 'bettercampaign')}
                            </a>
                          </Button>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Campaign Info */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                {__('Campaign Info', 'bettercampaign')}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">{__('Created', 'bettercampaign')}</span>
                <span className="text-sm font-medium">{formatDate(campaign.created_at)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">{__('Last Updated', 'bettercampaign')}</span>
                <span className="text-sm font-medium">{formatDate(campaign.updated_at)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">{__('Features Count', 'bettercampaign')}</span>
                <span className="text-sm font-medium">{campaign.permissions.length}</span>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};
