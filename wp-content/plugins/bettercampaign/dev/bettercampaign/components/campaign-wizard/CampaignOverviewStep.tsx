import { __ } from '@wordpress/i18n';
import React from 'react';
import { UseFormReturn } from 'react-hook-form';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/bettercampaign/components/ui/card';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/bettercampaign/components/ui/form';
import { Input } from '@/bettercampaign/components/ui/input';
import { SimpleSelect } from '@/bettercampaign/components/ui/simple-select';
import { DateRangePicker } from '@/bettercampaign/components/ui/date-range-picker';
import { RadioGroup, RadioGroupItem } from '@/bettercampaign/components/ui/radio-group';
import { Label } from '@/bettercampaign/components/ui/label';
import { CampaignOverview } from '@/bettercampaign/lib/schemas';

interface CampaignOverviewStepProps {
  form: UseFormReturn<CampaignOverview>;
}

const campaignTypes = [
  { value: 'bfcm', label: __('Black Friday / Cyber Monday (BFCM)', 'bettercampaign'), description: __('Biggest annual sale', 'bettercampaign') },
  { value: 'new_year_sale', label: __('New Year Sale', 'bettercampaign'), description: __('Start the year with great deals', 'bettercampaign') },
  { value: 'christmas_sale', label: __('Christmas Sale', 'bettercampaign'), description: __('Holiday season promotions', 'bettercampaign') },
  { value: 'easter_sale', label: __('Easter Sale', 'bettercampaign'), description: __('Spring celebration offers', 'bettercampaign') },
  { value: 'valentines_day', label: __('Valentine\'s Day Campaign', 'bettercampaign'), description: __('Love-themed promotions', 'bettercampaign') },
  { value: 'halloween_deal', label: __('Halloween Deal', 'bettercampaign'), description: __('Spooky season specials', 'bettercampaign') },
  { value: 'summer_sale', label: __('Summer Sale', 'bettercampaign'), description: __('Hot weather, hot deals', 'bettercampaign') },
  { value: 'spring_flash_sale', label: __('Spring Flash Sale', 'bettercampaign'), description: __('Limited time spring offers', 'bettercampaign') },
  { value: 'back_to_school', label: __('Back to School Offer', 'bettercampaign'), description: __('Education season deals', 'bettercampaign') },
  { value: 'end_of_year_clearance', label: __('End of Year Clearance', 'bettercampaign'), description: __('Year-end inventory clearance', 'bettercampaign') },
];

// Campaign themes removed as requested

export const CampaignOverviewStep: React.FC<CampaignOverviewStepProps> = ({ form }) => {
  return (
    <Form {...form}>
      <Card>
        <CardHeader>
          <CardTitle>{__('Campaign Overview', 'bettercampaign')}</CardTitle>
          <CardDescription>
            {__('Set up the basic information for your marketing campaign', 'bettercampaign')}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
        {/* Campaign Name */}
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{__('Campaign Name', 'bettercampaign')}</FormLabel>
              <FormControl>
                <Input
                  placeholder={__('Enter campaign name...', 'bettercampaign')}
                  {...field}
                />
              </FormControl>
              <FormDescription>
                {__('A descriptive name for your campaign', 'bettercampaign')}
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Campaign Type */}
        <FormField
          control={form.control}
          name="type"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{__('Campaign Type', 'bettercampaign')}</FormLabel>
              <FormControl>
                <SimpleSelect
                  options={campaignTypes}
                  value={field.value}
                  onValueChange={field.onChange}
                  placeholder={__('Select campaign type', 'bettercampaign')}
                  showDescriptions={true}
                />
              </FormControl>
              <FormDescription>
                {__('Choose the type of campaign that best fits your promotion', 'bettercampaign')}
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Campaign Date Range */}
        {/* <FormField
          control={form.control}
          name="dateRange"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{__('Campaign Duration', 'bettercampaign')}</FormLabel>
              <FormControl>
                <DateRangePicker
                  dateRange={field.value}
                  onDateRangeChange={field.onChange}
                  placeholder={__('Select campaign start and end dates', 'bettercampaign')}
                />
              </FormControl>
              <FormDescription>
                {__('Choose when your campaign should start and end', 'bettercampaign')}
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        /> */}

        {/* Campaign Theme removed as requested */}
      </CardContent>
    </Card>
    </Form>
  );
};
