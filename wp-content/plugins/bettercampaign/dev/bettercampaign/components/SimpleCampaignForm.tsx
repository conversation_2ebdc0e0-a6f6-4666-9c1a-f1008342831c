import { __ } from '@wordpress/i18n';
import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/bettercampaign/components/ui/button';
import { Input } from '@/bettercampaign/components/ui/input';
import { Textarea } from '@/bettercampaign/components/ui/textarea';
import { Checkbox } from '@/bettercampaign/components/ui/checkbox';
import { Label } from '@/bettercampaign/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/bettercampaign/components/ui/card';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/bettercampaign/components/ui/form';
import { useToast } from '@/bettercampaign/components/ui/use-toast';
import { LoadingSpinner } from '@/bettercampaign/components/ui/loading-spinner';
import { <PERSON>rk<PERSON>, <PERSON> } from 'lucide-react';
import apiFetch from '@wordpress/api-fetch';

// Simplified form validation schema
const campaignSchema = z.object({
  title: z.string().min(1, __('Campaign title is required', 'bettercampaign')),
  prompt: z.string().min(10, __('Campaign description must be at least 10 characters', 'bettercampaign')),
  permissions: z.array(z.string()).min(1, __('Please select at least one feature', 'bettercampaign')),
});

type CampaignFormData = z.infer<typeof campaignSchema>;

interface SimpleCampaignFormProps {
  onSuccess?: (campaign: any) => void;
  onCancel?: () => void;
}

// Available features (simplified)
const availableFeatures = [
  {
    id: 'notification_bar',
    label: __('Notification Bar', 'bettercampaign'),
    description: __('Create attention-grabbing notification bars', 'bettercampaign'),
  },
  {
    id: 'popup',
    label: __('Exit Intent Popup', 'bettercampaign'),
    description: __('Capture visitors before they leave', 'bettercampaign'),
  },
  {
    id: 'deal_page',
    label: __('Landing Page', 'bettercampaign'),
    description: __('Create dedicated campaign landing pages', 'bettercampaign'),
  },
  {
    id: 'short_links',
    label: __('Short Links', 'bettercampaign'),
    description: __('Generate trackable short links', 'bettercampaign'),
  },
  {
    id: 'price_tracking',
    label: __('Price Tracking', 'bettercampaign'),
    description: __('Monitor and update product prices', 'bettercampaign'),
  },
  {
    id: 'ai_content',
    label: __('AI Content', 'bettercampaign'),
    description: __('Generate marketing content with AI', 'bettercampaign'),
  },
];

const SimpleCampaignForm: React.FC<SimpleCampaignFormProps> = ({ onSuccess, onCancel }) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();

  const form = useForm<CampaignFormData>({
    resolver: zodResolver(campaignSchema),
    defaultValues: {
      title: '',
      prompt: '',
      permissions: [],
    },
  });

  const onSubmit = async (data: CampaignFormData) => {
    setIsSubmitting(true);
    
    try {
      const response = await apiFetch({
        path: 'bettercampaign/v1/campaigns',
        method: 'POST',
        data: data,
      });

      if (response && (response as any).success) {
        toast({
          title: __('Success', 'bettercampaign'),
          description: __('Campaign created successfully!', 'bettercampaign'),
        });
        
        form.reset();
        onSuccess?.(response);
      } else {
        throw new Error((response as any).message || __('Failed to create campaign', 'bettercampaign'));
      }
    } catch (error) {
      toast({
        title: __('Error', 'bettercampaign'),
        description: error instanceof Error ? error.message : __('Failed to create campaign', 'bettercampaign'),
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="max-w-2xl mx-auto">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-primary/10 rounded-lg">
            <Sparkles className="h-5 w-5 text-primary" />
          </div>
          <div>
            <h2 className="text-xl font-semibold">{__('Create New Campaign', 'bettercampaign')}</h2>
            <p className="text-sm text-muted-foreground">
              {__('Set up your marketing campaign in just a few steps', 'bettercampaign')}
            </p>
          </div>
        </div>
        {onCancel && (
          <Button variant="ghost" size="sm" onClick={onCancel}>
            <X className="h-4 w-4" />
          </Button>
        )}
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Campaign Title */}
          <FormField
            control={form.control}
            name="title"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{__('Campaign Title', 'bettercampaign')}</FormLabel>
                <FormControl>
                  <Input 
                    placeholder={__('Enter a descriptive campaign title...', 'bettercampaign')} 
                    {...field} 
                  />
                </FormControl>
                <FormDescription>
                  {__('A clear, descriptive name for your marketing campaign', 'bettercampaign')}
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Campaign Description */}
          <FormField
            control={form.control}
            name="prompt"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{__('Campaign Description', 'bettercampaign')}</FormLabel>
                <FormControl>
                  <Textarea 
                    placeholder={__('Describe your campaign goals, target audience, key messages, and any specific requirements...', 'bettercampaign')}
                    className="min-h-[120px] resize-none"
                    {...field} 
                  />
                </FormControl>
                <FormDescription>
                  {__('Provide detailed information about your campaign objectives and strategy', 'bettercampaign')}
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Features Selection */}
          <FormField
            control={form.control}
            name="permissions"
            render={() => (
              <FormItem>
                <div className="mb-4">
                  <FormLabel className="text-base font-medium">
                    {__('Campaign Features', 'bettercampaign')}
                  </FormLabel>
                  <FormDescription>
                    {__('Select the features you want to include in this campaign', 'bettercampaign')}
                  </FormDescription>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {availableFeatures.map((feature) => (
                    <FormField
                      key={feature.id}
                      control={form.control}
                      name="permissions"
                      render={({ field }) => {
                        return (
                          <FormItem
                            key={feature.id}
                            className="flex flex-row items-start space-x-3 space-y-0 rounded-lg border p-4 hover:bg-muted/50 transition-colors"
                          >
                            <FormControl>
                              <Checkbox
                                checked={field.value?.includes(feature.id)}
                                onCheckedChange={(checked) => {
                                  return checked
                                    ? field.onChange([...field.value, feature.id])
                                    : field.onChange(
                                        field.value?.filter(
                                          (value) => value !== feature.id
                                        )
                                      )
                                }}
                              />
                            </FormControl>
                            <div className="space-y-1 leading-none">
                              <FormLabel className="font-medium cursor-pointer">
                                {feature.label}
                              </FormLabel>
                              <FormDescription className="text-xs">
                                {feature.description}
                              </FormDescription>
                            </div>
                          </FormItem>
                        )
                      }}
                    />
                  ))}
                </div>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Form Actions */}
          <div className="flex justify-end space-x-3 pt-6 border-t">
            {onCancel && (
              <Button 
                type="button" 
                variant="outline" 
                onClick={onCancel}
                disabled={isSubmitting}
              >
                {__('Cancel', 'bettercampaign')}
              </Button>
            )}
            <Button 
              type="submit" 
              disabled={isSubmitting}
              className="min-w-[140px]"
            >
              {isSubmitting ? (
                <LoadingSpinner 
                  size="sm" 
                  text={__('Creating...', 'bettercampaign')} 
                />
              ) : (
                <>
                  <Sparkles className="h-4 w-4 mr-2" />
                  {__('Create Campaign', 'bettercampaign')}
                </>
              )}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
};

export default SimpleCampaignForm;
