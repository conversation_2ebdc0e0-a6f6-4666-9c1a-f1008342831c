*, ::before, ::after {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}/*
! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com
*//*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
  box-sizing: border-box; /* 1 */
  border-width: 0; /* 2 */
  border-style: solid; /* 2 */
  border-color: #e5e7eb; /* 2 */
}

::before,
::after {
  --tw-content: '';
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/

html,
:host {
  line-height: 1.5; /* 1 */
  -webkit-text-size-adjust: 100%; /* 2 */
  -moz-tab-size: 4; /* 3 */
  -o-tab-size: 4;
     tab-size: 4; /* 3 */
  font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"; /* 4 */
  font-feature-settings: normal; /* 5 */
  font-variation-settings: normal; /* 6 */
  -webkit-tap-highlight-color: transparent; /* 7 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
  margin: 0; /* 1 */
  line-height: inherit; /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
  height: 0; /* 1 */
  color: inherit; /* 2 */
  border-top-width: 1px; /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
  color: inherit;
  text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
  font-weight: bolder;
}

/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; /* 1 */
  font-feature-settings: normal; /* 2 */
  font-variation-settings: normal; /* 3 */
  font-size: 1em; /* 4 */
}

/*
Add the correct font size in all browsers.
*/

small {
  font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
  text-indent: 0; /* 1 */
  border-color: inherit; /* 2 */
  border-collapse: collapse; /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
  font-family: inherit; /* 1 */
  font-feature-settings: inherit; /* 1 */
  font-variation-settings: inherit; /* 1 */
  font-size: 100%; /* 1 */
  font-weight: inherit; /* 1 */
  line-height: inherit; /* 1 */
  letter-spacing: inherit; /* 1 */
  color: inherit; /* 1 */
  margin: 0; /* 2 */
  padding: 0; /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
  text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
  -webkit-appearance: button; /* 1 */
  background-color: transparent; /* 2 */
  background-image: none; /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
  outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
  box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
  vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type='search'] {
  -webkit-appearance: textfield; /* 1 */
  outline-offset: -2px; /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

::-webkit-file-upload-button {
  -webkit-appearance: button; /* 1 */
  font: inherit; /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
  display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

/*
Reset default styling for dialogs.
*/
dialog {
  padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
  resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::-moz-placeholder, textarea::-moz-placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}

input::placeholder,
textarea::placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role="button"] {
  cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/
:disabled {
  cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block; /* 1 */
  vertical-align: middle; /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}

/* Make elements with the HTML hidden attribute stay hidden by default */
[hidden]:where(:not([hidden="until-found"])) {
  display: none;
}:root{--background: 0 0% 100%;--foreground: 222.2 84% 4.9%;--card: 0 0% 100%;--card-foreground: 222.2 84% 4.9%;--popover: 0 0% 100%;--popover-foreground: 222.2 84% 4.9%;--primary: 221.2 83.2% 53.3%;--primary-foreground: 210 40% 98%;--secondary: 210 40% 96%;--secondary-foreground: 222.2 84% 4.9%;--muted: 210 40% 96%;--muted-foreground: 215.4 16.3% 46.9%;--accent: 210 40% 96%;--accent-foreground: 222.2 84% 4.9%;--destructive: 0 84.2% 60.2%;--destructive-foreground: 210 40% 98%;--border: 214.3 31.8% 91.4%;--input: 214.3 31.8% 91.4%;--ring: 221.2 83.2% 53.3%;--radius: 0.5rem}* {
  border-color: hsl(var(--border));
}body {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
}.container {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: 2rem;
  padding-right: 2rem;
}@media (min-width: 1400px) {

  .container {
    max-width: 1400px;
  }
}.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}.pointer-events-none {
  pointer-events: none;
}.pointer-events-auto {
  pointer-events: auto;
}.invisible {
  visibility: hidden;
}.static {
  position: static;
}.fixed {
  position: fixed;
}.absolute {
  position: absolute;
}.relative {
  position: relative;
}.sticky {
  position: sticky;
}.inset-0 {
  inset: 0px;
}.inset-x-0 {
  right: 0px;
  left: 0px;
}.bottom-0 {
  bottom: 0px;
}.left-0 {
  right: 0px;
}.left-2 {
  right: 0.5rem;
}.left-3 {
  right: 0.75rem;
}.left-\[50\%\] {
  right: 50%;
}.right-0 {
  left: 0px;
}.right-2 {
  left: 0.5rem;
}.right-4 {
  left: 1rem;
}.top-0 {
  top: 0px;
}.top-1\/2 {
  top: 50%;
}.top-2 {
  top: 0.5rem;
}.top-4 {
  top: 1rem;
}.top-6 {
  top: 1.5rem;
}.top-\[50\%\] {
  top: 50%;
}.z-50 {
  z-index: 50;
}.z-\[100\] {
  z-index: 100;
}.z-\[999995\] {
  z-index: 999995;
}.z-\[999999\] {
  z-index: 999999;
}.-mx-1 {
  margin-right: -0.25rem;
  margin-left: -0.25rem;
}.mx-4 {
  margin-right: 1rem;
  margin-left: 1rem;
}.mx-auto {
  margin-right: auto;
  margin-left: auto;
}.my-1 {
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
}.mb-1 {
  margin-bottom: 0.25rem;
}.mb-2 {
  margin-bottom: 0.5rem;
}.mb-3 {
  margin-bottom: 0.75rem;
}.mb-4 {
  margin-bottom: 1rem;
}.mb-6 {
  margin-bottom: 1.5rem;
}.ml-2 {
  margin-right: 0.5rem;
}.ml-4 {
  margin-right: 1rem;
}.ml-auto {
  margin-right: auto;
}.mr-1 {
  margin-left: 0.25rem;
}.mr-2 {
  margin-left: 0.5rem;
}.mt-0\.5 {
  margin-top: 0.125rem;
}.mt-1 {
  margin-top: 0.25rem;
}.mt-2 {
  margin-top: 0.5rem;
}.mt-4 {
  margin-top: 1rem;
}.mt-6 {
  margin-top: 1.5rem;
}.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}.block {
  display: block;
}.flex {
  display: flex;
}.inline-flex {
  display: inline-flex;
}.table {
  display: table;
}.grid {
  display: grid;
}.hidden {
  display: none;
}.aspect-square {
  aspect-ratio: 1 / 1;
}.size-4 {
  width: 1rem;
  height: 1rem;
}.size-\[--cell-size\] {
  width: var(--cell-size);
  height: var(--cell-size);
}.h-10 {
  height: 2.5rem;
}.h-11 {
  height: 2.75rem;
}.h-12 {
  height: 3rem;
}.h-2 {
  height: 0.5rem;
}.h-2\.5 {
  height: 0.625rem;
}.h-20 {
  height: 5rem;
}.h-24 {
  height: 6rem;
}.h-3 {
  height: 0.75rem;
}.h-3\.5 {
  height: 0.875rem;
}.h-32 {
  height: 8rem;
}.h-4 {
  height: 1rem;
}.h-5 {
  height: 1.25rem;
}.h-6 {
  height: 1.5rem;
}.h-64 {
  height: 16rem;
}.h-8 {
  height: 2rem;
}.h-9 {
  height: 2.25rem;
}.h-\[--cell-size\] {
  height: var(--cell-size);
}.h-\[1px\] {
  height: 1px;
}.h-\[var\(--radix-select-trigger-height\)\] {
  height: var(--radix-select-trigger-height);
}.h-auto {
  height: auto;
}.h-full {
  height: 100%;
}.h-px {
  height: 1px;
}.max-h-60 {
  max-height: 15rem;
}.max-h-\[--radix-select-content-available-height\] {
  max-height: var(--radix-select-content-available-height);
}.max-h-\[90vh\] {
  max-height: 90vh;
}.max-h-\[var\(--radix-dropdown-menu-content-available-height\)\] {
  max-height: var(--radix-dropdown-menu-content-available-height);
}.max-h-screen {
  max-height: 100vh;
}.min-h-\[120px\] {
  min-height: 120px;
}.min-h-\[80px\] {
  min-height: 80px;
}.w-10 {
  width: 2.5rem;
}.w-11 {
  width: 2.75rem;
}.w-12 {
  width: 3rem;
}.w-2 {
  width: 0.5rem;
}.w-2\.5 {
  width: 0.625rem;
}.w-24 {
  width: 6rem;
}.w-3 {
  width: 0.75rem;
}.w-3\.5 {
  width: 0.875rem;
}.w-4 {
  width: 1rem;
}.w-5 {
  width: 1.25rem;
}.w-6 {
  width: 1.5rem;
}.w-72 {
  width: 18rem;
}.w-8 {
  width: 2rem;
}.w-96 {
  width: 24rem;
}.w-\[--cell-size\] {
  width: var(--cell-size);
}.w-\[1px\] {
  width: 1px;
}.w-\[50px\] {
  width: 50px;
}.w-auto {
  width: auto;
}.w-fit {
  width: -moz-fit-content;
  width: fit-content;
}.w-full {
  width: 100%;
}.min-w-\[--cell-size\] {
  min-width: var(--cell-size);
}.min-w-\[120px\] {
  min-width: 120px;
}.min-w-\[140px\] {
  min-width: 140px;
}.min-w-\[8rem\] {
  min-width: 8rem;
}.min-w-\[var\(--radix-select-trigger-width\)\] {
  min-width: var(--radix-select-trigger-width);
}.max-w-2xl {
  max-width: 42rem;
}.max-w-4xl {
  max-width: 56rem;
}.max-w-7xl {
  max-width: 80rem;
}.max-w-lg {
  max-width: 32rem;
}.max-w-sm {
  max-width: 24rem;
}.flex-1 {
  flex: 1 1 0%;
}.flex-shrink-0 {
  flex-shrink: 0;
}.shrink-0 {
  flex-shrink: 0;
}.caption-bottom {
  caption-side: bottom;
}.border-collapse {
  border-collapse: collapse;
}.origin-\[--radix-dropdown-menu-content-transform-origin\] {
  transform-origin: var(--radix-dropdown-menu-content-transform-origin);
}.origin-\[--radix-popover-content-transform-origin\] {
  transform-origin: var(--radix-popover-content-transform-origin);
}.origin-\[--radix-select-content-transform-origin\] {
  transform-origin: var(--radix-select-content-transform-origin);
}.-translate-y-1\/2 {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}.translate-x-0 {
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}.translate-x-\[-50\%\] {
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}.translate-x-full {
  --tw-translate-x: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}.translate-y-\[-50\%\] {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}.transform {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}@keyframes spin {

  to {
    transform: rotate(-360deg);
  }
}.animate-spin {
  animation: spin 1s linear infinite;
}.cursor-default {
  cursor: default;
}.cursor-not-allowed {
  cursor: not-allowed;
}.cursor-pointer {
  cursor: pointer;
}.select-none {
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}.resize-none {
  resize: none;
}.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}.flex-row {
  flex-direction: row;
}.flex-col {
  flex-direction: column;
}.flex-col-reverse {
  flex-direction: column-reverse;
}.flex-wrap {
  flex-wrap: wrap;
}.items-start {
  align-items: flex-start;
}.items-center {
  align-items: center;
}.justify-start {
  justify-content: flex-start;
}.justify-end {
  justify-content: flex-end;
}.justify-center {
  justify-content: center;
}.justify-between {
  justify-content: space-between;
}.gap-1 {
  gap: 0.25rem;
}.gap-1\.5 {
  gap: 0.375rem;
}.gap-2 {
  gap: 0.5rem;
}.gap-3 {
  gap: 0.75rem;
}.gap-4 {
  gap: 1rem;
}.gap-6 {
  gap: 1.5rem;
}.space-x-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-left: calc(0.5rem * var(--tw-space-x-reverse));
  margin-right: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}.space-x-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-left: calc(0.75rem * var(--tw-space-x-reverse));
  margin-right: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
}.space-x-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-left: calc(1rem * var(--tw-space-x-reverse));
  margin-right: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}.space-y-0 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0px * var(--tw-space-y-reverse));
}.space-y-0\.5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.125rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.125rem * var(--tw-space-y-reverse));
}.space-y-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));
}.space-y-1\.5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.375rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.375rem * var(--tw-space-y-reverse));
}.space-y-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}.space-y-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}.space-y-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}.space-y-6 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}.space-y-8 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(2rem * var(--tw-space-y-reverse));
}.overflow-auto {
  overflow: auto;
}.overflow-hidden {
  overflow: hidden;
}.overflow-y-auto {
  overflow-y: auto;
}.overflow-x-hidden {
  overflow-x: hidden;
}.whitespace-nowrap {
  white-space: nowrap;
}.break-all {
  word-break: break-all;
}.rounded {
  border-radius: 0.25rem;
}.rounded-full {
  border-radius: 9999px;
}.rounded-lg {
  border-radius: var(--radius);
}.rounded-md {
  border-radius: calc(var(--radius) - 2px);
}.rounded-none {
  border-radius: 0px;
}.rounded-sm {
  border-radius: calc(var(--radius) - 4px);
}.rounded-l-md {
  border-top-right-radius: calc(var(--radius) - 2px);
  border-bottom-right-radius: calc(var(--radius) - 2px);
}.rounded-l-none {
  border-top-right-radius: 0px;
  border-bottom-right-radius: 0px;
}.rounded-r-md {
  border-top-left-radius: calc(var(--radius) - 2px);
  border-bottom-left-radius: calc(var(--radius) - 2px);
}.rounded-t-lg {
  border-top-right-radius: var(--radius);
  border-top-left-radius: var(--radius);
}.border {
  border-width: 1px;
}.border-2 {
  border-width: 2px;
}.border-b {
  border-bottom-width: 1px;
}.border-b-2 {
  border-bottom-width: 2px;
}.border-r-0 {
  border-left-width: 0px;
}.border-t {
  border-top-width: 1px;
}.border-dashed {
  border-style: dashed;
}.border-blue-200 {
  --tw-border-opacity: 1;
  border-color: rgb(191 219 254 / var(--tw-border-opacity, 1));
}.border-blue-300 {
  --tw-border-opacity: 1;
  border-color: rgb(147 197 253 / var(--tw-border-opacity, 1));
}.border-border {
  border-color: hsl(var(--border));
}.border-destructive {
  border-color: hsl(var(--destructive));
}.border-destructive\/50 {
  border-color: hsl(var(--destructive) / 0.5);
}.border-gray-200 {
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
}.border-green-200 {
  --tw-border-opacity: 1;
  border-color: rgb(187 247 208 / var(--tw-border-opacity, 1));
}.border-green-300 {
  --tw-border-opacity: 1;
  border-color: rgb(134 239 172 / var(--tw-border-opacity, 1));
}.border-input {
  border-color: hsl(var(--input));
}.border-muted-foreground\/25 {
  border-color: hsl(var(--muted-foreground) / 0.25);
}.border-primary {
  border-color: hsl(var(--primary));
}.border-primary\/20 {
  border-color: hsl(var(--primary) / 0.2);
}.border-transparent {
  border-color: transparent;
}.bg-accent {
  background-color: hsl(var(--accent));
}.bg-background {
  background-color: hsl(var(--background));
}.bg-background\/95 {
  background-color: hsl(var(--background) / 0.95);
}.bg-black {
  --tw-bg-opacity: 1;
  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));
}.bg-black\/50 {
  background-color: rgb(0 0 0 / 0.5);
}.bg-black\/80 {
  background-color: rgb(0 0 0 / 0.8);
}.bg-blue-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));
}.bg-blue-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));
}.bg-blue-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));
}.bg-border {
  background-color: hsl(var(--border));
}.bg-card {
  background-color: hsl(var(--card));
}.bg-destructive {
  background-color: hsl(var(--destructive));
}.bg-gray-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}.bg-green-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));
}.bg-green-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));
}.bg-green-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1));
}.bg-muted {
  background-color: hsl(var(--muted));
}.bg-muted\/50 {
  background-color: hsl(var(--muted) / 0.5);
}.bg-orange-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 237 213 / var(--tw-bg-opacity, 1));
}.bg-popover {
  background-color: hsl(var(--popover));
}.bg-primary {
  background-color: hsl(var(--primary));
}.bg-primary\/10 {
  background-color: hsl(var(--primary) / 0.1);
}.bg-primary\/5 {
  background-color: hsl(var(--primary) / 0.05);
}.bg-purple-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(243 232 255 / var(--tw-bg-opacity, 1));
}.bg-red-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));
}.bg-secondary {
  background-color: hsl(var(--secondary));
}.bg-transparent {
  background-color: transparent;
}.bg-white {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}.bg-white\/30 {
  background-color: rgb(255 255 255 / 0.3);
}.bg-white\/50 {
  background-color: rgb(255 255 255 / 0.5);
}.bg-opacity-50 {
  --tw-bg-opacity: 0.5;
}.bg-gradient-to-br {
  background-image: linear-gradient(to bottom left, var(--tw-gradient-stops));
}.from-primary\/5 {
  --tw-gradient-from: hsl(var(--primary) / 0.05) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--primary) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}.to-primary\/10 {
  --tw-gradient-to: hsl(var(--primary) / 0.1) var(--tw-gradient-to-position);
}.fill-current {
  fill: currentColor;
}.object-cover {
  -o-object-fit: cover;
     object-fit: cover;
}.p-0 {
  padding: 0px;
}.p-1 {
  padding: 0.25rem;
}.p-2 {
  padding: 0.5rem;
}.p-3 {
  padding: 0.75rem;
}.p-4 {
  padding: 1rem;
}.p-6 {
  padding: 1.5rem;
}.px-2 {
  padding-right: 0.5rem;
  padding-left: 0.5rem;
}.px-2\.5 {
  padding-right: 0.625rem;
  padding-left: 0.625rem;
}.px-3 {
  padding-right: 0.75rem;
  padding-left: 0.75rem;
}.px-4 {
  padding-right: 1rem;
  padding-left: 1rem;
}.px-8 {
  padding-right: 2rem;
  padding-left: 2rem;
}.px-\[--cell-size\] {
  padding-right: var(--cell-size);
  padding-left: var(--cell-size);
}.py-0\.5 {
  padding-top: 0.125rem;
  padding-bottom: 0.125rem;
}.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}.py-1\.5 {
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}.py-12 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}.pb-2 {
  padding-bottom: 0.5rem;
}.pb-3 {
  padding-bottom: 0.75rem;
}.pb-4 {
  padding-bottom: 1rem;
}.pl-2 {
  padding-right: 0.5rem;
}.pl-8 {
  padding-right: 2rem;
}.pr-1 {
  padding-left: 0.25rem;
}.pr-2 {
  padding-left: 0.5rem;
}.pr-8 {
  padding-left: 2rem;
}.pt-0 {
  padding-top: 0px;
}.pt-2 {
  padding-top: 0.5rem;
}.pt-6 {
  padding-top: 1.5rem;
}.text-left {
  text-align: right;
}.text-center {
  text-align: center;
}.align-middle {
  vertical-align: middle;
}.font-mono {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}.text-3xl {
  font-size: 1.875rem;
  line-height: 2.25rem;
}.text-\[0\.8rem\] {
  font-size: 0.8rem;
}.text-base {
  font-size: 1rem;
  line-height: 1.5rem;
}.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}.font-bold {
  font-weight: 700;
}.font-medium {
  font-weight: 500;
}.font-normal {
  font-weight: 400;
}.font-semibold {
  font-weight: 600;
}.leading-none {
  line-height: 1;
}.tracking-tight {
  letter-spacing: -0.025em;
}.tracking-widest {
  letter-spacing: 0.1em;
}.text-accent-foreground {
  color: hsl(var(--accent-foreground));
}.text-blue-600 {
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity, 1));
}.text-blue-700 {
  --tw-text-opacity: 1;
  color: rgb(29 78 216 / var(--tw-text-opacity, 1));
}.text-blue-800 {
  --tw-text-opacity: 1;
  color: rgb(30 64 175 / var(--tw-text-opacity, 1));
}.text-blue-900 {
  --tw-text-opacity: 1;
  color: rgb(30 58 138 / var(--tw-text-opacity, 1));
}.text-card-foreground {
  color: hsl(var(--card-foreground));
}.text-current {
  color: currentColor;
}.text-destructive {
  color: hsl(var(--destructive));
}.text-destructive-foreground {
  color: hsl(var(--destructive-foreground));
}.text-foreground {
  color: hsl(var(--foreground));
}.text-foreground\/50 {
  color: hsl(var(--foreground) / 0.5);
}.text-gray-400 {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}.text-gray-600 {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}.text-gray-900 {
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}.text-green-500 {
  --tw-text-opacity: 1;
  color: rgb(34 197 94 / var(--tw-text-opacity, 1));
}.text-green-600 {
  --tw-text-opacity: 1;
  color: rgb(22 163 74 / var(--tw-text-opacity, 1));
}.text-green-700 {
  --tw-text-opacity: 1;
  color: rgb(21 128 61 / var(--tw-text-opacity, 1));
}.text-green-800 {
  --tw-text-opacity: 1;
  color: rgb(22 101 52 / var(--tw-text-opacity, 1));
}.text-green-900 {
  --tw-text-opacity: 1;
  color: rgb(20 83 45 / var(--tw-text-opacity, 1));
}.text-muted-foreground {
  color: hsl(var(--muted-foreground));
}.text-orange-600 {
  --tw-text-opacity: 1;
  color: rgb(234 88 12 / var(--tw-text-opacity, 1));
}.text-popover-foreground {
  color: hsl(var(--popover-foreground));
}.text-primary {
  color: hsl(var(--primary));
}.text-primary-foreground {
  color: hsl(var(--primary-foreground));
}.text-purple-600 {
  --tw-text-opacity: 1;
  color: rgb(147 51 234 / var(--tw-text-opacity, 1));
}.text-secondary-foreground {
  color: hsl(var(--secondary-foreground));
}.text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}.line-through {
  text-decoration-line: line-through;
}.underline-offset-4 {
  text-underline-offset: 4px;
}.opacity-0 {
  opacity: 0;
}.opacity-100 {
  opacity: 1;
}.opacity-50 {
  opacity: 0.5;
}.opacity-60 {
  opacity: 0.6;
}.opacity-70 {
  opacity: 0.7;
}.opacity-90 {
  opacity: 0.9;
}.shadow-2xl {
  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}.shadow-lg {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}.shadow-md {
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}.shadow-sm {
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}.shadow-xl {
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}.outline-none {
  outline: 2px solid transparent;
  outline-offset: 2px;
}.outline {
  outline-style: solid;
}.ring-0 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}.ring-offset-background {
  --tw-ring-offset-color: hsl(var(--background));
}.filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}.backdrop-blur {
  --tw-backdrop-blur: blur(8px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}.backdrop-blur-sm {
  --tw-backdrop-blur: blur(4px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}.backdrop-filter {
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}.transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}.transition-opacity {
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}.transition-shadow {
  transition-property: box-shadow;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}.transition-transform {
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}.duration-200 {
  transition-duration: 200ms;
}.duration-300 {
  transition-duration: 300ms;
}.ease-in-out {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}@keyframes enter {

  from {
    opacity: var(--tw-enter-opacity, 1);
    transform: translate3d(var(--tw-enter-translate-x, 0), var(--tw-enter-translate-y, 0), 0) scale3d(var(--tw-enter-scale, 1), var(--tw-enter-scale, 1), var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0));
  }
}@keyframes exit {

  to {
    opacity: var(--tw-exit-opacity, 1);
    transform: translate3d(var(--tw-exit-translate-x, 0), var(--tw-exit-translate-y, 0), 0) scale3d(var(--tw-exit-scale, 1), var(--tw-exit-scale, 1), var(--tw-exit-scale, 1)) rotate(var(--tw-exit-rotate, 0));
  }
}.duration-200 {
  animation-duration: 200ms;
}.duration-300 {
  animation-duration: 300ms;
}.ease-in-out {
  animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}.paused {
  animation-play-state: paused;
}.\[--cell-size\:2rem\] {
  --cell-size: 2rem;
}.better-campaign-modal{z-index:999990 !important}.better-campaign-modal [data-radix-popper-content-wrapper]{z-index:999995 !important}.better-campaign-modal [data-radix-select-content]{z-index:999995 !important;position:fixed !important}.better-campaign-modal [data-radix-calendar]{z-index:999995 !important;position:fixed !important}.better-campaign-modal [data-radix-popover-content]{z-index:999995 !important;position:fixed !important}.better-campaign-modal [data-radix-portal]{z-index:999995 !important}.better-campaign-modal .rdp{z-index:999995 !important;position:fixed !important}.better-campaign-modal [role=listbox],.better-campaign-modal [role=menu],.better-campaign-modal [role=option]{z-index:999995 !important}.better-campaign-modal [data-radix-tooltip-content]{z-index:999996 !important}[data-radix-popper-content-wrapper]{z-index:999995 !important}[data-radix-select-content]{z-index:999995 !important}[data-radix-popover-content]{z-index:999995 !important}[data-radix-portal]{z-index:999995 !important}.rdp{z-index:999995 !important}[data-radix-select-trigger]{position:relative !important}button[aria-haspopup=dialog]{position:relative !important}.select-content,.popover-content,.calendar-content{z-index:999995 !important;position:fixed !important}.campaign-wizard-step{position:relative;z-index:1}.form-field{position:relative}.form-field [data-radix-select-content]{z-index:999995 !important}.form-field [data-radix-popover-content]{z-index:999995 !important}.bettercampaign-admin{font-family:-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen-Sans,Ubuntu,Cantarell,"Helvetica Neue",sans-serif}#bettercampaign{background:#fff;border-radius:8px;box-shadow:0 1px 3px rgba(0,0,0,.1);margin:20px 0;min-height:500px}.wp-admin #bettercampaign{margin:20px 2px 0 20px}@media screen and (max-width: 782px){.wp-admin #bettercampaign{margin:20px 0 0 10px}}.file\:border-0::file-selector-button {
  border-width: 0px;
}.file\:bg-transparent::file-selector-button {
  background-color: transparent;
}.file\:text-sm::file-selector-button {
  font-size: 0.875rem;
  line-height: 1.25rem;
}.file\:font-medium::file-selector-button {
  font-weight: 500;
}.placeholder\:text-muted-foreground::-moz-placeholder {
  color: hsl(var(--muted-foreground));
}.placeholder\:text-muted-foreground::placeholder {
  color: hsl(var(--muted-foreground));
}.hover\:border-primary\/50:hover {
  border-color: hsl(var(--primary) / 0.5);
}.hover\:bg-accent:hover {
  background-color: hsl(var(--accent));
}.hover\:bg-blue-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(29 78 216 / var(--tw-bg-opacity, 1));
}.hover\:bg-destructive\/80:hover {
  background-color: hsl(var(--destructive) / 0.8);
}.hover\:bg-destructive\/90:hover {
  background-color: hsl(var(--destructive) / 0.9);
}.hover\:bg-gray-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}.hover\:bg-green-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));
}.hover\:bg-green-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(187 247 208 / var(--tw-bg-opacity, 1));
}.hover\:bg-green-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(21 128 61 / var(--tw-bg-opacity, 1));
}.hover\:bg-muted\/50:hover {
  background-color: hsl(var(--muted) / 0.5);
}.hover\:bg-muted\/80:hover {
  background-color: hsl(var(--muted) / 0.8);
}.hover\:bg-primary\/80:hover {
  background-color: hsl(var(--primary) / 0.8);
}.hover\:bg-primary\/90:hover {
  background-color: hsl(var(--primary) / 0.9);
}.hover\:bg-secondary:hover {
  background-color: hsl(var(--secondary));
}.hover\:bg-secondary\/80:hover {
  background-color: hsl(var(--secondary) / 0.8);
}.hover\:bg-white\/50:hover {
  background-color: rgb(255 255 255 / 0.5);
}.hover\:text-accent-foreground:hover {
  color: hsl(var(--accent-foreground));
}.hover\:text-foreground:hover {
  color: hsl(var(--foreground));
}.hover\:underline:hover {
  text-decoration-line: underline;
}.hover\:opacity-100:hover {
  opacity: 1;
}.hover\:opacity-80:hover {
  opacity: 0.8;
}.hover\:shadow-md:hover {
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}.focus\:bg-accent:focus {
  background-color: hsl(var(--accent));
}.focus\:text-accent-foreground:focus {
  color: hsl(var(--accent-foreground));
}.focus\:opacity-100:focus {
  opacity: 1;
}.focus\:outline-none:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}.focus\:ring-2:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}.focus\:ring-blue-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));
}.focus\:ring-ring:focus {
  --tw-ring-color: hsl(var(--ring));
}.focus\:ring-offset-2:focus {
  --tw-ring-offset-width: 2px;
}.focus-visible\:outline-none:focus-visible {
  outline: 2px solid transparent;
  outline-offset: 2px;
}.focus-visible\:ring-2:focus-visible {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}.focus-visible\:ring-ring:focus-visible {
  --tw-ring-color: hsl(var(--ring));
}.focus-visible\:ring-offset-2:focus-visible {
  --tw-ring-offset-width: 2px;
}.focus-visible\:ring-offset-background:focus-visible {
  --tw-ring-offset-color: hsl(var(--background));
}.disabled\:pointer-events-none:disabled {
  pointer-events: none;
}.disabled\:cursor-not-allowed:disabled {
  cursor: not-allowed;
}.disabled\:opacity-50:disabled {
  opacity: 0.5;
}.group:hover .group-hover\:text-gray-600 {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}.group:hover .group-hover\:opacity-100 {
  opacity: 1;
}.group.destructive .group-\[\.destructive\]\:border-muted\/40 {
  border-color: hsl(var(--muted) / 0.4);
}.group.destructive .group-\[\.destructive\]\:text-red-300 {
  --tw-text-opacity: 1;
  color: rgb(252 165 165 / var(--tw-text-opacity, 1));
}.group.destructive .group-\[\.destructive\]\:hover\:border-destructive\/30:hover {
  border-color: hsl(var(--destructive) / 0.3);
}.group.destructive .group-\[\.destructive\]\:hover\:bg-destructive:hover {
  background-color: hsl(var(--destructive));
}.group.destructive .group-\[\.destructive\]\:hover\:text-destructive-foreground:hover {
  color: hsl(var(--destructive-foreground));
}.group.destructive .group-\[\.destructive\]\:hover\:text-red-50:hover {
  --tw-text-opacity: 1;
  color: rgb(254 242 242 / var(--tw-text-opacity, 1));
}.group.destructive .group-\[\.destructive\]\:focus\:ring-destructive:focus {
  --tw-ring-color: hsl(var(--destructive));
}.group.destructive .group-\[\.destructive\]\:focus\:ring-red-400:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(248 113 113 / var(--tw-ring-opacity, 1));
}.group.destructive .group-\[\.destructive\]\:focus\:ring-offset-red-600:focus {
  --tw-ring-offset-color: #dc2626;
}.peer:disabled ~ .peer-disabled\:cursor-not-allowed {
  cursor: not-allowed;
}.peer:disabled ~ .peer-disabled\:opacity-70 {
  opacity: 0.7;
}.aria-disabled\:opacity-50[aria-disabled="true"] {
  opacity: 0.5;
}.aria-selected\:text-muted-foreground[aria-selected="true"] {
  color: hsl(var(--muted-foreground));
}.data-\[disabled\]\:pointer-events-none[data-disabled] {
  pointer-events: none;
}.data-\[side\=bottom\]\:translate-y-1[data-side="bottom"] {
  --tw-translate-y: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}.data-\[side\=left\]\:-translate-x-1[data-side="left"] {
  --tw-translate-x: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}.data-\[side\=right\]\:translate-x-1[data-side="right"] {
  --tw-translate-x: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}.data-\[side\=top\]\:-translate-y-1[data-side="top"] {
  --tw-translate-y: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}.data-\[state\=checked\]\:translate-x-5[data-state="checked"] {
  --tw-translate-x: 1.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}.data-\[state\=unchecked\]\:translate-x-0[data-state="unchecked"] {
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}.data-\[swipe\=cancel\]\:translate-x-0[data-swipe="cancel"] {
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}.data-\[swipe\=end\]\:translate-x-\[var\(--radix-toast-swipe-end-x\)\][data-swipe="end"] {
  --tw-translate-x: var(--radix-toast-swipe-end-x);
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}.data-\[swipe\=move\]\:translate-x-\[var\(--radix-toast-swipe-move-x\)\][data-swipe="move"] {
  --tw-translate-x: var(--radix-toast-swipe-move-x);
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}.data-\[range-end\=true\]\:rounded-md[data-range-end="true"] {
  border-radius: calc(var(--radius) - 2px);
}.data-\[range-middle\=true\]\:rounded-none[data-range-middle="true"] {
  border-radius: 0px;
}.data-\[range-start\=true\]\:rounded-md[data-range-start="true"] {
  border-radius: calc(var(--radius) - 2px);
}.data-\[selected\=true\]\:rounded-none[data-selected="true"] {
  border-radius: 0px;
}.data-\[range-end\=true\]\:bg-primary[data-range-end="true"] {
  background-color: hsl(var(--primary));
}.data-\[range-middle\=true\]\:bg-accent[data-range-middle="true"] {
  background-color: hsl(var(--accent));
}.data-\[range-start\=true\]\:bg-primary[data-range-start="true"] {
  background-color: hsl(var(--primary));
}.data-\[selected-single\=true\]\:bg-primary[data-selected-single="true"] {
  background-color: hsl(var(--primary));
}.data-\[state\=active\]\:bg-background[data-state="active"] {
  background-color: hsl(var(--background));
}.data-\[state\=checked\]\:bg-primary[data-state="checked"] {
  background-color: hsl(var(--primary));
}.data-\[state\=open\]\:bg-accent[data-state="open"] {
  background-color: hsl(var(--accent));
}.data-\[state\=selected\]\:bg-muted[data-state="selected"] {
  background-color: hsl(var(--muted));
}.data-\[state\=unchecked\]\:bg-input[data-state="unchecked"] {
  background-color: hsl(var(--input));
}.data-\[placeholder\]\:text-muted-foreground[data-placeholder] {
  color: hsl(var(--muted-foreground));
}.data-\[range-end\=true\]\:text-primary-foreground[data-range-end="true"] {
  color: hsl(var(--primary-foreground));
}.data-\[range-middle\=true\]\:text-accent-foreground[data-range-middle="true"] {
  color: hsl(var(--accent-foreground));
}.data-\[range-start\=true\]\:text-primary-foreground[data-range-start="true"] {
  color: hsl(var(--primary-foreground));
}.data-\[selected-single\=true\]\:text-primary-foreground[data-selected-single="true"] {
  color: hsl(var(--primary-foreground));
}.data-\[state\=active\]\:text-foreground[data-state="active"] {
  color: hsl(var(--foreground));
}.data-\[state\=checked\]\:text-primary-foreground[data-state="checked"] {
  color: hsl(var(--primary-foreground));
}.data-\[state\=open\]\:text-muted-foreground[data-state="open"] {
  color: hsl(var(--muted-foreground));
}.data-\[disabled\]\:opacity-50[data-disabled] {
  opacity: 0.5;
}.data-\[state\=active\]\:shadow-sm[data-state="active"] {
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}.data-\[swipe\=move\]\:transition-none[data-swipe="move"] {
  transition-property: none;
}.data-\[state\=open\]\:animate-in[data-state="open"] {
  animation-name: enter;
  animation-duration: 150ms;
  --tw-enter-opacity: initial;
  --tw-enter-scale: initial;
  --tw-enter-rotate: initial;
  --tw-enter-translate-x: initial;
  --tw-enter-translate-y: initial;
}.data-\[state\=closed\]\:animate-out[data-state="closed"] {
  animation-name: exit;
  animation-duration: 150ms;
  --tw-exit-opacity: initial;
  --tw-exit-scale: initial;
  --tw-exit-rotate: initial;
  --tw-exit-translate-x: initial;
  --tw-exit-translate-y: initial;
}.data-\[swipe\=end\]\:animate-out[data-swipe="end"] {
  animation-name: exit;
  animation-duration: 150ms;
  --tw-exit-opacity: initial;
  --tw-exit-scale: initial;
  --tw-exit-rotate: initial;
  --tw-exit-translate-x: initial;
  --tw-exit-translate-y: initial;
}.data-\[state\=closed\]\:fade-out-0[data-state="closed"] {
  --tw-exit-opacity: 0;
}.data-\[state\=closed\]\:fade-out-80[data-state="closed"] {
  --tw-exit-opacity: 0.8;
}.data-\[state\=open\]\:fade-in-0[data-state="open"] {
  --tw-enter-opacity: 0;
}.data-\[state\=closed\]\:zoom-out-95[data-state="closed"] {
  --tw-exit-scale: .95;
}.data-\[state\=open\]\:zoom-in-95[data-state="open"] {
  --tw-enter-scale: .95;
}.data-\[side\=bottom\]\:slide-in-from-top-2[data-side="bottom"] {
  --tw-enter-translate-y: -0.5rem;
}.data-\[side\=left\]\:slide-in-from-right-2[data-side="left"] {
  --tw-enter-translate-x: 0.5rem;
}.data-\[side\=right\]\:slide-in-from-left-2[data-side="right"] {
  --tw-enter-translate-x: -0.5rem;
}.data-\[side\=top\]\:slide-in-from-bottom-2[data-side="top"] {
  --tw-enter-translate-y: 0.5rem;
}.data-\[state\=closed\]\:slide-out-to-left-1\/2[data-state="closed"] {
  --tw-exit-translate-x: -50%;
}.data-\[state\=closed\]\:slide-out-to-right-full[data-state="closed"] {
  --tw-exit-translate-x: 100%;
}.data-\[state\=closed\]\:slide-out-to-top-\[48\%\][data-state="closed"] {
  --tw-exit-translate-y: -48%;
}.data-\[state\=open\]\:slide-in-from-left-1\/2[data-state="open"] {
  --tw-enter-translate-x: -50%;
}.data-\[state\=open\]\:slide-in-from-top-\[48\%\][data-state="open"] {
  --tw-enter-translate-y: -48%;
}.data-\[state\=open\]\:slide-in-from-top-full[data-state="open"] {
  --tw-enter-translate-y: -100%;
}.group\/day[data-focused="true"] .group-data-\[focused\=true\]\/day\:relative {
  position: relative;
}.group\/day[data-focused="true"] .group-data-\[focused\=true\]\/day\:z-10 {
  z-index: 10;
}.group\/day[data-focused="true"] .group-data-\[focused\=true\]\/day\:border-ring {
  border-color: hsl(var(--ring));
}.group\/day[data-focused="true"] .group-data-\[focused\=true\]\/day\:ring-\[3px\] {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}.group\/day[data-focused="true"] .group-data-\[focused\=true\]\/day\:ring-ring\/50 {
  --tw-ring-color: hsl(var(--ring) / 0.5);
}@supports ((-webkit-backdrop-filter: var(--tw)) or (backdrop-filter: var(--tw))) {

  .supports-\[backdrop-filter\]\:bg-background\/60 {
    background-color: hsl(var(--background) / 0.6);
  }
}.dark\:border-destructive:is(.dark *) {
  border-color: hsl(var(--destructive));
}@media (min-width: 640px) {

  .sm\:bottom-0 {
    bottom: 0px;
  }

  .sm\:right-0 {
    left: 0px;
  }

  .sm\:top-auto {
    top: auto;
  }

  .sm\:inline {
    display: inline;
  }

  .sm\:inline-flex {
    display: inline-flex;
  }

  .sm\:hidden {
    display: none;
  }

  .sm\:flex-row {
    flex-direction: row;
  }

  .sm\:flex-col {
    flex-direction: column;
  }

  .sm\:justify-end {
    justify-content: flex-end;
  }

  .sm\:space-x-2 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-left: calc(0.5rem * var(--tw-space-x-reverse));
    margin-right: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .sm\:rounded-lg {
    border-radius: var(--radius);
  }

  .sm\:text-left {
    text-align: right;
  }

  .data-\[state\=open\]\:sm\:slide-in-from-bottom-full[data-state="open"] {
    --tw-enter-translate-y: 100%;
  }
}@media (min-width: 768px) {

  .md\:max-w-\[420px\] {
    max-width: 420px;
  }

  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .md\:flex-row {
    flex-direction: row;
  }

  .md\:text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }
}@media (min-width: 1024px) {

  .lg\:col-span-2 {
    grid-column: span 2 / span 2;
  }

  .lg\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}.\[\&\:first-child\[data-selected\=true\]_button\]\:rounded-l-md:first-child[data-selected=true] button {
  border-top-right-radius: calc(var(--radius) - 2px);
  border-bottom-right-radius: calc(var(--radius) - 2px);
}.\[\&\:has\(\[role\=checkbox\]\)\]\:pr-0:has([role=checkbox]) {
  padding-left: 0px;
}.\[\&\:last-child\[data-selected\=true\]_button\]\:rounded-r-md:last-child[data-selected=true] button {
  border-top-left-radius: calc(var(--radius) - 2px);
  border-bottom-left-radius: calc(var(--radius) - 2px);
}.\[\&\>span\]\:line-clamp-1>span {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}.\[\&\>span\]\:text-xs>span {
  font-size: 0.75rem;
  line-height: 1rem;
}.\[\&\>span\]\:opacity-70>span {
  opacity: 0.7;
}.\[\&\>svg\+div\]\:translate-y-\[-3px\]>svg+div {
  --tw-translate-y: -3px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}.\[\&\>svg\]\:absolute>svg {
  position: absolute;
}.\[\&\>svg\]\:left-4>svg {
  right: 1rem;
}.\[\&\>svg\]\:top-4>svg {
  top: 1rem;
}.\[\&\>svg\]\:size-3\.5>svg {
  width: 0.875rem;
  height: 0.875rem;
}.\[\&\>svg\]\:text-destructive>svg {
  color: hsl(var(--destructive));
}.\[\&\>svg\]\:text-foreground>svg {
  color: hsl(var(--foreground));
}.\[\&\>svg\]\:text-muted-foreground>svg {
  color: hsl(var(--muted-foreground));
}.\[\&\>svg\~\*\]\:pl-7>svg~* {
  padding-right: 1.75rem;
}.\[\&\>tr\]\:last\:border-b-0:last-child>tr {
  border-bottom-width: 0px;
}.\[\&_p\]\:leading-relaxed p {
  line-height: 1.625;
}.\[\&_svg\]\:pointer-events-none svg {
  pointer-events: none;
}.\[\&_svg\]\:size-4 svg {
  width: 1rem;
  height: 1rem;
}.\[\&_svg\]\:shrink-0 svg {
  flex-shrink: 0;
}.\[\&_tr\:last-child\]\:border-0 tr:last-child {
  border-width: 0px;
}.\[\&_tr\]\:border-b tr {
  border-bottom-width: 1px;
}[data-slot=card-content] .\[\[data-slot\=card-content\]_\&\]\:bg-transparent {
  background-color: transparent;
}[data-slot=popover-content] .\[\[data-slot\=popover-content\]_\&\]\:bg-transparent {
  background-color: transparent;
}
