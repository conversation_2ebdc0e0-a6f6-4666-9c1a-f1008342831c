<?php

// Simple autoloader for BetterCampaign plugin
spl_autoload_register(function ($class) {
    // Only handle BetterCampaign namespace
    if (strpos($class, 'BetterCampaign\\') !== 0) {
        return;
    }

    // Convert namespace to file path
    $class = str_replace('BetterCampaign\\', '', $class);
    $class = str_replace('\\', DIRECTORY_SEPARATOR, $class);
    
    $file = BC_INCLUDES . $class . '.php';
    
    if (file_exists($file)) {
        require_once $file;
    }
});

// Simple Settings class stub to avoid dependency issues
if (!class_exists('UsabilityDynamics\Settings')) {
    class UsabilityDynamicsSettings {
        protected $settings = [];
        protected $key = '';
        
        public function __construct($args = []) {
            $this->key = isset($args['key']) ? $args['key'] : 'bettercampaign';
            $this->settings = get_option($this->key, []);
        }
        
        public function get($path = '', $default = null) {
            if (empty($path)) {
                return $this->settings;
            }
            
            $keys = explode('.', $path);
            $value = $this->settings;
            
            foreach ($keys as $key) {
                if (isset($value[$key])) {
                    $value = $value[$key];
                } else {
                    return $default;
                }
            }
            
            return $value;
        }
        
        public function set($path, $value) {
            if (empty($path)) {
                $this->settings = $value;
            } else {
                $keys = explode('.', $path);
                $current = &$this->settings;
                
                foreach ($keys as $key) {
                    if (!isset($current[$key]) || !is_array($current[$key])) {
                        $current[$key] = [];
                    }
                    $current = &$current[$key];
                }
                
                $current = $value;
            }
            
            return update_option($this->key, $this->settings);
        }
    }
}

// Make the stub available in the correct namespace
if (!class_exists('UsabilityDynamics\Settings')) {
    class_alias('UsabilityDynamicsSettings', 'UsabilityDynamics\Settings');
}
