<?php
/**
 * Plugin Name:       BetterCampaign
 * Plugin URI:        https://bettercampaign.com
 * Description:       BetterCampaign is a powerful email marketing tool that helps you create, send, and track your email campaigns.
 * Version:           1.0.0
 * Author:            MD Shakibul Islam
 * Author URI:        https://github.com/shuvo7670
 * License:           GPL-3.0+
 * License URI:       https://www.gnu.org/licenses/gpl-3.0.html
 * Text Domain:       bettercampaign
 * Domain Path:       /languages
 *
 * @package           BetterCampaign
 * @link              https://github.com/shuvo7670
 * @since             1.0.0
 */

/**
 * If this file is called directly, abort.
 */
if ( ! defined( 'WPINC' ) ) {
	die;
}

/**
 * Defines CONSTANTS for Whole plugins.
 */
define( 'BC_FILE', __FILE__ );
define( 'BC_VERSION', '1.0.0' );
define( 'BC_URL', plugins_url( '/', __FILE__ ) );
define( 'BC_PATH', plugin_dir_path( __FILE__ ) );
define( 'BC_BASENAME', plugin_basename( __FILE__ ) );

define( 'BC_ASSETS', BC_URL . 'assets/' );
define( 'BC_ASSETS_PATH', BC_PATH . 'assets/' );
define( 'BC_DEV_ASSETS', BC_URL . 'build/' );
define( 'BC_DEV_ASSETS_PATH', BC_PATH . 'build/' );
define( 'BC_INCLUDES', BC_PATH . 'includes/' );

define( 'BC_TEXTDOMAIN', 'bettercampaign' );
define( 'BC_PLUGIN_URL', 'https://bettercampaign.com' );
define( 'BC_ADMIN_URL', BC_ASSETS . 'admin/' );
define( 'BC_PUBLIC_URL', BC_ASSETS . 'public/' );

// Development mode constant
if ( ! defined( 'SC_DEV' ) ) {
    define( 'SC_DEV', getenv('SC_DEV') === '1' );
}

/**
 * The Core Engine of the Plugin
 */
if ( ! class_exists( '\BetterCampaign\BetterCampaign' ) ) {
    require_once BC_PATH . 'vendor/autoload.php';
    require_once BC_PATH . 'includes/Admin/migrations.php';

    function activate_bettercampaign() {
        if ( function_exists( 'bettercampaign_run_migrations' ) ) {
            bettercampaign_run_migrations();
        }
        \BetterCampaign\BetterCampaign::get_instance()->activator();
    }

    /**
     * Plugin Activator
     */
    register_activation_hook( BC_FILE, 'activate_bettercampaign' );
    \BetterCampaign\BetterCampaign::get_instance();
}