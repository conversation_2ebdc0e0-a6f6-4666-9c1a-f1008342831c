=== NotificationX Pro ===
Contributors: wpdevteam, re_enter_rupok, Asif2B<PERSON>, priyomukul, alimuz<PERSON><PERSON><PERSON>, shuvo247, fuadragib
Donate link: http://notificationx.com
Tags: sales notification, fomo, social proof, woocommerce notification, notification bar, woocommerce sales, sales popup, popups, boost sales, elementor, email subscription, recent sales popup, woo popups, woocommerce review, wordpress marketing, marketing, evergreen notice, increase engagement, woocommerce, envato, freemius, givewp, learndash, bottom bar, top bar, marketing plugin, alert, email marketing, conversion, gamification
Requires at least: 5.0
Tested up to: 6.7
Requires PHP: 5.6
Stable tag: 3.0.4
License: GPL-3.0-or-later
License URI: https://www.gnu.org/licenses/gpl-3.0.html

Most advanced social proof popups and countdown notifications for WordPress.

== Description ==

Want to build instant credibility for your business and boost your conversion rate right away? 97% of your total website visitors don’t buy the product due to the lack of trust and credibility. Best FOMO, Social Proof, Sales Popup & WooCommerce Notification Bar With Elementor Support.

== Description ==

= Best FOMO, Social Proof, Sales Popup & WooCommerce Notification Bar With Elementor Support =

Want to build instant credibility for your business and boost your conversion rate right away? **97%** of your total website visitors don’t buy the product due to the lack of trust and credibility. Get instant success with **WooCommerce Sales Popup Notification**!

https://www.youtube.com/watch?v=E-QR1QdRUrg

## BUILD INSTANT CREDIBILITY USING SOCIAL PROOF NOTIFICATION ##

People don’t like being left behind and they love to follow the trends. If your visitors know what other people are buying, reading, commenting then it’s more likely that they will get the confidence instantly and they will want to buy it as well because of FOMO effect.

#### **What is 'FOMO'?** ####
FOMO means "**F**ear **O**f **M**issing **O**ut"! It’s a popular marketing technique to make your visitors eager to buy from your site leveraging the sense of urgency.

## 🌟 USE FOMO TO INCREASE YOUR LEADS, SALES & ENGAGEMENTS ##

[NotificationX](https://notificationx.com) helps you to grab the attention of your website visitors and gain their trust instantly by showing real-time sales and engagement notifications. It creates urgency among your visitors in order to purchase your products right now. No one wants to be left out, after all.

https://www.youtube.com/watch?v=J-1m27V4pEE

**Sales Notification & Popup Solution for WooCommerce, EDD, Freemius, Envato & More.**


## 📌 BEST NOTIFICATION BAR PLUGIN FOR WORDPRESS ##

Let the visitors know about your special offers, deals, announcement, etc. with a call to action. **NotificationX** brings the [best notification bar](https://notificationx.com/features/) solution for WordPress, and it's absolutely free. You could turn on the end, start time, you will get detail analytics how your notification bar performed as well.


https://www.youtube.com/watch?v=l7s9FXgzbEM


### 🔝 FEATURES: ###

- Super simple interface, ready themes, and advanced design options to easily set up notifications alerts
- Quick Builder Setup to improve user experience
- Supports WooCommerce, Easy Digital Download, Freemius, Envato
- Customization option to the display across the site, on specific posts, pages or exclude from the specific location
- Advanced **15+ integrations** to power up your social proof marketing strategies
- Connect NotificationX with **Zapier** to build absolutely anything [PRO]
- Global Queue Management to get full control over your popup notifications [PRO]
- Advanced Role Management to decide who can create or edit notifications & check Analytics report [PRO]
- Check the results from **Analytics tool** [PRO] and see how many times a certain notification popup has been viewed, clicked or even its Click-through Rate
- Option to deactivate unnecessary integrations to keep the site lightweight
- Review Popup will increase your engagement and credibility.


## 🚀 BOOST CONVERSIONS WITH DIFFERENT NOTIFICATIONS ##

1. **Sales Notifications:** With NotificationX, you can easily design attention-grabbing Sales Notifications to increase your sales rate. If you have WooCommerce or Easy Digital Downloads installed, you can display the recent purchases activity by other customers to influence the website visitors. [More source in Pro: LearnDash, Envato, Themeforest, CodeCanyon)]


2. **WP Comments:** Lets you show the recent comments made by users on your WordPress posts as a notification alert. This is helpful to gain immediate attention from your visitors to check out the specified posts.


3. **WordPress/Woocommerce Reviews:** Showcase your Plugin/Theme reviews from WordPress.org & build credibility. [Source: WordPress, Freemius, WooCommerce & also ReviewX]

4. **WordPress Download Count:** Display how many people have downloaded your product from WordPress.org recently. [Source: WooCommerce, EDD, Freemius, Envato]


5. **Notification Bar:** With "Notification Bar", you can easily display nice looking special discount offer or important notice on your website. Besides, you can even use a countdown timer to create a sense of urgency among your customers or users to hurry up.

6. **Contact Form Notifications:** Display your favourite contact form popup notifications on websites and create urgency among your site visitors to get engaged with you. [Source: Contact Form 7, Ninja Forms, WPForms, Gravity Forms (PRO)]

7. **Email Subscriptions [PRO]:** MailChimp notification lets you showcase the users who have subscribed to your MailChimp list as a popup on your website. [Source: MailChimp, ConvertKit]


8. **CUSTOM NOTIFICATIONS [PRO]:** You can create advanced and customized notifications just the way you want using these options.


## 🔥 POWER UP WITH ADVANCED INTEGRATIONS ##

1. **[WooCommerce](https://notificationx.com/integrations/woocommerce/):** Showcase your product sales alert and customer activities live on the website with an automated process to increase your eCommerce site conversions rates. **From V 1.7.0 we now support WooCommerce Product reviews as well.**

2. **[Easy Digital Downloads](https://notificationx.com/integrations/easy-digital-downloads/):** Display your clients’ live sales update on the website to build credibility and gather more traffic to boost business growth.

3. **[Freemius [PRO]](https://notificationx.com/integrations/freemius/):** Lets you showcase your customers' real-time sales and review activities through live notification pop up from the Freemius account to your website.

4. **[GiveWP](https://notificationx.com/integrations/givewp/):** Showcase your donor live notification popup on the website to influence others to make more donations.

5. **[Zapier [PRO]](https://notificationx.com/integrations/zapier/):** Integrate Zapier with NotificationX to pull data from supported apps and display custom popups of users real-time notification to convert your visitors into your permanent client.

6. **[MailChimp [PRO]](https://notificationx.com/integrations/mailchimp/):** Create urgency among your visitors to signup your email list to get updates and increase your overall engagement rates.

7. **[ConvertKit [PRO]](https://notificationx.com/integrations/convertkit/):** Enhance your email marketing strategy by showcasing real-time signup activities from ConvertKit and boost site engagement rate instantly.

8. **[LearnDash [PRO]](https://notificationx.com/integrations/learndash/):** Showcase your course enrollment activities to gain credibility and influence others to signup for your next online course.

9. **[Envato [PRO]](https://notificationx.com/integrations/envato/):** Display your download or sales notification from Envato (Themeforest/CodeCanyon) and increase conversion rates.

10. **[Google Analytics[PRO]](https://notificationx.com/integrations/google-analytics/):** Display Google Analytics insights and visitor counts with more marketing insight to engage your potential buyers.

11. **[ReviewX](https://reviewx.io/):** Display beautiful reviews from ReviewX. Currently it supports WooCommerce, and you could showcase your multi-criteria reviews.

12. **[Elementor](https://wordpress.org/plugins/elementor/):** With the introduction of Elementor integration, you can create & design stunning Notification Bars quite easily. You can choose from various pre-set layouts and customize the layout any way you want. Also can create a GDPR Cookie consent bar[PRO], and use an evergreen timer [PRO] to make your notification bar status refreshing all the time for visitors.



## 🏆 FEATURED IN TOP PUBLICATIONS ##

https://www.youtube.com/watch?v=75vntaHp9Y4

[WPMayor](https://wpmayor.com/notificationx-review-add-fomo-inducing-notifications-to-wordpress/):
> 'NotificationX is a freemium WordPress plugin that helps bring those two marketing strategies to your WordPress site, with eye-catching notification alerts for new sales, comments, reviews, and more. The designs are modern and nice-looking right out of the box, and I like how it supports different notification types, which makes it work for an eCommerce store, blog, WordPress theme/plugin shop, and more.'

[BobWP](https://bobwp.com/notification-display-woocommerce-notificationx-plugin/):
> 'The power of NotificationX lies in its integrations. The integration with WordPress.org makes it slick to pull in download numbers and reviews. This is a perfect option if you are selling freemium plugins and themes. The plugin is intuitive and easy to set up. With the various notifications, you can get creative and go beyond product sales with your WooCommerce store.'

**Thanks:** This project is inspired by WPFomify, Puneet & his team did amazing work, and we are very thankful to them. They are one of the first to do Fomo in WordPress right way, we are passionately taking it to whole different level and loved to see we are inspiring them as well, thats the beauty of OpenSource!


### Documentation and Support ###

- For documentation and tutorials go to our [Documentation](https://notificationx.com/docs)
- If you have any more questions, visit our support on the [Plugin’s Forum](https://wordpress.org/support/plugin/notificationx/)
- For more information about features, FAQs and documentation, check out our website at [NotificationX](https://notificationx.com)

### Loving NotificationX? ###

- Join our [Facebook Group](https://www.facebook.com/groups/NotificationX.Community/)
- Learn from our tutorials on [Youtube Channel](https://wpdeveloper.com/go/youtube-channel)
- Or [rate us](https://notificationx.com/go/review-nx) on WordPress

### 🎁 GET FREEBIES FOR YOUR WORDPRESS SITE ###

NotificationX is backed by WPDeveloper, a dedicated team trusted by more than 3000,000 users. Consider checking out our other WordPress solutions & boost your WordPress website:

🔝 [Essential Addons For Elementor](https://wordpress.org/plugins/essential-addons-for-elementor-lite/): Most popular Elementor extensions with 2 Million+ active users in the WordPress repository

📄 [EmbedPress](https://wordpress.org/plugins/embedpress/): EmbedPress lets you embed videos, images, posts, audio, maps and upload PDF, DOC, PPT & all other types of content into your WordPress site with one-click and showcase it beautifully for the visitors.

☁ [Templately](https://wordpress.org/plugins/templately/): Ultimate Template clouds with 1000+ ready templates for Elementor & Gutenberg along with FREE cloud collaboration with your team

📚 [BetterDocs](https://wordpress.org/plugins/betterdocs/): Best Documentation & Knowledge Base Plugin for WordPress reduce manual support tickets & improve user experience.

⏰ [SchedulePress](https://wordpress.org/plugins/wp-scheduled-posts/): Advanced editorial calendar & complete solution for WordPress Post Scheduling, social sharing, missed scheduled alerts and more.

⭐ [ReviewX](https://wordpress.org/plugins/reviewx/): WooCommerce Product review plugin that allows users to submit product reviews with multiple criteria, photos, videos, and more.

💼 [Easy.Jobs](https://wordpress.org/plugins/easyjobs/): Smart and easy recruitment and talent sourcing solution for hiring remotely with AI-powered screening system, question sets, remote interviews, branded career pages and much more.

🚫 [Disable Comments](https://wordpress.org/plugins/disable-comments/):  Gives you global control over all comment-related settings for your WordPress website and prevents spam. Disable comments on pages, post types, media files and block commenting via REST-API or XML RPC.

⚡ [Flexia](http://wordpress.org/plugins/flexia): Most lightweight, customizable & multi purpose theme for WordPress.



== Installation ==

1. Upload `notificationx-pro` to the `/wp-content/plugins/` directory
2. Activate the plugin through the 'Plugins' menu in WordPress



== Frequently Asked Questions ==

= Do I need coding skills to use NotificationX? =

Not at all! NotificationX has simple and super user-friendly setup wizard to help you configure the notification without any need of code intervention.

= Can I use NotificationX on non eCommerce websites?

Yes, NotificationX can be used to display comment activity, promotional announcements and more which can be use on any website.

= Do I need to edit my current WordPress theme?

No, NotificationX works seamlessly with any WordPress themes. You just need to install the plugin and you will get the direction for the rest.

= Is our website’s data legally safe with NotificationX?

Yes. Your data is legally safe and we guarantee to not make use of your data under any circumstances.

== Screenshots ==


== Changelog ==

= 3.0.4 - 04/06/2025 =
Fixed: Gravity Forms previous entries were not appearing in frontend.
Few minor bug fixes and improvements.

= 3.0.3 - 07/05/2025 =
Added: Cookie Scanner in GDPR.
Fixed: Text domain issue.
Few minor bug fixes and improvements

= 3.0.2 - 16/03/2025 =
Fixed: Expired invitation link for Zapier integration.
Improved: Notification filter based on Days, Hours, and Minutes.
Few minor bug fixes and improvements.

= 3.0.1 - 10/12/2024 =
Improved: Freemius notifications preview.
Few minor bug fixes and improvements

= 3.0.0 - 14/11/2024 =
Added: Mobile responsive design layout.
Added: CSV import limit and image sizing for custom notifications
Fixed: Username display issue in Freemius notifications
Few minor bug fixes and improvements

= 2.9.6 - 05/09/2024 =
Added: Display Notification based on Category
Few minor bug fixes and improvements

= 2.9.5 - 27/8/2024 =
Improved: Licensing mechanism revamped
Few minor bug fixes & improvements

= 2.9.4 - 16/7/2024 =
Added: Bulk Time updating option for Custom Notification.
Added: Custom CSS field on Advanced Design.
Few minor bug fixes and improvements.

= 2.9.3 - 01/7/2024 =
Added: Custom Notification import using CSV file.
Added: Bulk Edit and Delete options for Custom Notifications
Fixed: Growth Alert preview on Mobile devices.
Few minor bug fixes and improvements

= 2.9.2 - 25/4/2024 =
Fixed: License Activation issue with JWT Auth plugin
Improved: Page Analytics Notification.
Few minor bug fixes and improvements

= 2.9.1 - 24/3/2024 =
Fixed: Compatibility issues with PHP 8.3
Few minor bug fixes and improvements

= 2.9.0 - 10/3/2024 =
- Added: ActiveCampaign Integration.
- Few minor bug fixes and improvements

= 2.8.11 - 25/2/2024 =
- Improved: Advanced Templating for Donation type Notification.
- Improved: Added an option to choose Google Review language.
- Improved: Youtube Channel notification using handler.
- Few minor bug fixes and improvements

= 2.8.10 - 12/02/2024 =
- Added: Animation for displaying Notifications.
- Added: Google Analytics notification for individual page.
- Fixed: Data mismatch of Google analytics
- Few minor bug fixes and improvements.

= 2.8.9 - 17/01/2024 =
- Added: Discount Alert Notification type.
- Few minor bug fixes and improvements.

= 2.8.8 - 18/12/2023 =
- Improved: WooCommerce Integration.
- Few minor bug fixes and improvements.

= 2.8.7 - 23/11/2023 =
- Improved: Security enhancement.
- Few minor bug fixes and improvements.

= 2.8.6 - 20/11/2023 =
- Added: SureCart Integration
- Few minor bug fixes and improvements.

= 2.8.5 - 29/10/2023 =
- Fixed: Elementor Form wasn't showing on Content section.
- Fixed: "Undefined Array" error for Google Analytics Notification.
- Added: Product plan and price on Freemius Notification.
- Few minor bug fixes and improvements.

= 2.8.4 - 09/10/2023 =
- Fixed: Fatal error in Comment type notification.
- Fixed: Close button wasn't working in Cross domain notices.
- Improvement: Cross Domain notification asset loading.
- Fixed: Compatibility issues with PHP 8.2
- Few minor bug fixes and improvements.

= 2.8.3 - 11/09/2023 =
- Added: YouTube integration for both Channel & Videos.
- Improved: Added option for Site Title & Fav icon in Flashing Tab.
- Few minor bug fixes and improvements.

= 2.8.2 - 08/08/2023 =
- Fixed: Sales Notification isn’t working for a specific Learndash course.
- Few minor bug fixes and improvements.

= 2.8.1 - 10/07/2023 =
- Added: New WooCommerce Sales Templates.
- Fixed: Compatibility issues with PHP 8.1.
- Few minor bug fixes and improvements.

= 2.8.0 - 19/06/2023 =
- Added: Flashing Tab notification.
- Few minor bug fixes & improvements

= 2.7.3 - 14/05/2023 =
- Added: Growth alert in preview mode.
- Added: Addons support in freemius notifications.
- Improved: Escaped license error message for Security Enhancement
- Improved: WooCommerce/EDD product filtering to ensure speed and prevent website crashes.
- Few minor bug fixes & improvements

= 2.7.2 - 16/03/2023 =
- Added: Notification Preview feature.
- Few minor bug fixes and improvements

= 2.7.1 - 12/02/2023 =
- Added: Custom Link option added in Page Analytics & Email Subscription.
- Few minor bug fixes and improvements

= 2.7.0 - 21/12/2022 =
- Added: Google Review Notification.
- Improvement: Added random order support in global queue management.
- Improvement: Added bundle product support in Freemius notification.
- Few minor bug fixes and improvements.

= 2.6.0 - 13/09/2022 =
- Added: Elementor Form Integration.
- Fixed: Google Analytics conflict with other plugins.
- Few minor bug fixes and improvements

= 2.5.5 - 02/08/2022 =
- Fixed: Custom Notification wasn’t working on some specific template.
- Fixed: Conflict with google library.
- Fixed: Zapier notification issues.
- Improved: Cross domain functionalities.
- Few minor bug fixes and improvements

= 2.5.4 - 13/06/2022 =
- Fixed: Random order wasn’t working in Custom Notification.
- Fixed: Display The Last notification limit in Custom Notification.
- Few minor bug fixes and improvements

= 2.5.3 - 18/04/2022 =
- Added: Tutor LMS integration on Growth Alert Notification.
- Added: LearnDash integration on Growth Alert Notification.
- Update: Cross Domain Notice.
- Few minor bug fixes and improvements

= 2.5.2 - 22/03/2022 =
- Fixed: Fixed update issue.
- Few minor bug fixes and improvements

= 2.5.1 - 14/03/2022 =
- Few minor bug fixes and improvements

= 2.5.0 - 14/03/2022 =
- Added: Display stock level notice in cart, single product & shop archive page.
- Few minor bug fixes and improvements

= 2.4.2 - 14/02/2022 =
- Added: Inline notification for Gutenberg.
- Few minor bug fixes and improvements

= 2.4.1 - 25/01/2022 =
- Fixed: License activation issue on PHP 8.0.
- Few minor bug fixes and improvements

= 2.4.0 - 11/01/2022 =
- Added: Inline Notification on Shop Archive & Single product page for WooCommerce and EDD.
- Added: Inline notification Shortcode.
- Improvement: Google Analytics & Map.
- Few minor bug fixes and improvements

= 2.3.1 - 15/12/2021 =
- Added: Option for selecting WooCommerce order status
- Added: Category and Specific product selection for Review Notification.
- Few minor bug fixes and improvements

= 2.3.0 - 05/12/2021 =
- Added: Google dynamic map for Geo Notification.
- Fixed: Notification time translation issue.
- Few minor bug fix and improvements.

= 2.2.1 - 16/11/2021 =
- Fixed: Advance Template for Google Analytics
- Fixed: Advance Template’s sales count.
- Few minor bug fix and improvements.

= 2.2.0 - 26/10/2021 =
- Added: Cross Domain Notice
- Fixed: Duplicate Notification for Zapier
- Few minor bug fix and improvements.

= 2.1.1 - 12/10/2021 =
- Fixed: MailChimp Notification with New Data
- Fixed: Notification Missing Map Image
- Few minor bug fix and improvements.

= 2.1.0 - 27/09/2021 =
- Added: WPML Compatibility
- Fixed: PHP Fatal Error when creating Google Analytics
- Few minor bug fix & improvements

= 2.0.3 - 16/09/2021 =

- Fixed: Date picker not working properly for Custom Notifications
- Fixed: MailChimp alerts not fetching the new data
- Few minor bug fix & improvements


= 2.0.2 - 31/08/2021 =

- Fixed: Role Management not working properly
- Fixed: Google Analytics Alert template issue
- Updated: Google library for Analytics
- Few minor bug fix and improvements

= 2.0.1 - 17/08/2021 =

- Fixed: Sound alert not working properly
- Fixed: "Hide on Selected Post IDs" not working properly
- Few minor bug fix and improvements

= 2.0.0 - 12/08/2021 =

- Revamped : Rebuilt entire plugin with ReactJS
- Revamped : Code Structure for better performance
- Redesigned : UI/UX Structure
- Fixed: Analytics Counting and Updating for Views & Clicks
- Fixed: Global Queue not working properly for Custom Notifications
- Added: Random order feature on most of the sources
- Few minor bug fix and improvements

= 1.5.5 - 01/06/2021 =

- Fixed: Custom notifications not working when date is not set
- Fixed: MailChimp Connection issue
- Few minor bug fix and improvements

= 1.5.4 - 15/03/2021 =

- Added: Random Order option for Notification (WooCommerce)
- Fixed: Uncaught JSError issue
- Few minor bug fix and improvements

= 1.5.3 - 23/12/2020 =

- Added: Google Analytics 4 Support
- Fixed: Google Analytics App verification issue
- Fixed: Gravity Form Alerts | Missing Regenerate button
- Fixed: Missing 'Select Sound' Option for few NotificationX types
- Few minor bug fix and improvements


= 1.5.2 - 30/09/2020 =

Fixed: Shortcode not working with Elementor Page Builder
Few minor bugfix and improvements

= 1.5.1 - 19/08/2020 =

- Fixed: WooCommerce 4.4.0 compatibility Issue.
- Few minor bugfix and improvements


= 1.5.0 - 29/07/2020 =

- Added: Evergreen Countdown for Notification Bar
- Added: Centralized Queue Management for notifications
- Few minor bugfix and improvements


= 1.4.14 - 25/06/2020 =

- Fixed: Critical Error with NxPro_Google_Service_Exception class not found

= 1.4.13 - 24/06/2020 =

- Fixed: Critical Error with NxPro_Google_Client class not defined
- Few minor bugfix and improvements

= 1.4.12 - 07/06/2020 =

- Fixed: Zapier SSL Connection issue
- Improvement: Added more field keys for Zapier


= 1.4.11 - 24/05/2020 =

- Added: Combined data for Analytics
- Added: Custom Field support for Zapier

= 1.4.10 - 04/05/2020 =

- Enhanced: Custom Post Type support in Display Control
- Fix: Custom Post ID dependency fix
- Fix: Freemius issue fix
- Fix: Analytics issue fix
- Fix: Minor bug fix

= 1.4.9 - 08/03/2020 =

- Few Minor Bugfix and improvements

= 1.4.8 - 25/02/2020 =

- Enhanced: Email Reporting Frequency
- Enhanced: Email Subject Editing Option

= 1.4.7 - 06/02/2020 =

- Fixed: Source Image URL Issue

= 1.4.6 - 03/02/2020 =

- Improved: Cron settings issue
- Fixed: Sales count issue
- Enhancement: Analytics and Reporting
- Few Minor Bugfix and improvements

= 1.4.5 - 12/12/2019 =

- Added: Gravity Forms integration
- Enhancement: Option to enable/disable Analytics feature

= 1.4.4 - 12/12/2019 =

- Fix: Analytics issue
- Few Minor Bugfix and improvements

= 1.4.3 - 06/11/2019 =

- Fix: Content template issue for WooCommerce notification
- Few Minor Bugfix and improvements

= 1.4.2 - 28/10/2019 =

- Fixed: Google Analytics PHP 5.4 compatibility
- Few Minor Bugfix and improvements

= 1.4.1 - 24/10/2019 =

- Fix: Sales Notification Sound Issue
- UI/UX Enhancements: Remove Analytics Header from Edit & Add new notification screens
- UI/UX Enhancements: Added Analytics Header for Settings and Analytics Screen
- Fix: Google Analytics Sound Issue
- UI/UX Fix: Error Message for Realtime Notice
- UX Enhancements: Documentation Links Added
- Few Minor Bugfix and improvements

= 1.4.0 - 23/10/2019 =

- Integration: Google Analytics for Total Site View & Real Time Total Site View
- UI Enhancements: New NX Analytics Header
- Few Minor Bugfix and improvements

= 1.3.1 - 03/10/2019 =

- Enhanced: Tutor LMS Integration - Show Notification By Course
- Fixed: Shortcode CSS Line Height Issue
- Few Minor Bugfix and improvements

= 1.3.0 - 24/09/2019 =

- Fixed: Template Toggling Issue for Sales Count Themes
- Enhanced: Enabled Sales Count themes for all type of content
- Fixed: Time Field Issue for Custom Notification
- Fixed: ConvertKit Settings Issue
- Fixed: UTM Control Issue
- Fixed: Maps Templates default text from removed
- Enhanced: optional field added for Maps Template to change from Free
- Few Minor fix and compatibility issue fixed

= 1.2.10 - 20/09/2019 =

- Added : Modules Condition
- Fixed : Template Data Persisting Issue
- Fixed : Maps Template Advanced Issue
- Few Minor fix and compatibility issue fixed

= 1.2.9 - 17/09/2019 =

- Added : Independent Custom Notification
- Fixed : Reponsive CSS - for Right Notifications
- Fixed : Theme Five Design Issue
- Fixed : Sales Count Theme Issu
- Few Minor fix and compatibility issue fixed

= 1.2.8 - 09/09/2019 =

- Fixed : Notification Bar Stats
- Fixed : Notification responsiveness issue
- Few Minor fix and compatibility issue fixed

= 1.2.7 - 08/09/2019 =

- Fixed : Custom Notification and maps location issue
- Fixed : Notification Bar Analytics
- Improved : Few theme design
- Few Minor fix and compatibility issue fixed

= 1.2.6 - 06/09/2019 =

- Added : Template auto changing feature
- Fixed : Non Numeric issue
- Few Minor fix and compatibility issue fixed

= 1.2.5 - 06/09/2019 =

- Improved : Advanced Design options
- Few Minor fix and compatibility issue fixed

= 1.2.4 - 05/09/2019 =

- Fixed : Sound issue
- Added : Shortcodes styling
- Added : Sales count feaute
- Few Minor fix and compatibility issue fixed

= 1.2.3 - 02/09/2019 =

- Added : More sound option
- Added : Shortcodes
- Few Minor fix and compatibility issue fixed

= 1.2.2 - 02/09/2019 =

- Fixed : Lite version auto installation issue
- Improved : License activation link to proper tab

= 1.2.1 - 26/08/2019 =

- Fixed : Templating Custom Field issue
- Improved : Envato Sales Notification
- Few minor bug fix and improvements

= 1.2.0 - 22/08/2019 =

- Added : Envato Sales Notifications
- Added : LearnDash Sales Notifications
- Added : Content Length options for some of Comments And Reviews themes.
- Few minor bug fix and improvements

= 1.1.3 - 20/08/2019 =

- Added : Zapier setup instructions metabox
- Improved : Themes Image issue fix and optimized
- Improved : Rel Nofollow added in all external links
- Few minor bug fix and improvements

= 1.1.2 - 08/08/2019 =

- Added : New Types Design and Zapier added in Reviews and Sales
- Added : Custom Page ID display option added
- Improved : Zapier integraion
- Improved : Comment and Review templates
- Few minor bug fix and improvements

= 1.1.1 - 01/08/2019 =

- Added : Modular controls for integrations
- Improved : New admin screen for better UX
- Improved : Analytics
- Few minor bug fix and improvements

= 1.1.0 - 29/07/2019 =

- Added : Analytics for all Notifications
- Improved : Cron job minimized to improve performance
- Few minor bug fix and improvements

= 1.0.2 - 21/07/2019 =

- Fixed : Freemius issue
- Added : Analytics
- Few minor bug fix and improvements

= 1.0.1 - 16/07/2019 =

- Fixed : Mailchimp issue
- Fixed : ConvertKit and Comments theme-five dependency
- Sound settings reorganized
- Few minor bug fix and improvements


= 1.0.0 - 09/07/2019 =

* initial release.


== Upgrade Notice ==
[Minor Update] Bugfix & improvements.

