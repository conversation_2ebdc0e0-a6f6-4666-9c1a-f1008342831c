<?php

// autoload_classmap.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'ArithmeticError' => $baseDir . '/includes/Extensions/Google/lib/Symfony/Polyfill/Php70/Resources/stubs/ArithmeticError.php',
    'AssertionError' => $baseDir . '/includes/Extensions/Google/lib/Symfony/Polyfill/Php70/Resources/stubs/AssertionError.php',
    'Composer\\InstalledVersions' => $vendorDir . '/composer/InstalledVersions.php',
    'DivisionByZeroError' => $baseDir . '/includes/Extensions/Google/lib/Symfony/Polyfill/Php70/Resources/stubs/DivisionByZeroError.php',
    'Error' => $baseDir . '/includes/Extensions/Google/lib/Symfony/Polyfill/Php70/Resources/stubs/Error.php',
    'Normalizer' => $baseDir . '/includes/Extensions/Google/lib/Symfony/Polyfill/Intl/Normalizer/Resources/stubs/Normalizer.php',
    'NotificationXPro\\Admin\\Admin' => $baseDir . '/includes/Admin/Admin.php',
    'NotificationXPro\\Admin\\Cron' => $baseDir . '/includes/Admin/Cron.php',
    'NotificationXPro\\Admin\\Entries' => $baseDir . '/includes/Admin/Entries.php',
    'NotificationXPro\\Admin\\Reports\\ReportEmail' => $baseDir . '/includes/Admin/Reports/ReportEmail.php',
    'NotificationXPro\\Admin\\Settings' => $baseDir . '/includes/Admin/Settings.php',
    'NotificationXPro\\Admin\\XSS' => $baseDir . '/includes/Admin/XSS.php',
    'NotificationXPro\\CoreInstallerPro' => $baseDir . '/includes/CoreInstallerPro.php',
    'NotificationXPro\\Core\\CSV_Handler' => $baseDir . '/includes/Core/CSV_Handler.php',
    'NotificationXPro\\Core\\Database' => $baseDir . '/includes/Core/Database.php',
    'NotificationXPro\\Core\\Helper' => $baseDir . '/includes/Core/Helper.php',
    'NotificationXPro\\Core\\Licensing\\Licensing' => $baseDir . '/includes/Core/Licensing/Licensing.php',
    'NotificationXPro\\Core\\Licensing\\ProPluginUpdater' => $baseDir . '/includes/Core/Licensing/ProPluginUpdater.php',
    'NotificationXPro\\Core\\Locations' => $baseDir . '/includes/Core/Locations.php',
    'NotificationXPro\\Core\\PostType' => $baseDir . '/includes/Core/PostType.php',
    'NotificationXPro\\Core\\QuickBuild' => $baseDir . '/includes/Core/QuickBuild.php',
    'NotificationXPro\\Core\\REST' => $baseDir . '/includes/Core/REST.php',
    'NotificationXPro\\Core\\RoleManagement' => $baseDir . '/includes/Core/RoleManagement.php',
    'NotificationXPro\\Core\\Shortcode' => $baseDir . '/includes/Features/Shortcode.php',
    'NotificationXPro\\Core\\Upgrader' => $baseDir . '/includes/Core/Upgrader.php',
    'NotificationXPro\\Dependencies\\WPDeveloper\\Licensing\\AJAXApi' => $baseDir . '/includes/Dependencies/WPDeveloper/Licensing/AJAXApi.php',
    'NotificationXPro\\Dependencies\\WPDeveloper\\Licensing\\Contracts\\ApiAdapter' => $baseDir . '/includes/Dependencies/WPDeveloper/Licensing/Contracts/ApiAdapter.php',
    'NotificationXPro\\Dependencies\\WPDeveloper\\Licensing\\LicenseManager' => $baseDir . '/includes/Dependencies/WPDeveloper/Licensing/LicenseManager.php',
    'NotificationXPro\\Dependencies\\WPDeveloper\\Licensing\\PluginUpdater' => $baseDir . '/includes/Dependencies/WPDeveloper/Licensing/PluginUpdater.php',
    'NotificationXPro\\Dependencies\\WPDeveloper\\Licensing\\RESTApi' => $baseDir . '/includes/Dependencies/WPDeveloper/Licensing/RESTApi.php',
    'NotificationXPro\\Extensions\\ActiveCampaign\\ActiveCampaign' => $baseDir . '/includes/Extensions/ActiveCampaign/ActiveCampaign.php',
    'NotificationXPro\\Extensions\\ConvertKit\\ConvertKit' => $baseDir . '/includes/Extensions/ConvertKit/ConvertKit.php',
    'NotificationXPro\\Extensions\\CustomNotification\\CustomNotification' => $baseDir . '/includes/Extensions/CustomNotification/CustomNotification.php',
    'NotificationXPro\\Extensions\\CustomNotification\\CustomNotificationConversions' => $baseDir . '/includes/Extensions/CustomNotification/CustomNotificationConversions.php',
    'NotificationXPro\\Extensions\\EDD\\EDD' => $baseDir . '/includes/Extensions/EDD/EDD.php',
    'NotificationXPro\\Extensions\\EDD\\EDDInline' => $baseDir . '/includes/Extensions/EDD/EDDInline.php',
    'NotificationXPro\\Extensions\\EDD\\_EDD' => $baseDir . '/includes/Extensions/EDD/_EDD.php',
    'NotificationXPro\\Extensions\\Elementor\\From' => $baseDir . '/includes/Extensions/Elementor/From.php',
    'NotificationXPro\\Extensions\\Envato\\Envato' => $baseDir . '/includes/Extensions/Envato/Envato.php',
    'NotificationXPro\\Extensions\\ExtensionFactory' => $baseDir . '/includes/Extensions/ExtensionFactory.php',
    'NotificationXPro\\Extensions\\FlashingTab\\FlashingTab' => $baseDir . '/includes/Extensions/FlashingTab/FlashingTab.php',
    'NotificationXPro\\Extensions\\Freemius\\Api_Base' => $baseDir . '/includes/Extensions/Freemius/inc/freemius/FreemiusBase.php',
    'NotificationXPro\\Extensions\\Freemius\\Api_WordPress' => $baseDir . '/includes/Extensions/Freemius/inc/freemius/FreemiusWordPress.php',
    'NotificationXPro\\Extensions\\Freemius\\Freemius' => $baseDir . '/includes/Extensions/Freemius/Freemius.php',
    'NotificationXPro\\Extensions\\Freemius\\FreemiusConversions' => $baseDir . '/includes/Extensions/Freemius/FreemiusConversions.php',
    'NotificationXPro\\Extensions\\Freemius\\FreemiusReviews' => $baseDir . '/includes/Extensions/Freemius/FreemiusReviews.php',
    'NotificationXPro\\Extensions\\Freemius\\FreemiusStats' => $baseDir . '/includes/Extensions/Freemius/FreemiusStats.php',
    'NotificationXPro\\Extensions\\Freemius\\Freemius_ArgumentNotExistException' => $baseDir . '/includes/Extensions/Freemius/inc/freemius/Exceptions/ArgumentNotExistException.php',
    'NotificationXPro\\Extensions\\Freemius\\Freemius_EmptyArgumentException' => $baseDir . '/includes/Extensions/Freemius/inc/freemius/Exceptions/EmptyArgumentException.php',
    'NotificationXPro\\Extensions\\Freemius\\Freemius_Exception' => $baseDir . '/includes/Extensions/Freemius/inc/freemius/Exceptions/Exception.php',
    'NotificationXPro\\Extensions\\Freemius\\Freemius_InvalidArgumentException' => $baseDir . '/includes/Extensions/Freemius/inc/freemius/Exceptions/InvalidArgumentException.php',
    'NotificationXPro\\Extensions\\Freemius\\Freemius_OAuthException' => $baseDir . '/includes/Extensions/Freemius/inc/freemius/Exceptions/OAuthException.php',
    'NotificationXPro\\Extensions\\Freemius\\Helper' => $baseDir . '/includes/Extensions/Freemius/inc/Helper.php',
    'NotificationXPro\\Extensions\\GRVF\\GravityForms' => $baseDir . '/includes/Extensions/GRVF/GravityForms.php',
    'NotificationXPro\\Extensions\\Give\\Give' => $baseDir . '/includes/Extensions/Give/Give.php',
    'NotificationXPro\\Extensions\\GlobalFields' => $baseDir . '/includes/Extensions/GlobalFields.php',
    'NotificationXPro\\Extensions\\Google\\GoogleReviews' => $baseDir . '/includes/Extensions/Google/GoogleReviews.php',
    'NotificationXPro\\Extensions\\Google\\YouTube' => $baseDir . '/includes/Extensions/Google/Youtube.php',
    'NotificationXPro\\Extensions\\Google_Analytics\\GoogleClient' => $baseDir . '/includes/Extensions/Google/inc/GoogleClient.php',
    'NotificationXPro\\Extensions\\Google_Analytics\\Google_Analytics' => $baseDir . '/includes/Extensions/Google/Google_Analytics.php',
    'NotificationXPro\\Extensions\\Google_Analytics\\Google_Analytics_Updater' => $baseDir . '/includes/Extensions/Google/inc/class-nxpro-ga-updater.php',
    'NotificationXPro\\Extensions\\Google_Analytics\\Google_Analytics_V4' => $baseDir . '/includes/Extensions/Google/inc/class-nxpro-ga4.php',
    'NotificationXPro\\Extensions\\Google_Analytics\\Google_Client' => $baseDir . '/includes/Extensions/Google/inc/class-nxpro-google-client-helper.php',
    'NotificationXPro\\Extensions\\IFTTT\\IFTTT' => $baseDir . '/includes/Extensions/IFTTT/IFTTT.php',
    'NotificationXPro\\Extensions\\LearnDash\\LearnDash' => $baseDir . '/includes/Extensions/LearnDash/LearnDash.php',
    'NotificationXPro\\Extensions\\LearnDash\\LearnDashInline' => $baseDir . '/includes/Extensions/LearnDash/LearnDashInline.php',
    'NotificationXPro\\Extensions\\LearnDash\\_LearnDash' => $baseDir . '/includes/Extensions/LearnDash/_LearnDash.php',
    'NotificationXPro\\Extensions\\LearnPress\\LearnPress' => $baseDir . '/includes/Extensions/LearnPress/LearnPress.php',
    'NotificationXPro\\Extensions\\LearnPress\\LearnPressInline' => $baseDir . '/includes/Extensions/LearnPress/LearnPressInline.php',
    'NotificationXPro\\Extensions\\LearnPress\\_LearnPress' => $baseDir . '/includes/Extensions/LearnPress/_LearnPress.php',
    'NotificationXPro\\Extensions\\MailChimp\\API' => $baseDir . '/includes/Extensions/MailChimp/inc/API.php',
    'NotificationXPro\\Extensions\\MailChimp\\Batch' => $baseDir . '/includes/Extensions/MailChimp/inc/Batch.php',
    'NotificationXPro\\Extensions\\MailChimp\\Helper' => $baseDir . '/includes/Extensions/MailChimp/inc/Helper.php',
    'NotificationXPro\\Extensions\\MailChimp\\MailChimp' => $baseDir . '/includes/Extensions/MailChimp/MailChimp.php',
    'NotificationXPro\\Extensions\\MailChimp\\Webhook' => $baseDir . '/includes/Extensions/MailChimp/inc/Webhook.php',
    'NotificationXPro\\Extensions\\OfferAnnouncement\\Announcements' => $baseDir . '/includes/Extensions/OfferAnnouncement/Announcements.php',
    'NotificationXPro\\Extensions\\PressBar\\PressBar' => $baseDir . '/includes/Extensions/PressBar/PressBar.php',
    'NotificationXPro\\Extensions\\Tutor\\Tutor' => $baseDir . '/includes/Extensions/Tutor/Tutor.php',
    'NotificationXPro\\Extensions\\Tutor\\TutorInline' => $baseDir . '/includes/Extensions/Tutor/TutorInline.php',
    'NotificationXPro\\Extensions\\Tutor\\_Tutor' => $baseDir . '/includes/Extensions/Tutor/_Tutor.php',
    'NotificationXPro\\Extensions\\WooCommerce\\WooCommerce' => $baseDir . '/includes/Extensions/WooCommerce/WooCommerce.php',
    'NotificationXPro\\Extensions\\WooCommerce\\WooCommerceSales' => $baseDir . '/includes/Extensions/WooCommerce/WooCommerceSales.php',
    'NotificationXPro\\Extensions\\WooCommerce\\WooCommerceSalesInline' => $baseDir . '/includes/Extensions/WooCommerce/WooCommerceSalesInline.php',
    'NotificationXPro\\Extensions\\WooCommerce\\WooCommerceSalesReviews' => $baseDir . '/includes/Extensions/WooCommerce/WooCommerceSalesReviews.php',
    'NotificationXPro\\Extensions\\WooCommerce\\WooInline' => $baseDir . '/includes/Extensions/WooCommerce/WooInline.php',
    'NotificationXPro\\Extensions\\WooCommerce\\WooReviews' => $baseDir . '/includes/Extensions/WooCommerce/WooReviews.php',
    'NotificationXPro\\Extensions\\Zapier\\Zapier' => $baseDir . '/includes/Extensions/Zapier/Zapier.php',
    'NotificationXPro\\Extensions\\Zapier\\ZapierConversions' => $baseDir . '/includes/Extensions/Zapier/ZapierConversions.php',
    'NotificationXPro\\Extensions\\Zapier\\ZapierEmailSubscription' => $baseDir . '/includes/Extensions/Zapier/ZapierEmailSubscription.php',
    'NotificationXPro\\Extensions\\Zapier\\ZapierReviews' => $baseDir . '/includes/Extensions/Zapier/ZapierReviews.php',
    'NotificationXPro\\Feature\\Maps' => $baseDir . '/includes/Features/Maps.php',
    'NotificationXPro\\Feature\\SalesFeatures' => $baseDir . '/includes/Features/SalesFeatures.php',
    'NotificationXPro\\Feature\\Sound' => $baseDir . '/includes/Features/Sound.php',
    'NotificationXPro\\FrontEnd\\FrontEnd' => $baseDir . '/includes/FrontEnd/FrontEnd.php',
    'NotificationXPro\\NotificationX' => $baseDir . '/includes/NotificationX.php',
    'NotificationXPro\\Types\\Comments' => $baseDir . '/includes/Types/Comments.php',
    'NotificationXPro\\Types\\ContactForm' => $baseDir . '/includes/Types/ContactForm.php',
    'NotificationXPro\\Types\\Conversions' => $baseDir . '/includes/Types/Conversions.php',
    'NotificationXPro\\Types\\CustomNotification' => $baseDir . '/includes/Types/CustomNotification.php',
    'NotificationXPro\\Types\\Donations' => $baseDir . '/includes/Types/Donations.php',
    'NotificationXPro\\Types\\DownloadStats' => $baseDir . '/includes/Types/DownloadStats.php',
    'NotificationXPro\\Types\\ELearning' => $baseDir . '/includes/Types/ELearning.php',
    'NotificationXPro\\Types\\EmailSubscription' => $baseDir . '/includes/Types/EmailSubscription.php',
    'NotificationXPro\\Types\\Inline' => $baseDir . '/includes/Types/Inline.php',
    'NotificationXPro\\Types\\NotificationBar' => $baseDir . '/includes/Types/NotificationBar.php',
    'NotificationXPro\\Types\\PageAnalytics' => $baseDir . '/includes/Types/PageAnalytics.php',
    'NotificationXPro\\Types\\Reviews' => $baseDir . '/includes/Types/Reviews.php',
    'NotificationXPro\\Types\\WooCommerceSales' => $baseDir . '/includes/Types/WooCommerceSales.php',
    'NxProGA\\Firebase\\JWT\\BeforeValidException' => $baseDir . '/includes/Extensions/Google/lib/Firebase/JWT/BeforeValidException.php',
    'NxProGA\\Firebase\\JWT\\ExpiredException' => $baseDir . '/includes/Extensions/Google/lib/Firebase/JWT/ExpiredException.php',
    'NxProGA\\Firebase\\JWT\\JWK' => $baseDir . '/includes/Extensions/Google/lib/Firebase/JWT/JWK.php',
    'NxProGA\\Firebase\\JWT\\JWT' => $baseDir . '/includes/Extensions/Google/lib/Firebase/JWT/JWT.php',
    'NxProGA\\Firebase\\JWT\\Key' => $baseDir . '/includes/Extensions/Google/lib/Firebase/JWT/Key.php',
    'NxProGA\\Firebase\\JWT\\SignatureInvalidException' => $baseDir . '/includes/Extensions/Google/lib/Firebase/JWT/SignatureInvalidException.php',
    'NxProGA\\Google\\AccessToken\\Revoke' => $baseDir . '/includes/Extensions/Google/lib/Google/AccessToken/Revoke.php',
    'NxProGA\\Google\\AccessToken\\Verify' => $baseDir . '/includes/Extensions/Google/lib/Google/AccessToken/Verify.php',
    'NxProGA\\Google\\AuthHandler\\AuthHandlerFactory' => $baseDir . '/includes/Extensions/Google/lib/Google/AuthHandler/AuthHandlerFactory.php',
    'NxProGA\\Google\\AuthHandler\\Guzzle5AuthHandler' => $baseDir . '/includes/Extensions/Google/lib/Google/AuthHandler/Guzzle5AuthHandler.php',
    'NxProGA\\Google\\AuthHandler\\Guzzle6AuthHandler' => $baseDir . '/includes/Extensions/Google/lib/Google/AuthHandler/Guzzle6AuthHandler.php',
    'NxProGA\\Google\\AuthHandler\\Guzzle7AuthHandler' => $baseDir . '/includes/Extensions/Google/lib/Google/AuthHandler/Guzzle7AuthHandler.php',
    'NxProGA\\Google\\Auth\\AccessToken' => $baseDir . '/includes/Extensions/Google/lib/Google/Auth/AccessToken.php',
    'NxProGA\\Google\\Auth\\ApplicationDefaultCredentials' => $baseDir . '/includes/Extensions/Google/lib/Google/Auth/ApplicationDefaultCredentials.php',
    'NxProGA\\Google\\Auth\\CacheTrait' => $baseDir . '/includes/Extensions/Google/lib/Google/Auth/CacheTrait.php',
    'NxProGA\\Google\\Auth\\Cache\\InvalidArgumentException' => $baseDir . '/includes/Extensions/Google/lib/Google/Auth/Cache/InvalidArgumentException.php',
    'NxProGA\\Google\\Auth\\Cache\\Item' => $baseDir . '/includes/Extensions/Google/lib/Google/Auth/Cache/Item.php',
    'NxProGA\\Google\\Auth\\Cache\\MemoryCacheItemPool' => $baseDir . '/includes/Extensions/Google/lib/Google/Auth/Cache/MemoryCacheItemPool.php',
    'NxProGA\\Google\\Auth\\Cache\\SysVCacheItemPool' => $baseDir . '/includes/Extensions/Google/lib/Google/Auth/Cache/SysVCacheItemPool.php',
    'NxProGA\\Google\\Auth\\CredentialsLoader' => $baseDir . '/includes/Extensions/Google/lib/Google/Auth/CredentialsLoader.php',
    'NxProGA\\Google\\Auth\\Credentials\\AppIdentityCredentials' => $baseDir . '/includes/Extensions/Google/lib/Google/Auth/Credentials/AppIdentityCredentials.php',
    'NxProGA\\Google\\Auth\\Credentials\\GCECredentials' => $baseDir . '/includes/Extensions/Google/lib/Google/Auth/Credentials/GCECredentials.php',
    'NxProGA\\Google\\Auth\\Credentials\\IAMCredentials' => $baseDir . '/includes/Extensions/Google/lib/Google/Auth/Credentials/IAMCredentials.php',
    'NxProGA\\Google\\Auth\\Credentials\\InsecureCredentials' => $baseDir . '/includes/Extensions/Google/lib/Google/Auth/Credentials/InsecureCredentials.php',
    'NxProGA\\Google\\Auth\\Credentials\\ServiceAccountCredentials' => $baseDir . '/includes/Extensions/Google/lib/Google/Auth/Credentials/ServiceAccountCredentials.php',
    'NxProGA\\Google\\Auth\\Credentials\\ServiceAccountJwtAccessCredentials' => $baseDir . '/includes/Extensions/Google/lib/Google/Auth/Credentials/ServiceAccountJwtAccessCredentials.php',
    'NxProGA\\Google\\Auth\\Credentials\\UserRefreshCredentials' => $baseDir . '/includes/Extensions/Google/lib/Google/Auth/Credentials/UserRefreshCredentials.php',
    'NxProGA\\Google\\Auth\\FetchAuthTokenCache' => $baseDir . '/includes/Extensions/Google/lib/Google/Auth/FetchAuthTokenCache.php',
    'NxProGA\\Google\\Auth\\FetchAuthTokenInterface' => $baseDir . '/includes/Extensions/Google/lib/Google/Auth/FetchAuthTokenInterface.php',
    'NxProGA\\Google\\Auth\\GCECache' => $baseDir . '/includes/Extensions/Google/lib/Google/Auth/GCECache.php',
    'NxProGA\\Google\\Auth\\GetQuotaProjectInterface' => $baseDir . '/includes/Extensions/Google/lib/Google/Auth/GetQuotaProjectInterface.php',
    'NxProGA\\Google\\Auth\\HttpHandler\\Guzzle5HttpHandler' => $baseDir . '/includes/Extensions/Google/lib/Google/Auth/HttpHandler/Guzzle5HttpHandler.php',
    'NxProGA\\Google\\Auth\\HttpHandler\\Guzzle6HttpHandler' => $baseDir . '/includes/Extensions/Google/lib/Google/Auth/HttpHandler/Guzzle6HttpHandler.php',
    'NxProGA\\Google\\Auth\\HttpHandler\\Guzzle7HttpHandler' => $baseDir . '/includes/Extensions/Google/lib/Google/Auth/HttpHandler/Guzzle7HttpHandler.php',
    'NxProGA\\Google\\Auth\\HttpHandler\\HttpClientCache' => $baseDir . '/includes/Extensions/Google/lib/Google/Auth/HttpHandler/HttpClientCache.php',
    'NxProGA\\Google\\Auth\\HttpHandler\\HttpHandlerFactory' => $baseDir . '/includes/Extensions/Google/lib/Google/Auth/HttpHandler/HttpHandlerFactory.php',
    'NxProGA\\Google\\Auth\\Iam' => $baseDir . '/includes/Extensions/Google/lib/Google/Auth/Iam.php',
    'NxProGA\\Google\\Auth\\Middleware\\AuthTokenMiddleware' => $baseDir . '/includes/Extensions/Google/lib/Google/Auth/Middleware/AuthTokenMiddleware.php',
    'NxProGA\\Google\\Auth\\Middleware\\ProxyAuthTokenMiddleware' => $baseDir . '/includes/Extensions/Google/lib/Google/Auth/Middleware/ProxyAuthTokenMiddleware.php',
    'NxProGA\\Google\\Auth\\Middleware\\ScopedAccessTokenMiddleware' => $baseDir . '/includes/Extensions/Google/lib/Google/Auth/Middleware/ScopedAccessTokenMiddleware.php',
    'NxProGA\\Google\\Auth\\Middleware\\SimpleMiddleware' => $baseDir . '/includes/Extensions/Google/lib/Google/Auth/Middleware/SimpleMiddleware.php',
    'NxProGA\\Google\\Auth\\OAuth2' => $baseDir . '/includes/Extensions/Google/lib/Google/Auth/OAuth2.php',
    'NxProGA\\Google\\Auth\\ProjectIdProviderInterface' => $baseDir . '/includes/Extensions/Google/lib/Google/Auth/ProjectIdProviderInterface.php',
    'NxProGA\\Google\\Auth\\ServiceAccountSignerTrait' => $baseDir . '/includes/Extensions/Google/lib/Google/Auth/ServiceAccountSignerTrait.php',
    'NxProGA\\Google\\Auth\\SignBlobInterface' => $baseDir . '/includes/Extensions/Google/lib/Google/Auth/SignBlobInterface.php',
    'NxProGA\\Google\\Auth\\UpdateMetadataInterface' => $baseDir . '/includes/Extensions/Google/lib/Google/Auth/UpdateMetadataInterface.php',
    'NxProGA\\Google\\Client' => $baseDir . '/includes/Extensions/Google/lib/Google/Client.php',
    'NxProGA\\Google\\Collection' => $baseDir . '/includes/Extensions/Google/lib/Google/Collection.php',
    'NxProGA\\Google\\Exception' => $baseDir . '/includes/Extensions/Google/lib/Google/Exception.php',
    'NxProGA\\Google\\Http\\Batch' => $baseDir . '/includes/Extensions/Google/lib/Google/Http/Batch.php',
    'NxProGA\\Google\\Http\\MediaFileUpload' => $baseDir . '/includes/Extensions/Google/lib/Google/Http/MediaFileUpload.php',
    'NxProGA\\Google\\Http\\REST' => $baseDir . '/includes/Extensions/Google/lib/Google/Http/REST.php',
    'NxProGA\\Google\\Model' => $baseDir . '/includes/Extensions/Google/lib/Google/Model.php',
    'NxProGA\\Google\\Service' => $baseDir . '/includes/Extensions/Google/lib/Google/Service.php',
    'NxProGA\\Google\\Service\\Analytics' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics.php',
    'NxProGA\\Google\\Service\\AnalyticsReporting' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/AnalyticsReporting.php',
    'NxProGA\\Google\\Service\\AnalyticsReporting\\Activity' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/AnalyticsReporting/Activity.php',
    'NxProGA\\Google\\Service\\AnalyticsReporting\\Cohort' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/AnalyticsReporting/Cohort.php',
    'NxProGA\\Google\\Service\\AnalyticsReporting\\CohortGroup' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/AnalyticsReporting/CohortGroup.php',
    'NxProGA\\Google\\Service\\AnalyticsReporting\\ColumnHeader' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/AnalyticsReporting/ColumnHeader.php',
    'NxProGA\\Google\\Service\\AnalyticsReporting\\CustomDimension' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/AnalyticsReporting/CustomDimension.php',
    'NxProGA\\Google\\Service\\AnalyticsReporting\\DateRange' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/AnalyticsReporting/DateRange.php',
    'NxProGA\\Google\\Service\\AnalyticsReporting\\DateRangeValues' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/AnalyticsReporting/DateRangeValues.php',
    'NxProGA\\Google\\Service\\AnalyticsReporting\\Dimension' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/AnalyticsReporting/Dimension.php',
    'NxProGA\\Google\\Service\\AnalyticsReporting\\DimensionFilter' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/AnalyticsReporting/DimensionFilter.php',
    'NxProGA\\Google\\Service\\AnalyticsReporting\\DimensionFilterClause' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/AnalyticsReporting/DimensionFilterClause.php',
    'NxProGA\\Google\\Service\\AnalyticsReporting\\DynamicSegment' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/AnalyticsReporting/DynamicSegment.php',
    'NxProGA\\Google\\Service\\AnalyticsReporting\\EcommerceData' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/AnalyticsReporting/EcommerceData.php',
    'NxProGA\\Google\\Service\\AnalyticsReporting\\EventData' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/AnalyticsReporting/EventData.php',
    'NxProGA\\Google\\Service\\AnalyticsReporting\\GetReportsRequest' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/AnalyticsReporting/GetReportsRequest.php',
    'NxProGA\\Google\\Service\\AnalyticsReporting\\GetReportsResponse' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/AnalyticsReporting/GetReportsResponse.php',
    'NxProGA\\Google\\Service\\AnalyticsReporting\\GoalData' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/AnalyticsReporting/GoalData.php',
    'NxProGA\\Google\\Service\\AnalyticsReporting\\GoalSetData' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/AnalyticsReporting/GoalSetData.php',
    'NxProGA\\Google\\Service\\AnalyticsReporting\\Metric' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/AnalyticsReporting/Metric.php',
    'NxProGA\\Google\\Service\\AnalyticsReporting\\MetricFilter' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/AnalyticsReporting/MetricFilter.php',
    'NxProGA\\Google\\Service\\AnalyticsReporting\\MetricFilterClause' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/AnalyticsReporting/MetricFilterClause.php',
    'NxProGA\\Google\\Service\\AnalyticsReporting\\MetricHeader' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/AnalyticsReporting/MetricHeader.php',
    'NxProGA\\Google\\Service\\AnalyticsReporting\\MetricHeaderEntry' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/AnalyticsReporting/MetricHeaderEntry.php',
    'NxProGA\\Google\\Service\\AnalyticsReporting\\OrFiltersForSegment' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/AnalyticsReporting/OrFiltersForSegment.php',
    'NxProGA\\Google\\Service\\AnalyticsReporting\\OrderBy' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/AnalyticsReporting/OrderBy.php',
    'NxProGA\\Google\\Service\\AnalyticsReporting\\PageviewData' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/AnalyticsReporting/PageviewData.php',
    'NxProGA\\Google\\Service\\AnalyticsReporting\\Pivot' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/AnalyticsReporting/Pivot.php',
    'NxProGA\\Google\\Service\\AnalyticsReporting\\PivotHeader' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/AnalyticsReporting/PivotHeader.php',
    'NxProGA\\Google\\Service\\AnalyticsReporting\\PivotHeaderEntry' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/AnalyticsReporting/PivotHeaderEntry.php',
    'NxProGA\\Google\\Service\\AnalyticsReporting\\PivotValueRegion' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/AnalyticsReporting/PivotValueRegion.php',
    'NxProGA\\Google\\Service\\AnalyticsReporting\\ProductData' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/AnalyticsReporting/ProductData.php',
    'NxProGA\\Google\\Service\\AnalyticsReporting\\Report' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/AnalyticsReporting/Report.php',
    'NxProGA\\Google\\Service\\AnalyticsReporting\\ReportData' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/AnalyticsReporting/ReportData.php',
    'NxProGA\\Google\\Service\\AnalyticsReporting\\ReportRequest' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/AnalyticsReporting/ReportRequest.php',
    'NxProGA\\Google\\Service\\AnalyticsReporting\\ReportRow' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/AnalyticsReporting/ReportRow.php',
    'NxProGA\\Google\\Service\\AnalyticsReporting\\ResourceQuotasRemaining' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/AnalyticsReporting/ResourceQuotasRemaining.php',
    'NxProGA\\Google\\Service\\AnalyticsReporting\\Resource\\Reports' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/AnalyticsReporting/Resource/Reports.php',
    'NxProGA\\Google\\Service\\AnalyticsReporting\\Resource\\UserActivity' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/AnalyticsReporting/Resource/UserActivity.php',
    'NxProGA\\Google\\Service\\AnalyticsReporting\\ScreenviewData' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/AnalyticsReporting/ScreenviewData.php',
    'NxProGA\\Google\\Service\\AnalyticsReporting\\SearchUserActivityRequest' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/AnalyticsReporting/SearchUserActivityRequest.php',
    'NxProGA\\Google\\Service\\AnalyticsReporting\\SearchUserActivityResponse' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/AnalyticsReporting/SearchUserActivityResponse.php',
    'NxProGA\\Google\\Service\\AnalyticsReporting\\Segment' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/AnalyticsReporting/Segment.php',
    'NxProGA\\Google\\Service\\AnalyticsReporting\\SegmentDefinition' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/AnalyticsReporting/SegmentDefinition.php',
    'NxProGA\\Google\\Service\\AnalyticsReporting\\SegmentDimensionFilter' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/AnalyticsReporting/SegmentDimensionFilter.php',
    'NxProGA\\Google\\Service\\AnalyticsReporting\\SegmentFilter' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/AnalyticsReporting/SegmentFilter.php',
    'NxProGA\\Google\\Service\\AnalyticsReporting\\SegmentFilterClause' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/AnalyticsReporting/SegmentFilterClause.php',
    'NxProGA\\Google\\Service\\AnalyticsReporting\\SegmentMetricFilter' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/AnalyticsReporting/SegmentMetricFilter.php',
    'NxProGA\\Google\\Service\\AnalyticsReporting\\SegmentSequenceStep' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/AnalyticsReporting/SegmentSequenceStep.php',
    'NxProGA\\Google\\Service\\AnalyticsReporting\\SequenceSegment' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/AnalyticsReporting/SequenceSegment.php',
    'NxProGA\\Google\\Service\\AnalyticsReporting\\SimpleSegment' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/AnalyticsReporting/SimpleSegment.php',
    'NxProGA\\Google\\Service\\AnalyticsReporting\\TransactionData' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/AnalyticsReporting/TransactionData.php',
    'NxProGA\\Google\\Service\\AnalyticsReporting\\User' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/AnalyticsReporting/User.php',
    'NxProGA\\Google\\Service\\AnalyticsReporting\\UserActivitySession' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/AnalyticsReporting/UserActivitySession.php',
    'NxProGA\\Google\\Service\\Analytics\\Account' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/Account.php',
    'NxProGA\\Google\\Service\\Analytics\\AccountChildLink' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/AccountChildLink.php',
    'NxProGA\\Google\\Service\\Analytics\\AccountPermissions' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/AccountPermissions.php',
    'NxProGA\\Google\\Service\\Analytics\\AccountRef' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/AccountRef.php',
    'NxProGA\\Google\\Service\\Analytics\\AccountSummaries' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/AccountSummaries.php',
    'NxProGA\\Google\\Service\\Analytics\\AccountSummary' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/AccountSummary.php',
    'NxProGA\\Google\\Service\\Analytics\\AccountTicket' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/AccountTicket.php',
    'NxProGA\\Google\\Service\\Analytics\\AccountTreeRequest' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/AccountTreeRequest.php',
    'NxProGA\\Google\\Service\\Analytics\\AccountTreeResponse' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/AccountTreeResponse.php',
    'NxProGA\\Google\\Service\\Analytics\\Accounts' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/Accounts.php',
    'NxProGA\\Google\\Service\\Analytics\\AdWordsAccount' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/AdWordsAccount.php',
    'NxProGA\\Google\\Service\\Analytics\\AnalyticsDataimportDeleteUploadDataRequest' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/AnalyticsDataimportDeleteUploadDataRequest.php',
    'NxProGA\\Google\\Service\\Analytics\\Column' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/Column.php',
    'NxProGA\\Google\\Service\\Analytics\\Columns' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/Columns.php',
    'NxProGA\\Google\\Service\\Analytics\\CustomDataSource' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/CustomDataSource.php',
    'NxProGA\\Google\\Service\\Analytics\\CustomDataSourceChildLink' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/CustomDataSourceChildLink.php',
    'NxProGA\\Google\\Service\\Analytics\\CustomDataSourceParentLink' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/CustomDataSourceParentLink.php',
    'NxProGA\\Google\\Service\\Analytics\\CustomDataSources' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/CustomDataSources.php',
    'NxProGA\\Google\\Service\\Analytics\\CustomDimension' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/CustomDimension.php',
    'NxProGA\\Google\\Service\\Analytics\\CustomDimensionParentLink' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/CustomDimensionParentLink.php',
    'NxProGA\\Google\\Service\\Analytics\\CustomDimensions' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/CustomDimensions.php',
    'NxProGA\\Google\\Service\\Analytics\\CustomMetric' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/CustomMetric.php',
    'NxProGA\\Google\\Service\\Analytics\\CustomMetricParentLink' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/CustomMetricParentLink.php',
    'NxProGA\\Google\\Service\\Analytics\\CustomMetrics' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/CustomMetrics.php',
    'NxProGA\\Google\\Service\\Analytics\\EntityAdWordsLink' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/EntityAdWordsLink.php',
    'NxProGA\\Google\\Service\\Analytics\\EntityAdWordsLinkEntity' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/EntityAdWordsLinkEntity.php',
    'NxProGA\\Google\\Service\\Analytics\\EntityAdWordsLinks' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/EntityAdWordsLinks.php',
    'NxProGA\\Google\\Service\\Analytics\\EntityUserLink' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/EntityUserLink.php',
    'NxProGA\\Google\\Service\\Analytics\\EntityUserLinkEntity' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/EntityUserLinkEntity.php',
    'NxProGA\\Google\\Service\\Analytics\\EntityUserLinkPermissions' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/EntityUserLinkPermissions.php',
    'NxProGA\\Google\\Service\\Analytics\\EntityUserLinks' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/EntityUserLinks.php',
    'NxProGA\\Google\\Service\\Analytics\\Experiment' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/Experiment.php',
    'NxProGA\\Google\\Service\\Analytics\\ExperimentParentLink' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/ExperimentParentLink.php',
    'NxProGA\\Google\\Service\\Analytics\\ExperimentVariations' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/ExperimentVariations.php',
    'NxProGA\\Google\\Service\\Analytics\\Experiments' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/Experiments.php',
    'NxProGA\\Google\\Service\\Analytics\\Filter' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/Filter.php',
    'NxProGA\\Google\\Service\\Analytics\\FilterAdvancedDetails' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/FilterAdvancedDetails.php',
    'NxProGA\\Google\\Service\\Analytics\\FilterExpression' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/FilterExpression.php',
    'NxProGA\\Google\\Service\\Analytics\\FilterLowercaseDetails' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/FilterLowercaseDetails.php',
    'NxProGA\\Google\\Service\\Analytics\\FilterParentLink' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/FilterParentLink.php',
    'NxProGA\\Google\\Service\\Analytics\\FilterRef' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/FilterRef.php',
    'NxProGA\\Google\\Service\\Analytics\\FilterSearchAndReplaceDetails' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/FilterSearchAndReplaceDetails.php',
    'NxProGA\\Google\\Service\\Analytics\\FilterUppercaseDetails' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/FilterUppercaseDetails.php',
    'NxProGA\\Google\\Service\\Analytics\\Filters' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/Filters.php',
    'NxProGA\\Google\\Service\\Analytics\\GaData' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/GaData.php',
    'NxProGA\\Google\\Service\\Analytics\\GaDataColumnHeaders' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/GaDataColumnHeaders.php',
    'NxProGA\\Google\\Service\\Analytics\\GaDataDataTable' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/GaDataDataTable.php',
    'NxProGA\\Google\\Service\\Analytics\\GaDataDataTableCols' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/GaDataDataTableCols.php',
    'NxProGA\\Google\\Service\\Analytics\\GaDataDataTableRows' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/GaDataDataTableRows.php',
    'NxProGA\\Google\\Service\\Analytics\\GaDataDataTableRowsC' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/GaDataDataTableRowsC.php',
    'NxProGA\\Google\\Service\\Analytics\\GaDataProfileInfo' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/GaDataProfileInfo.php',
    'NxProGA\\Google\\Service\\Analytics\\GaDataQuery' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/GaDataQuery.php',
    'NxProGA\\Google\\Service\\Analytics\\Goal' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/Goal.php',
    'NxProGA\\Google\\Service\\Analytics\\GoalEventDetails' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/GoalEventDetails.php',
    'NxProGA\\Google\\Service\\Analytics\\GoalEventDetailsEventConditions' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/GoalEventDetailsEventConditions.php',
    'NxProGA\\Google\\Service\\Analytics\\GoalParentLink' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/GoalParentLink.php',
    'NxProGA\\Google\\Service\\Analytics\\GoalUrlDestinationDetails' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/GoalUrlDestinationDetails.php',
    'NxProGA\\Google\\Service\\Analytics\\GoalUrlDestinationDetailsSteps' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/GoalUrlDestinationDetailsSteps.php',
    'NxProGA\\Google\\Service\\Analytics\\GoalVisitNumPagesDetails' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/GoalVisitNumPagesDetails.php',
    'NxProGA\\Google\\Service\\Analytics\\GoalVisitTimeOnSiteDetails' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/GoalVisitTimeOnSiteDetails.php',
    'NxProGA\\Google\\Service\\Analytics\\Goals' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/Goals.php',
    'NxProGA\\Google\\Service\\Analytics\\HashClientIdRequest' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/HashClientIdRequest.php',
    'NxProGA\\Google\\Service\\Analytics\\HashClientIdResponse' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/HashClientIdResponse.php',
    'NxProGA\\Google\\Service\\Analytics\\IncludeConditions' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/IncludeConditions.php',
    'NxProGA\\Google\\Service\\Analytics\\LinkedForeignAccount' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/LinkedForeignAccount.php',
    'NxProGA\\Google\\Service\\Analytics\\McfData' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/McfData.php',
    'NxProGA\\Google\\Service\\Analytics\\McfDataColumnHeaders' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/McfDataColumnHeaders.php',
    'NxProGA\\Google\\Service\\Analytics\\McfDataProfileInfo' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/McfDataProfileInfo.php',
    'NxProGA\\Google\\Service\\Analytics\\McfDataQuery' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/McfDataQuery.php',
    'NxProGA\\Google\\Service\\Analytics\\McfDataRows' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/McfDataRows.php',
    'NxProGA\\Google\\Service\\Analytics\\McfDataRowsConversionPathValue' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/McfDataRowsConversionPathValue.php',
    'NxProGA\\Google\\Service\\Analytics\\Profile' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/Profile.php',
    'NxProGA\\Google\\Service\\Analytics\\ProfileChildLink' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/ProfileChildLink.php',
    'NxProGA\\Google\\Service\\Analytics\\ProfileFilterLink' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/ProfileFilterLink.php',
    'NxProGA\\Google\\Service\\Analytics\\ProfileFilterLinks' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/ProfileFilterLinks.php',
    'NxProGA\\Google\\Service\\Analytics\\ProfileParentLink' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/ProfileParentLink.php',
    'NxProGA\\Google\\Service\\Analytics\\ProfilePermissions' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/ProfilePermissions.php',
    'NxProGA\\Google\\Service\\Analytics\\ProfileRef' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/ProfileRef.php',
    'NxProGA\\Google\\Service\\Analytics\\ProfileSummary' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/ProfileSummary.php',
    'NxProGA\\Google\\Service\\Analytics\\Profiles' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/Profiles.php',
    'NxProGA\\Google\\Service\\Analytics\\RealtimeData' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/RealtimeData.php',
    'NxProGA\\Google\\Service\\Analytics\\RealtimeDataColumnHeaders' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/RealtimeDataColumnHeaders.php',
    'NxProGA\\Google\\Service\\Analytics\\RealtimeDataProfileInfo' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/RealtimeDataProfileInfo.php',
    'NxProGA\\Google\\Service\\Analytics\\RealtimeDataQuery' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/RealtimeDataQuery.php',
    'NxProGA\\Google\\Service\\Analytics\\RemarketingAudience' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/RemarketingAudience.php',
    'NxProGA\\Google\\Service\\Analytics\\RemarketingAudienceAudienceDefinition' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/RemarketingAudienceAudienceDefinition.php',
    'NxProGA\\Google\\Service\\Analytics\\RemarketingAudienceStateBasedAudienceDefinition' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/RemarketingAudienceStateBasedAudienceDefinition.php',
    'NxProGA\\Google\\Service\\Analytics\\RemarketingAudienceStateBasedAudienceDefinitionExcludeConditions' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/RemarketingAudienceStateBasedAudienceDefinitionExcludeConditions.php',
    'NxProGA\\Google\\Service\\Analytics\\RemarketingAudiences' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/RemarketingAudiences.php',
    'NxProGA\\Google\\Service\\Analytics\\Resource\\Data' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/Resource/Data.php',
    'NxProGA\\Google\\Service\\Analytics\\Resource\\DataGa' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/Resource/DataGa.php',
    'NxProGA\\Google\\Service\\Analytics\\Resource\\DataMcf' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/Resource/DataMcf.php',
    'NxProGA\\Google\\Service\\Analytics\\Resource\\DataRealtime' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/Resource/DataRealtime.php',
    'NxProGA\\Google\\Service\\Analytics\\Resource\\Management' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/Resource/Management.php',
    'NxProGA\\Google\\Service\\Analytics\\Resource\\ManagementAccountSummaries' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/Resource/ManagementAccountSummaries.php',
    'NxProGA\\Google\\Service\\Analytics\\Resource\\ManagementAccountUserLinks' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/Resource/ManagementAccountUserLinks.php',
    'NxProGA\\Google\\Service\\Analytics\\Resource\\ManagementAccounts' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/Resource/ManagementAccounts.php',
    'NxProGA\\Google\\Service\\Analytics\\Resource\\ManagementClientId' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/Resource/ManagementClientId.php',
    'NxProGA\\Google\\Service\\Analytics\\Resource\\ManagementCustomDataSources' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/Resource/ManagementCustomDataSources.php',
    'NxProGA\\Google\\Service\\Analytics\\Resource\\ManagementCustomDimensions' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/Resource/ManagementCustomDimensions.php',
    'NxProGA\\Google\\Service\\Analytics\\Resource\\ManagementCustomMetrics' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/Resource/ManagementCustomMetrics.php',
    'NxProGA\\Google\\Service\\Analytics\\Resource\\ManagementExperiments' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/Resource/ManagementExperiments.php',
    'NxProGA\\Google\\Service\\Analytics\\Resource\\ManagementFilters' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/Resource/ManagementFilters.php',
    'NxProGA\\Google\\Service\\Analytics\\Resource\\ManagementGoals' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/Resource/ManagementGoals.php',
    'NxProGA\\Google\\Service\\Analytics\\Resource\\ManagementProfileFilterLinks' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/Resource/ManagementProfileFilterLinks.php',
    'NxProGA\\Google\\Service\\Analytics\\Resource\\ManagementProfileUserLinks' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/Resource/ManagementProfileUserLinks.php',
    'NxProGA\\Google\\Service\\Analytics\\Resource\\ManagementProfiles' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/Resource/ManagementProfiles.php',
    'NxProGA\\Google\\Service\\Analytics\\Resource\\ManagementRemarketingAudience' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/Resource/ManagementRemarketingAudience.php',
    'NxProGA\\Google\\Service\\Analytics\\Resource\\ManagementSegments' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/Resource/ManagementSegments.php',
    'NxProGA\\Google\\Service\\Analytics\\Resource\\ManagementUnsampledReports' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/Resource/ManagementUnsampledReports.php',
    'NxProGA\\Google\\Service\\Analytics\\Resource\\ManagementUploads' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/Resource/ManagementUploads.php',
    'NxProGA\\Google\\Service\\Analytics\\Resource\\ManagementWebPropertyAdWordsLinks' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/Resource/ManagementWebPropertyAdWordsLinks.php',
    'NxProGA\\Google\\Service\\Analytics\\Resource\\ManagementWebproperties' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/Resource/ManagementWebproperties.php',
    'NxProGA\\Google\\Service\\Analytics\\Resource\\ManagementWebpropertyUserLinks' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/Resource/ManagementWebpropertyUserLinks.php',
    'NxProGA\\Google\\Service\\Analytics\\Resource\\Metadata' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/Resource/Metadata.php',
    'NxProGA\\Google\\Service\\Analytics\\Resource\\MetadataColumns' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/Resource/MetadataColumns.php',
    'NxProGA\\Google\\Service\\Analytics\\Resource\\Provisioning' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/Resource/Provisioning.php',
    'NxProGA\\Google\\Service\\Analytics\\Resource\\UserDeletion' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/Resource/UserDeletion.php',
    'NxProGA\\Google\\Service\\Analytics\\Resource\\UserDeletionUserDeletionRequest' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/Resource/UserDeletionUserDeletionRequest.php',
    'NxProGA\\Google\\Service\\Analytics\\Segment' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/Segment.php',
    'NxProGA\\Google\\Service\\Analytics\\Segments' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/Segments.php',
    'NxProGA\\Google\\Service\\Analytics\\UnsampledReport' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/UnsampledReport.php',
    'NxProGA\\Google\\Service\\Analytics\\UnsampledReportCloudStorageDownloadDetails' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/UnsampledReportCloudStorageDownloadDetails.php',
    'NxProGA\\Google\\Service\\Analytics\\UnsampledReportDriveDownloadDetails' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/UnsampledReportDriveDownloadDetails.php',
    'NxProGA\\Google\\Service\\Analytics\\UnsampledReports' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/UnsampledReports.php',
    'NxProGA\\Google\\Service\\Analytics\\Upload' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/Upload.php',
    'NxProGA\\Google\\Service\\Analytics\\Uploads' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/Uploads.php',
    'NxProGA\\Google\\Service\\Analytics\\UserDeletionRequest' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/UserDeletionRequest.php',
    'NxProGA\\Google\\Service\\Analytics\\UserDeletionRequestId' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/UserDeletionRequestId.php',
    'NxProGA\\Google\\Service\\Analytics\\UserRef' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/UserRef.php',
    'NxProGA\\Google\\Service\\Analytics\\WebPropertyRef' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/WebPropertyRef.php',
    'NxProGA\\Google\\Service\\Analytics\\WebPropertySummary' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/WebPropertySummary.php',
    'NxProGA\\Google\\Service\\Analytics\\Webproperties' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/Webproperties.php',
    'NxProGA\\Google\\Service\\Analytics\\Webproperty' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/Webproperty.php',
    'NxProGA\\Google\\Service\\Analytics\\WebpropertyChildLink' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/WebpropertyChildLink.php',
    'NxProGA\\Google\\Service\\Analytics\\WebpropertyParentLink' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/WebpropertyParentLink.php',
    'NxProGA\\Google\\Service\\Analytics\\WebpropertyPermissions' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Analytics/WebpropertyPermissions.php',
    'NxProGA\\Google\\Service\\Exception' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Exception.php',
    'NxProGA\\Google\\Service\\Resource' => $baseDir . '/includes/Extensions/Google/lib/Google/Service/Resource.php',
    'NxProGA\\Google\\Task\\Composer' => $baseDir . '/includes/Extensions/Google/lib/Google/Task/Composer.php',
    'NxProGA\\Google\\Task\\Exception' => $baseDir . '/includes/Extensions/Google/lib/Google/Task/Exception.php',
    'NxProGA\\Google\\Task\\Retryable' => $baseDir . '/includes/Extensions/Google/lib/Google/Task/Retryable.php',
    'NxProGA\\Google\\Task\\Runner' => $baseDir . '/includes/Extensions/Google/lib/Google/Task/Runner.php',
    'NxProGA\\Google\\Utils\\UriTemplate' => $baseDir . '/includes/Extensions/Google/lib/Google/Utils/UriTemplate.php',
    'NxProGA\\GuzzleHttp\\Client' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/Client.php',
    'NxProGA\\GuzzleHttp\\ClientInterface' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/ClientInterface.php',
    'NxProGA\\GuzzleHttp\\Cookie\\CookieJar' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/Cookie/CookieJar.php',
    'NxProGA\\GuzzleHttp\\Cookie\\CookieJarInterface' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/Cookie/CookieJarInterface.php',
    'NxProGA\\GuzzleHttp\\Cookie\\FileCookieJar' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/Cookie/FileCookieJar.php',
    'NxProGA\\GuzzleHttp\\Cookie\\SessionCookieJar' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/Cookie/SessionCookieJar.php',
    'NxProGA\\GuzzleHttp\\Cookie\\SetCookie' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/Cookie/SetCookie.php',
    'NxProGA\\GuzzleHttp\\Exception\\BadResponseException' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/Exception/BadResponseException.php',
    'NxProGA\\GuzzleHttp\\Exception\\ClientException' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/Exception/ClientException.php',
    'NxProGA\\GuzzleHttp\\Exception\\ConnectException' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/Exception/ConnectException.php',
    'NxProGA\\GuzzleHttp\\Exception\\GuzzleException' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/Exception/GuzzleException.php',
    'NxProGA\\GuzzleHttp\\Exception\\InvalidArgumentException' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/Exception/InvalidArgumentException.php',
    'NxProGA\\GuzzleHttp\\Exception\\RequestException' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/Exception/RequestException.php',
    'NxProGA\\GuzzleHttp\\Exception\\SeekException' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/Exception/SeekException.php',
    'NxProGA\\GuzzleHttp\\Exception\\ServerException' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/Exception/ServerException.php',
    'NxProGA\\GuzzleHttp\\Exception\\TooManyRedirectsException' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/Exception/TooManyRedirectsException.php',
    'NxProGA\\GuzzleHttp\\Exception\\TransferException' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/Exception/TransferException.php',
    'NxProGA\\GuzzleHttp\\HandlerStack' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/HandlerStack.php',
    'NxProGA\\GuzzleHttp\\Handler\\CurlFactory' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/Handler/CurlFactory.php',
    'NxProGA\\GuzzleHttp\\Handler\\CurlFactoryInterface' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/Handler/CurlFactoryInterface.php',
    'NxProGA\\GuzzleHttp\\Handler\\CurlHandler' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/Handler/CurlHandler.php',
    'NxProGA\\GuzzleHttp\\Handler\\CurlMultiHandler' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/Handler/CurlMultiHandler.php',
    'NxProGA\\GuzzleHttp\\Handler\\EasyHandle' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/Handler/EasyHandle.php',
    'NxProGA\\GuzzleHttp\\Handler\\MockHandler' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/Handler/MockHandler.php',
    'NxProGA\\GuzzleHttp\\Handler\\Proxy' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/Handler/Proxy.php',
    'NxProGA\\GuzzleHttp\\Handler\\StreamHandler' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/Handler/StreamHandler.php',
    'NxProGA\\GuzzleHttp\\MessageFormatter' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/MessageFormatter.php',
    'NxProGA\\GuzzleHttp\\Middleware' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/Middleware.php',
    'NxProGA\\GuzzleHttp\\Pool' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/Pool.php',
    'NxProGA\\GuzzleHttp\\PrepareBodyMiddleware' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/PrepareBodyMiddleware.php',
    'NxProGA\\GuzzleHttp\\Promise\\AggregateException' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/Promise/AggregateException.php',
    'NxProGA\\GuzzleHttp\\Promise\\CancellationException' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/Promise/CancellationException.php',
    'NxProGA\\GuzzleHttp\\Promise\\Coroutine' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/Promise/Coroutine.php',
    'NxProGA\\GuzzleHttp\\Promise\\Create' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/Promise/Create.php',
    'NxProGA\\GuzzleHttp\\Promise\\Each' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/Promise/Each.php',
    'NxProGA\\GuzzleHttp\\Promise\\EachPromise' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/Promise/EachPromise.php',
    'NxProGA\\GuzzleHttp\\Promise\\FulfilledPromise' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/Promise/FulfilledPromise.php',
    'NxProGA\\GuzzleHttp\\Promise\\Is' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/Promise/Is.php',
    'NxProGA\\GuzzleHttp\\Promise\\Promise' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/Promise/Promise.php',
    'NxProGA\\GuzzleHttp\\Promise\\PromiseInterface' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/Promise/PromiseInterface.php',
    'NxProGA\\GuzzleHttp\\Promise\\PromisorInterface' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/Promise/PromisorInterface.php',
    'NxProGA\\GuzzleHttp\\Promise\\RejectedPromise' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/Promise/RejectedPromise.php',
    'NxProGA\\GuzzleHttp\\Promise\\RejectionException' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/Promise/RejectionException.php',
    'NxProGA\\GuzzleHttp\\Promise\\TaskQueue' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/Promise/TaskQueue.php',
    'NxProGA\\GuzzleHttp\\Promise\\TaskQueueInterface' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/Promise/TaskQueueInterface.php',
    'NxProGA\\GuzzleHttp\\Promise\\Utils' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/Promise/Utils.php',
    'NxProGA\\GuzzleHttp\\Psr7\\AppendStream' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/Psr7/AppendStream.php',
    'NxProGA\\GuzzleHttp\\Psr7\\BufferStream' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/Psr7/BufferStream.php',
    'NxProGA\\GuzzleHttp\\Psr7\\CachingStream' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/Psr7/CachingStream.php',
    'NxProGA\\GuzzleHttp\\Psr7\\DroppingStream' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/Psr7/DroppingStream.php',
    'NxProGA\\GuzzleHttp\\Psr7\\FnStream' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/Psr7/FnStream.php',
    'NxProGA\\GuzzleHttp\\Psr7\\Header' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/Psr7/Header.php',
    'NxProGA\\GuzzleHttp\\Psr7\\InflateStream' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/Psr7/InflateStream.php',
    'NxProGA\\GuzzleHttp\\Psr7\\LazyOpenStream' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/Psr7/LazyOpenStream.php',
    'NxProGA\\GuzzleHttp\\Psr7\\LimitStream' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/Psr7/LimitStream.php',
    'NxProGA\\GuzzleHttp\\Psr7\\Message' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/Psr7/Message.php',
    'NxProGA\\GuzzleHttp\\Psr7\\MessageTrait' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/Psr7/MessageTrait.php',
    'NxProGA\\GuzzleHttp\\Psr7\\MimeType' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/Psr7/MimeType.php',
    'NxProGA\\GuzzleHttp\\Psr7\\MultipartStream' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/Psr7/MultipartStream.php',
    'NxProGA\\GuzzleHttp\\Psr7\\NoSeekStream' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/Psr7/NoSeekStream.php',
    'NxProGA\\GuzzleHttp\\Psr7\\PumpStream' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/Psr7/PumpStream.php',
    'NxProGA\\GuzzleHttp\\Psr7\\Query' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/Psr7/Query.php',
    'NxProGA\\GuzzleHttp\\Psr7\\Request' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/Psr7/Request.php',
    'NxProGA\\GuzzleHttp\\Psr7\\Response' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/Psr7/Response.php',
    'NxProGA\\GuzzleHttp\\Psr7\\Rfc7230' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/Psr7/Rfc7230.php',
    'NxProGA\\GuzzleHttp\\Psr7\\ServerRequest' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/Psr7/ServerRequest.php',
    'NxProGA\\GuzzleHttp\\Psr7\\Stream' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/Psr7/Stream.php',
    'NxProGA\\GuzzleHttp\\Psr7\\StreamDecoratorTrait' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/Psr7/StreamDecoratorTrait.php',
    'NxProGA\\GuzzleHttp\\Psr7\\StreamWrapper' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/Psr7/StreamWrapper.php',
    'NxProGA\\GuzzleHttp\\Psr7\\UploadedFile' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/Psr7/UploadedFile.php',
    'NxProGA\\GuzzleHttp\\Psr7\\Uri' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/Psr7/Uri.php',
    'NxProGA\\GuzzleHttp\\Psr7\\UriComparator' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/Psr7/UriComparator.php',
    'NxProGA\\GuzzleHttp\\Psr7\\UriNormalizer' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/Psr7/UriNormalizer.php',
    'NxProGA\\GuzzleHttp\\Psr7\\UriResolver' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/Psr7/UriResolver.php',
    'NxProGA\\GuzzleHttp\\Psr7\\Utils' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/Psr7/Utils.php',
    'NxProGA\\GuzzleHttp\\RedirectMiddleware' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/RedirectMiddleware.php',
    'NxProGA\\GuzzleHttp\\RequestOptions' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/RequestOptions.php',
    'NxProGA\\GuzzleHttp\\RetryMiddleware' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/RetryMiddleware.php',
    'NxProGA\\GuzzleHttp\\TransferStats' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/TransferStats.php',
    'NxProGA\\GuzzleHttp\\UriTemplate' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/UriTemplate.php',
    'NxProGA\\GuzzleHttp\\Utils' => $baseDir . '/includes/Extensions/Google/lib/GuzzleHttp/Utils.php',
    'NxProGA\\Monolog\\ErrorHandler' => $baseDir . '/includes/Extensions/Google/lib/Monolog/ErrorHandler.php',
    'NxProGA\\Monolog\\Formatter\\ChromePHPFormatter' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Formatter/ChromePHPFormatter.php',
    'NxProGA\\Monolog\\Formatter\\ElasticaFormatter' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Formatter/ElasticaFormatter.php',
    'NxProGA\\Monolog\\Formatter\\FlowdockFormatter' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Formatter/FlowdockFormatter.php',
    'NxProGA\\Monolog\\Formatter\\FluentdFormatter' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Formatter/FluentdFormatter.php',
    'NxProGA\\Monolog\\Formatter\\FormatterInterface' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Formatter/FormatterInterface.php',
    'NxProGA\\Monolog\\Formatter\\GelfMessageFormatter' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Formatter/GelfMessageFormatter.php',
    'NxProGA\\Monolog\\Formatter\\HtmlFormatter' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Formatter/HtmlFormatter.php',
    'NxProGA\\Monolog\\Formatter\\JsonFormatter' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Formatter/JsonFormatter.php',
    'NxProGA\\Monolog\\Formatter\\LineFormatter' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Formatter/LineFormatter.php',
    'NxProGA\\Monolog\\Formatter\\LogglyFormatter' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Formatter/LogglyFormatter.php',
    'NxProGA\\Monolog\\Formatter\\LogstashFormatter' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Formatter/LogstashFormatter.php',
    'NxProGA\\Monolog\\Formatter\\MongoDBFormatter' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Formatter/MongoDBFormatter.php',
    'NxProGA\\Monolog\\Formatter\\NormalizerFormatter' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Formatter/NormalizerFormatter.php',
    'NxProGA\\Monolog\\Formatter\\ScalarFormatter' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Formatter/ScalarFormatter.php',
    'NxProGA\\Monolog\\Formatter\\WildfireFormatter' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Formatter/WildfireFormatter.php',
    'NxProGA\\Monolog\\Handler\\AbstractHandler' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Handler/AbstractHandler.php',
    'NxProGA\\Monolog\\Handler\\AbstractProcessingHandler' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Handler/AbstractProcessingHandler.php',
    'NxProGA\\Monolog\\Handler\\AbstractSyslogHandler' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Handler/AbstractSyslogHandler.php',
    'NxProGA\\Monolog\\Handler\\AmqpHandler' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Handler/AmqpHandler.php',
    'NxProGA\\Monolog\\Handler\\BrowserConsoleHandler' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Handler/BrowserConsoleHandler.php',
    'NxProGA\\Monolog\\Handler\\BufferHandler' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Handler/BufferHandler.php',
    'NxProGA\\Monolog\\Handler\\ChromePHPHandler' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Handler/ChromePHPHandler.php',
    'NxProGA\\Monolog\\Handler\\CouchDBHandler' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Handler/CouchDBHandler.php',
    'NxProGA\\Monolog\\Handler\\CubeHandler' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Handler/CubeHandler.php',
    'NxProGA\\Monolog\\Handler\\Curl\\Util' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Handler/Curl/Util.php',
    'NxProGA\\Monolog\\Handler\\DeduplicationHandler' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Handler/DeduplicationHandler.php',
    'NxProGA\\Monolog\\Handler\\DoctrineCouchDBHandler' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Handler/DoctrineCouchDBHandler.php',
    'NxProGA\\Monolog\\Handler\\DynamoDbHandler' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Handler/DynamoDbHandler.php',
    'NxProGA\\Monolog\\Handler\\ElasticSearchHandler' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Handler/ElasticSearchHandler.php',
    'NxProGA\\Monolog\\Handler\\ErrorLogHandler' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Handler/ErrorLogHandler.php',
    'NxProGA\\Monolog\\Handler\\FilterHandler' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Handler/FilterHandler.php',
    'NxProGA\\Monolog\\Handler\\FingersCrossedHandler' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Handler/FingersCrossedHandler.php',
    'NxProGA\\Monolog\\Handler\\FingersCrossed\\ActivationStrategyInterface' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Handler/FingersCrossed/ActivationStrategyInterface.php',
    'NxProGA\\Monolog\\Handler\\FingersCrossed\\ChannelLevelActivationStrategy' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Handler/FingersCrossed/ChannelLevelActivationStrategy.php',
    'NxProGA\\Monolog\\Handler\\FingersCrossed\\ErrorLevelActivationStrategy' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Handler/FingersCrossed/ErrorLevelActivationStrategy.php',
    'NxProGA\\Monolog\\Handler\\FirePHPHandler' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Handler/FirePHPHandler.php',
    'NxProGA\\Monolog\\Handler\\FleepHookHandler' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Handler/FleepHookHandler.php',
    'NxProGA\\Monolog\\Handler\\FlowdockHandler' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Handler/FlowdockHandler.php',
    'NxProGA\\Monolog\\Handler\\FormattableHandlerInterface' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Handler/FormattableHandlerInterface.php',
    'NxProGA\\Monolog\\Handler\\FormattableHandlerTrait' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Handler/FormattableHandlerTrait.php',
    'NxProGA\\Monolog\\Handler\\GelfHandler' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Handler/GelfHandler.php',
    'NxProGA\\Monolog\\Handler\\GroupHandler' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Handler/GroupHandler.php',
    'NxProGA\\Monolog\\Handler\\HandlerInterface' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Handler/HandlerInterface.php',
    'NxProGA\\Monolog\\Handler\\HandlerWrapper' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Handler/HandlerWrapper.php',
    'NxProGA\\Monolog\\Handler\\HipChatHandler' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Handler/HipChatHandler.php',
    'NxProGA\\Monolog\\Handler\\IFTTTHandler' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Handler/IFTTTHandler.php',
    'NxProGA\\Monolog\\Handler\\InsightOpsHandler' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Handler/InsightOpsHandler.php',
    'NxProGA\\Monolog\\Handler\\LogEntriesHandler' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Handler/LogEntriesHandler.php',
    'NxProGA\\Monolog\\Handler\\LogglyHandler' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Handler/LogglyHandler.php',
    'NxProGA\\Monolog\\Handler\\MailHandler' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Handler/MailHandler.php',
    'NxProGA\\Monolog\\Handler\\MandrillHandler' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Handler/MandrillHandler.php',
    'NxProGA\\Monolog\\Handler\\MissingExtensionException' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Handler/MissingExtensionException.php',
    'NxProGA\\Monolog\\Handler\\MongoDBHandler' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Handler/MongoDBHandler.php',
    'NxProGA\\Monolog\\Handler\\NativeMailerHandler' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Handler/NativeMailerHandler.php',
    'NxProGA\\Monolog\\Handler\\NewRelicHandler' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Handler/NewRelicHandler.php',
    'NxProGA\\Monolog\\Handler\\NullHandler' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Handler/NullHandler.php',
    'NxProGA\\Monolog\\Handler\\PHPConsoleHandler' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Handler/PHPConsoleHandler.php',
    'NxProGA\\Monolog\\Handler\\ProcessableHandlerInterface' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Handler/ProcessableHandlerInterface.php',
    'NxProGA\\Monolog\\Handler\\ProcessableHandlerTrait' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Handler/ProcessableHandlerTrait.php',
    'NxProGA\\Monolog\\Handler\\PsrHandler' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Handler/PsrHandler.php',
    'NxProGA\\Monolog\\Handler\\PushoverHandler' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Handler/PushoverHandler.php',
    'NxProGA\\Monolog\\Handler\\RavenHandler' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Handler/RavenHandler.php',
    'NxProGA\\Monolog\\Handler\\RedisHandler' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Handler/RedisHandler.php',
    'NxProGA\\Monolog\\Handler\\RollbarHandler' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Handler/RollbarHandler.php',
    'NxProGA\\Monolog\\Handler\\RotatingFileHandler' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Handler/RotatingFileHandler.php',
    'NxProGA\\Monolog\\Handler\\SamplingHandler' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Handler/SamplingHandler.php',
    'NxProGA\\Monolog\\Handler\\SlackHandler' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Handler/SlackHandler.php',
    'NxProGA\\Monolog\\Handler\\SlackWebhookHandler' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Handler/SlackWebhookHandler.php',
    'NxProGA\\Monolog\\Handler\\Slack\\SlackRecord' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Handler/Slack/SlackRecord.php',
    'NxProGA\\Monolog\\Handler\\SlackbotHandler' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Handler/SlackbotHandler.php',
    'NxProGA\\Monolog\\Handler\\SocketHandler' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Handler/SocketHandler.php',
    'NxProGA\\Monolog\\Handler\\StreamHandler' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Handler/StreamHandler.php',
    'NxProGA\\Monolog\\Handler\\SwiftMailerHandler' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Handler/SwiftMailerHandler.php',
    'NxProGA\\Monolog\\Handler\\SyslogHandler' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Handler/SyslogHandler.php',
    'NxProGA\\Monolog\\Handler\\SyslogUdpHandler' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Handler/SyslogUdpHandler.php',
    'NxProGA\\Monolog\\Handler\\SyslogUdp\\UdpSocket' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Handler/SyslogUdp/UdpSocket.php',
    'NxProGA\\Monolog\\Handler\\TestHandler' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Handler/TestHandler.php',
    'NxProGA\\Monolog\\Handler\\WhatFailureGroupHandler' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Handler/WhatFailureGroupHandler.php',
    'NxProGA\\Monolog\\Handler\\ZendMonitorHandler' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Handler/ZendMonitorHandler.php',
    'NxProGA\\Monolog\\Logger' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Logger.php',
    'NxProGA\\Monolog\\Processor\\GitProcessor' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Processor/GitProcessor.php',
    'NxProGA\\Monolog\\Processor\\IntrospectionProcessor' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Processor/IntrospectionProcessor.php',
    'NxProGA\\Monolog\\Processor\\MemoryPeakUsageProcessor' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Processor/MemoryPeakUsageProcessor.php',
    'NxProGA\\Monolog\\Processor\\MemoryProcessor' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Processor/MemoryProcessor.php',
    'NxProGA\\Monolog\\Processor\\MemoryUsageProcessor' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Processor/MemoryUsageProcessor.php',
    'NxProGA\\Monolog\\Processor\\MercurialProcessor' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Processor/MercurialProcessor.php',
    'NxProGA\\Monolog\\Processor\\ProcessIdProcessor' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Processor/ProcessIdProcessor.php',
    'NxProGA\\Monolog\\Processor\\ProcessorInterface' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Processor/ProcessorInterface.php',
    'NxProGA\\Monolog\\Processor\\PsrLogMessageProcessor' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Processor/PsrLogMessageProcessor.php',
    'NxProGA\\Monolog\\Processor\\TagProcessor' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Processor/TagProcessor.php',
    'NxProGA\\Monolog\\Processor\\UidProcessor' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Processor/UidProcessor.php',
    'NxProGA\\Monolog\\Processor\\WebProcessor' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Processor/WebProcessor.php',
    'NxProGA\\Monolog\\Registry' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Registry.php',
    'NxProGA\\Monolog\\ResettableInterface' => $baseDir . '/includes/Extensions/Google/lib/Monolog/ResettableInterface.php',
    'NxProGA\\Monolog\\SignalHandler' => $baseDir . '/includes/Extensions/Google/lib/Monolog/SignalHandler.php',
    'NxProGA\\Monolog\\Utils' => $baseDir . '/includes/Extensions/Google/lib/Monolog/Utils.php',
    'NxProGA\\Psr\\Cache\\CacheException' => $baseDir . '/includes/Extensions/Google/lib/Psr/Cache/CacheException.php',
    'NxProGA\\Psr\\Cache\\CacheItemInterface' => $baseDir . '/includes/Extensions/Google/lib/Psr/Cache/CacheItemInterface.php',
    'NxProGA\\Psr\\Cache\\CacheItemPoolInterface' => $baseDir . '/includes/Extensions/Google/lib/Psr/Cache/CacheItemPoolInterface.php',
    'NxProGA\\Psr\\Cache\\InvalidArgumentException' => $baseDir . '/includes/Extensions/Google/lib/Psr/Cache/InvalidArgumentException.php',
    'NxProGA\\Psr\\Http\\Message\\MessageInterface' => $baseDir . '/includes/Extensions/Google/lib/Psr/Http/Message/MessageInterface.php',
    'NxProGA\\Psr\\Http\\Message\\RequestInterface' => $baseDir . '/includes/Extensions/Google/lib/Psr/Http/Message/RequestInterface.php',
    'NxProGA\\Psr\\Http\\Message\\ResponseInterface' => $baseDir . '/includes/Extensions/Google/lib/Psr/Http/Message/ResponseInterface.php',
    'NxProGA\\Psr\\Http\\Message\\ServerRequestInterface' => $baseDir . '/includes/Extensions/Google/lib/Psr/Http/Message/ServerRequestInterface.php',
    'NxProGA\\Psr\\Http\\Message\\StreamInterface' => $baseDir . '/includes/Extensions/Google/lib/Psr/Http/Message/StreamInterface.php',
    'NxProGA\\Psr\\Http\\Message\\UploadedFileInterface' => $baseDir . '/includes/Extensions/Google/lib/Psr/Http/Message/UploadedFileInterface.php',
    'NxProGA\\Psr\\Http\\Message\\UriInterface' => $baseDir . '/includes/Extensions/Google/lib/Psr/Http/Message/UriInterface.php',
    'NxProGA\\Psr\\Log\\AbstractLogger' => $baseDir . '/includes/Extensions/Google/lib/Psr/Log/AbstractLogger.php',
    'NxProGA\\Psr\\Log\\InvalidArgumentException' => $baseDir . '/includes/Extensions/Google/lib/Psr/Log/InvalidArgumentException.php',
    'NxProGA\\Psr\\Log\\LogLevel' => $baseDir . '/includes/Extensions/Google/lib/Psr/Log/LogLevel.php',
    'NxProGA\\Psr\\Log\\LoggerAwareInterface' => $baseDir . '/includes/Extensions/Google/lib/Psr/Log/LoggerAwareInterface.php',
    'NxProGA\\Psr\\Log\\LoggerAwareTrait' => $baseDir . '/includes/Extensions/Google/lib/Psr/Log/LoggerAwareTrait.php',
    'NxProGA\\Psr\\Log\\LoggerInterface' => $baseDir . '/includes/Extensions/Google/lib/Psr/Log/LoggerInterface.php',
    'NxProGA\\Psr\\Log\\LoggerTrait' => $baseDir . '/includes/Extensions/Google/lib/Psr/Log/LoggerTrait.php',
    'NxProGA\\Psr\\Log\\NullLogger' => $baseDir . '/includes/Extensions/Google/lib/Psr/Log/NullLogger.php',
    'NxProGA\\Psr\\Log\\Test\\DummyTest' => $baseDir . '/includes/Extensions/Google/lib/Psr/Log/Test/DummyTest.php',
    'NxProGA\\Psr\\Log\\Test\\LoggerInterfaceTest' => $baseDir . '/includes/Extensions/Google/lib/Psr/Log/Test/LoggerInterfaceTest.php',
    'NxProGA\\Psr\\Log\\Test\\TestLogger' => $baseDir . '/includes/Extensions/Google/lib/Psr/Log/Test/TestLogger.php',
    'NxProGA\\Symfony\\Polyfill\\Intl\\Idn\\Idn' => $baseDir . '/includes/Extensions/Google/lib/Symfony/Polyfill/Intl/Idn/Idn.php',
    'NxProGA\\Symfony\\Polyfill\\Intl\\Idn\\Info' => $baseDir . '/includes/Extensions/Google/lib/Symfony/Polyfill/Intl/Idn/Info.php',
    'NxProGA\\Symfony\\Polyfill\\Intl\\Idn\\Resources\\unidata\\DisallowedRanges' => $baseDir . '/includes/Extensions/Google/lib/Symfony/Polyfill/Intl/Idn/Resources/unidata/DisallowedRanges.php',
    'NxProGA\\Symfony\\Polyfill\\Intl\\Idn\\Resources\\unidata\\Regex' => $baseDir . '/includes/Extensions/Google/lib/Symfony/Polyfill/Intl/Idn/Resources/unidata/Regex.php',
    'NxProGA\\Symfony\\Polyfill\\Intl\\Normalizer\\Normalizer' => $baseDir . '/includes/Extensions/Google/lib/Symfony/Polyfill/Intl/Normalizer/Normalizer.php',
    'NxProGA\\Symfony\\Polyfill\\Php70\\Php70' => $baseDir . '/includes/Extensions/Google/lib/Symfony/Polyfill/Php70/Php70.php',
    'NxProGA\\Symfony\\Polyfill\\Php72\\Php72' => $baseDir . '/includes/Extensions/Google/lib/Symfony/Polyfill/Php72/Php72.php',
    'NxProGA\\phpseclib\\Crypt\\AES' => $baseDir . '/includes/Extensions/Google/lib/phpseclib/Crypt/AES.php',
    'NxProGA\\phpseclib\\Crypt\\Base' => $baseDir . '/includes/Extensions/Google/lib/phpseclib/Crypt/Base.php',
    'NxProGA\\phpseclib\\Crypt\\Blowfish' => $baseDir . '/includes/Extensions/Google/lib/phpseclib/Crypt/Blowfish.php',
    'NxProGA\\phpseclib\\Crypt\\DES' => $baseDir . '/includes/Extensions/Google/lib/phpseclib/Crypt/DES.php',
    'NxProGA\\phpseclib\\Crypt\\Hash' => $baseDir . '/includes/Extensions/Google/lib/phpseclib/Crypt/Hash.php',
    'NxProGA\\phpseclib\\Crypt\\RC2' => $baseDir . '/includes/Extensions/Google/lib/phpseclib/Crypt/RC2.php',
    'NxProGA\\phpseclib\\Crypt\\RC4' => $baseDir . '/includes/Extensions/Google/lib/phpseclib/Crypt/RC4.php',
    'NxProGA\\phpseclib\\Crypt\\RSA' => $baseDir . '/includes/Extensions/Google/lib/phpseclib/Crypt/RSA.php',
    'NxProGA\\phpseclib\\Crypt\\Random' => $baseDir . '/includes/Extensions/Google/lib/phpseclib/Crypt/Random.php',
    'NxProGA\\phpseclib\\Crypt\\Rijndael' => $baseDir . '/includes/Extensions/Google/lib/phpseclib/Crypt/Rijndael.php',
    'NxProGA\\phpseclib\\Crypt\\TripleDES' => $baseDir . '/includes/Extensions/Google/lib/phpseclib/Crypt/TripleDES.php',
    'NxProGA\\phpseclib\\Crypt\\Twofish' => $baseDir . '/includes/Extensions/Google/lib/phpseclib/Crypt/Twofish.php',
    'NxProGA\\phpseclib\\File\\ANSI' => $baseDir . '/includes/Extensions/Google/lib/phpseclib/File/ANSI.php',
    'NxProGA\\phpseclib\\File\\ASN1' => $baseDir . '/includes/Extensions/Google/lib/phpseclib/File/ASN1.php',
    'NxProGA\\phpseclib\\File\\ASN1\\Element' => $baseDir . '/includes/Extensions/Google/lib/phpseclib/File/ASN1/Element.php',
    'NxProGA\\phpseclib\\File\\X509' => $baseDir . '/includes/Extensions/Google/lib/phpseclib/File/X509.php',
    'NxProGA\\phpseclib\\Math\\BigInteger' => $baseDir . '/includes/Extensions/Google/lib/phpseclib/Math/BigInteger.php',
    'NxProGA\\phpseclib\\Net\\SCP' => $baseDir . '/includes/Extensions/Google/lib/phpseclib/Net/SCP.php',
    'NxProGA\\phpseclib\\Net\\SFTP' => $baseDir . '/includes/Extensions/Google/lib/phpseclib/Net/SFTP.php',
    'NxProGA\\phpseclib\\Net\\SFTP\\Stream' => $baseDir . '/includes/Extensions/Google/lib/phpseclib/Net/SFTP/Stream.php',
    'NxProGA\\phpseclib\\Net\\SSH1' => $baseDir . '/includes/Extensions/Google/lib/phpseclib/Net/SSH1.php',
    'NxProGA\\phpseclib\\Net\\SSH2' => $baseDir . '/includes/Extensions/Google/lib/phpseclib/Net/SSH2.php',
    'NxProGA\\phpseclib\\System\\SSH\\Agent' => $baseDir . '/includes/Extensions/Google/lib/phpseclib/System/SSH/Agent.php',
    'NxProGA\\phpseclib\\System\\SSH\\Agent\\Identity' => $baseDir . '/includes/Extensions/Google/lib/phpseclib/System/SSH/Agent/Identity.php',
    'NxProGA_NxProGA_ArithmeticError' => $baseDir . '/includes/Extensions/Google/classes/symfony/polyfill-php70/Resources/stubs/ArithmeticError.php',
    'NxProGA_NxProGA_AssertionError' => $baseDir . '/includes/Extensions/Google/classes/symfony/polyfill-php70/Resources/stubs/AssertionError.php',
    'NxProGA_NxProGA_DivisionByZeroError' => $baseDir . '/includes/Extensions/Google/classes/symfony/polyfill-php70/Resources/stubs/DivisionByZeroError.php',
    'NxProGA_NxProGA_Error' => $baseDir . '/includes/Extensions/Google/classes/symfony/polyfill-php70/Resources/stubs/Error.php',
    'NxProGA_NxProGA_Normalizer' => $baseDir . '/includes/Extensions/Google/classes/symfony/polyfill-intl-normalizer/Resources/stubs/Normalizer.php',
    'NxProGA_NxProGA_ParseError' => $baseDir . '/includes/Extensions/Google/classes/symfony/polyfill-php70/Resources/stubs/ParseError.php',
    'NxProGA_NxProGA_SessionUpdateTimestampHandlerInterface' => $baseDir . '/includes/Extensions/Google/classes/symfony/polyfill-php70/Resources/stubs/SessionUpdateTimestampHandlerInterface.php',
    'NxProGA_NxProGA_TypeError' => $baseDir . '/includes/Extensions/Google/classes/symfony/polyfill-php70/Resources/stubs/TypeError.php',
    'ParseError' => $baseDir . '/includes/Extensions/Google/lib/Symfony/Polyfill/Php70/Resources/stubs/ParseError.php',
    'SessionUpdateTimestampHandlerInterface' => $baseDir . '/includes/Extensions/Google/lib/Symfony/Polyfill/Php70/Resources/stubs/SessionUpdateTimestampHandlerInterface.php',
    'TypeError' => $baseDir . '/includes/Extensions/Google/lib/Symfony/Polyfill/Php70/Resources/stubs/TypeError.php',
);
