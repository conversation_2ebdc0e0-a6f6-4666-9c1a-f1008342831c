(globalThis.webpackChunknotificationx=globalThis.webpackChunknotificationx||[]).push([[4166],{9846:function(s,e,a){!function(s){"use strict";function e(s,e,a,i){var n={s:["viensas secunds","'iensas secunds"],ss:[s+" secunds",s+" secunds"],m:["'n míut","'iens míut"],mm:[s+" míuts",s+" míuts"],h:["'n þora","'iensa þora"],hh:[s+" þoras",s+" þoras"],d:["'n ziua","'iensa ziua"],dd:[s+" ziuas",s+" ziuas"],M:["'n mes","'iens mes"],MM:[s+" mesen",s+" mesen"],y:["'n ar","'iens ar"],yy:[s+" ars",s+" ars"]};return i||e?n[a][0]:n[a][1]}s.define<PERSON>ale("tzl",{months:"Januar_Fevraglh_Març_Avrïu_Mai_Gün_Julia_Guscht_Setemvar_Listopäts_Noemvar_Zecemvar".split("_"),monthsShort:"Jan_Fev_Mar_Avr_Mai_Gün_Jul_Gus_Set_Lis_Noe_Zec".split("_"),weekdays:"Súladi_Lúneçi_Maitzi_Márcuri_Xhúadi_Viénerçi_Sáturi".split("_"),weekdaysShort:"Súl_Lún_Mai_Már_Xhú_Vié_Sát".split("_"),weekdaysMin:"Sú_Lú_Ma_Má_Xh_Vi_Sá".split("_"),longDateFormat:{LT:"HH.mm",LTS:"HH.mm.ss",L:"DD.MM.YYYY",LL:"D. MMMM [dallas] YYYY",LLL:"D. MMMM [dallas] YYYY HH.mm",LLLL:"dddd, [li] D. MMMM [dallas] YYYY HH.mm"},meridiemParse:/d\'o|d\'a/i,isPM:function(s){return"d'o"===s.toLowerCase()},meridiem:function(s,e,a){return s>11?a?"d'o":"D'O":a?"d'a":"D'A"},calendar:{sameDay:"[oxhi à] LT",nextDay:"[demà à] LT",nextWeek:"dddd [à] LT",lastDay:"[ieiri à] LT",lastWeek:"[sür el] dddd [lasteu à] LT",sameElse:"L"},relativeTime:{future:"osprei %s",past:"ja%s",s:e,ss:e,m:e,mm:e,h:e,hh:e,d:e,dd:e,M:e,MM:e,y:e,yy:e},dayOfMonthOrdinalParse:/\d{1,2}\./,ordinal:"%d.",week:{dow:1,doy:4}})}(a(5093))}}]);