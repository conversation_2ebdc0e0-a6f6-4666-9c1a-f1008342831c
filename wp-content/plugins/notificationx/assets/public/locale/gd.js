(globalThis.webpackChunknotificationx=globalThis.webpackChunknotificationx||[]).push([[3281],{217:function(a,i,n){!function(a){"use strict";a.defineLocale("gd",{months:["<PERSON>","<PERSON> Gearran","Am M<PERSON>","An G<PERSON>an","An Cè<PERSON>an","An t-Ògmhios","An t-Iuchar","An Lùnastal","An t-Sultain","An Dàmhair","An t-Samhain","An Dùbhlachd"],monthsShort:["<PERSON>ao<PERSON>","<PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","Ògmh","Iuch","<PERSON>ùn","<PERSON><PERSON>","<PERSON>àmh","<PERSON>h","<PERSON>ùbh"],monthsParseExact:!0,weekdays:["Didòmhnaich","Di<PERSON>ain","<PERSON><PERSON><PERSON>irt","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>"],weekdaysShort:["Did","Dil","<PERSON><PERSON>","Di<PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","Di<PERSON>"],weekdaysMin:["<PERSON><PERSON>","<PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","Ar","Ha","Sa"],longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd, D MMMM YYYY HH:mm"},calendar:{sameDay:"[An-diugh aig] LT",nextDay:"[A-màireach aig] LT",nextWeek:"dddd [aig] LT",lastDay:"[An-dè aig] LT",lastWeek:"dddd [seo chaidh] [aig] LT",sameElse:"L"},relativeTime:{future:"ann an %s",past:"bho chionn %s",s:"beagan diogan",ss:"%d diogan",m:"mionaid",mm:"%d mionaidean",h:"uair",hh:"%d uairean",d:"latha",dd:"%d latha",M:"mìos",MM:"%d mìosan",y:"bliadhna",yy:"%d bliadhna"},dayOfMonthOrdinalParse:/\d{1,2}(d|na|mh)/,ordinal:function(a){return a+(1===a?"d":a%10==2?"na":"mh")},week:{dow:1,doy:4}})}(n(5093))}}]);