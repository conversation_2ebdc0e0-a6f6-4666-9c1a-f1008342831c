(globalThis.webpackChunknotificationx=globalThis.webpackChunknotificationx||[]).push([[8861],{1713:function(_,e,t){!function(_){"use strict";_.defineLocale("he",{months:"ינואר_פברואר_מרץ_אפריל_מאי_יוני_יולי_אוגוסט_ספטמבר_אוקטובר_נובמבר_דצמבר".split("_"),monthsShort:"ינו׳_פבר׳_מרץ_אפר׳_מאי_יוני_יולי_אוג׳_ספט׳_אוק׳_נוב׳_דצמ׳".split("_"),weekdays:"ראשון_שני_שלישי_רביעי_חמישי_שישי_שבת".split("_"),weekdaysShort:"א׳_ב׳_ג׳_ד׳_ה׳_ו׳_ש׳".split("_"),weekdaysMin:"א_ב_ג_ד_ה_ו_ש".split("_"),longDateFormat:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D [ב]MMMM YYYY",LLL:"D [ב]MMMM YYYY HH:mm",LLLL:"dddd, D [ב]MMMM YYYY HH:mm",l:"D/M/YYYY",ll:"D MMM YYYY",lll:"D MMM YYYY HH:mm",llll:"ddd, D MMM YYYY HH:mm"},calendar:{sameDay:"[היום ב־]LT",nextDay:"[מחר ב־]LT",nextWeek:"dddd [בשעה] LT",lastDay:"[אתמול ב־]LT",lastWeek:"[ביום] dddd [האחרון בשעה] LT",sameElse:"L"},relativeTime:{future:"בעוד %s",past:"לפני %s",s:"מספר שניות",ss:"%d שניות",m:"דקה",mm:"%d דקות",h:"שעה",hh:function(_){return 2===_?"שעתיים":_+" שעות"},d:"יום",dd:function(_){return 2===_?"יומיים":_+" ימים"},M:"חודש",MM:function(_){return 2===_?"חודשיים":_+" חודשים"},y:"שנה",yy:function(_){return 2===_?"שנתיים":_%10==0&&10!==_?_+" שנה":_+" שנים"}},meridiemParse:/אחה"צ|לפנה"צ|אחרי הצהריים|לפני הצהריים|לפנות בוקר|בבוקר|בערב/i,isPM:function(_){return/^(אחה"צ|אחרי הצהריים|בערב)$/.test(_)},meridiem:function(_,e,t){return _<5?"לפנות בוקר":_<10?"בבוקר":_<12?t?'לפנה"צ':"לפני הצהריים":_<18?t?'אחה"צ':"אחרי הצהריים":"בערב"}})}(t(5093))}}]);