"use strict";(globalThis.webpackChunknotificationx=globalThis.webpackChunknotificationx||[]).push([[223],{5223:(e,t,l)=>{l.r(t),l.d(t,{default:()=>a});var r=l(1609),o=l.n(r),n=l(7723);const a=function(e){var t=e.offer_discount,l=e.link_text,a=e.link_button_bg_color,i=e.link_button_text_color,c=e.announcementCSS,d=(0,r.useRef)();return o().createElement("svg",{width:"92",height:"98",viewBox:"0 0 92 98",fill:"none",xmlns:"http://www.w3.org/2000/svg"},o().createElement("path",{d:"M10 0H82V78C82 84.6274 76.6274 90 70 90H22C15.3726 90 10 84.6274 10 78V0Z",fill:(null==c?void 0:c.discountBackground)?null==c?void 0:c.discountBackground:"#4F19CD"}),o().createElement("path",{d:"M82 0L87 5L92 10H82V0Z",fill:(null==c?void 0:c.discountBackground)?null==c?void 0:c.discountBackground:"#806FF6"}),o().createElement("path",{d:"M10 0L5 5L0 10H10V0Z",fill:(null==c?void 0:c.discountBackground)?null==c?void 0:c.discountBackground:"#806FF6"}),o().createElement("g",null,o().createElement("text",{ref:d,xmlSpace:"preserve",style:{whiteSpace:"pre"},fontFamily:"DM Sans",fontSize:"24",fontWeight:"bold",letterSpacing:"0em",fill:(null==c?void 0:c.discountTextColor)?null==c?void 0:c.discountTextColor:"#fff"},o().createElement("tspan",{x:"16",y:"53.548"},t,o().createElement("tspan",{fontSize:"14"},"%")))),o().createElement("g",{filter:"url(#filter1_d_620_42)"},o().createElement("text",{xmlSpace:"preserve",style:{whiteSpace:"pre"},fontFamily:"DM Sans",fontSize:"16",fontWeight:"bold",letterSpacing:"0em",fill:(null==c?void 0:c.discountTextColor)?null==c?void 0:c.discountTextColor:"#fff"},o().createElement("tspan",{x:"37",y:"73.456"},(0,n.__)("OFF","notificationx")))),o().createElement("rect",{x:"13",y:"3",width:"66",height:"17",rx:"2",fill:(null==c?void 0:c.linkButtonBgColor)?null==c?void 0:c.linkButtonBgColor:"#806FF6"}),o().createElement("g",{filter:"url(#filter2_d_620_42)"},o().createElement("text",{fill:(null==c?void 0:c.linkButtonTextColor)?null==c?void 0:c.linkButtonTextColor:"#fff",xmlSpace:"preserve",style:{whiteSpace:"pre",backgroundColor:a,color:i},fontFamily:"DM Sans",fontSize:"10",fontWeight:"500",letterSpacing:"0em"},o().createElement("tspan",{x:"22.709",y:"14"},l))),o().createElement("defs",null,o().createElement("filter",{id:"filter0_d_620_42",x:"21.428",y:"34.064",width:"45.2434",height:"21.272",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},o().createElement("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),o().createElement("feColorMatrix",{in:"SourceAlpha",type:"matrix",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),o().createElement("feOffset",{dy:"1"}),o().createElement("feComposite",{in2:"hardAlpha",operator:"out"}),o().createElement("feColorMatrix",{type:"matrix",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"}),o().createElement("feBlend",{mode:"normal",in2:"BackgroundImageFix",result:"effect1_dropShadow_620_42"}),o().createElement("feBlend",{mode:"normal",in:"SourceGraphic",in2:"effect1_dropShadow_620_42",result:"shape"})),o().createElement("filter",{id:"filter1_d_620_42",x:"37.72",y:"61.608",width:"29.0688",height:"12.584",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},o().createElement("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),o().createElement("feColorMatrix",{in:"SourceAlpha",type:"matrix",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),o().createElement("feOffset",{dy:"1"}),o().createElement("feComposite",{in2:"hardAlpha",operator:"out"}),o().createElement("feColorMatrix",{type:"matrix",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"}),o().createElement("feBlend",{mode:"normal",in2:"BackgroundImageFix",result:"effect1_dropShadow_620_42"}),o().createElement("feBlend",{mode:"normal",in:"SourceGraphic",in2:"effect1_dropShadow_620_42",result:"shape"})),o().createElement("filter",{id:"filter2_d_620_42",x:"23.1689",y:"6.8",width:"45.9189",height:"8.31999",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},o().createElement("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),o().createElement("feColorMatrix",{in:"SourceAlpha",type:"matrix",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),o().createElement("feOffset",{dy:"1"}),o().createElement("feComposite",{in2:"hardAlpha",operator:"out"}),o().createElement("feColorMatrix",{type:"matrix",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"}),o().createElement("feBlend",{mode:"normal",in2:"BackgroundImageFix",result:"effect1_dropShadow_620_42"}),o().createElement("feBlend",{mode:"normal",in:"SourceGraphic",in2:"effect1_dropShadow_620_42",result:"shape"}))))}}}]);