<?php
namespace Elementor;

use <PERSON><PERSON>or\Core\Upgrade\Manager as Upgrade_Manager;

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

/**
 * Elementor fonts.
 *
 * Elementor fonts handler class is responsible for registering the supported
 * fonts used by Elementor.
 *
 * @since 1.0.0
 */
class Fonts {

	/**
	 * The system font name.
	 */
	const SYSTEM = 'system';

	/**
	 * The google font name.
	 */
	const GOOGLE = 'googlefonts';

	/**
	 * The google early access font name.
	 */
	const EARLYACCESS = 'earlyaccess';

	/**
	 * The local font name.
	 */
	const LOCAL = 'local';

	private static $fonts;

	/**
	 * Font groups.
	 *
	 * Used to hold font types/groups.
	 *
	 * @since 1.9.4
	 * @access private
	 * @static
	 *
	 * @var null|array
	 */
	private static $font_groups;

	private static $is_google_fonts_enabled = null;

	/**
	 * Get font Groups.
	 *
	 * Retrieve the list of font groups.
	 *
	 * @since 1.9.4
	 * @access public
	 * @static
	 *
	 * @return array Supported font groups/types.
	 */
	public static function get_font_groups() {
		if ( null === self::$font_groups ) {
			$font_groups = [
				self::SYSTEM => esc_html__( 'System', 'elementor' ),
			];

			if ( static::is_google_fonts_enabled() ) {
				$font_groups = array_merge( $font_groups, [
					self::GOOGLE => esc_html__( 'Google', 'elementor' ),
					self::EARLYACCESS => esc_html__( 'Google (Early Access)', 'elementor' ),
				] );
			}

			/**
			 * Font groups.
			 *
			 * Filters the fonts groups used by Elementor.
			 *
			 * @since 1.9.4
			 *
			 * @param array $font_groups Font groups.
			 */
			$font_groups = apply_filters( 'elementor/fonts/groups', $font_groups );

			self::$font_groups = $font_groups;
		}

		return self::$font_groups;
	}

	/**
	 * Get fonts.
	 *
	 * Retrieve the list of supported fonts.
	 *
	 * @since 1.0.0
	 * @access public
	 * @static
	 *
	 * @return array Supported fonts.
	 */
	public static function get_fonts() {
		if ( null === self::$fonts ) {
			$additional_fonts = [];

			/**
			 * Additional fonts.
			 *
			 * Filters the fonts used by Elementor to add additional fonts.
			 *
			 * @since 1.9.4
			 *
			 * @param array $additional_fonts Additional Elementor fonts.
			 */
			$additional_fonts = apply_filters( 'elementor/fonts/additional_fonts', $additional_fonts );

			self::$fonts = array_replace( self::get_native_fonts(), $additional_fonts );
		}

		return self::$fonts;
	}

	/**
	 * Get Elementor native fonts.
	 *
	 * Retrieve the list of supported fonts.
	 *
	 * @since 1.9.4
	 * @access private
	 * @static
	 *
	 * @return array Supported fonts.
	 */
	private static function get_native_fonts() {
		$fonts = [
			// System fonts.
			'Arial' => self::SYSTEM,
			'Tahoma' => self::SYSTEM,
			'Verdana' => self::SYSTEM,
			'Helvetica' => self::SYSTEM,
			'Times New Roman' => self::SYSTEM,
			'Trebuchet MS' => self::SYSTEM,
			'Georgia' => self::SYSTEM,
		];

		if ( static::is_google_fonts_enabled() ) {
			$fonts = array_merge( $fonts, [
				// Google Fonts (last update: 05/05/2024).
				'ABeeZee' => self::GOOGLE,
				'ADLaM Display' => self::GOOGLE,
				'AR One Sans' => self::GOOGLE,
				'Abel' => self::GOOGLE,
				'Abhaya Libre' => self::GOOGLE,
				'Aboreto' => self::GOOGLE,
				'Abril Fatface' => self::GOOGLE,
				'Abyssinica SIL' => self::GOOGLE,
				'Aclonica' => self::GOOGLE,
				'Acme' => self::GOOGLE,
				'Actor' => self::GOOGLE,
				'Adamina' => self::GOOGLE,
				'Advent Pro' => self::GOOGLE,
				'Afacad' => self::GOOGLE,
				'Agbalumo' => self::GOOGLE,
				'Agdasima' => self::GOOGLE,
				'Aguafina Script' => self::GOOGLE,
				'Akatab' => self::GOOGLE,
				'Akaya Kanadaka' => self::GOOGLE,
				'Akaya Telivigala' => self::GOOGLE,
				'Akronim' => self::GOOGLE,
				'Akshar' => self::GOOGLE,
				'Aladin' => self::GOOGLE,
				'Alata' => self::GOOGLE,
				'Alatsi' => self::GOOGLE,
				'Albert Sans' => self::GOOGLE,
				'Aldrich' => self::GOOGLE,
				'Alef' => self::GOOGLE,
				'Alef Hebrew' => self::EARLYACCESS, // Hack for Google Early Access.
				'Alegreya' => self::GOOGLE,
				'Alegreya SC' => self::GOOGLE,
				'Alegreya Sans' => self::GOOGLE,
				'Alegreya Sans SC' => self::GOOGLE,
				'Aleo' => self::GOOGLE,
				'Alex Brush' => self::GOOGLE,
				'Alexandria' => self::GOOGLE,
				'Alfa Slab One' => self::GOOGLE,
				'Alice' => self::GOOGLE,
				'Alike' => self::GOOGLE,
				'Alike Angular' => self::GOOGLE,
				'Alkalami' => self::GOOGLE,
				'Alkatra' => self::GOOGLE,
				'Allan' => self::GOOGLE,
				'Allerta' => self::GOOGLE,
				'Allerta Stencil' => self::GOOGLE,
				'Allison' => self::GOOGLE,
				'Allura' => self::GOOGLE,
				'Almarai' => self::GOOGLE,
				'Almendra' => self::GOOGLE,
				'Almendra Display' => self::GOOGLE,
				'Almendra SC' => self::GOOGLE,
				'Alumni Sans' => self::GOOGLE,
				'Alumni Sans Collegiate One' => self::GOOGLE,
				'Alumni Sans Inline One' => self::GOOGLE,
				'Alumni Sans Pinstripe' => self::GOOGLE,
				'Amarante' => self::GOOGLE,
				'Amaranth' => self::GOOGLE,
				'Amatic SC' => self::GOOGLE,
				'Amethysta' => self::GOOGLE,
				'Amiko' => self::GOOGLE,
				'Amiri' => self::GOOGLE,
				'Amiri Quran' => self::GOOGLE,
				'Amita' => self::GOOGLE,
				'Anaheim' => self::GOOGLE,
				'Andada Pro' => self::GOOGLE,
				'Andika' => self::GOOGLE,
				'Anek Bangla' => self::GOOGLE,
				'Anek Devanagari' => self::GOOGLE,
				'Anek Gujarati' => self::GOOGLE,
				'Anek Gurmukhi' => self::GOOGLE,
				'Anek Kannada' => self::GOOGLE,
				'Anek Latin' => self::GOOGLE,
				'Anek Malayalam' => self::GOOGLE,
				'Anek Odia' => self::GOOGLE,
				'Anek Tamil' => self::GOOGLE,
				'Anek Telugu' => self::GOOGLE,
				'Angkor' => self::GOOGLE,
				'Annapurna SIL' => self::GOOGLE,
				'Annie Use Your Telescope' => self::GOOGLE,
				'Anonymous Pro' => self::GOOGLE,
				'Anta' => self::GOOGLE,
				'Antic' => self::GOOGLE,
				'Antic Didone' => self::GOOGLE,
				'Antic Slab' => self::GOOGLE,
				'Anton' => self::GOOGLE,
				'Antonio' => self::GOOGLE,
				'Anuphan' => self::GOOGLE,
				'Anybody' => self::GOOGLE,
				'Aoboshi One' => self::GOOGLE,
				'Arapey' => self::GOOGLE,
				'Arbutus' => self::GOOGLE,
				'Arbutus Slab' => self::GOOGLE,
				'Architects Daughter' => self::GOOGLE,
				'Archivo' => self::GOOGLE,
				'Archivo Black' => self::GOOGLE,
				'Archivo Narrow' => self::GOOGLE,
				'Are You Serious' => self::GOOGLE,
				'Aref Ruqaa' => self::GOOGLE,
				'Aref Ruqaa Ink' => self::GOOGLE,
				'Arima' => self::GOOGLE,
				'Arimo' => self::GOOGLE,
				'Arizonia' => self::GOOGLE,
				'Armata' => self::GOOGLE,
				'Arsenal' => self::GOOGLE,
				'Artifika' => self::GOOGLE,
				'Arvo' => self::GOOGLE,
				'Arya' => self::GOOGLE,
				'Asap' => self::GOOGLE,
				'Asap Condensed' => self::GOOGLE,
				'Asar' => self::GOOGLE,
				'Asset' => self::GOOGLE,
				'Assistant' => self::GOOGLE,
				'Astloch' => self::GOOGLE,
				'Asul' => self::GOOGLE,
				'Athiti' => self::GOOGLE,
				'Atkinson Hyperlegible' => self::GOOGLE,
				'Atma' => self::GOOGLE,
				'Atomic Age' => self::GOOGLE,
				'Aubrey' => self::GOOGLE,
				'Audiowide' => self::GOOGLE,
				'Autour One' => self::GOOGLE,
				'Average' => self::GOOGLE,
				'Average Sans' => self::GOOGLE,
				'Averia Gruesa Libre' => self::GOOGLE,
				'Averia Libre' => self::GOOGLE,
				'Averia Sans Libre' => self::GOOGLE,
				'Averia Serif Libre' => self::GOOGLE,
				'Azeret Mono' => self::GOOGLE,
				'B612' => self::GOOGLE,
				'B612 Mono' => self::GOOGLE,
				'BIZ UDGothic' => self::GOOGLE,
				'BIZ UDMincho' => self::GOOGLE,
				'BIZ UDPGothic' => self::GOOGLE,
				'BIZ UDPMincho' => self::GOOGLE,
				'Babylonica' => self::GOOGLE,
				'Bacasime Antique' => self::GOOGLE,
				'Bad Script' => self::GOOGLE,
				'Bagel Fat One' => self::GOOGLE,
				'Bahiana' => self::GOOGLE,
				'Bahianita' => self::GOOGLE,
				'Bai Jamjuree' => self::GOOGLE,
				'Bakbak One' => self::GOOGLE,
				'Ballet' => self::GOOGLE,
				'Baloo 2' => self::GOOGLE,
				'Baloo Bhai 2' => self::GOOGLE,
				'Baloo Bhaijaan 2' => self::GOOGLE,
				'Baloo Bhaina 2' => self::GOOGLE,
				'Baloo Chettan 2' => self::GOOGLE,
				'Baloo Da 2' => self::GOOGLE,
				'Baloo Paaji 2' => self::GOOGLE,
				'Baloo Tamma 2' => self::GOOGLE,
				'Baloo Tammudu 2' => self::GOOGLE,
				'Baloo Thambi 2' => self::GOOGLE,
				'Balsamiq Sans' => self::GOOGLE,
				'Balthazar' => self::GOOGLE,
				'Bangers' => self::GOOGLE,
				'Barlow' => self::GOOGLE,
				'Barlow Condensed' => self::GOOGLE,
				'Barlow Semi Condensed' => self::GOOGLE,
				'Barriecito' => self::GOOGLE,
				'Barrio' => self::GOOGLE,
				'Basic' => self::GOOGLE,
				'Baskervville' => self::GOOGLE,
				'Battambang' => self::GOOGLE,
				'Baumans' => self::GOOGLE,
				'Bayon' => self::GOOGLE,
				'Be Vietnam Pro' => self::GOOGLE,
				'Beau Rivage' => self::GOOGLE,
				'Bebas Neue' => self::GOOGLE,
				'Belanosima' => self::GOOGLE,
				'Belgrano' => self::GOOGLE,
				'Bellefair' => self::GOOGLE,
				'Belleza' => self::GOOGLE,
				'Bellota' => self::GOOGLE,
				'Bellota Text' => self::GOOGLE,
				'BenchNine' => self::GOOGLE,
				'Benne' => self::GOOGLE,
				'Bentham' => self::GOOGLE,
				'Berkshire Swash' => self::GOOGLE,
				'Besley' => self::GOOGLE,
				'Beth Ellen' => self::GOOGLE,
				'Bevan' => self::GOOGLE,
				'BhuTuka Expanded One' => self::GOOGLE,
				'Big Shoulders Display' => self::GOOGLE,
				'Big Shoulders Inline Display' => self::GOOGLE,
				'Big Shoulders Inline Text' => self::GOOGLE,
				'Big Shoulders Stencil Display' => self::GOOGLE,
				'Big Shoulders Stencil Text' => self::GOOGLE,
				'Big Shoulders Text' => self::GOOGLE,
				'Bigelow Rules' => self::GOOGLE,
				'Bigshot One' => self::GOOGLE,
				'Bilbo' => self::GOOGLE,
				'Bilbo Swash Caps' => self::GOOGLE,
				'BioRhyme' => self::GOOGLE,
				'BioRhyme Expanded' => self::GOOGLE,
				'Birthstone' => self::GOOGLE,
				'Birthstone Bounce' => self::GOOGLE,
				'Biryani' => self::GOOGLE,
				'Bitter' => self::GOOGLE,
				'Black And White Picture' => self::GOOGLE,
				'Black Han Sans' => self::GOOGLE,
				'Black Ops One' => self::GOOGLE,
				'Blaka' => self::GOOGLE,
				'Blaka Hollow' => self::GOOGLE,
				'Blaka Ink' => self::GOOGLE,
				'Blinker' => self::GOOGLE,
				'Bodoni Moda' => self::GOOGLE,
				'Bokor' => self::GOOGLE,
				'Bona Nova' => self::GOOGLE,
				'Bonbon' => self::GOOGLE,
				'Bonheur Royale' => self::GOOGLE,
				'Boogaloo' => self::GOOGLE,
				'Borel' => self::GOOGLE,
				'Bowlby One' => self::GOOGLE,
				'Bowlby One SC' => self::GOOGLE,
				'Braah One' => self::GOOGLE,
				'Brawler' => self::GOOGLE,
				'Bree Serif' => self::GOOGLE,
				'Bricolage Grotesque' => self::GOOGLE,
				'Briem Hand' => self::GOOGLE,
				'Bruno Ace' => self::GOOGLE,
				'Bruno Ace SC' => self::GOOGLE,
				'Brygada 1918' => self::GOOGLE,
				'Bubblegum Sans' => self::GOOGLE,
				'Bubbler One' => self::GOOGLE,
				'Buda' => self::GOOGLE,
				'Buenard' => self::GOOGLE,
				'Bungee' => self::GOOGLE,
				'Bungee Hairline' => self::GOOGLE,
				'Bungee Inline' => self::GOOGLE,
				'Bungee Outline' => self::GOOGLE,
				'Bungee Shade' => self::GOOGLE,
				'Bungee Spice' => self::GOOGLE,
				'Butcherman' => self::GOOGLE,
				'Butterfly Kids' => self::GOOGLE,
				'Cabin' => self::GOOGLE,
				'Cabin Condensed' => self::GOOGLE,
				'Cabin Sketch' => self::GOOGLE,
				'Caesar Dressing' => self::GOOGLE,
				'Cagliostro' => self::GOOGLE,
				'Cairo' => self::GOOGLE,
				'Cairo Play' => self::GOOGLE,
				'Caladea' => self::GOOGLE,
				'Calistoga' => self::GOOGLE,
				'Calligraffitti' => self::GOOGLE,
				'Cambay' => self::GOOGLE,
				'Cambo' => self::GOOGLE,
				'Candal' => self::GOOGLE,
				'Cantarell' => self::GOOGLE,
				'Cantata One' => self::GOOGLE,
				'Cantora One' => self::GOOGLE,
				'Caprasimo' => self::GOOGLE,
				'Capriola' => self::GOOGLE,
				'Caramel' => self::GOOGLE,
				'Carattere' => self::GOOGLE,
				'Cardo' => self::GOOGLE,
				'Carlito' => self::GOOGLE,
				'Carme' => self::GOOGLE,
				'Carrois Gothic' => self::GOOGLE,
				'Carrois Gothic SC' => self::GOOGLE,
				'Carter One' => self::GOOGLE,
				'Castoro' => self::GOOGLE,
				'Castoro Titling' => self::GOOGLE,
				'Catamaran' => self::GOOGLE,
				'Caudex' => self::GOOGLE,
				'Caveat' => self::GOOGLE,
				'Caveat Brush' => self::GOOGLE,
				'Cedarville Cursive' => self::GOOGLE,
				'Ceviche One' => self::GOOGLE,
				'Chakra Petch' => self::GOOGLE,
				'Changa' => self::GOOGLE,
				'Changa One' => self::GOOGLE,
				'Chango' => self::GOOGLE,
				'Charis SIL' => self::GOOGLE,
				'Charm' => self::GOOGLE,
				'Charmonman' => self::GOOGLE,
				'Chathura' => self::GOOGLE,
				'Chau Philomene One' => self::GOOGLE,
				'Chela One' => self::GOOGLE,
				'Chelsea Market' => self::GOOGLE,
				'Chenla' => self::GOOGLE,
				'Cherish' => self::GOOGLE,
				'Cherry Bomb One' => self::GOOGLE,
				'Cherry Cream Soda' => self::GOOGLE,
				'Cherry Swash' => self::GOOGLE,
				'Chewy' => self::GOOGLE,
				'Chicle' => self::GOOGLE,
				'Chilanka' => self::GOOGLE,
				'Chivo' => self::GOOGLE,
				'Chivo Mono' => self::GOOGLE,
				'Chokokutai' => self::GOOGLE,
				'Chonburi' => self::GOOGLE,
				'Cinzel' => self::GOOGLE,
				'Cinzel Decorative' => self::GOOGLE,
				'Clicker Script' => self::GOOGLE,
				'Climate Crisis' => self::GOOGLE,
				'Coda' => self::GOOGLE,
				'Coda Caption' => self::GOOGLE,
				'Codystar' => self::GOOGLE,
				'Coiny' => self::GOOGLE,
				'Combo' => self::GOOGLE,
				'Comfortaa' => self::GOOGLE,
				'Comforter' => self::GOOGLE,
				'Comforter Brush' => self::GOOGLE,
				'Comic Neue' => self::GOOGLE,
				'Coming Soon' => self::GOOGLE,
				'Comme' => self::GOOGLE,
				'Commissioner' => self::GOOGLE,
				'Concert One' => self::GOOGLE,
				'Condiment' => self::GOOGLE,
				'Content' => self::GOOGLE,
				'Contrail One' => self::GOOGLE,
				'Convergence' => self::GOOGLE,
				'Cookie' => self::GOOGLE,
				'Copse' => self::GOOGLE,
				'Corben' => self::GOOGLE,
				'Corinthia' => self::GOOGLE,
				'Cormorant' => self::GOOGLE,
				'Cormorant Garamond' => self::GOOGLE,
				'Cormorant Infant' => self::GOOGLE,
				'Cormorant SC' => self::GOOGLE,
				'Cormorant Unicase' => self::GOOGLE,
				'Cormorant Upright' => self::GOOGLE,
				'Courgette' => self::GOOGLE,
				'Courier Prime' => self::GOOGLE,
				'Cousine' => self::GOOGLE,
				'Coustard' => self::GOOGLE,
				'Covered By Your Grace' => self::GOOGLE,
				'Crafty Girls' => self::GOOGLE,
				'Creepster' => self::GOOGLE,
				'Crete Round' => self::GOOGLE,
				'Crimson Pro' => self::GOOGLE,
				'Crimson Text' => self::GOOGLE,
				'Croissant One' => self::GOOGLE,
				'Crushed' => self::GOOGLE,
				'Cuprum' => self::GOOGLE,
				'Cute Font' => self::GOOGLE,
				'Cutive' => self::GOOGLE,
				'Cutive Mono' => self::GOOGLE,
				'DM Mono' => self::GOOGLE,
				'DM Sans' => self::GOOGLE,
				'DM Serif Display' => self::GOOGLE,
				'DM Serif Text' => self::GOOGLE,
				'Dai Banna SIL' => self::GOOGLE,
				'Damion' => self::GOOGLE,
				'Dancing Script' => self::GOOGLE,
				'Dangrek' => self::GOOGLE,
				'Darker Grotesque' => self::GOOGLE,
				'Darumadrop One' => self::GOOGLE,
				'David Libre' => self::GOOGLE,
				'Dawning of a New Day' => self::GOOGLE,
				'Days One' => self::GOOGLE,
				'Dekko' => self::GOOGLE,
				'Dela Gothic One' => self::GOOGLE,
				'Delicious Handrawn' => self::GOOGLE,
				'Delius' => self::GOOGLE,
				'Delius Swash Caps' => self::GOOGLE,
				'Delius Unicase' => self::GOOGLE,
				'Della Respira' => self::GOOGLE,
				'Denk One' => self::GOOGLE,
				'Devonshire' => self::GOOGLE,
				'Dhurjati' => self::GOOGLE,
				'Didact Gothic' => self::GOOGLE,
				'Diphylleia' => self::GOOGLE,
				'Diplomata' => self::GOOGLE,
				'Diplomata SC' => self::GOOGLE,
				'Do Hyeon' => self::GOOGLE,
				'Dokdo' => self::GOOGLE,
				'Domine' => self::GOOGLE,
				'Donegal One' => self::GOOGLE,
				'Dongle' => self::GOOGLE,
				'Doppio One' => self::GOOGLE,
				'Dorsa' => self::GOOGLE,
				'Dosis' => self::GOOGLE,
				'DotGothic16' => self::GOOGLE,
				'Dr Sugiyama' => self::GOOGLE,
				'Droid Arabic Kufi' => self::EARLYACCESS, // Hack for Google Early Access.
				'Droid Arabic Naskh' => self::EARLYACCESS, // Hack for Google Early Access.
				'Duru Sans' => self::GOOGLE,
				'DynaPuff' => self::GOOGLE,
				'Dynalight' => self::GOOGLE,
				'EB Garamond' => self::GOOGLE,
				'Eagle Lake' => self::GOOGLE,
				'East Sea Dokdo' => self::GOOGLE,
				'Eater' => self::GOOGLE,
				'Economica' => self::GOOGLE,
				'Eczar' => self::GOOGLE,
				'Edu NSW ACT Foundation' => self::GOOGLE,
				'Edu QLD Beginner' => self::GOOGLE,
				'Edu SA Beginner' => self::GOOGLE,
				'Edu TAS Beginner' => self::GOOGLE,
				'Edu VIC WA NT Beginner' => self::GOOGLE,
				'El Messiri' => self::GOOGLE,
				'Electrolize' => self::GOOGLE,
				'Elsie' => self::GOOGLE,
				'Elsie Swash Caps' => self::GOOGLE,
				'Emblema One' => self::GOOGLE,
				'Emilys Candy' => self::GOOGLE,
				'Encode Sans' => self::GOOGLE,
				'Encode Sans Condensed' => self::GOOGLE,
				'Encode Sans Expanded' => self::GOOGLE,
				'Encode Sans SC' => self::GOOGLE,
				'Encode Sans Semi Condensed' => self::GOOGLE,
				'Encode Sans Semi Expanded' => self::GOOGLE,
				'Engagement' => self::GOOGLE,
				'Englebert' => self::GOOGLE,
				'Enriqueta' => self::GOOGLE,
				'Ephesis' => self::GOOGLE,
				'Epilogue' => self::GOOGLE,
				'Erica One' => self::GOOGLE,
				'Esteban' => self::GOOGLE,
				'Estonia' => self::GOOGLE,
				'Euphoria Script' => self::GOOGLE,
				'Ewert' => self::GOOGLE,
				'Exo' => self::GOOGLE,
				'Exo 2' => self::GOOGLE,
				'Expletus Sans' => self::GOOGLE,
				'Explora' => self::GOOGLE,
				'Fahkwang' => self::GOOGLE,
				'Familjen Grotesk' => self::GOOGLE,
				'Fanwood Text' => self::GOOGLE,
				'Farro' => self::GOOGLE,
				'Farsan' => self::GOOGLE,
				'Fascinate' => self::GOOGLE,
				'Fascinate Inline' => self::GOOGLE,
				'Faster One' => self::GOOGLE,
				'Fasthand' => self::GOOGLE,
				'Fauna One' => self::GOOGLE,
				'Faustina' => self::GOOGLE,
				'Federant' => self::GOOGLE,
				'Federo' => self::GOOGLE,
				'Felipa' => self::GOOGLE,
				'Fenix' => self::GOOGLE,
				'Festive' => self::GOOGLE,
				'Figtree' => self::GOOGLE,
				'Finger Paint' => self::GOOGLE,
				'Finlandica' => self::GOOGLE,
				'Fira Code' => self::GOOGLE,
				'Fira Mono' => self::GOOGLE,
				'Fira Sans' => self::GOOGLE,
				'Fira Sans Condensed' => self::GOOGLE,
				'Fira Sans Extra Condensed' => self::GOOGLE,
				'Fjalla One' => self::GOOGLE,
				'Fjord One' => self::GOOGLE,
				'Flamenco' => self::GOOGLE,
				'Flavors' => self::GOOGLE,
				'Fleur De Leah' => self::GOOGLE,
				'Flow Block' => self::GOOGLE,
				'Flow Circular' => self::GOOGLE,
				'Flow Rounded' => self::GOOGLE,
				'Foldit' => self::GOOGLE,
				'Fondamento' => self::GOOGLE,
				'Fontdiner Swanky' => self::GOOGLE,
				'Forum' => self::GOOGLE,
				'Fragment Mono' => self::GOOGLE,
				'Francois One' => self::GOOGLE,
				'Frank Ruhl Libre' => self::GOOGLE,
				'Fraunces' => self::GOOGLE,
				'Freckle Face' => self::GOOGLE,
				'Fredericka the Great' => self::GOOGLE,
				'Fredoka' => self::GOOGLE,
				'Fredoka One' => self::GOOGLE,
				'Freehand' => self::GOOGLE,
				'Freeman' => self::GOOGLE,
				'Fresca' => self::GOOGLE,
				'Frijole' => self::GOOGLE,
				'Fruktur' => self::GOOGLE,
				'Fugaz One' => self::GOOGLE,
				'Fuggles' => self::GOOGLE,
				'Fuzzy Bubbles' => self::GOOGLE,
				'GFS Didot' => self::GOOGLE,
				'GFS Neohellenic' => self::GOOGLE,
				'Gabarito' => self::GOOGLE,
				'Gabriela' => self::GOOGLE,
				'Gaegu' => self::GOOGLE,
				'Gafata' => self::GOOGLE,
				'Gajraj One' => self::GOOGLE,
				'Galada' => self::GOOGLE,
				'Galdeano' => self::GOOGLE,
				'Galindo' => self::GOOGLE,
				'Gamja Flower' => self::GOOGLE,
				'Gantari' => self::GOOGLE,
				'Gasoek One' => self::GOOGLE,
				'Gayathri' => self::GOOGLE,
				'Gelasio' => self::GOOGLE,
				'Gemunu Libre' => self::GOOGLE,
				'Genos' => self::GOOGLE,
				'Gentium Book Basic' => self::GOOGLE,
				'Gentium Book Plus' => self::GOOGLE,
				'Gentium Plus' => self::GOOGLE,
				'Geo' => self::GOOGLE,
				'Geologica' => self::GOOGLE,
				'Georama' => self::GOOGLE,
				'Geostar' => self::GOOGLE,
				'Geostar Fill' => self::GOOGLE,
				'Germania One' => self::GOOGLE,
				'Gideon Roman' => self::GOOGLE,
				'Gidugu' => self::GOOGLE,
				'Gilda Display' => self::GOOGLE,
				'Girassol' => self::GOOGLE,
				'Give You Glory' => self::GOOGLE,
				'Glass Antiqua' => self::GOOGLE,
				'Glegoo' => self::GOOGLE,
				'Gloock' => self::GOOGLE,
				'Gloria Hallelujah' => self::GOOGLE,
				'Glory' => self::GOOGLE,
				'Gluten' => self::GOOGLE,
				'Goblin One' => self::GOOGLE,
				'Gochi Hand' => self::GOOGLE,
				'Goldman' => self::GOOGLE,
				'Golos Text' => self::GOOGLE,
				'Gorditas' => self::GOOGLE,
				'Gothic A1' => self::GOOGLE,
				'Gotu' => self::GOOGLE,
				'Goudy Bookletter 1911' => self::GOOGLE,
				'Gowun Batang' => self::GOOGLE,
				'Gowun Dodum' => self::GOOGLE,
				'Graduate' => self::GOOGLE,
				'Grand Hotel' => self::GOOGLE,
				'Grandiflora One' => self::GOOGLE,
				'Grandstander' => self::GOOGLE,
				'Grape Nuts' => self::GOOGLE,
				'Gravitas One' => self::GOOGLE,
				'Great Vibes' => self::GOOGLE,
				'Grechen Fuemen' => self::GOOGLE,
				'Grenze' => self::GOOGLE,
				'Grenze Gotisch' => self::GOOGLE,
				'Grey Qo' => self::GOOGLE,
				'Griffy' => self::GOOGLE,
				'Gruppo' => self::GOOGLE,
				'Gudea' => self::GOOGLE,
				'Gugi' => self::GOOGLE,
				'Gulzar' => self::GOOGLE,
				'Gupter' => self::GOOGLE,
				'Gurajada' => self::GOOGLE,
				'Gwendolyn' => self::GOOGLE,
				'Habibi' => self::GOOGLE,
				'Hachi Maru Pop' => self::GOOGLE,
				'Hahmlet' => self::GOOGLE,
				'Halant' => self::GOOGLE,
				'Hammersmith One' => self::GOOGLE,
				'Hanalei' => self::GOOGLE,
				'Hanalei Fill' => self::GOOGLE,
				'Handjet' => self::GOOGLE,
				'Handlee' => self::GOOGLE,
				'Hanken Grotesk' => self::GOOGLE,
				'Hanuman' => self::GOOGLE,
				'Happy Monkey' => self::GOOGLE,
				'Harmattan' => self::GOOGLE,
				'Headland One' => self::GOOGLE,
				'Hedvig Letters Sans' => self::GOOGLE,
				'Hedvig Letters Serif' => self::GOOGLE,
				'Heebo' => self::GOOGLE,
				'Henny Penny' => self::GOOGLE,
				'Hepta Slab' => self::GOOGLE,
				'Herr Von Muellerhoff' => self::GOOGLE,
				'Hi Melody' => self::GOOGLE,
				'Hina Mincho' => self::GOOGLE,
				'Hind' => self::GOOGLE,
				'Hind Guntur' => self::GOOGLE,
				'Hind Madurai' => self::GOOGLE,
				'Hind Siliguri' => self::GOOGLE,
				'Hind Vadodara' => self::GOOGLE,
				'Holtwood One SC' => self::GOOGLE,
				'Homemade Apple' => self::GOOGLE,
				'Homenaje' => self::GOOGLE,
				'Honk' => self::GOOGLE,
				'Hubballi' => self::GOOGLE,
				'Hurricane' => self::GOOGLE,
				'IBM Plex Mono' => self::GOOGLE,
				'IBM Plex Sans' => self::GOOGLE,
				'IBM Plex Sans Arabic' => self::GOOGLE,
				'IBM Plex Sans Condensed' => self::GOOGLE,
				'IBM Plex Sans Devanagari' => self::GOOGLE,
				'IBM Plex Sans Hebrew' => self::GOOGLE,
				'IBM Plex Sans JP' => self::GOOGLE,
				'IBM Plex Sans KR' => self::GOOGLE,
				'IBM Plex Sans Thai' => self::GOOGLE,
				'IBM Plex Sans Thai Looped' => self::GOOGLE,
				'IBM Plex Serif' => self::GOOGLE,
				'IM Fell DW Pica' => self::GOOGLE,
				'IM Fell DW Pica SC' => self::GOOGLE,
				'IM Fell Double Pica' => self::GOOGLE,
				'IM Fell Double Pica SC' => self::GOOGLE,
				'IM Fell English' => self::GOOGLE,
				'IM Fell English SC' => self::GOOGLE,
				'IM Fell French Canon' => self::GOOGLE,
				'IM Fell French Canon SC' => self::GOOGLE,
				'IM Fell Great Primer' => self::GOOGLE,
				'IM Fell Great Primer SC' => self::GOOGLE,
				'Ibarra Real Nova' => self::GOOGLE,
				'Iceberg' => self::GOOGLE,
				'Iceland' => self::GOOGLE,
				'Imbue' => self::GOOGLE,
				'Imperial Script' => self::GOOGLE,
				'Imprima' => self::GOOGLE,
				'Inclusive Sans' => self::GOOGLE,
				'Inconsolata' => self::GOOGLE,
				'Inder' => self::GOOGLE,
				'Indie Flower' => self::GOOGLE,
				'Ingrid Darling' => self::GOOGLE,
				'Inika' => self::GOOGLE,
				'Inknut Antiqua' => self::GOOGLE,
				'Inria Sans' => self::GOOGLE,
				'Inria Serif' => self::GOOGLE,
				'Inspiration' => self::GOOGLE,
				'Instrument Sans' => self::GOOGLE,
				'Instrument Serif' => self::GOOGLE,
				'Inter' => self::GOOGLE,
				'Inter Tight' => self::GOOGLE,
				'Irish Grover' => self::GOOGLE,
				'Island Moments' => self::GOOGLE,
				'Istok Web' => self::GOOGLE,
				'Italiana' => self::GOOGLE,
				'Italianno' => self::GOOGLE,
				'Itim' => self::GOOGLE,
				'Jacquard 12 Charted' => self::GOOGLE,
				'Jacquard 24' => self::GOOGLE,
				'Jacquard 24 Charted' => self::GOOGLE,
				'Jacquarda Bastarda 9' => self::GOOGLE,
				'Jacquarda Bastarda 9 Charted' => self::GOOGLE,
				'Jacques Francois' => self::GOOGLE,
				'Jacques Francois Shadow' => self::GOOGLE,
				'Jaini' => self::GOOGLE,
				'Jaini Purva' => self::GOOGLE,
				'Jaldi' => self::GOOGLE,
				'Jaro' => self::GOOGLE,
				'Jersey 10' => self::GOOGLE,
				'Jersey 10 Charted' => self::GOOGLE,
				'Jersey 15' => self::GOOGLE,
				'Jersey 15 Charted' => self::GOOGLE,
				'Jersey 20' => self::GOOGLE,
				'Jersey 20 Charted' => self::GOOGLE,
				'Jersey 25' => self::GOOGLE,
				'Jersey 25 Charted' => self::GOOGLE,
				'JetBrains Mono' => self::GOOGLE,
				'Jim Nightshade' => self::GOOGLE,
				'Joan' => self::GOOGLE,
				'Jockey One' => self::GOOGLE,
				'Jolly Lodger' => self::GOOGLE,
				'Jomhuria' => self::GOOGLE,
				'Jomolhari' => self::GOOGLE,
				'Josefin Sans' => self::GOOGLE,
				'Josefin Slab' => self::GOOGLE,
				'Jost' => self::GOOGLE,
				'Joti One' => self::GOOGLE,
				'Jua' => self::GOOGLE,
				'Judson' => self::GOOGLE,
				'Julee' => self::GOOGLE,
				'Julius Sans One' => self::GOOGLE,
				'Junge' => self::GOOGLE,
				'Jura' => self::GOOGLE,
				'Just Another Hand' => self::GOOGLE,
				'Just Me Again Down Here' => self::GOOGLE,
				'K2D' => self::GOOGLE,
				'Kablammo' => self::GOOGLE,
				'Kadwa' => self::GOOGLE,
				'Kaisei Decol' => self::GOOGLE,
				'Kaisei HarunoUmi' => self::GOOGLE,
				'Kaisei Opti' => self::GOOGLE,
				'Kaisei Tokumin' => self::GOOGLE,
				'Kalam' => self::GOOGLE,
				'Kalnia' => self::GOOGLE,
				'Kameron' => self::GOOGLE,
				'Kanit' => self::GOOGLE,
				'Kantumruy' => self::GOOGLE,
				'Kantumruy Pro' => self::GOOGLE,
				'Karantina' => self::GOOGLE,
				'Karla' => self::GOOGLE,
				'Karma' => self::GOOGLE,
				'Katibeh' => self::GOOGLE,
				'Kaushan Script' => self::GOOGLE,
				'Kavivanar' => self::GOOGLE,
				'Kavoon' => self::GOOGLE,
				'Kay Pho Du' => self::GOOGLE,
				'Kdam Thmor Pro' => self::GOOGLE,
				'Keania One' => self::GOOGLE,
				'Kelly Slab' => self::GOOGLE,
				'Kenia' => self::GOOGLE,
				'Khand' => self::GOOGLE,
				'Khmer' => self::GOOGLE,
				'Khula' => self::GOOGLE,
				'Kings' => self::GOOGLE,
				'Kirang Haerang' => self::GOOGLE,
				'Kite One' => self::GOOGLE,
				'Kiwi Maru' => self::GOOGLE,
				'Klee One' => self::GOOGLE,
				'Knewave' => self::GOOGLE,
				'KoHo' => self::GOOGLE,
				'Kodchasan' => self::GOOGLE,
				'Kode Mono' => self::GOOGLE,
				'Koh Santepheap' => self::GOOGLE,
				'Kolker Brush' => self::GOOGLE,
				'Konkhmer Sleokchher' => self::GOOGLE,
				'Kosugi' => self::GOOGLE,
				'Kosugi Maru' => self::GOOGLE,
				'Kotta One' => self::GOOGLE,
				'Koulen' => self::GOOGLE,
				'Kranky' => self::GOOGLE,
				'Kreon' => self::GOOGLE,
				'Kristi' => self::GOOGLE,
				'Krona One' => self::GOOGLE,
				'Krub' => self::GOOGLE,
				'Kufam' => self::GOOGLE,
				'Kulim Park' => self::GOOGLE,
				'Kumar One' => self::GOOGLE,
				'Kumar One Outline' => self::GOOGLE,
				'Kumbh Sans' => self::GOOGLE,
				'Kurale' => self::GOOGLE,
				'La Belle Aurore' => self::GOOGLE,
				'Labrada' => self::GOOGLE,
				'Lacquer' => self::GOOGLE,
				'Laila' => self::GOOGLE,
				'Lakki Reddy' => self::GOOGLE,
				'Lalezar' => self::GOOGLE,
				'Lancelot' => self::GOOGLE,
				'Langar' => self::GOOGLE,
				'Lateef' => self::GOOGLE,
				'Lato' => self::GOOGLE,
				'Lavishly Yours' => self::GOOGLE,
				'League Gothic' => self::GOOGLE,
				'League Script' => self::GOOGLE,
				'League Spartan' => self::GOOGLE,
				'Leckerli One' => self::GOOGLE,
				'Ledger' => self::GOOGLE,
				'Lekton' => self::GOOGLE,
				'Lemon' => self::GOOGLE,
				'Lemonada' => self::GOOGLE,
				'Lexend' => self::GOOGLE,
				'Lexend Deca' => self::GOOGLE,
				'Lexend Exa' => self::GOOGLE,
				'Lexend Giga' => self::GOOGLE,
				'Lexend Mega' => self::GOOGLE,
				'Lexend Peta' => self::GOOGLE,
				'Lexend Tera' => self::GOOGLE,
				'Lexend Zetta' => self::GOOGLE,
				'Libre Barcode 128' => self::GOOGLE,
				'Libre Barcode 128 Text' => self::GOOGLE,
				'Libre Barcode 39' => self::GOOGLE,
				'Libre Barcode 39 Extended' => self::GOOGLE,
				'Libre Barcode 39 Extended Text' => self::GOOGLE,
				'Libre Barcode 39 Text' => self::GOOGLE,
				'Libre Barcode EAN13 Text' => self::GOOGLE,
				'Libre Baskerville' => self::GOOGLE,
				'Libre Bodoni' => self::GOOGLE,
				'Libre Caslon Display' => self::GOOGLE,
				'Libre Caslon Text' => self::GOOGLE,
				'Libre Franklin' => self::GOOGLE,
				'Licorice' => self::GOOGLE,
				'Life Savers' => self::GOOGLE,
				'Lilita One' => self::GOOGLE,
				'Lily Script One' => self::GOOGLE,
				'Limelight' => self::GOOGLE,
				'Linden Hill' => self::GOOGLE,
				'Linefont' => self::GOOGLE,
				'Lisu Bosa' => self::GOOGLE,
				'Literata' => self::GOOGLE,
				'Liu Jian Mao Cao' => self::GOOGLE,
				'Livvic' => self::GOOGLE,
				'Lobster' => self::GOOGLE,
				'Lobster Two' => self::GOOGLE,
				'Londrina Outline' => self::GOOGLE,
				'Londrina Shadow' => self::GOOGLE,
				'Londrina Sketch' => self::GOOGLE,
				'Londrina Solid' => self::GOOGLE,
				'Long Cang' => self::GOOGLE,
				'Lora' => self::GOOGLE,
				'Love Light' => self::GOOGLE,
				'Love Ya Like A Sister' => self::GOOGLE,
				'Loved by the King' => self::GOOGLE,
				'Lovers Quarrel' => self::GOOGLE,
				'Luckiest Guy' => self::GOOGLE,
				'Lugrasimo' => self::GOOGLE,
				'Lumanosimo' => self::GOOGLE,
				'Lunasima' => self::GOOGLE,
				'Lusitana' => self::GOOGLE,
				'Lustria' => self::GOOGLE,
				'Luxurious Roman' => self::GOOGLE,
				'Luxurious Script' => self::GOOGLE,
				'M PLUS 1' => self::GOOGLE,
				'M PLUS 1 Code' => self::GOOGLE,
				'M PLUS 1p' => self::GOOGLE,
				'M PLUS 2' => self::GOOGLE,
				'M PLUS Code Latin' => self::GOOGLE,
				'M PLUS Rounded 1c' => self::GOOGLE,
				'Ma Shan Zheng' => self::GOOGLE,
				'Macondo' => self::GOOGLE,
				'Macondo Swash Caps' => self::GOOGLE,
				'Mada' => self::GOOGLE,
				'Madimi One' => self::GOOGLE,
				'Magra' => self::GOOGLE,
				'Maiden Orange' => self::GOOGLE,
				'Maitree' => self::GOOGLE,
				'Major Mono Display' => self::GOOGLE,
				'Mako' => self::GOOGLE,
				'Mali' => self::GOOGLE,
				'Mallanna' => self::GOOGLE,
				'Mandali' => self::GOOGLE,
				'Manjari' => self::GOOGLE,
				'Manrope' => self::GOOGLE,
				'Mansalva' => self::GOOGLE,
				'Manuale' => self::GOOGLE,
				'Marcellus' => self::GOOGLE,
				'Marcellus SC' => self::GOOGLE,
				'Marck Script' => self::GOOGLE,
				'Margarine' => self::GOOGLE,
				'Marhey' => self::GOOGLE,
				'Markazi Text' => self::GOOGLE,
				'Marko One' => self::GOOGLE,
				'Marmelad' => self::GOOGLE,
				'Martel' => self::GOOGLE,
				'Martel Sans' => self::GOOGLE,
				'Martian Mono' => self::GOOGLE,
				'Marvel' => self::GOOGLE,
				'Mate' => self::GOOGLE,
				'Mate SC' => self::GOOGLE,
				'Material Icons' => self::GOOGLE,
				'Material Icons Outlined' => self::GOOGLE,
				'Material Icons Round' => self::GOOGLE,
				'Material Icons Sharp' => self::GOOGLE,
				'Material Icons Two Tone' => self::GOOGLE,
				'Material Symbols Outlined' => self::GOOGLE,
				'Material Symbols Rounded' => self::GOOGLE,
				'Material Symbols Sharp' => self::GOOGLE,
				'Maven Pro' => self::GOOGLE,
				'McLaren' => self::GOOGLE,
				'Mea Culpa' => self::GOOGLE,
				'Meddon' => self::GOOGLE,
				'MedievalSharp' => self::GOOGLE,
				'Medula One' => self::GOOGLE,
				'Meera Inimai' => self::GOOGLE,
				'Megrim' => self::GOOGLE,
				'Meie Script' => self::GOOGLE,
				'Meow Script' => self::GOOGLE,
				'Merienda' => self::GOOGLE,
				'Merienda One' => self::GOOGLE,
				'Merriweather' => self::GOOGLE,
				'Merriweather Sans' => self::GOOGLE,
				'Metal' => self::GOOGLE,
				'Metal Mania' => self::GOOGLE,
				'Metamorphous' => self::GOOGLE,
				'Metrophobic' => self::GOOGLE,
				'Michroma' => self::GOOGLE,
				'Micro 5' => self::GOOGLE,
				'Micro 5 Charted' => self::GOOGLE,
				'Milonga' => self::GOOGLE,
				'Miltonian' => self::GOOGLE,
				'Miltonian Tattoo' => self::GOOGLE,
				'Mina' => self::GOOGLE,
				'Mingzat' => self::GOOGLE,
				'Miniver' => self::GOOGLE,
				'Miriam Libre' => self::GOOGLE,
				'Mirza' => self::GOOGLE,
				'Miss Fajardose' => self::GOOGLE,
				'Mitr' => self::GOOGLE,
				'Mochiy Pop One' => self::GOOGLE,
				'Mochiy Pop P One' => self::GOOGLE,
				'Modak' => self::GOOGLE,
				'Modern Antiqua' => self::GOOGLE,
				'Mogra' => self::GOOGLE,
				'Mohave' => self::GOOGLE,
				'Moirai One' => self::GOOGLE,
				'Molengo' => self::GOOGLE,
				'Molle' => self::GOOGLE,
				'Monda' => self::GOOGLE,
				'Monofett' => self::GOOGLE,
				'Monomaniac One' => self::GOOGLE,
				'Monoton' => self::GOOGLE,
				'Monsieur La Doulaise' => self::GOOGLE,
				'Montaga' => self::GOOGLE,
				'Montagu Slab' => self::GOOGLE,
				'MonteCarlo' => self::GOOGLE,
				'Montez' => self::GOOGLE,
				'Montserrat' => self::GOOGLE,
				'Montserrat Alternates' => self::GOOGLE,
				'Montserrat Subrayada' => self::GOOGLE,
				'Moo Lah Lah' => self::GOOGLE,
				'Mooli' => self::GOOGLE,
				'Moon Dance' => self::GOOGLE,
				'Moul' => self::GOOGLE,
				'Moulpali' => self::GOOGLE,
				'Mountains of Christmas' => self::GOOGLE,
				'Mouse Memoirs' => self::GOOGLE,
				'Mr Bedfort' => self::GOOGLE,
				'Mr Dafoe' => self::GOOGLE,
				'Mr De Haviland' => self::GOOGLE,
				'Mrs Saint Delafield' => self::GOOGLE,
				'Mrs Sheppards' => self::GOOGLE,
				'Ms Madi' => self::GOOGLE,
				'Mukta' => self::GOOGLE,
				'Mukta Mahee' => self::GOOGLE,
				'Mukta Malar' => self::GOOGLE,
				'Mukta Vaani' => self::GOOGLE,
				'Mulish' => self::GOOGLE,
				'Murecho' => self::GOOGLE,
				'MuseoModerno' => self::GOOGLE,
				'My Soul' => self::GOOGLE,
				'Mynerve' => self::GOOGLE,
				'Mystery Quest' => self::GOOGLE,
				'NTR' => self::GOOGLE,
				'Nabla' => self::GOOGLE,
				'Namdhinggo' => self::GOOGLE,
				'Nanum Brush Script' => self::GOOGLE,
				'Nanum Gothic' => self::GOOGLE,
				'Nanum Gothic Coding' => self::GOOGLE,
				'Nanum Myeongjo' => self::GOOGLE,
				'Nanum Pen Script' => self::GOOGLE,
				'Narnoor' => self::GOOGLE,
				'Neonderthaw' => self::GOOGLE,
				'Nerko One' => self::GOOGLE,
				'Neucha' => self::GOOGLE,
				'Neuton' => self::GOOGLE,
				'New Rocker' => self::GOOGLE,
				'New Tegomin' => self::GOOGLE,
				'News Cycle' => self::GOOGLE,
				'Newsreader' => self::GOOGLE,
				'Niconne' => self::GOOGLE,
				'Niramit' => self::GOOGLE,
				'Nixie One' => self::GOOGLE,
				'Nobile' => self::GOOGLE,
				'Nokora' => self::GOOGLE,
				'Norican' => self::GOOGLE,
				'Nosifer' => self::GOOGLE,
				'Notable' => self::GOOGLE,
				'Nothing You Could Do' => self::GOOGLE,
				'Noticia Text' => self::GOOGLE,
				'Noto Color Emoji' => self::GOOGLE,
				'Noto Emoji' => self::GOOGLE,
				'Noto Kufi Arabic' => self::EARLYACCESS, // Hack for Google Early Access.
				'Noto Music' => self::GOOGLE,
				'Noto Naskh Arabic' => self::EARLYACCESS, // Hack for Google Early Access.
				'Noto Nastaliq Urdu' => self::GOOGLE,
				'Noto Rashi Hebrew' => self::GOOGLE,
				'Noto Sans' => self::GOOGLE,
				'Noto Sans Adlam' => self::GOOGLE,
				'Noto Sans Adlam Unjoined' => self::GOOGLE,
				'Noto Sans Anatolian Hieroglyphs' => self::GOOGLE,
				'Noto Sans Arabic' => self::GOOGLE,
				'Noto Sans Armenian' => self::GOOGLE,
				'Noto Sans Avestan' => self::GOOGLE,
				'Noto Sans Balinese' => self::GOOGLE,
				'Noto Sans Bamum' => self::GOOGLE,
				'Noto Sans Bassa Vah' => self::GOOGLE,
				'Noto Sans Batak' => self::GOOGLE,
				'Noto Sans Bengali' => self::GOOGLE,
				'Noto Sans Bhaiksuki' => self::GOOGLE,
				'Noto Sans Brahmi' => self::GOOGLE,
				'Noto Sans Buginese' => self::GOOGLE,
				'Noto Sans Buhid' => self::GOOGLE,
				'Noto Sans Canadian Aboriginal' => self::GOOGLE,
				'Noto Sans Carian' => self::GOOGLE,
				'Noto Sans Caucasian Albanian' => self::GOOGLE,
				'Noto Sans Chakma' => self::GOOGLE,
				'Noto Sans Cham' => self::GOOGLE,
				'Noto Sans Cherokee' => self::GOOGLE,
				'Noto Sans Chorasmian' => self::GOOGLE,
				'Noto Sans Coptic' => self::GOOGLE,
				'Noto Sans Cuneiform' => self::GOOGLE,
				'Noto Sans Cypriot' => self::GOOGLE,
				'Noto Sans Cypro Minoan' => self::GOOGLE,
				'Noto Sans Deseret' => self::GOOGLE,
				'Noto Sans Devanagari' => self::GOOGLE,
				'Noto Sans Display' => self::GOOGLE,
				'Noto Sans Duployan' => self::GOOGLE,
				'Noto Sans Egyptian Hieroglyphs' => self::GOOGLE,
				'Noto Sans Elbasan' => self::GOOGLE,
				'Noto Sans Elymaic' => self::GOOGLE,
				'Noto Sans Ethiopic' => self::GOOGLE,
				'Noto Sans Georgian' => self::GOOGLE,
				'Noto Sans Glagolitic' => self::GOOGLE,
				'Noto Sans Gothic' => self::GOOGLE,
				'Noto Sans Grantha' => self::GOOGLE,
				'Noto Sans Gujarati' => self::GOOGLE,
				'Noto Sans Gunjala Gondi' => self::GOOGLE,
				'Noto Sans Gurmukhi' => self::GOOGLE,
				'Noto Sans HK' => self::GOOGLE,
				'Noto Sans Hanifi Rohingya' => self::GOOGLE,
				'Noto Sans Hanunoo' => self::GOOGLE,
				'Noto Sans Hatran' => self::GOOGLE,
				'Noto Sans Hebrew' => self::EARLYACCESS, // Hack for Google Early Access.
				'Noto Sans Imperial Aramaic' => self::GOOGLE,
				'Noto Sans Indic Siyaq Numbers' => self::GOOGLE,
				'Noto Sans Inscriptional Pahlavi' => self::GOOGLE,
				'Noto Sans Inscriptional Parthian' => self::GOOGLE,
				'Noto Sans JP' => self::GOOGLE,
				'Noto Sans Javanese' => self::GOOGLE,
				'Noto Sans KR' => self::GOOGLE,
				'Noto Sans Kaithi' => self::GOOGLE,
				'Noto Sans Kannada' => self::GOOGLE,
				'Noto Sans Kawi' => self::GOOGLE,
				'Noto Sans Kayah Li' => self::GOOGLE,
				'Noto Sans Kharoshthi' => self::GOOGLE,
				'Noto Sans Khmer' => self::GOOGLE,
				'Noto Sans Khojki' => self::GOOGLE,
				'Noto Sans Khudawadi' => self::GOOGLE,
				'Noto Sans Lao' => self::GOOGLE,
				'Noto Sans Lao Looped' => self::GOOGLE,
				'Noto Sans Lepcha' => self::GOOGLE,
				'Noto Sans Limbu' => self::GOOGLE,
				'Noto Sans Linear A' => self::GOOGLE,
				'Noto Sans Linear B' => self::GOOGLE,
				'Noto Sans Lisu' => self::GOOGLE,
				'Noto Sans Lycian' => self::GOOGLE,
				'Noto Sans Lydian' => self::GOOGLE,
				'Noto Sans Mahajani' => self::GOOGLE,
				'Noto Sans Malayalam' => self::GOOGLE,
				'Noto Sans Mandaic' => self::GOOGLE,
				'Noto Sans Manichaean' => self::GOOGLE,
				'Noto Sans Marchen' => self::GOOGLE,
				'Noto Sans Masaram Gondi' => self::GOOGLE,
				'Noto Sans Math' => self::GOOGLE,
				'Noto Sans Mayan Numerals' => self::GOOGLE,
				'Noto Sans Medefaidrin' => self::GOOGLE,
				'Noto Sans Meetei Mayek' => self::GOOGLE,
				'Noto Sans Mende Kikakui' => self::GOOGLE,
				'Noto Sans Meroitic' => self::GOOGLE,
				'Noto Sans Miao' => self::GOOGLE,
				'Noto Sans Modi' => self::GOOGLE,
				'Noto Sans Mongolian' => self::GOOGLE,
				'Noto Sans Mono' => self::GOOGLE,
				'Noto Sans Mro' => self::GOOGLE,
				'Noto Sans Multani' => self::GOOGLE,
				'Noto Sans Myanmar' => self::GOOGLE,
				'Noto Sans N Ko' => self::GOOGLE,
				'Noto Sans NKo' => self::GOOGLE,
				'Noto Sans NKo Unjoined' => self::GOOGLE,
				'Noto Sans Nabataean' => self::GOOGLE,
				'Noto Sans Nag Mundari' => self::GOOGLE,
				'Noto Sans Nandinagari' => self::GOOGLE,
				'Noto Sans New Tai Lue' => self::GOOGLE,
				'Noto Sans Newa' => self::GOOGLE,
				'Noto Sans Nushu' => self::GOOGLE,
				'Noto Sans Ogham' => self::GOOGLE,
				'Noto Sans Ol Chiki' => self::GOOGLE,
				'Noto Sans Old Hungarian' => self::GOOGLE,
				'Noto Sans Old Italic' => self::GOOGLE,
				'Noto Sans Old North Arabian' => self::GOOGLE,
				'Noto Sans Old Permic' => self::GOOGLE,
				'Noto Sans Old Persian' => self::GOOGLE,
				'Noto Sans Old Sogdian' => self::GOOGLE,
				'Noto Sans Old South Arabian' => self::GOOGLE,
				'Noto Sans Old Turkic' => self::GOOGLE,
				'Noto Sans Oriya' => self::GOOGLE,
				'Noto Sans Osage' => self::GOOGLE,
				'Noto Sans Osmanya' => self::GOOGLE,
				'Noto Sans Pahawh Hmong' => self::GOOGLE,
				'Noto Sans Palmyrene' => self::GOOGLE,
				'Noto Sans Pau Cin Hau' => self::GOOGLE,
				'Noto Sans Phags Pa' => self::GOOGLE,
				'Noto Sans Phoenician' => self::GOOGLE,
				'Noto Sans Psalter Pahlavi' => self::GOOGLE,
				'Noto Sans Rejang' => self::GOOGLE,
				'Noto Sans Runic' => self::GOOGLE,
				'Noto Sans SC' => self::GOOGLE,
				'Noto Sans Samaritan' => self::GOOGLE,
				'Noto Sans Saurashtra' => self::GOOGLE,
				'Noto Sans Sharada' => self::GOOGLE,
				'Noto Sans Shavian' => self::GOOGLE,
				'Noto Sans Siddham' => self::GOOGLE,
				'Noto Sans SignWriting' => self::GOOGLE,
				'Noto Sans Sinhala' => self::GOOGLE,
				'Noto Sans Sogdian' => self::GOOGLE,
				'Noto Sans Sora Sompeng' => self::GOOGLE,
				'Noto Sans Soyombo' => self::GOOGLE,
				'Noto Sans Sundanese' => self::GOOGLE,
				'Noto Sans Syloti Nagri' => self::GOOGLE,
				'Noto Sans Symbols' => self::GOOGLE,
				'Noto Sans Symbols 2' => self::GOOGLE,
				'Noto Sans Syriac' => self::GOOGLE,
				'Noto Sans Syriac Eastern' => self::GOOGLE,
				'Noto Sans TC' => self::GOOGLE,
				'Noto Sans Tagalog' => self::GOOGLE,
				'Noto Sans Tagbanwa' => self::GOOGLE,
				'Noto Sans Tai Le' => self::GOOGLE,
				'Noto Sans Tai Tham' => self::GOOGLE,
				'Noto Sans Tai Viet' => self::GOOGLE,
				'Noto Sans Takri' => self::GOOGLE,
				'Noto Sans Tamil' => self::GOOGLE,
				'Noto Sans Tamil Supplement' => self::GOOGLE,
				'Noto Sans Tangsa' => self::GOOGLE,
				'Noto Sans Telugu' => self::GOOGLE,
				'Noto Sans Thaana' => self::GOOGLE,
				'Noto Sans Thai' => self::GOOGLE,
				'Noto Sans Thai Looped' => self::GOOGLE,
				'Noto Sans Tifinagh' => self::GOOGLE,
				'Noto Sans Tirhuta' => self::GOOGLE,
				'Noto Sans Ugaritic' => self::GOOGLE,
				'Noto Sans Vai' => self::GOOGLE,
				'Noto Sans Vithkuqi' => self::GOOGLE,
				'Noto Sans Wancho' => self::GOOGLE,
				'Noto Sans Warang Citi' => self::GOOGLE,
				'Noto Sans Yi' => self::GOOGLE,
				'Noto Sans Zanabazar Square' => self::GOOGLE,
				'Noto Serif' => self::GOOGLE,
				'Noto Serif Ahom' => self::GOOGLE,
				'Noto Serif Armenian' => self::GOOGLE,
				'Noto Serif Balinese' => self::GOOGLE,
				'Noto Serif Bengali' => self::GOOGLE,
				'Noto Serif Devanagari' => self::GOOGLE,
				'Noto Serif Display' => self::GOOGLE,
				'Noto Serif Dogra' => self::GOOGLE,
				'Noto Serif Ethiopic' => self::GOOGLE,
				'Noto Serif Georgian' => self::GOOGLE,
				'Noto Serif Grantha' => self::GOOGLE,
				'Noto Serif Gujarati' => self::GOOGLE,
				'Noto Serif Gurmukhi' => self::GOOGLE,
				'Noto Serif HK' => self::GOOGLE,
				'Noto Serif Hebrew' => self::GOOGLE,
				'Noto Serif JP' => self::GOOGLE,
				'Noto Serif KR' => self::GOOGLE,
				'Noto Serif Kannada' => self::GOOGLE,
				'Noto Serif Khitan Small Script' => self::GOOGLE,
				'Noto Serif Khmer' => self::GOOGLE,
				'Noto Serif Khojki' => self::GOOGLE,
				'Noto Serif Lao' => self::GOOGLE,
				'Noto Serif Makasar' => self::GOOGLE,
				'Noto Serif Malayalam' => self::GOOGLE,
				'Noto Serif Myanmar' => self::GOOGLE,
				'Noto Serif NP Hmong' => self::GOOGLE,
				'Noto Serif Nyiakeng Puachue Hmong' => self::GOOGLE,
				'Noto Serif Old Uyghur' => self::GOOGLE,
				'Noto Serif Oriya' => self::GOOGLE,
				'Noto Serif Ottoman Siyaq' => self::GOOGLE,
				'Noto Serif SC' => self::GOOGLE,
				'Noto Serif Sinhala' => self::GOOGLE,
				'Noto Serif TC' => self::GOOGLE,
				'Noto Serif Tamil' => self::GOOGLE,
				'Noto Serif Tangut' => self::GOOGLE,
				'Noto Serif Telugu' => self::GOOGLE,
				'Noto Serif Thai' => self::GOOGLE,
				'Noto Serif Tibetan' => self::GOOGLE,
				'Noto Serif Toto' => self::GOOGLE,
				'Noto Serif Vithkuqi' => self::GOOGLE,
				'Noto Serif Yezidi' => self::GOOGLE,
				'Noto Traditional Nushu' => self::GOOGLE,
				'Noto Znamenny Musical Notation' => self::GOOGLE,
				'Nova Cut' => self::GOOGLE,
				'Nova Flat' => self::GOOGLE,
				'Nova Mono' => self::GOOGLE,
				'Nova Oval' => self::GOOGLE,
				'Nova Round' => self::GOOGLE,
				'Nova Script' => self::GOOGLE,
				'Nova Slim' => self::GOOGLE,
				'Nova Square' => self::GOOGLE,
				'Numans' => self::GOOGLE,
				'Nunito' => self::GOOGLE,
				'Nunito Sans' => self::GOOGLE,
				'Nuosu SIL' => self::GOOGLE,
				'Odibee Sans' => self::GOOGLE,
				'Odor Mean Chey' => self::GOOGLE,
				'Offside' => self::GOOGLE,
				'Oi' => self::GOOGLE,
				'Ojuju' => self::GOOGLE,
				'Old Standard TT' => self::GOOGLE,
				'Oldenburg' => self::GOOGLE,
				'Ole' => self::GOOGLE,
				'Oleo Script' => self::GOOGLE,
				'Oleo Script Swash Caps' => self::GOOGLE,
				'Onest' => self::GOOGLE,
				'Oooh Baby' => self::GOOGLE,
				'Open Sans' => self::GOOGLE,
				'Open Sans Hebrew' => self::EARLYACCESS, // Hack for Google Early Access.
				'Open Sans Hebrew Condensed' => self::EARLYACCESS, // Hack for Google Early Access.
				'Oranienbaum' => self::GOOGLE,
				'Orbit' => self::GOOGLE,
				'Orbitron' => self::GOOGLE,
				'Oregano' => self::GOOGLE,
				'Orelega One' => self::GOOGLE,
				'Orienta' => self::GOOGLE,
				'Original Surfer' => self::GOOGLE,
				'Oswald' => self::GOOGLE,
				'Outfit' => self::GOOGLE,
				'Over the Rainbow' => self::GOOGLE,
				'Overlock' => self::GOOGLE,
				'Overlock SC' => self::GOOGLE,
				'Overpass' => self::GOOGLE,
				'Overpass Mono' => self::GOOGLE,
				'Ovo' => self::GOOGLE,
				'Oxanium' => self::GOOGLE,
				'Oxygen' => self::GOOGLE,
				'Oxygen Mono' => self::GOOGLE,
				'PT Mono' => self::GOOGLE,
				'PT Sans' => self::GOOGLE,
				'PT Sans Caption' => self::GOOGLE,
				'PT Sans Narrow' => self::GOOGLE,
				'PT Serif' => self::GOOGLE,
				'PT Serif Caption' => self::GOOGLE,
				'Pacifico' => self::GOOGLE,
				'Padauk' => self::GOOGLE,
				'Padyakke Expanded One' => self::GOOGLE,
				'Palanquin' => self::GOOGLE,
				'Palanquin Dark' => self::GOOGLE,
				'Palette Mosaic' => self::GOOGLE,
				'Pangolin' => self::GOOGLE,
				'Paprika' => self::GOOGLE,
				'Parisienne' => self::GOOGLE,
				'Passero One' => self::GOOGLE,
				'Passion One' => self::GOOGLE,
				'Passions Conflict' => self::GOOGLE,
				'Pathway Extreme' => self::GOOGLE,
				'Pathway Gothic One' => self::GOOGLE,
				'Patrick Hand' => self::GOOGLE,
				'Patrick Hand SC' => self::GOOGLE,
				'Pattaya' => self::GOOGLE,
				'Patua One' => self::GOOGLE,
				'Pavanam' => self::GOOGLE,
				'Paytone One' => self::GOOGLE,
				'Peddana' => self::GOOGLE,
				'Peralta' => self::GOOGLE,
				'Permanent Marker' => self::GOOGLE,
				'Petemoss' => self::GOOGLE,
				'Petit Formal Script' => self::GOOGLE,
				'Petrona' => self::GOOGLE,
				'Philosopher' => self::GOOGLE,
				'Phudu' => self::GOOGLE,
				'Piazzolla' => self::GOOGLE,
				'Piedra' => self::GOOGLE,
				'Pinyon Script' => self::GOOGLE,
				'Pirata One' => self::GOOGLE,
				'Pixelify Sans' => self::GOOGLE,
				'Plaster' => self::GOOGLE,
				'Platypi' => self::GOOGLE,
				'Play' => self::GOOGLE,
				'Playball' => self::GOOGLE,
				'Playfair' => self::GOOGLE,
				'Playfair Display' => self::GOOGLE,
				'Playfair Display SC' => self::GOOGLE,
				'Playpen Sans' => self::GOOGLE,
				'Plus Jakarta Sans' => self::GOOGLE,
				'Podkova' => self::GOOGLE,
				'Poetsen One' => self::GOOGLE,
				'Poiret One' => self::GOOGLE,
				'Poller One' => self::GOOGLE,
				'Poltawski Nowy' => self::GOOGLE,
				'Poly' => self::GOOGLE,
				'Pompiere' => self::GOOGLE,
				'Pontano Sans' => self::GOOGLE,
				'Poor Story' => self::GOOGLE,
				'Poppins' => self::GOOGLE,
				'Port Lligat Sans' => self::GOOGLE,
				'Port Lligat Slab' => self::GOOGLE,
				'Potta One' => self::GOOGLE,
				'Pragati Narrow' => self::GOOGLE,
				'Praise' => self::GOOGLE,
				'Prata' => self::GOOGLE,
				'Preahvihear' => self::GOOGLE,
				'Press Start 2P' => self::GOOGLE,
				'Pridi' => self::GOOGLE,
				'Princess Sofia' => self::GOOGLE,
				'Prociono' => self::GOOGLE,
				'Prompt' => self::GOOGLE,
				'Prosto One' => self::GOOGLE,
				'Protest Guerrilla' => self::GOOGLE,
				'Protest Revolution' => self::GOOGLE,
				'Protest Riot' => self::GOOGLE,
				'Protest Strike' => self::GOOGLE,
				'Proza Libre' => self::GOOGLE,
				'Public Sans' => self::GOOGLE,
				'Puppies Play' => self::GOOGLE,
				'Puritan' => self::GOOGLE,
				'Purple Purse' => self::GOOGLE,
				'Qahiri' => self::GOOGLE,
				'Quando' => self::GOOGLE,
				'Quantico' => self::GOOGLE,
				'Quattrocento' => self::GOOGLE,
				'Quattrocento Sans' => self::GOOGLE,
				'Questrial' => self::GOOGLE,
				'Quicksand' => self::GOOGLE,
				'Quintessential' => self::GOOGLE,
				'Qwigley' => self::GOOGLE,
				'Qwitcher Grypen' => self::GOOGLE,
				'REM' => self::GOOGLE,
				'Racing Sans One' => self::GOOGLE,
				'Radio Canada' => self::GOOGLE,
				'Radio Canada Big' => self::GOOGLE,
				'Radley' => self::GOOGLE,
				'Rajdhani' => self::GOOGLE,
				'Rakkas' => self::GOOGLE,
				'Raleway' => self::GOOGLE,
				'Raleway Dots' => self::GOOGLE,
				'Ramabhadra' => self::GOOGLE,
				'Ramaraja' => self::GOOGLE,
				'Rambla' => self::GOOGLE,
				'Rammetto One' => self::GOOGLE,
				'Rampart One' => self::GOOGLE,
				'Ranchers' => self::GOOGLE,
				'Rancho' => self::GOOGLE,
				'Ranga' => self::GOOGLE,
				'Rasa' => self::GOOGLE,
				'Rationale' => self::GOOGLE,
				'Ravi Prakash' => self::GOOGLE,
				'Readex Pro' => self::GOOGLE,
				'Recursive' => self::GOOGLE,
				'Red Hat Display' => self::GOOGLE,
				'Red Hat Mono' => self::GOOGLE,
				'Red Hat Text' => self::GOOGLE,
				'Red Rose' => self::GOOGLE,
				'Redacted' => self::GOOGLE,
				'Redacted Script' => self::GOOGLE,
				'Reddit Mono' => self::GOOGLE,
				'Reddit Sans' => self::GOOGLE,
				'Reddit Sans Condensed' => self::GOOGLE,
				'Redressed' => self::GOOGLE,
				'Reem Kufi' => self::GOOGLE,
				'Reem Kufi Fun' => self::GOOGLE,
				'Reem Kufi Ink' => self::GOOGLE,
				'Reenie Beanie' => self::GOOGLE,
				'Reggae One' => self::GOOGLE,
				'Rethink Sans' => self::GOOGLE,
				'Revalia' => self::GOOGLE,
				'Rhodium Libre' => self::GOOGLE,
				'Ribeye' => self::GOOGLE,
				'Ribeye Marrow' => self::GOOGLE,
				'Righteous' => self::GOOGLE,
				'Risque' => self::GOOGLE,
				'Road Rage' => self::GOOGLE,
				'Roboto' => self::GOOGLE,
				'Roboto Condensed' => self::GOOGLE,
				'Roboto Flex' => self::GOOGLE,
				'Roboto Mono' => self::GOOGLE,
				'Roboto Serif' => self::GOOGLE,
				'Roboto Slab' => self::GOOGLE,
				'Rochester' => self::GOOGLE,
				'Rock 3D' => self::GOOGLE,
				'Rock Salt' => self::GOOGLE,
				'RocknRoll One' => self::GOOGLE,
				'Rokkitt' => self::GOOGLE,
				'Romanesco' => self::GOOGLE,
				'Ropa Sans' => self::GOOGLE,
				'Rosario' => self::GOOGLE,
				'Rosarivo' => self::GOOGLE,
				'Rouge Script' => self::GOOGLE,
				'Rowdies' => self::GOOGLE,
				'Rozha One' => self::GOOGLE,
				'Rubik' => self::GOOGLE,
				'Rubik 80s Fade' => self::GOOGLE,
				'Rubik Beastly' => self::GOOGLE,
				'Rubik Broken Fax' => self::GOOGLE,
				'Rubik Bubbles' => self::GOOGLE,
				'Rubik Burned' => self::GOOGLE,
				'Rubik Dirt' => self::GOOGLE,
				'Rubik Distressed' => self::GOOGLE,
				'Rubik Doodle Shadow' => self::GOOGLE,
				'Rubik Doodle Triangles' => self::GOOGLE,
				'Rubik Gemstones' => self::GOOGLE,
				'Rubik Glitch' => self::GOOGLE,
				'Rubik Glitch Pop' => self::GOOGLE,
				'Rubik Iso' => self::GOOGLE,
				'Rubik Lines' => self::GOOGLE,
				'Rubik Maps' => self::GOOGLE,
				'Rubik Marker Hatch' => self::GOOGLE,
				'Rubik Maze' => self::GOOGLE,
				'Rubik Microbe' => self::GOOGLE,
				'Rubik Mono One' => self::GOOGLE,
				'Rubik Moonrocks' => self::GOOGLE,
				'Rubik Pixels' => self::GOOGLE,
				'Rubik Puddles' => self::GOOGLE,
				'Rubik Scribble' => self::GOOGLE,
				'Rubik Spray Paint' => self::GOOGLE,
				'Rubik Storm' => self::GOOGLE,
				'Rubik Vinyl' => self::GOOGLE,
				'Rubik Wet Paint' => self::GOOGLE,
				'Ruda' => self::GOOGLE,
				'Rufina' => self::GOOGLE,
				'Ruge Boogie' => self::GOOGLE,
				'Ruluko' => self::GOOGLE,
				'Rum Raisin' => self::GOOGLE,
				'Ruslan Display' => self::GOOGLE,
				'Russo One' => self::GOOGLE,
				'Ruthie' => self::GOOGLE,
				'Ruwudu' => self::GOOGLE,
				'Rye' => self::GOOGLE,
				'STIX Two Text' => self::GOOGLE,
				'Sacramento' => self::GOOGLE,
				'Sahitya' => self::GOOGLE,
				'Sail' => self::GOOGLE,
				'Saira' => self::GOOGLE,
				'Saira Condensed' => self::GOOGLE,
				'Saira Extra Condensed' => self::GOOGLE,
				'Saira Semi Condensed' => self::GOOGLE,
				'Saira Stencil One' => self::GOOGLE,
				'Salsa' => self::GOOGLE,
				'Sanchez' => self::GOOGLE,
				'Sancreek' => self::GOOGLE,
				'Sansita' => self::GOOGLE,
				'Sansita Swashed' => self::GOOGLE,
				'Sarabun' => self::GOOGLE,
				'Sarala' => self::GOOGLE,
				'Sarina' => self::GOOGLE,
				'Sarpanch' => self::GOOGLE,
				'Sassy Frass' => self::GOOGLE,
				'Satisfy' => self::GOOGLE,
				'Sawarabi Gothic' => self::GOOGLE,
				'Sawarabi Mincho' => self::GOOGLE,
				'Scada' => self::GOOGLE,
				'Scheherazade New' => self::GOOGLE,
				'Schibsted Grotesk' => self::GOOGLE,
				'Schoolbell' => self::GOOGLE,
				'Scope One' => self::GOOGLE,
				'Seaweed Script' => self::GOOGLE,
				'Secular One' => self::GOOGLE,
				'Sedan' => self::GOOGLE,
				'Sedan SC' => self::GOOGLE,
				'Sedgwick Ave' => self::GOOGLE,
				'Sedgwick Ave Display' => self::GOOGLE,
				'Sen' => self::GOOGLE,
				'Send Flowers' => self::GOOGLE,
				'Sevillana' => self::GOOGLE,
				'Seymour One' => self::GOOGLE,
				'Shadows Into Light' => self::GOOGLE,
				'Shadows Into Light Two' => self::GOOGLE,
				'Shalimar' => self::GOOGLE,
				'Shantell Sans' => self::GOOGLE,
				'Shanti' => self::GOOGLE,
				'Share' => self::GOOGLE,
				'Share Tech' => self::GOOGLE,
				'Share Tech Mono' => self::GOOGLE,
				'Shippori Antique' => self::GOOGLE,
				'Shippori Antique B1' => self::GOOGLE,
				'Shippori Mincho' => self::GOOGLE,
				'Shippori Mincho B1' => self::GOOGLE,
				'Shizuru' => self::GOOGLE,
				'Shojumaru' => self::GOOGLE,
				'Short Stack' => self::GOOGLE,
				'Shrikhand' => self::GOOGLE,
				'Siemreap' => self::GOOGLE,
				'Sigmar' => self::GOOGLE,
				'Sigmar One' => self::GOOGLE,
				'Signika' => self::GOOGLE,
				'Signika Negative' => self::GOOGLE,
				'Silkscreen' => self::GOOGLE,
				'Simonetta' => self::GOOGLE,
				'Single Day' => self::GOOGLE,
				'Sintony' => self::GOOGLE,
				'Sirin Stencil' => self::GOOGLE,
				'Six Caps' => self::GOOGLE,
				'Sixtyfour' => self::GOOGLE,
				'Skranji' => self::GOOGLE,
				'Slabo 13px' => self::GOOGLE,
				'Slabo 27px' => self::GOOGLE,
				'Slackey' => self::GOOGLE,
				'Slackside One' => self::GOOGLE,
				'Smokum' => self::GOOGLE,
				'Smooch' => self::GOOGLE,
				'Smooch Sans' => self::GOOGLE,
				'Smythe' => self::GOOGLE,
				'Sniglet' => self::GOOGLE,
				'Snippet' => self::GOOGLE,
				'Snowburst One' => self::GOOGLE,
				'Sofadi One' => self::GOOGLE,
				'Sofia' => self::GOOGLE,
				'Sofia Sans' => self::GOOGLE,
				'Sofia Sans Condensed' => self::GOOGLE,
				'Sofia Sans Extra Condensed' => self::GOOGLE,
				'Sofia Sans Semi Condensed' => self::GOOGLE,
				'Solitreo' => self::GOOGLE,
				'Solway' => self::GOOGLE,
				'Sometype Mono' => self::GOOGLE,
				'Song Myung' => self::GOOGLE,
				'Sono' => self::GOOGLE,
				'Sonsie One' => self::GOOGLE,
				'Sora' => self::GOOGLE,
				'Sorts Mill Goudy' => self::GOOGLE,
				'Source Code Pro' => self::GOOGLE,
				'Source Sans 3' => self::GOOGLE,
				'Source Sans Pro' => self::GOOGLE,
				'Source Serif 4' => self::GOOGLE,
				'Source Serif Pro' => self::GOOGLE,
				'Space Grotesk' => self::GOOGLE,
				'Space Mono' => self::GOOGLE,
				'Special Elite' => self::GOOGLE,
				'Spectral' => self::GOOGLE,
				'Spectral SC' => self::GOOGLE,
				'Spicy Rice' => self::GOOGLE,
				'Spinnaker' => self::GOOGLE,
				'Spirax' => self::GOOGLE,
				'Splash' => self::GOOGLE,
				'Spline Sans' => self::GOOGLE,
				'Spline Sans Mono' => self::GOOGLE,
				'Squada One' => self::GOOGLE,
				'Square Peg' => self::GOOGLE,
				'Sree Krushnadevaraya' => self::GOOGLE,
				'Sriracha' => self::GOOGLE,
				'Srisakdi' => self::GOOGLE,
				'Staatliches' => self::GOOGLE,
				'Stalemate' => self::GOOGLE,
				'Stalinist One' => self::GOOGLE,
				'Stardos Stencil' => self::GOOGLE,
				'Stick' => self::GOOGLE,
				'Stick No Bills' => self::GOOGLE,
				'Stint Ultra Condensed' => self::GOOGLE,
				'Stint Ultra Expanded' => self::GOOGLE,
				'Stoke' => self::GOOGLE,
				'Strait' => self::GOOGLE,
				'Style Script' => self::GOOGLE,
				'Stylish' => self::GOOGLE,
				'Sue Ellen Francisco' => self::GOOGLE,
				'Suez One' => self::GOOGLE,
				'Sulphur Point' => self::GOOGLE,
				'Sumana' => self::GOOGLE,
				'Sunflower' => self::GOOGLE,
				'Sunshiney' => self::GOOGLE,
				'Supermercado One' => self::GOOGLE,
				'Sura' => self::GOOGLE,
				'Suranna' => self::GOOGLE,
				'Suravaram' => self::GOOGLE,
				'Suwannaphum' => self::GOOGLE,
				'Swanky and Moo Moo' => self::GOOGLE,
				'Syncopate' => self::GOOGLE,
				'Syne' => self::GOOGLE,
				'Syne Mono' => self::GOOGLE,
				'Syne Tactile' => self::GOOGLE,
				'Tac One' => self::GOOGLE,
				'Tai Heritage Pro' => self::GOOGLE,
				'Tajawal' => self::GOOGLE,
				'Tangerine' => self::GOOGLE,
				'Tapestry' => self::GOOGLE,
				'Taprom' => self::GOOGLE,
				'Tauri' => self::GOOGLE,
				'Taviraj' => self::GOOGLE,
				'Teachers' => self::GOOGLE,
				'Teko' => self::GOOGLE,
				'Tektur' => self::GOOGLE,
				'Telex' => self::GOOGLE,
				'Tenali Ramakrishna' => self::GOOGLE,
				'Tenor Sans' => self::GOOGLE,
				'Text Me One' => self::GOOGLE,
				'Texturina' => self::GOOGLE,
				'Thasadith' => self::GOOGLE,
				'The Girl Next Door' => self::GOOGLE,
				'The Nautigal' => self::GOOGLE,
				'Tienne' => self::GOOGLE,
				'Tillana' => self::GOOGLE,
				'Tilt Neon' => self::GOOGLE,
				'Tilt Prism' => self::GOOGLE,
				'Tilt Warp' => self::GOOGLE,
				'Timmana' => self::GOOGLE,
				'Tinos' => self::GOOGLE,
				'Tiro Bangla' => self::GOOGLE,
				'Tiro Devanagari Hindi' => self::GOOGLE,
				'Tiro Devanagari Marathi' => self::GOOGLE,
				'Tiro Devanagari Sanskrit' => self::GOOGLE,
				'Tiro Gurmukhi' => self::GOOGLE,
				'Tiro Kannada' => self::GOOGLE,
				'Tiro Tamil' => self::GOOGLE,
				'Tiro Telugu' => self::GOOGLE,
				'Titan One' => self::GOOGLE,
				'Titillium Web' => self::GOOGLE,
				'Tomorrow' => self::GOOGLE,
				'Tourney' => self::GOOGLE,
				'Trade Winds' => self::GOOGLE,
				'Train One' => self::GOOGLE,
				'Trirong' => self::GOOGLE,
				'Trispace' => self::GOOGLE,
				'Trocchi' => self::GOOGLE,
				'Trochut' => self::GOOGLE,
				'Truculenta' => self::GOOGLE,
				'Trykker' => self::GOOGLE,
				'Tsukimi Rounded' => self::GOOGLE,
				'Tulpen One' => self::GOOGLE,
				'Turret Road' => self::GOOGLE,
				'Twinkle Star' => self::GOOGLE,
				'Ubuntu' => self::GOOGLE,
				'Ubuntu Condensed' => self::GOOGLE,
				'Ubuntu Mono' => self::GOOGLE,
				'Ubuntu Sans' => self::GOOGLE,
				'Ubuntu Sans Mono' => self::GOOGLE,
				'Uchen' => self::GOOGLE,
				'Ultra' => self::GOOGLE,
				'Unbounded' => self::GOOGLE,
				'Uncial Antiqua' => self::GOOGLE,
				'Underdog' => self::GOOGLE,
				'Unica One' => self::GOOGLE,
				'UnifrakturCook' => self::GOOGLE,
				'UnifrakturMaguntia' => self::GOOGLE,
				'Unkempt' => self::GOOGLE,
				'Unlock' => self::GOOGLE,
				'Unna' => self::GOOGLE,
				'Updock' => self::GOOGLE,
				'Urbanist' => self::GOOGLE,
				'VT323' => self::GOOGLE,
				'Vampiro One' => self::GOOGLE,
				'Varela' => self::GOOGLE,
				'Varela Round' => self::GOOGLE,
				'Varta' => self::GOOGLE,
				'Vast Shadow' => self::GOOGLE,
				'Vazirmatn' => self::GOOGLE,
				'Vesper Libre' => self::GOOGLE,
				'Viaoda Libre' => self::GOOGLE,
				'Vibes' => self::GOOGLE,
				'Vibur' => self::GOOGLE,
				'Victor Mono' => self::GOOGLE,
				'Vidaloka' => self::GOOGLE,
				'Viga' => self::GOOGLE,
				'Vina Sans' => self::GOOGLE,
				'Voces' => self::GOOGLE,
				'Volkhov' => self::GOOGLE,
				'Vollkorn' => self::GOOGLE,
				'Vollkorn SC' => self::GOOGLE,
				'Voltaire' => self::GOOGLE,
				'Vujahday Script' => self::GOOGLE,
				'Waiting for the Sunrise' => self::GOOGLE,
				'Wallpoet' => self::GOOGLE,
				'Walter Turncoat' => self::GOOGLE,
				'Warnes' => self::GOOGLE,
				'Water Brush' => self::GOOGLE,
				'Waterfall' => self::GOOGLE,
				'Wavefont' => self::GOOGLE,
				'Wellfleet' => self::GOOGLE,
				'Wendy One' => self::GOOGLE,
				'Whisper' => self::GOOGLE,
				'WindSong' => self::GOOGLE,
				'Wire One' => self::GOOGLE,
				'Wix Madefor Display' => self::GOOGLE,
				'Wix Madefor Text' => self::GOOGLE,
				'Work Sans' => self::GOOGLE,
				'Workbench' => self::GOOGLE,
				'Xanh Mono' => self::GOOGLE,
				'Yaldevi' => self::GOOGLE,
				'Yanone Kaffeesatz' => self::GOOGLE,
				'Yantramanav' => self::GOOGLE,
				'Yarndings 12' => self::GOOGLE,
				'Yarndings 12 Charted' => self::GOOGLE,
				'Yarndings 20' => self::GOOGLE,
				'Yarndings 20 Charted' => self::GOOGLE,
				'Yatra One' => self::GOOGLE,
				'Yellowtail' => self::GOOGLE,
				'Yeon Sung' => self::GOOGLE,
				'Yeseva One' => self::GOOGLE,
				'Yesteryear' => self::GOOGLE,
				'Yomogi' => self::GOOGLE,
				'Young Serif' => self::GOOGLE,
				'Yrsa' => self::GOOGLE,
				'Ysabeau' => self::GOOGLE,
				'Ysabeau Infant' => self::GOOGLE,
				'Ysabeau Office' => self::GOOGLE,
				'Ysabeau SC' => self::GOOGLE,
				'Yuji Boku' => self::GOOGLE,
				'Yuji Hentaigana Akari' => self::GOOGLE,
				'Yuji Hentaigana Akebono' => self::GOOGLE,
				'Yuji Mai' => self::GOOGLE,
				'Yuji Syuku' => self::GOOGLE,
				'Yusei Magic' => self::GOOGLE,
				'ZCOOL KuaiLe' => self::GOOGLE,
				'ZCOOL QingKe HuangYou' => self::GOOGLE,
				'ZCOOL XiaoWei' => self::GOOGLE,
				'Zen Antique' => self::GOOGLE,
				'Zen Antique Soft' => self::GOOGLE,
				'Zen Dots' => self::GOOGLE,
				'Zen Kaku Gothic Antique' => self::GOOGLE,
				'Zen Kaku Gothic New' => self::GOOGLE,
				'Zen Kurenaido' => self::GOOGLE,
				'Zen Loop' => self::GOOGLE,
				'Zen Maru Gothic' => self::GOOGLE,
				'Zen Old Mincho' => self::GOOGLE,
				'Zen Tokyo Zoo' => self::GOOGLE,
				'Zeyada' => self::GOOGLE,
				'Zhi Mang Xing' => self::GOOGLE,
				'Zilla Slab' => self::GOOGLE,
				'Zilla Slab Highlight' => self::GOOGLE,
			] );
		}

		return $fonts;
	}

	/**
	 * Get font type.
	 *
	 * Retrieve the font type for a given font.
	 *
	 * @since 1.0.0
	 * @access public
	 * @static
	 *
	 * @param string $name Font name.
	 *
	 * @return string|false Font type, or false if font doesn't exist.
	 */
	public static function get_font_type( $name ) {
		$fonts = self::get_fonts();

		if ( empty( $fonts[ $name ] ) ) {
			return false;
		}

		return $fonts[ $name ];
	}

	/**
	 * Get fonts by group.
	 *
	 * Retrieve all the fonts belong to specific group.
	 *
	 * @since 1.0.0
	 * @access public
	 * @static
	 *
	 * @param array $groups Optional. Font group. Default is an empty array.
	 *
	 * @return array Font type, or false if font doesn't exist.
	 */
	public static function get_fonts_by_groups( $groups = [] ) {
		return array_filter( self::get_fonts(), function( $font ) use ( $groups ) {
			return in_array( $font, $groups );
		} );
	}

	public static function is_google_fonts_enabled(): bool {
		if ( null === static::$is_google_fonts_enabled ) {
			$default_value = '1';

			// TODO: For future use, using for new installs.
			// $is_new_site = Upgrade_Manager::install_compare( '3.10.0', '>=' );
			// $default_value = $is_new_site ? '0' : '1';.

			$option = get_option( 'elementor_google_font', $default_value );

			static::$is_google_fonts_enabled = '1' === $option;
		}

		return static::$is_google_fonts_enabled;
	}

	public static function get_font_display_setting() {
		return get_option( 'elementor_font_display', 'auto' );
	}

	public static function reset_local_cache() {
		static::$is_google_fonts_enabled = null;
		static::$font_groups = null;
	}
}
