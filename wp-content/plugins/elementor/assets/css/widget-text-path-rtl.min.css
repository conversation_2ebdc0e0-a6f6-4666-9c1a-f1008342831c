/*! elementor - v3.29.0 - 04-06-2025 */
.elementor-widget-text-path{font-size:20px;text-align:var(--alignment,start)}.elementor-widget-text-path svg{height:auto;max-width:100%;overflow:visible;transform:rotate(var(--rotate,0)) scaleX(var(--scale-x,1)) scaleY(var(--scale-y,1));width:var(--width);word-spacing:var(--word-spacing)}.elementor-widget-text-path svg path{vector-effect:non-scaling-stroke;fill:var(--path-fill,transparent);stroke:var(--stroke-color,transparent);stroke-width:var(--stroke-width,1px);transition:var(--stroke-transition) stroke,var(--stroke-transition) fill}.elementor-widget-text-path svg:hover path{--path-fill:var( --path-fill-hover );--stroke-color:var( --stroke-color-hover );--stroke-width:var( --stroke-width-hover )}.elementor-widget-text-path svg text{--fill:var( --text-color );fill:var(--fill);direction:var(--direction,rtl);transition:var(--transition) stroke,var(--transition) stroke-width,var(--transition) fill}.elementor-widget-text-path svg text:hover{--color:var( --text-color-hover,var( --text-color ) );--fill:var( --color );color:var(--color)}