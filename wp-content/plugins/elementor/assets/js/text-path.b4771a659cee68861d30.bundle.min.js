/*! elementor - v3.29.0 - 04-06-2025 */
/*! For license information please see text-path.b4771a659cee68861d30.bundle.min.js.LICENSE.txt */
"use strict";(self.webpackChunkelementorFrontend=self.webpackChunkelementorFrontend||[]).push([[30],{241:(e,t,n)=>{var o=n(6784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=n(7672),r=o(n(4786));class TextPathHandler extends elementorModules.frontend.handlers.Base{getDefaultSettings(){return{selectors:{pathContainer:".e-text-path",svg:".e-text-path > svg"}}}getDefaultElements(){const{selectors:e}=this.getSettings(),t=this.$element[0];return{widgetWrapper:t,pathContainer:t.querySelector(e.pathContainer),svg:t.querySelector(e.svg),textPath:t.querySelector(e.textPath)}}onInit(){this.elements=this.getDefaultElements(),this.fetchSVG().then((()=>{this.pathId=`e-path-${this.elements.widgetWrapper.dataset.id}`,this.textPathId=`e-text-path-${this.elements.widgetWrapper.dataset.id}`,this.elements.svg&&this.initTextPath()}))}fetchSVG(){const{url:e}=this.elements.pathContainer.dataset;return e&&e.endsWith(".svg")?fetch(e).then((e=>e.text())).then((e=>{this.elements.pathContainer.innerHTML=r.default.sanitize(e),this.elements=this.getDefaultElements()})):Promise.reject(e)}setOffset(e){this.elements.textPath&&(this.isRTL()&&(e=100-parseInt(e)),this.elements.textPath.setAttribute("startOffset",e+"%"))}onElementChange(e){const{start_point:t,text:n}=this.getElementSettings();switch(e){case"start_point":this.setOffset(t.size);break;case"text":this.setText(n);break;case"text_path_direction":this.setOffset(t.size),this.setText(n)}}attachIdToPath(){(this.elements.svg.querySelector("[data-path-anchor]")||this.elements.svg.querySelector("path")).id=this.pathId}initTextPath(){const{start_point:e}=this.getElementSettings(),t=this.elements.pathContainer.dataset.text;this.attachIdToPath(),this.elements.svg.innerHTML+=`\n\t\t\t<text>\n\t\t\t\t<textPath id="${this.textPathId}" href="#${this.pathId}"></textPath>\n\t\t\t</text>\n\t\t`,this.elements.textPath=this.elements.svg.querySelector(`#${this.textPathId}`),this.setOffset(e.size),this.setText(t)}setText(e){const{is_external:t,nofollow:n}=this.getElementSettings().link,{linkUrl:o}=this.elements.pathContainer.dataset,i=t?"_blank":"",l=n?"nofollow":"";o&&(e=`<a href="${(0,a.escapeHTML)(o)}" rel="${l}" target="${i}">${(0,a.escapeHTML)(e)}</a>`,e=r.default.sanitize(e,{ADD_ATTR:["target"]})),this.elements.textPath.innerHTML=e;const s=this.elements.svg.querySelector(`#${this.textPathId}-clone`);if(s&&s.remove(),this.shouldReverseText()){const t=this.elements.textPath.cloneNode();t.id+="-clone",t.classList.add("elementor-hidden"),t.textContent=e,this.elements.textPath.parentNode.appendChild(t),this.reverseToRTL()}}isRTL(){const{text_path_direction:e}=this.getElementSettings();let t=elementorFrontend.config.is_rtl;return e&&(t="rtl"===e),t}shouldReverseText(){if(!this.isRTL())return!1;if(elementorFrontend.utils.environment.firefox)return!1;return!elementorFrontend.utils.environment.blink||!this.isFixedChromiumVersion()}isFixedChromiumVersion(){return parseInt(navigator.userAgent.match(/(?:Chrom(?:e|ium)|Edg)\/([0-9]+)\./)[1])>=96}reverseToRTL(){let e=this.elements.textPath;e=e.querySelector("a")||e;e.textContent=e.textContent.replace(/([\u0591-\u07FF\u200F\u202B\u202E\uFB1D-\uFDFD\uFE70-\uFEFC\s$&+,:;=?@#|'<>.^*()%!-]+)/gi,(e=>e.split("").reverse().join(""))),e.setAttribute("aria-hidden",!0)}}t.default=TextPathHandler},4786:e=>{const{entries:t,setPrototypeOf:n,isFrozen:o,getPrototypeOf:a,getOwnPropertyDescriptor:r}=Object;let{freeze:i,seal:l,create:s}=Object,{apply:c,construct:u}="undefined"!=typeof Reflect&&Reflect;i||(i=function freeze(e){return e}),l||(l=function seal(e){return e}),c||(c=function apply(e,t,n){return e.apply(t,n)}),u||(u=function construct(e,t){return new e(...t)});const d=unapply(Array.prototype.forEach),p=unapply(Array.prototype.lastIndexOf),m=unapply(Array.prototype.pop),f=unapply(Array.prototype.push),h=unapply(Array.prototype.splice),g=unapply(String.prototype.toLowerCase),T=unapply(String.prototype.toString),y=unapply(String.prototype.match),S=unapply(String.prototype.replace),_=unapply(String.prototype.indexOf),E=unapply(String.prototype.trim),A=unapply(Object.prototype.hasOwnProperty),b=unapply(RegExp.prototype.test),x=function unconstruct(e){return function(){for(var t=arguments.length,n=new Array(t),o=0;o<t;o++)n[o]=arguments[o];return u(e,n)}}(TypeError);function unapply(e){return function(t){t instanceof RegExp&&(t.lastIndex=0);for(var n=arguments.length,o=new Array(n>1?n-1:0),a=1;a<n;a++)o[a-1]=arguments[a];return c(e,t,o)}}function addToSet(e,t){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:g;n&&n(e,null);let r=t.length;for(;r--;){let n=t[r];if("string"==typeof n){const e=a(n);e!==n&&(o(t)||(t[r]=e),n=e)}e[n]=!0}return e}function cleanArray(e){for(let t=0;t<e.length;t++){A(e,t)||(e[t]=null)}return e}function clone(e){const n=s(null);for(const[o,a]of t(e)){A(e,o)&&(Array.isArray(a)?n[o]=cleanArray(a):a&&"object"==typeof a&&a.constructor===Object?n[o]=clone(a):n[o]=a)}return n}function lookupGetter(e,t){for(;null!==e;){const n=r(e,t);if(n){if(n.get)return unapply(n.get);if("function"==typeof n.value)return unapply(n.value)}e=a(e)}return function fallbackValue(){return null}}const N=i(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),v=i(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),R=i(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),w=i(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),C=i(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),k=i(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),D=i(["#text"]),O=i(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","popover","popovertarget","popovertargetaction","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","wrap","xmlns","slot"]),L=i(["accent-height","accumulate","additive","alignment-baseline","amplitude","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","exponent","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","intercept","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","slope","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","tablevalues","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),I=i(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),M=i(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),P=l(/\{\{[\w\W]*|[\w\W]*\}\}/gm),z=l(/<%[\w\W]*|[\w\W]*%>/gm),H=l(/\$\{[\w\W]*/gm),F=l(/^data-[\-\w.\u00B7-\uFFFF]+$/),U=l(/^aria-[\-\w]+$/),G=l(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp|matrix):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),B=l(/^(?:\w+script|data):/i),W=l(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),Y=l(/^html$/i),j=l(/^[a-z][.\w]*(-[.\w]+)+$/i);var $=Object.freeze({__proto__:null,ARIA_ATTR:U,ATTR_WHITESPACE:W,CUSTOM_ELEMENT:j,DATA_ATTR:F,DOCTYPE_NAME:Y,ERB_EXPR:z,IS_ALLOWED_URI:G,IS_SCRIPT_OR_DATA:B,MUSTACHE_EXPR:P,TMPLIT_EXPR:H});const q=1,V=3,X=7,K=8,Z=9,J=function getGlobal(){return"undefined"==typeof window?null:window};var Q=function createDOMPurify(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:J();const DOMPurify=e=>createDOMPurify(e);if(DOMPurify.version="3.2.6",DOMPurify.removed=[],!e||!e.document||e.document.nodeType!==Z||!e.Element)return DOMPurify.isSupported=!1,DOMPurify;let{document:n}=e;const o=n,a=o.currentScript,{DocumentFragment:r,HTMLTemplateElement:l,Node:c,Element:u,NodeFilter:P,NamedNodeMap:z=e.NamedNodeMap||e.MozNamedAttrMap,HTMLFormElement:H,DOMParser:F,trustedTypes:U}=e,B=u.prototype,W=lookupGetter(B,"cloneNode"),j=lookupGetter(B,"remove"),Q=lookupGetter(B,"nextSibling"),ee=lookupGetter(B,"childNodes"),te=lookupGetter(B,"parentNode");if("function"==typeof l){const e=n.createElement("template");e.content&&e.content.ownerDocument&&(n=e.content.ownerDocument)}let ne,oe="";const{implementation:ae,createNodeIterator:re,createDocumentFragment:ie,getElementsByTagName:le}=n,{importNode:se}=o;let ce={afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]};DOMPurify.isSupported="function"==typeof t&&"function"==typeof te&&ae&&void 0!==ae.createHTMLDocument;const{MUSTACHE_EXPR:ue,ERB_EXPR:de,TMPLIT_EXPR:pe,DATA_ATTR:me,ARIA_ATTR:fe,IS_SCRIPT_OR_DATA:he,ATTR_WHITESPACE:ge,CUSTOM_ELEMENT:Te}=$;let{IS_ALLOWED_URI:ye}=$,Se=null;const _e=addToSet({},[...N,...v,...R,...C,...D]);let Ee=null;const Ae=addToSet({},[...O,...L,...I,...M]);let be=Object.seal(s(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),xe=null,Ne=null,ve=!0,Re=!0,we=!1,Ce=!0,ke=!1,De=!0,Oe=!1,Le=!1,Ie=!1,Me=!1,Pe=!1,ze=!1,He=!0,Fe=!1,Ue=!0,Ge=!1,Be={},We=null;const Ye=addToSet({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]);let je=null;const $e=addToSet({},["audio","video","img","source","image","track"]);let qe=null;const Ve=addToSet({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),Xe="http://www.w3.org/1998/Math/MathML",Ke="http://www.w3.org/2000/svg",Ze="http://www.w3.org/1999/xhtml";let Je=Ze,Qe=!1,et=null;const tt=addToSet({},[Xe,Ke,Ze],T);let nt=addToSet({},["mi","mo","mn","ms","mtext"]),ot=addToSet({},["annotation-xml"]);const at=addToSet({},["title","style","font","a","script"]);let rt=null;const it=["application/xhtml+xml","text/html"];let lt=null,st=null;const ct=n.createElement("form"),ut=function isRegexOrFunction(e){return e instanceof RegExp||e instanceof Function},dt=function _parseConfig(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(!st||st!==e){if(e&&"object"==typeof e||(e={}),e=clone(e),rt=-1===it.indexOf(e.PARSER_MEDIA_TYPE)?"text/html":e.PARSER_MEDIA_TYPE,lt="application/xhtml+xml"===rt?T:g,Se=A(e,"ALLOWED_TAGS")?addToSet({},e.ALLOWED_TAGS,lt):_e,Ee=A(e,"ALLOWED_ATTR")?addToSet({},e.ALLOWED_ATTR,lt):Ae,et=A(e,"ALLOWED_NAMESPACES")?addToSet({},e.ALLOWED_NAMESPACES,T):tt,qe=A(e,"ADD_URI_SAFE_ATTR")?addToSet(clone(Ve),e.ADD_URI_SAFE_ATTR,lt):Ve,je=A(e,"ADD_DATA_URI_TAGS")?addToSet(clone($e),e.ADD_DATA_URI_TAGS,lt):$e,We=A(e,"FORBID_CONTENTS")?addToSet({},e.FORBID_CONTENTS,lt):Ye,xe=A(e,"FORBID_TAGS")?addToSet({},e.FORBID_TAGS,lt):clone({}),Ne=A(e,"FORBID_ATTR")?addToSet({},e.FORBID_ATTR,lt):clone({}),Be=!!A(e,"USE_PROFILES")&&e.USE_PROFILES,ve=!1!==e.ALLOW_ARIA_ATTR,Re=!1!==e.ALLOW_DATA_ATTR,we=e.ALLOW_UNKNOWN_PROTOCOLS||!1,Ce=!1!==e.ALLOW_SELF_CLOSE_IN_ATTR,ke=e.SAFE_FOR_TEMPLATES||!1,De=!1!==e.SAFE_FOR_XML,Oe=e.WHOLE_DOCUMENT||!1,Me=e.RETURN_DOM||!1,Pe=e.RETURN_DOM_FRAGMENT||!1,ze=e.RETURN_TRUSTED_TYPE||!1,Ie=e.FORCE_BODY||!1,He=!1!==e.SANITIZE_DOM,Fe=e.SANITIZE_NAMED_PROPS||!1,Ue=!1!==e.KEEP_CONTENT,Ge=e.IN_PLACE||!1,ye=e.ALLOWED_URI_REGEXP||G,Je=e.NAMESPACE||Ze,nt=e.MATHML_TEXT_INTEGRATION_POINTS||nt,ot=e.HTML_INTEGRATION_POINTS||ot,be=e.CUSTOM_ELEMENT_HANDLING||{},e.CUSTOM_ELEMENT_HANDLING&&ut(e.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(be.tagNameCheck=e.CUSTOM_ELEMENT_HANDLING.tagNameCheck),e.CUSTOM_ELEMENT_HANDLING&&ut(e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(be.attributeNameCheck=e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),e.CUSTOM_ELEMENT_HANDLING&&"boolean"==typeof e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements&&(be.allowCustomizedBuiltInElements=e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),ke&&(Re=!1),Pe&&(Me=!0),Be&&(Se=addToSet({},D),Ee=[],!0===Be.html&&(addToSet(Se,N),addToSet(Ee,O)),!0===Be.svg&&(addToSet(Se,v),addToSet(Ee,L),addToSet(Ee,M)),!0===Be.svgFilters&&(addToSet(Se,R),addToSet(Ee,L),addToSet(Ee,M)),!0===Be.mathMl&&(addToSet(Se,C),addToSet(Ee,I),addToSet(Ee,M))),e.ADD_TAGS&&(Se===_e&&(Se=clone(Se)),addToSet(Se,e.ADD_TAGS,lt)),e.ADD_ATTR&&(Ee===Ae&&(Ee=clone(Ee)),addToSet(Ee,e.ADD_ATTR,lt)),e.ADD_URI_SAFE_ATTR&&addToSet(qe,e.ADD_URI_SAFE_ATTR,lt),e.FORBID_CONTENTS&&(We===Ye&&(We=clone(We)),addToSet(We,e.FORBID_CONTENTS,lt)),Ue&&(Se["#text"]=!0),Oe&&addToSet(Se,["html","head","body"]),Se.table&&(addToSet(Se,["tbody"]),delete xe.tbody),e.TRUSTED_TYPES_POLICY){if("function"!=typeof e.TRUSTED_TYPES_POLICY.createHTML)throw x('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if("function"!=typeof e.TRUSTED_TYPES_POLICY.createScriptURL)throw x('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');ne=e.TRUSTED_TYPES_POLICY,oe=ne.createHTML("")}else void 0===ne&&(ne=function _createTrustedTypesPolicy(e,t){if("object"!=typeof e||"function"!=typeof e.createPolicy)return null;let n=null;const o="data-tt-policy-suffix";t&&t.hasAttribute(o)&&(n=t.getAttribute(o));const a="dompurify"+(n?"#"+n:"");try{return e.createPolicy(a,{createHTML:e=>e,createScriptURL:e=>e})}catch(e){return console.warn("TrustedTypes policy "+a+" could not be created."),null}}(U,a)),null!==ne&&"string"==typeof oe&&(oe=ne.createHTML(""));i&&i(e),st=e}},pt=addToSet({},[...v,...R,...w]),mt=addToSet({},[...C,...k]),ft=function _forceRemove(e){f(DOMPurify.removed,{element:e});try{te(e).removeChild(e)}catch(t){j(e)}},ht=function _removeAttribute(e,t){try{f(DOMPurify.removed,{attribute:t.getAttributeNode(e),from:t})}catch(e){f(DOMPurify.removed,{attribute:null,from:t})}if(t.removeAttribute(e),"is"===e)if(Me||Pe)try{ft(t)}catch(e){}else try{t.setAttribute(e,"")}catch(e){}},gt=function _initDocument(e){let t=null,o=null;if(Ie)e="<remove></remove>"+e;else{const t=y(e,/^[\r\n\t ]+/);o=t&&t[0]}"application/xhtml+xml"===rt&&Je===Ze&&(e='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+e+"</body></html>");const a=ne?ne.createHTML(e):e;if(Je===Ze)try{t=(new F).parseFromString(a,rt)}catch(e){}if(!t||!t.documentElement){t=ae.createDocument(Je,"template",null);try{t.documentElement.innerHTML=Qe?oe:a}catch(e){}}const r=t.body||t.documentElement;return e&&o&&r.insertBefore(n.createTextNode(o),r.childNodes[0]||null),Je===Ze?le.call(t,Oe?"html":"body")[0]:Oe?t.documentElement:r},Tt=function _createNodeIterator(e){return re.call(e.ownerDocument||e,e,P.SHOW_ELEMENT|P.SHOW_COMMENT|P.SHOW_TEXT|P.SHOW_PROCESSING_INSTRUCTION|P.SHOW_CDATA_SECTION,null)},yt=function _isClobbered(e){return e instanceof H&&("string"!=typeof e.nodeName||"string"!=typeof e.textContent||"function"!=typeof e.removeChild||!(e.attributes instanceof z)||"function"!=typeof e.removeAttribute||"function"!=typeof e.setAttribute||"string"!=typeof e.namespaceURI||"function"!=typeof e.insertBefore||"function"!=typeof e.hasChildNodes)},St=function _isNode(e){return"function"==typeof c&&e instanceof c};function _executeHooks(e,t,n){d(e,(e=>{e.call(DOMPurify,t,n,st)}))}const _t=function _sanitizeElements(e){let t=null;if(_executeHooks(ce.beforeSanitizeElements,e,null),yt(e))return ft(e),!0;const n=lt(e.nodeName);if(_executeHooks(ce.uponSanitizeElement,e,{tagName:n,allowedTags:Se}),De&&e.hasChildNodes()&&!St(e.firstElementChild)&&b(/<[/\w!]/g,e.innerHTML)&&b(/<[/\w!]/g,e.textContent))return ft(e),!0;if(e.nodeType===X)return ft(e),!0;if(De&&e.nodeType===K&&b(/<[/\w]/g,e.data))return ft(e),!0;if(!Se[n]||xe[n]){if(!xe[n]&&At(n)){if(be.tagNameCheck instanceof RegExp&&b(be.tagNameCheck,n))return!1;if(be.tagNameCheck instanceof Function&&be.tagNameCheck(n))return!1}if(Ue&&!We[n]){const t=te(e)||e.parentNode,n=ee(e)||e.childNodes;if(n&&t){for(let o=n.length-1;o>=0;--o){const a=W(n[o],!0);a.__removalCount=(e.__removalCount||0)+1,t.insertBefore(a,Q(e))}}}return ft(e),!0}return e instanceof u&&!function _checkValidNamespace(e){let t=te(e);t&&t.tagName||(t={namespaceURI:Je,tagName:"template"});const n=g(e.tagName),o=g(t.tagName);return!!et[e.namespaceURI]&&(e.namespaceURI===Ke?t.namespaceURI===Ze?"svg"===n:t.namespaceURI===Xe?"svg"===n&&("annotation-xml"===o||nt[o]):Boolean(pt[n]):e.namespaceURI===Xe?t.namespaceURI===Ze?"math"===n:t.namespaceURI===Ke?"math"===n&&ot[o]:Boolean(mt[n]):e.namespaceURI===Ze?!(t.namespaceURI===Ke&&!ot[o])&&!(t.namespaceURI===Xe&&!nt[o])&&!mt[n]&&(at[n]||!pt[n]):!("application/xhtml+xml"!==rt||!et[e.namespaceURI]))}(e)?(ft(e),!0):"noscript"!==n&&"noembed"!==n&&"noframes"!==n||!b(/<\/no(script|embed|frames)/i,e.innerHTML)?(ke&&e.nodeType===V&&(t=e.textContent,d([ue,de,pe],(e=>{t=S(t,e," ")})),e.textContent!==t&&(f(DOMPurify.removed,{element:e.cloneNode()}),e.textContent=t)),_executeHooks(ce.afterSanitizeElements,e,null),!1):(ft(e),!0)},Et=function _isValidAttribute(e,t,o){if(He&&("id"===t||"name"===t)&&(o in n||o in ct))return!1;if(Re&&!Ne[t]&&b(me,t));else if(ve&&b(fe,t));else if(!Ee[t]||Ne[t]){if(!(At(e)&&(be.tagNameCheck instanceof RegExp&&b(be.tagNameCheck,e)||be.tagNameCheck instanceof Function&&be.tagNameCheck(e))&&(be.attributeNameCheck instanceof RegExp&&b(be.attributeNameCheck,t)||be.attributeNameCheck instanceof Function&&be.attributeNameCheck(t))||"is"===t&&be.allowCustomizedBuiltInElements&&(be.tagNameCheck instanceof RegExp&&b(be.tagNameCheck,o)||be.tagNameCheck instanceof Function&&be.tagNameCheck(o))))return!1}else if(qe[t]);else if(b(ye,S(o,ge,"")));else if("src"!==t&&"xlink:href"!==t&&"href"!==t||"script"===e||0!==_(o,"data:")||!je[e]){if(we&&!b(he,S(o,ge,"")));else if(o)return!1}else;return!0},At=function _isBasicCustomElement(e){return"annotation-xml"!==e&&y(e,Te)},bt=function _sanitizeAttributes(e){_executeHooks(ce.beforeSanitizeAttributes,e,null);const{attributes:t}=e;if(!t||yt(e))return;const n={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:Ee,forceKeepAttr:void 0};let o=t.length;for(;o--;){const a=t[o],{name:r,namespaceURI:i,value:l}=a,s=lt(r),c=l;let u="value"===r?c:E(c);if(n.attrName=s,n.attrValue=u,n.keepAttr=!0,n.forceKeepAttr=void 0,_executeHooks(ce.uponSanitizeAttribute,e,n),u=n.attrValue,!Fe||"id"!==s&&"name"!==s||(ht(r,e),u="user-content-"+u),De&&b(/((--!?|])>)|<\/(style|title)/i,u)){ht(r,e);continue}if(n.forceKeepAttr)continue;if(!n.keepAttr){ht(r,e);continue}if(!Ce&&b(/\/>/i,u)){ht(r,e);continue}ke&&d([ue,de,pe],(e=>{u=S(u,e," ")}));const p=lt(e.nodeName);if(Et(p,s,u)){if(ne&&"object"==typeof U&&"function"==typeof U.getAttributeType)if(i);else switch(U.getAttributeType(p,s)){case"TrustedHTML":u=ne.createHTML(u);break;case"TrustedScriptURL":u=ne.createScriptURL(u)}if(u!==c)try{i?e.setAttributeNS(i,r,u):e.setAttribute(r,u),yt(e)?ft(e):m(DOMPurify.removed)}catch(t){ht(r,e)}}else ht(r,e)}_executeHooks(ce.afterSanitizeAttributes,e,null)},xt=function _sanitizeShadowDOM(e){let t=null;const n=Tt(e);for(_executeHooks(ce.beforeSanitizeShadowDOM,e,null);t=n.nextNode();)_executeHooks(ce.uponSanitizeShadowNode,t,null),_t(t),bt(t),t.content instanceof r&&_sanitizeShadowDOM(t.content);_executeHooks(ce.afterSanitizeShadowDOM,e,null)};return DOMPurify.sanitize=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=null,a=null,i=null,l=null;if(Qe=!e,Qe&&(e="\x3c!--\x3e"),"string"!=typeof e&&!St(e)){if("function"!=typeof e.toString)throw x("toString is not a function");if("string"!=typeof(e=e.toString()))throw x("dirty is not a string, aborting")}if(!DOMPurify.isSupported)return e;if(Le||dt(t),DOMPurify.removed=[],"string"==typeof e&&(Ge=!1),Ge){if(e.nodeName){const t=lt(e.nodeName);if(!Se[t]||xe[t])throw x("root node is forbidden and cannot be sanitized in-place")}}else if(e instanceof c)n=gt("\x3c!----\x3e"),a=n.ownerDocument.importNode(e,!0),a.nodeType===q&&"BODY"===a.nodeName||"HTML"===a.nodeName?n=a:n.appendChild(a);else{if(!Me&&!ke&&!Oe&&-1===e.indexOf("<"))return ne&&ze?ne.createHTML(e):e;if(n=gt(e),!n)return Me?null:ze?oe:""}n&&Ie&&ft(n.firstChild);const s=Tt(Ge?e:n);for(;i=s.nextNode();)_t(i),bt(i),i.content instanceof r&&xt(i.content);if(Ge)return e;if(Me){if(Pe)for(l=ie.call(n.ownerDocument);n.firstChild;)l.appendChild(n.firstChild);else l=n;return(Ee.shadowroot||Ee.shadowrootmode)&&(l=se.call(o,l,!0)),l}let u=Oe?n.outerHTML:n.innerHTML;return Oe&&Se["!doctype"]&&n.ownerDocument&&n.ownerDocument.doctype&&n.ownerDocument.doctype.name&&b(Y,n.ownerDocument.doctype.name)&&(u="<!DOCTYPE "+n.ownerDocument.doctype.name+">\n"+u),ke&&d([ue,de,pe],(e=>{u=S(u,e," ")})),ne&&ze?ne.createHTML(u):u},DOMPurify.setConfig=function(){dt(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}),Le=!0},DOMPurify.clearConfig=function(){st=null,Le=!1},DOMPurify.isValidAttribute=function(e,t,n){st||dt({});const o=lt(e),a=lt(t);return Et(o,a,n)},DOMPurify.addHook=function(e,t){"function"==typeof t&&f(ce[e],t)},DOMPurify.removeHook=function(e,t){if(void 0!==t){const n=p(ce[e],t);return-1===n?void 0:h(ce[e],n,1)[0]}return m(ce[e])},DOMPurify.removeHooks=function(e){ce[e]=[]},DOMPurify.removeAllHooks=function(){ce={afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]}},DOMPurify}();e.exports=Q}}]);