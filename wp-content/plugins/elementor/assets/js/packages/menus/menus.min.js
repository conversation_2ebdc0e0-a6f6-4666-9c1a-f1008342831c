!function(){"use strict";var e={d:function(t,n){for(var r in n)e.o(n,r)&&!e.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:n[r]})},o:function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r:function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{createMenu:function(){return i}});var n=window.elementorV2.locations,r=window.React;function o(e,t){return({id:n,group:o="default",priority:i=10,overwrite:u=!1,props:c,useProps:s})=>{if(!(o in e))return;const d=t,a=s||(()=>c);e[o].inject({id:n,component:e=>{const t=a();return r.createElement(d,{...e,...t})},options:{priority:i,overwrite:u}})}}function i({groups:e=[],components:t}){const i=function(e){return e.reduce(((e,t)=>(e[t]=(0,n.createLocation)(),e)),{})}([...e,"default"]),u=function(e,t){return Object.entries(t).reduce(((t,[n,r])=>{const i=`register${u=n,u.charAt(0).toUpperCase()+u.slice(1)}`;var u;return{...t,[i]:o(e,r)}}),{})}(i,t),c=function(e){return()=>(0,r.useMemo)((()=>Object.entries(e).reduce(((e,[t,n])=>{const r=n.getInjections().map((e=>({id:e.id,MenuItem:e.component})));return{...e,[t]:r}}),{})),[])}(i);return{useMenuItems:c,...u}}(window.elementorV2=window.elementorV2||{}).menus=t}(),window.elementorV2.menus?.init?.();