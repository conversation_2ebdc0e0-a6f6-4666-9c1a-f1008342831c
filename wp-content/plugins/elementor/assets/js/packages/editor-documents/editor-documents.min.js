!function(){"use strict";var t={d:function(e,n){for(var i in n)t.o(n,i)&&!t.o(e,i)&&Object.defineProperty(e,i,{enumerable:!0,get:n[i]})},o:function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},r:function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})}},e={};t.r(e),t.d(e,{__useActiveDocument:function(){return l},__useActiveDocumentActions:function(){return D},__useHostDocument:function(){return m},__useNavigateToDocument:function(){return b},getCurrentDocument:function(){return C},getV1DocumentsManager:function(){return p},init:function(){return I},setDocumentModifiedStatus:function(){return y},slice:function(){return f}});var n=window.elementorV2.editor,i=window.elementorV2.store,o=window.React,a=window.wp.i18n,s=window.elementorV2.editorV1Adapters,r=window.elementorV2.utils,c=t=>t.documents.entities,d=(0,i.__createSelector)(c,(t=>t.documents.activeId),((t,e)=>e&&t[e]?t[e]:null)),u=(0,i.__createSelector)(c,(t=>t.documents.hostId),((t,e)=>e&&t[e]?t[e]:null));function l(){return(0,i.__useSelector)(d)}function m(){return(0,i.__useSelector)(u)}function _(){return function(){const t=l(),e=m(),n=t&&"kit"!==t.type.value?t:e;(0,o.useEffect)((()=>{if(void 0===n?.title)return;const t=(0,a.__)('Edit "%s" with Elementor',"elementor").replace("%s",n.title);window.document.title=t}),[n?.title])}(),null}function v(t){return!(!t.activeId||!t.entities[t.activeId])}var f=(0,i.__createSlice)({name:"documents",initialState:{entities:{},activeId:null,hostId:null},reducers:{init(t,{payload:e}){t.entities=e.entities,t.hostId=e.hostId,t.activeId=e.activeId},activateDocument(t,e){t.entities[e.payload.id]=e.payload,t.activeId=e.payload.id},setAsHost(t,e){t.hostId=e.payload},updateActiveDocument(t,e){v(t)&&(t.entities[t.activeId]={...t.entities[t.activeId],...e.payload})},startSaving(t){v(t)&&(t.entities[t.activeId].isSaving=!0)},endSaving(t,e){v(t)&&(t.entities[t.activeId]={...e.payload,isSaving:!1})},startSavingDraft:t=>{v(t)&&(t.entities[t.activeId].isSavingDraft=!0)},endSavingDraft(t,e){v(t)&&(t.entities[t.activeId]={...e.payload,isSavingDraft:!1})},markAsDirty(t){v(t)&&(t.entities[t.activeId].isDirty=!0)},markAsPristine(t){v(t)&&(t.entities[t.activeId].isDirty=!1)}}});function p(){const t=window.elementor?.documents;if(!t)throw new Error("Elementor Editor V1 documents manager not found");return t}function g(t){switch(window.elementor?.getPreferences?.("exit_to")||"this_post"){case"dashboard":return t.config.urls.main_dashboard;case"all_posts":return t.config.urls.all_post_type;default:return t.config.urls.exit_to_dashboard}}function w(t){return t?.config?.panel?.show_copy_and_share??!1}function h(t){return t.config.urls.permalink??""}function S(t){const e=t.config.revisions.current_id!==t.id,n=g(t);return{id:t.id,title:t.container.settings.get("post_title"),type:{value:t.config.type,label:t.config.panel.title},status:{value:t.config.status.value,label:t.config.status.label},links:{permalink:h(t),platformEdit:n},isDirty:t.editor.isChanged||e,isSaving:t.editor.isSaving,isSavingDraft:!1,permissions:{allowAddingWidgets:t.config.panel?.allow_adding_widgets??!0,showCopyAndShare:w(t)},userCan:{publish:t.config.user.can_publish}}}function y(t){(0,s.__privateRunCommandSync)("document/save/set-is-modified",{status:t},{internal:!0})}function I(){(0,i.__registerSlice)(f),function(){const{init:t}=f.actions;(0,s.__privateListenTo)((0,s.v1ReadyEvent)(),(()=>{const e=p(),n=Object.entries(e.documents).reduce(((t,[e,n])=>(t[e]=S(n),t)),{});(0,i.__dispatch)(t({entities:n,hostId:e.getInitialId(),activeId:e.getCurrentId()}))}))}(),function(){const{activateDocument:t,setAsHost:e}=f.actions;(0,s.__privateListenTo)((0,s.commandEndEvent)("editor/documents/open"),(()=>{const n=p(),o=S(n.getCurrent());(0,i.__dispatch)(t(o)),n.getInitialId()===o.id&&(0,i.__dispatch)(e(o.id))}))}(),function(){const{startSaving:t,endSaving:e,startSavingDraft:n,endSavingDraft:o}=f.actions,a=t=>{const e=t;return"autosave"===e.args?.status};(0,s.__privateListenTo)((0,s.commandStartEvent)("document/save/save"),(e=>{a(e)?(0,i.__dispatch)(n()):(0,i.__dispatch)(t())})),(0,s.__privateListenTo)((0,s.commandEndEvent)("document/save/save"),(t=>{const n=S(p().getCurrent());a(t)?(0,i.__dispatch)(o(n)):(0,i.__dispatch)(e(n))}))}(),function(){const{updateActiveDocument:t}=f.actions,e=(0,r.debounce)((e=>{const n=e;if(!("post_title"in n.args?.settings))return;const o=p().getCurrent().container.settings.get("post_title");(0,i.__dispatch)(t({title:o}))}),400);(0,s.__privateListenTo)((0,s.commandEndEvent)("document/elements/settings"),e)}(),function(){const{markAsDirty:t,markAsPristine:e}=f.actions;(0,s.__privateListenTo)((0,s.commandEndEvent)("document/save/set-is-modified"),(()=>{const n=d((0,i.__getState)())?.isSaving;n||(p().getCurrent().editor.isChanged?(0,i.__dispatch)(t()):(0,i.__dispatch)(e()))}))}(),function(){const{updateActiveDocument:t}=f.actions,e=(0,r.debounce)((e=>{const n=e;if(!("exit_to"in n.args?.settings))return;const o=p().getCurrent(),a=g(o),s=h(o);(0,i.__dispatch)(t({links:{platformEdit:a,permalink:s}}))}),400);(0,s.__privateListenTo)((0,s.commandEndEvent)("document/elements/settings"),e)}(),(0,n.injectIntoLogic)({id:"documents-hooks",component:_})}function D(){const t=l(),e=t?.links?.permalink??"";return{save:(0,o.useCallback)((()=>(0,s.__privateRunCommand)("document/save/default")),[]),saveDraft:(0,o.useCallback)((()=>(0,s.__privateRunCommand)("document/save/draft")),[]),saveTemplate:(0,o.useCallback)((()=>(0,s.__privateOpenRoute)("library/save-template")),[]),copyAndShare:(0,o.useCallback)((()=>{navigator.clipboard.writeText(e)}),[e])}}function b(){return(0,o.useCallback)((async t=>{await(0,s.__privateRunCommand)("editor/documents/switch",{id:t,setAsInitial:!0});const e=new URL(window.location.href);e.searchParams.set("post",t.toString()),e.searchParams.delete("active-document"),history.replaceState({},"",e)}),[])}function C(){return d((0,i.__getState)())}(window.elementorV2=window.elementorV2||{}).editorDocuments=e}(),window.elementorV2.editorDocuments?.init?.();