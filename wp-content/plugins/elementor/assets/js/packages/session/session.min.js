!function(){"use strict";var e={d:function(t,n){for(var o in n)e.o(n,o)&&!e.o(t,o)&&Object.defineProperty(t,o,{enumerable:!0,get:n[o]})},o:function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r:function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{Context:function(){return i},SessionStorageProvider:function(){return a},getSessionStorageItem:function(){return o},removeSessionStorageItem:function(){return s},setSessionStorageItem:function(){return r},useSessionStorage:function(){return u}});var n=window.React,o=e=>JSON.parse(sessionStorage.getItem(e)||"{}")?.item,r=(e,t)=>{sessionStorage.setItem(e,JSON.stringify({item:t})),window.dispatchEvent(new StorageEvent("storage",{key:e,storageArea:sessionStorage}))},s=e=>{sessionStorage.removeItem(e),window.dispatchEvent(new StorageEvent("storage",{key:e,storageArea:sessionStorage}))},i=(0,n.createContext)(null);function a({children:e,prefix:t}){const o=(0,n.useContext)(i)?.prefix??"",r=o?`${o}/${t}`:t;return n.createElement(i.Provider,{value:{prefix:r}},e)}var u=e=>{const t=`${(0,n.useContext)(i)?.prefix??""}/${e}`,[o,a]=(0,n.useState)();return(0,n.useEffect)((()=>g(t,(e=>{a(e??null)}))),[t]),[o,e=>{r(t,e)},()=>{s(t)}]},g=(e,t)=>{t(o(e));const n=new AbortController;return window.addEventListener("storage",(n=>{n.key===e&&n.storageArea===sessionStorage&&t(o(e))}),{signal:n.signal}),()=>{n.abort()}};(window.elementorV2=window.elementorV2||{}).session=t}(),window.elementorV2.session?.init?.();