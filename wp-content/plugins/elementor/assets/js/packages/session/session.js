/*! For license information please see session.js.LICENSE.txt */
!function(){"use strict";var e={react:function(e){e.exports=window.React}},t={};function r(n){var o=t[n];if(void 0!==o)return o.exports;var s=t[n]={exports:{}};return e[n](s,s.exports,r),s.exports}r.d=function(e,t){for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var n={};!function(){r.r(n),r.d(n,{Context:function(){return i},SessionStorageProvider:function(){return a},getSessionStorageItem:function(){return t},removeSessionStorageItem:function(){return s},setSessionStorageItem:function(){return o},useSessionStorage:function(){return u}});var e=r("react"),t=e=>JSON.parse(sessionStorage.getItem(e)||"{}")?.item,o=(e,t)=>{sessionStorage.setItem(e,JSON.stringify({item:t})),window.dispatchEvent(new StorageEvent("storage",{key:e,storageArea:sessionStorage}))},s=e=>{sessionStorage.removeItem(e),window.dispatchEvent(new StorageEvent("storage",{key:e,storageArea:sessionStorage}))},i=(0,e.createContext)(null);function a({children:t,prefix:r}){const n=(0,e.useContext)(i)?.prefix??"",o=n?`${n}/${r}`:r;return e.createElement(i.Provider,{value:{prefix:o}},t)}var u=t=>{const r=`${(0,e.useContext)(i)?.prefix??""}/${t}`,[n,a]=(0,e.useState)();return(0,e.useEffect)((()=>c(r,(e=>{a(e??null)}))),[r]),[n,e=>{o(r,e)},()=>{s(r)}]},c=(e,r)=>{r(t(e));const n=new AbortController;return window.addEventListener("storage",(n=>{n.key===e&&n.storageArea===sessionStorage&&r(t(e))}),{signal:n.signal}),()=>{n.abort()}}}(),(window.elementorV2=window.elementorV2||{}).session=n}(),window.elementorV2.session?.init?.();