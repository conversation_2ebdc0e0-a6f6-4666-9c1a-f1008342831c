!function(){"use strict";var e={d:function(t,n){for(var r in n)e.o(n,r)&&!e.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:n[r]})},o:function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r:function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{__privateDispatchReadyEvent:function(){return p},__privateFlushListeners:function(){return E},__privateIsRouteActive:function(){return b},__privateListenTo:function(){return h},__privateOpenRoute:function(){return u},__privateRegisterRoute:function(){return a},__privateRunCommand:function(){return o},__privateRunCommandSync:function(){return i},__privateSetReady:function(){return v},__privateUseIsRouteActive:function(){return j},__privateUseListenTo:function(){return P},__privateUseRouteStatus:function(){return A},blockCommand:function(){return L},changeEditMode:function(){return R},commandEndEvent:function(){return s},commandStartEvent:function(){return c},isExperimentActive:function(){return $},registerDataHook:function(){return D},routeCloseEvent:function(){return l},routeOpenEvent:function(){return d},undoable:function(){return M},useEditMode:function(){return O},v1ReadyEvent:function(){return m},windowEvent:function(){return f}});var n=window.React,r=window.elementorV2.utils;async function o(e,t,{internal:n=!1}={}){const r=i(e,t,{internal:n});return r instanceof Promise?r:(u=r)&&"object"==typeof u&&Object.hasOwn(u,"promise")&&Object.hasOwn(u,"then")&&Object.hasOwn(u,"fail")?(o=r,new Promise(((e,t)=>{o.then(e,t)}))):Promise.resolve(r);var o,u}function i(e,t,{internal:n=!1}={}){const r=window,o=n?r.$e?.internal:r.$e?.run;if(!o)throw new Error(`\`${n?"$e.internal":"$e.run"}()\` is not available`);return o(e,t)}function u(e){const t=window;if(!t.$e?.route)return Promise.reject("`$e.route()` is not available");try{return Promise.resolve(t.$e.route(e))}catch(e){return Promise.reject(e)}}function a(e){const t=window;if(!t.$e?.routes?.register)return Promise.reject("`$e.routes.register()` is not available");const n=e.split("/");if(n.length<2)return Promise.reject(`\`${e}\` is an invalid route`);const r=n.pop(),o=n.join("/");try{return Promise.resolve(t.$e.routes.register(o,r,(()=>null)))}catch(e){return Promise.reject(e)}}var c=e=>({type:"command",name:e,state:"before"}),s=e=>({type:"command",name:e,state:"after"}),d=e=>({type:"route",name:e,state:"open"}),l=e=>({type:"route",name:e,state:"close"}),f=e=>({type:"window-event",name:e}),m=()=>f("elementor/initialized"),w=!1;function v(e){w=e}function p(){return function(){const e=window.__elementorEditorV1LoadingPromise;return e||Promise.reject("Elementor Editor V1 is not loaded")}().then((()=>{v(!0),window.dispatchEvent(new CustomEvent("elementor/initialized"))}))}var y=new Map,g=new AbortController;function h(e,t){Array.isArray(e)||(e=[e]);const n=e.map((e=>{const{type:n,name:r}=e;switch(n){case"command":return function(e,t,n){return _(`elementor/commands/run/${t}`,(t=>{"command"===t.type&&t.command===e&&n(t)}))}(r,e.state,t);case"route":return function(e,t,n){return _(`elementor/routes/${t}`,(t=>{"route"===t.type&&t.route.startsWith(e)&&n(t)}))}(r,e.state,t);case"window-event":return _(r,t)}}));return()=>{n.forEach((e=>e()))}}function E(){g.abort(),y.clear(),v(!1),g=new AbortController}function _(e,t){return!y.has(e)&&(y.set(e,[]),function(e){window.addEventListener(e,function(e){return t=>{if(!w)return;const n=function(e){return e instanceof CustomEvent&&e.detail?.command?{type:"command",command:e.detail.command,args:e.detail.args,originalEvent:e}:e instanceof CustomEvent&&e.detail?.route?{type:"route",route:e.detail.route,originalEvent:e}:{type:"window-event",event:e.type,originalEvent:e}}(t);y.get(e)?.forEach((e=>{e(n)}))}}(e),{signal:g.signal})}(e)),y.get(e)?.push(t),()=>{const n=y.get(e);if(!n?.length)return;const r=n.filter((e=>e!==t));y.set(e,r)}}function b(e){const t=window;return!!t.$e?.routes?.isPartOf(e)}var $=e=>{const t=window;return!!t.elementorCommon?.config?.experimentalFeatures?.[e]};function P(e,t,r=[]){const[o,i]=(0,n.useState)((()=>t()));return(0,n.useEffect)((()=>{const n=()=>i(t());return n(),h(e,n)}),r),o}function j(e){return P([d(e),l(e)],(()=>b(e)),[e])}function O(){return P(f("elementor/edit-mode/change"),C)}function C(){return window.elementor.channels.dataEditMode.request("activeMode")}function R(e){return window.elementor.changeEditMode(e)}function A(e,{blockOnKitRoutes:t=!0,allowedEditModes:n=["edit"]}={}){const r=j(e),o=j("panel/global"),i=O(),u=!n.includes(i);return{isActive:r&&!u,isBlocked:u||t&&o}}var S=(0,r.createError)({code:"history_manager_not_available",message:"Cannot access History manager."});function M(e,t){return e.redo??=e.do,n=>{const r=n,o=e,i=function(){const e=window,t=e.elementor?.documents?.getCurrent?.()?.history;if(!t)throw new S;return t}();let u=o.do(r);return i.addItem({title:V(t.title)(r,u),subTitle:V(t.subtitle)(r,u),type:"",restore:(e,t)=>{t?u=o.redo(r,u):o.undo(r,u)}}),u}}function V(e){return"function"==typeof e?e:()=>e??""}var k=0;function D(e,t,n){const r=window,o=r.$e?.modules?.hookData,i={after:o?.After,dependency:o?.Dependency}[e];if(!i)throw new Error(`Data hook '${e}' is not available`);const u=++k,a=new class extends i{getCommand(){return t}getId(){return`${t}--data--${u}`}apply(e){return n(e)}};return a.register(),a}function L({command:e,condition:t}){return D("dependency",e,(e=>!t(e)))}(window.elementorV2=window.elementorV2||{}).editorV1Adapters=t}(),window.elementorV2.editorV1Adapters?.init?.();