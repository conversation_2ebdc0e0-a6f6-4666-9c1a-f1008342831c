!function(){"use strict";var e={d:function(t,n){for(var o in n)e.o(n,o)&&!e.o(t,o)&&Object.defineProperty(t,o,{enumerable:!0,get:n[o]})},o:function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r:function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{extendIconsMap:function(){return y},init:function(){return ye}});var n=window.elementorV2.icons,o=window.elementorV2.editorAppBar,a=window.elementorV2.editorPanels,r=window.React,i=window.wp.i18n,l=window.elementorV2.ui,s=window.wp.apiFetch,c=window.elementorV2.query,u=window.elementorV2.editorDocuments,m=window.elementorV2.editorV1Adapters,d=window.elementorV2.env,p={page:n.PageTemplateIcon,section:n.SectionTemplateIcon,container:n.ContainerTemplateIcon,"wp-page":n.PageTypeIcon,"wp-post":n.PostTypeIcon};function y(e){Object.assign(p,e)}var g={type:"page",editMode:{mode:"none",details:{}},setEditMode:()=>null,resetEditMode:()=>null,setError:()=>null},E=(0,r.createContext)(g),f=({type:e,setError:t,children:n})=>{const[o,a]=(0,r.useState)(g.editMode);return r.createElement(E.Provider,{value:{type:e,editMode:o,setEditMode:a,resetEditMode:()=>{a(g.editMode)},setError:t}},n)};function h(){const e=(0,r.useContext)(E);if(!e)throw new Error("The `usePostListContext()` hook must be used within an `<PostListContextProvider />`");return e}var v=({open:e,onClose:t})=>r.createElement(l.Snackbar,{open:e,onClose:t,anchorOrigin:{vertical:"bottom",horizontal:"left"}},r.createElement(l.Alert,{onClose:t,severity:"error",sx:{width:"100%"}},r.createElement(l.Typography,{component:"span",sx:{fontWeight:"bold"}},"We couldn’t complete the action.")," ","Please try again")),_={page:{labels:{singular_name:(0,i.__)("Page","elementor"),plural_name:(0,i.__)("Pages","elementor")},rest_base:"pages"}};var w=e=>["site-navigation","posts",e],P=e=>{if(!e)return e;const t=[];return e.pages.forEach((e=>{t.push(...e.data)})),t};var x=()=>{const e="/wp/v2/users/me?"+new URLSearchParams({_fields:["capabilities"].join(","),context:"edit"}).toString();return s({path:e})},b=()=>["site-navigation","user"];function I(){return(0,c.useQuery)({queryKey:b(),queryFn:()=>x()})}function T(){const{setEditMode:e}=h(),{data:t}=I();return r.createElement(l.Button,{size:"small",startIcon:r.createElement(n.PlusIcon,null),disabled:!t?.capabilities?.edit_pages,onClick:()=>{e({mode:"create",details:{}})},sx:{px:1.5}},(0,i.__)("Add New","elementor"))}var S=(0,l.styled)(n.ChevronDownIcon,{shouldForwardProp:e=>"isOpen"!==e})((({theme:e,isOpen:t})=>({transform:t?"rotate(0deg)":"rotate(-90deg)",transition:e.transitions.create("transform",{duration:e.transitions.duration.standard})}))),M=(0,l.styled)(l.ListItemIcon)((({theme:e})=>({minWidth:e.spacing(4)})));function C({label:e,Icon:t,isOpenByDefault:n=!1,children:o}){const[a,i]=(0,r.useState)(n);return r.createElement(r.Fragment,null,r.createElement(l.ListItem,null,r.createElement(M,{sx:{color:"text.secondary"}},r.createElement(l.IconButton,{onClick:()=>i((e=>!e)),size:"small",sx:{color:"inherit"}},r.createElement(S,{fontSize:"small",isOpen:a}))),r.createElement(M,{size:"small",sx:{color:"inherit"}},r.createElement(t,{fontSize:"small"})),r.createElement(l.ListItemText,{primaryTypographyProps:{variant:"subtitle2",component:"span"},primary:e})),r.createElement(l.Collapse,{in:a,timeout:"auto",unmountOnExit:!0},r.createElement(l.List,{dense:!0},o)),r.createElement(l.Divider,{sx:{mt:1}}))}function D(){return r.createElement(l.Box,{sx:{display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",pt:"40px",gap:"16px"}},r.createElement(n.Error404TemplateIcon,null),r.createElement(l.Box,{sx:{display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",gap:"8px"}},r.createElement(l.Typography,{variant:"body1",color:"text.primary"},(0,i.__)("We couldn’t display your pages.","elementor")),r.createElement(l.Box,null,r.createElement(l.Typography,{variant:"body2",color:"text.primary",sx:{textAlign:"center"}},(0,i.__)("It’s probably a temporary issue.","elementor")),r.createElement(l.Typography,{variant:"body2",color:"text.primary",sx:{textAlign:"center"}},(0,i.__)("If the problem persists,","elementor")," ",r.createElement(l.Link,{target:"_blank",href:"https://go.elementor.com/wp-editor-support-open-ticket/"},"Notify support")))))}var k=6,L=()=>{const e=`/elementor/v1/site-navigation/recent-posts?${new URLSearchParams({posts_per_page:`${k}`}).toString()}`;return s({path:e})},B=["site-navigation","recent-posts"];function A(e){const t=function(e){const t=(0,c.useQueryClient)();return(n={})=>{const o=w(e);return t.invalidateQueries({queryKey:B},n),t.invalidateQueries({queryKey:o},n)}}(e),n=()=>t({exact:!0}),o=(0,c.useMutation)({mutationFn:t=>((e,t)=>{const n=`/wp/v2/${_[e].rest_base}`;return s({path:n,method:"POST",data:t})})(e,t),onSuccess:n}),a=(0,c.useMutation)({mutationFn:t=>((e,t)=>{const n=`/wp/v2/${_[e].rest_base}`,{id:o,...a}=t;return s({path:`${n}/${o}`,method:"POST",data:a})})(e,t),onSuccess:n}),r=(0,c.useMutation)({mutationFn:t=>((e,t)=>{const n=`/wp/v2/${_[e].rest_base}`;return s({path:`${n}/${t}`,method:"DELETE"})})(e,t),onSuccess:n});return{createPost:o,updatePost:a,deletePost:r,duplicatePost:(0,c.useMutation)({mutationFn:e=>(e=>s({path:"/elementor/v1/site-navigation/duplicate-post",method:"POST",data:{post_id:e.id,title:e.title}}))(e),onSuccess:n})}}function O({postTitle:e,isLoading:t,callback:n}){const[o,a]=(0,r.useState)(e),[s,c]=(0,r.useState)(!1),[u,m]=(0,r.useState)(null),d=(0,r.useRef)(),p=e=>""!==e.trim(),y=()=>{p(o)&&n(o)};return r.createElement(r.Fragment,null,r.createElement(l.ListItem,{secondaryAction:r.createElement(F,{isLoading:t,closeButton:d})},r.createElement(l.Box,{width:"100%",component:"form",onSubmit:e=>{e.preventDefault(),y()}},r.createElement(l.TextField,{autoFocus:!0,fullWidth:!0,value:o,onChange:e=>{s||c(!0);const t=e.target.value;p(t)?m(null):m((0,i.__)("Name is required","elementor")),a(t)},disabled:t,error:!!u,onBlur:e=>{d.current!==e.relatedTarget&&y()},variant:"outlined",color:"secondary",size:"small"}))),u&&r.createElement(l.ListItem,null,r.createElement(l.ListItemText,{sx:{color:"error.main"}},u)))}function F({isLoading:e,closeButton:t}){const{resetEditMode:o}=h();return r.createElement(l.IconButton,{size:"small",color:"secondary",onClick:o,ref:t,disabled:e},e?r.createElement(l.CircularProgress,null):r.createElement(n.XIcon,{fontSize:"small"}))}function N(){const{type:e,resetEditMode:t}=h(),{createPost:n}=A(e),o=(0,u.__useNavigateToDocument)(),{setError:a}=h();return r.createElement(O,{postTitle:(0,i.__)("New Page","elementor"),isLoading:n.isPending,callback:async e=>{try{const{id:t}=await n.mutateAsync({title:e,status:"draft"});o(t)}catch{a()}finally{t()}}})}function z(){const{type:e,editMode:t,resetEditMode:n}=h(),o=(0,u.__useNavigateToDocument)(),{duplicatePost:a}=A(e),{setError:l}=h();return"duplicate"!==t.mode?null:r.createElement(O,{postTitle:`${t.details.title} ${(0,i.__)("copy","elementor")}`,isLoading:a.isPending,callback:async e=>{try{const{post_id:n}=await a.mutateAsync({id:t.details.postId,title:e});o(n)}catch{l()}finally{n()}}})}function V({post:e}){const{type:t,resetEditMode:n}=h(),{updatePost:o}=A(t),{setError:a}=h(),i=(0,u.__useActiveDocument)(),l=async e=>{const t=function(){const e=window.elementor?.documents;if(!e)throw new Error("Elementor Editor V1 documents manager not found");return e}().getCurrent().container;await(0,m.__privateRunCommand)("document/elements/settings",{container:t,settings:{post_title:e}})},s=i?.id===e.id,c=s?i?.title:e.title.rendered;return r.createElement(O,{postTitle:c,isLoading:o.isPending,callback:async t=>{t===c&&n();try{s?await l(t):await o.mutateAsync({id:e.id,title:t})}catch{a()}finally{n()}}})}function j(e=""){return(0,r.useMemo)((()=>{const t=document.createElement("textarea");t.innerHTML=e;const{value:n}=t;return t.remove(),n}),[e])}var $=({status:e})=>"publish"===e?null:r.createElement(l.Typography,{component:"span",variant:"body2",color:"text.secondary",sx:{textTransform:"capitalize",fontStyle:"italic",whiteSpace:"nowrap",flexBasis:"content"}},"(",e,")"),q=({title:e})=>{const t=j(e);return r.createElement(l.Typography,{component:"span",variant:"body2",color:"text.secondary",noWrap:!0,sx:{flexBasis:"auto"}},t)};function H({title:e,status:t}){return r.createElement(l.Box,{display:"flex"},r.createElement(q,{title:e})," ",r.createElement($,{status:t}))}function W({title:e,icon:t,MenuItemProps:n}){return r.createElement(l.MenuItem,{...n},r.createElement(l.ListItemIcon,{sx:{color:"inherit"}},r.createElement(t,null)),r.createElement(l.ListItemText,{primary:e}))}function Q({post:e}){const[t,o]=(0,r.useState)(!1),a=(0,u.__useActiveDocument)(),l=a?.id===e.id,s=!e.user_can.delete||e.isHome||l;return r.createElement(r.Fragment,null,r.createElement(W,{title:(0,i.__)("Delete","elementor"),icon:n.TrashIcon,MenuItemProps:{disabled:s,onClick:()=>o(!0),sx:{"&:hover":{color:"error.main"}}}}),t&&r.createElement(R,{post:e,setIsDialogOpen:o}))}function R({post:e,setIsDialogOpen:t}){const{type:n}=h(),{deletePost:o}=A(n),{setError:a}=h(),s=(0,i.sprintf)((0,i.__)('Delete "%s"?',"elementor"),e.title.rendered),c=()=>{o.isPending||t(!1)};return r.createElement(l.Dialog,{open:!0,onClose:c,"aria-labelledby":"delete-dialog"},r.createElement(l.DialogTitle,{noWrap:!0},s),r.createElement(l.Divider,null),r.createElement(l.DialogContent,null,r.createElement(l.DialogContentText,null,(0,i.__)("The page and its content will be deleted forever and we won’t be able to recover them.","elementor"))),r.createElement(l.DialogActions,null,r.createElement(l.Button,{variant:"contained",color:"secondary",onClick:c,disabled:o.isPending},(0,i.__)("Cancel","elementor")),r.createElement(l.Button,{variant:"contained",color:"error",onClick:async()=>{try{await o.mutateAsync(e.id)}catch{a(),t(!1)}},disabled:o.isPending},o.isPending?r.createElement(l.CircularProgress,null):(0,i.__)("Delete","elementor"))))}function K({post:e,popupState:t}){const{setEditMode:o}=h(),{data:a}=I(),l=!a?.capabilities?.edit_pages;return r.createElement(W,{title:(0,i.__)("Duplicate","elementor"),icon:n.CopyIcon,MenuItemProps:{disabled:l,onClick:()=>{t.close(),o({mode:"duplicate",details:{postId:e.id,title:e.title.rendered}})}}})}function U({post:e}){const{setEditMode:t}=h();return r.createElement(W,{title:(0,i.__)("Rename","elementor"),icon:n.EraseIcon,MenuItemProps:{disabled:!e.user_can.edit,onClick:()=>{t({mode:"rename",details:{postId:e.id}})}}})}function G({post:e,closeMenu:t}){const{updateSettingsMutation:o}=function(){const e=function(){const e=(0,c.useQueryClient)();return(t={})=>e.invalidateQueries({queryKey:["site-navigation","homepage"]},t)}();return{updateSettingsMutation:(0,c.useMutation)({mutationFn:e=>(e=>s({path:"/wp/v2/settings",method:"POST",data:e}))(e),onSuccess:async()=>e({exact:!0})})}}(),{setError:a}=h(),{data:u}=I(),m=!!u?.capabilities?.manage_options,d="publish"===e.status,p=!!e.isHome,y=!m||p||!d||o.isPending;return r.createElement(W,{title:(0,i.__)("Set as homepage","elementor"),icon:o.isPending?l.CircularProgress:n.HomeIcon,MenuItemProps:{disabled:y,onClick:async()=>{try{await o.mutateAsync({show_on_front:"page",page_on_front:e.id})}catch{a()}finally{t()}}}})}function X({post:e}){const{type:t}=h(),o=(0,i.__)("View %s","elementor").replace("%s",_[t].labels.singular_name);return r.createElement(W,{title:o,icon:n.EyeIcon,MenuItemProps:{onClick:()=>window.open(e.link,"_blank")}})}var Y=({children:e,isDisabled:t})=>{if(t){const t=r.createElement(l.Typography,{variant:"caption"},"You cannot edit this page.",r.createElement("br",null),"To edit it directly, contact the site owner");return r.createElement(l.Tooltip,{title:t,placement:"bottom",arrow:!1},e)}return r.createElement(r.Fragment,null,e)};function J({post:e}){const t=(0,u.__useActiveDocument)(),o=(0,u.__useNavigateToDocument)(),a=(0,l.usePopupState)({variant:"popover",popupId:"post-actions",disableAutoFocus:!0}),s=t?.id===e.id,c=s?t?.status.value:e.status,m=s?t?.title:e.title.rendered,d=!e.user_can.edit;return r.createElement(r.Fragment,null,r.createElement(Y,{isDisabled:d},r.createElement(l.ListItem,{disablePadding:!0,secondaryAction:r.createElement(l.IconButton,{value:!0,size:"small",...(0,l.bindTrigger)(a)},r.createElement(n.DotsVerticalIcon,{fontSize:"small"}))},r.createElement(l.ListItemButton,{selected:s,disabled:d,onClick:()=>{s||o(e.id)},dense:!0},r.createElement(l.ListItemText,{disableTypography:!0},r.createElement(H,{title:m,status:c})),e.isHome&&r.createElement(n.HomeIcon,{titleAccess:(0,i.__)("Homepage","elementor"),color:"disabled"})))),r.createElement(l.Menu,{PaperProps:{sx:{mt:2,width:200}},MenuListProps:{dense:!0},...(0,l.bindMenu)(a)},r.createElement(U,{post:e}),r.createElement(K,{post:e,popupState:a}),r.createElement(Q,{post:e}),r.createElement(X,{post:e}),r.createElement(l.Divider,null),r.createElement(G,{post:e,closeMenu:()=>a.close()})))}function Z({post:e}){const{editMode:t}=h();return"rename"===t.mode&&e?.id&&e?.id===t.details.postId?r.createElement(V,{post:e}):"create"!==t.mode||e?"duplicate"!==t.mode||e?e?r.createElement(J,{post:e}):null:r.createElement(z,null):r.createElement(N,null)}function ee({isOpenByDefault:e=!1}){const{type:t,editMode:o}=h(),{data:{posts:a,total:i},isLoading:u,isError:m,fetchNextPage:d,hasNextPage:p,isFetchingNextPage:y}=function(e){const t=(0,c.useInfiniteQuery)({queryKey:w(e),queryFn:({pageParam:t=1})=>(async(e,t)=>{const n=`/wp/v2/${_[e].rest_base}?`+new URLSearchParams({status:"any",order:"asc",page:t.toString(),per_page:10..toString(),_fields:["id","type","title","link","status","user_can"].join(",")}).toString(),o=await s({path:n,parse:!1});return{data:await o.json(),totalPages:Number(o.headers.get("x-wp-totalpages")),totalPosts:Number(o.headers.get("x-wp-total")),currentPage:t}})(e,t),initialPageParam:1,getNextPageParam:e=>e.currentPage<e.totalPages?e.currentPage+1:void 0});return{...t,data:{posts:P(t.data),total:t.data?.pages[0]?.totalPosts??0}}}(t),{data:g}=(0,c.useQuery)({queryKey:["site-navigation","homepage"],queryFn:()=>s({path:"/elementor/v1/site-navigation/homepage"})});if(m)return r.createElement(D,null);if(!a||u)return r.createElement(l.Box,{sx:{px:5}},r.createElement(l.Box,{display:"flex",justifyContent:"flex-end",alignItems:"center"},r.createElement(l.Skeleton,{sx:{my:4},animation:"wave",variant:"rounded",width:"110px",height:"28px"})),r.createElement(l.Box,null,r.createElement(l.Skeleton,{sx:{my:3},animation:"wave",variant:"rounded",width:"100%",height:"24px"}),r.createElement(l.Skeleton,{sx:{my:3},animation:"wave",variant:"rounded",width:"70%",height:"24px"}),r.createElement(l.Skeleton,{sx:{my:3},animation:"wave",variant:"rounded",width:"70%",height:"24px"}),r.createElement(l.Skeleton,{sx:{my:3},animation:"wave",variant:"rounded",width:"70%",height:"24px"})));const E=`${_[t].labels.plural_name} (${i.toString()})`,f=a.map((e=>e.id===g?{...e,isHome:!0}:e)).sort(((e,t)=>e.id===g?-1:t.id===g?1:0));return r.createElement(r.Fragment,null,r.createElement(l.Box,{display:"flex",justifyContent:"flex-end",alignItems:"center",sx:{py:1,px:2}},r.createElement(T,null)),r.createElement(l.List,{dense:!0},r.createElement(C,{label:E,Icon:n.PageTypeIcon,isOpenByDefault:e||!1},f.map((e=>r.createElement(Z,{key:e.id,post:e}))),["duplicate","create"].includes(o.mode)&&r.createElement(Z,null),p&&r.createElement(l.Box,{sx:{display:"flex",justifyContent:"center"}},r.createElement(l.Button,{onClick:d,color:"secondary"},y?r.createElement(l.CircularProgress,null):"Load More")))))}var{panel:te,usePanelStatus:ne,usePanelActions:oe}=(0,a.__createPanel)({id:"site-navigation-panel",component:()=>{const[e,t]=(0,r.useState)(!1);return r.createElement(a.Panel,null,r.createElement(a.PanelHeader,null,r.createElement(a.PanelHeaderTitle,null,(0,i.__)("Pages","elementor"))),r.createElement(a.PanelBody,null,r.createElement(f,{type:"page",setError:()=>t(!0)},r.createElement(ee,{isOpenByDefault:!0})),r.createElement(v,{open:e,onClose:()=>t(!1)})))}}),ae="/elementor/v1/site-navigation/add-new-post";function re({closePopup:e,...t}){const{create:o,isLoading:a}=function(){const[e,t]=(0,r.useState)(!1);return{create:()=>(t(!0),async function(){return await s({path:ae,method:"POST",data:{post_type:"page"}})}().then((e=>e)).finally((()=>t(!1)))),isLoading:e}}(),c=(0,u.__useNavigateToDocument)(),{data:m}=I();return r.createElement(l.MenuItem,{disabled:a||!m?.capabilities?.edit_pages,onClick:async()=>{const{id:t}=await o();e(),await c(t)},...t},r.createElement(l.ListItemIcon,null,a?r.createElement(l.CircularProgress,{size:"1.25rem"}):r.createElement(n.PlusIcon,{fontSize:"small"})),r.createElement(l.ListItemText,{primaryTypographyProps:{variant:"body2"},primary:(0,i.__)("Add new page","elementor")}))}function ie({title:e,status:t}){return r.createElement(le,{title:e},r.createElement(l.Stack,{component:"span",direction:"row",alignItems:"center",spacing:.5},r.createElement(l.Typography,{component:"span",variant:"body2",sx:{maxWidth:"120px"},noWrap:!0},e),"publish"!==t.value&&r.createElement(l.Typography,{component:"span",variant:"body2",sx:{fontStyle:"italic"}},"(",t.label,")")))}function le(e){return r.createElement(l.Tooltip,{PopperProps:{sx:{"&.MuiTooltip-popper .MuiTooltip-tooltip.MuiTooltip-tooltipPlacementBottom":{mt:2.7}}},...e})}var se=p;function ce({postType:e,docType:t,label:o}){const a="elementor_library"===e?"global":"primary",i=se[t]||n.PostTypeIcon;return r.createElement(l.Chip,{component:"span",size:"small",variant:"outlined",label:o,"data-value":t,color:a,icon:r.createElement(i,null),sx:{ml:1,cursor:"inherit"}})}function ue({post:e,closePopup:t,...n}){const o=(0,u.__useNavigateToDocument)(),a=j(e.title);return r.createElement(l.MenuItem,{disabled:!e.user_can.edit,onClick:async()=>{t(),await o(e.id)},...n},r.createElement(l.ListItemText,{sx:{flexGrow:0},primaryTypographyProps:{variant:"body2",noWrap:!0},primary:a}),r.createElement(ce,{postType:e.type.post_type,docType:e.type.doc_type,label:e.type.label}))}function me(){const e=(0,u.__useActiveDocument)(),t=(0,u.__useHostDocument)(),o=e&&"kit"!==e.type.value?e:t,{data:a}=(0,c.useQuery)({queryKey:B,queryFn:()=>L()}),s=a?a.filter((e=>e.id!==o?.id)).splice(0,k-1):[],m=(0,l.usePopupState)({variant:"popover",popupId:"elementor-v2-top-bar-recently-edited"}),d=j(o?.title);if(!o)return null;const p=(0,l.bindTrigger)(m);return r.createElement(r.Fragment,null,r.createElement(l.Button,{color:"inherit",size:"small",endIcon:r.createElement(n.ChevronDownIcon,{fontSize:"small"}),...p,onClick:e=>{const t=window,n=t?.elementor?.editorEvents?.config;n&&t.elementor.editorEvents.dispatchEvent(n.names.topBar.documentNameDropdown,{location:n.locations.topBar,secondaryLocation:n.secondaryLocations.documentNameDropdown,trigger:n.triggers.dropdownClick,element:n.elements.dropdown}),p.onClick(e)}},r.createElement(ie,{title:d,status:o.status})),r.createElement(l.Menu,{MenuListProps:{subheader:r.createElement(l.ListSubheader,{color:"primary",sx:{fontStyle:"italic",fontWeight:"300"}},(0,i.__)("Recent","elementor"))},PaperProps:{sx:{mt:2.5,width:320}},...(0,l.bindMenu)(m)},s.map((e=>r.createElement(ue,{key:e.id,post:e,closePopup:m.close}))),0===s.length&&r.createElement(l.MenuItem,{disabled:!0},r.createElement(l.ListItemText,{primaryTypographyProps:{variant:"caption",fontStyle:"italic"},primary:(0,i.__)("There are no other pages or templates on this site yet.","elementor")})),r.createElement(l.Divider,{disabled:0===s.length}),r.createElement(re,{closePopup:m.close})))}var{env:de}=(0,d.parseEnv)("@elementor/editor-site-navigation",(e=>e));function pe(){const{isOpen:e,isBlocked:t}=ne(),{open:o,close:a}=oe();return{title:(0,i.__)("Pages","elementor"),icon:n.PagesIcon,onClick:()=>e?a():o(),selected:e,disabled:t}}function ye(){(0,o.injectIntoPageIndication)({id:"document-recently-edited",component:me}),de.is_pages_panel_active&&((0,a.__registerPanel)(te),o.toolsMenu.registerToggleAction({id:"toggle-site-navigation-panel",priority:2,useProps:pe}))}(window.elementorV2=window.elementorV2||{}).editorSiteNavigation=t}(),window.elementorV2.editorSiteNavigation?.init?.();