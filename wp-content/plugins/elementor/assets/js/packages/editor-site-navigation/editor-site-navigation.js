/*! For license information please see editor-site-navigation.js.LICENSE.txt */
!function(){"use strict";var e={react:function(e){e.exports=window.React},"@elementor/editor-app-bar":function(e){e.exports=window.elementorV2.editorAppBar},"@elementor/editor-documents":function(e){e.exports=window.elementorV2.editorDocuments},"@elementor/editor-panels":function(e){e.exports=window.elementorV2.editorPanels},"@elementor/editor-v1-adapters":function(e){e.exports=window.elementorV2.editorV1Adapters},"@elementor/env":function(e){e.exports=window.elementorV2.env},"@elementor/icons":function(e){e.exports=window.elementorV2.icons},"@elementor/query":function(e){e.exports=window.elementorV2.query},"@elementor/ui":function(e){e.exports=window.elementorV2.ui},"@wordpress/api-fetch":function(e){e.exports=window.wp.apiFetch},"@wordpress/i18n":function(e){e.exports=window.wp.i18n}},t={};function n(o){var r=t[o];if(void 0!==r)return r.exports;var a=t[o]={exports:{}};return e[o](a,a.exports,n),a.exports}n.d=function(e,t){for(var o in t)n.o(t,o)&&!n.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var o={};!function(){n.r(o),n.d(o,{extendIconsMap:function(){return y},init:function(){return ye}});var e=n("@elementor/icons"),t=n("@elementor/editor-app-bar"),r=n("@elementor/editor-panels"),a=n("react"),i=n("@wordpress/i18n"),l=n("@elementor/ui"),s=n("@wordpress/api-fetch"),c=n("@elementor/query"),u=n("@elementor/editor-documents"),m=n("@elementor/editor-v1-adapters"),d=n("@elementor/env"),p={page:e.PageTemplateIcon,section:e.SectionTemplateIcon,container:e.ContainerTemplateIcon,"wp-page":e.PageTypeIcon,"wp-post":e.PostTypeIcon};function y(e){Object.assign(p,e)}var g={type:"page",editMode:{mode:"none",details:{}},setEditMode:()=>null,resetEditMode:()=>null,setError:()=>null},E=(0,a.createContext)(g),f=({type:e,setError:t,children:n})=>{const[o,r]=(0,a.useState)(g.editMode);return a.createElement(E.Provider,{value:{type:e,editMode:o,setEditMode:r,resetEditMode:()=>{r(g.editMode)},setError:t}},n)};function v(){const e=(0,a.useContext)(E);if(!e)throw new Error("The `usePostListContext()` hook must be used within an `<PostListContextProvider />`");return e}var h=({open:e,onClose:t})=>a.createElement(l.Snackbar,{open:e,onClose:t,anchorOrigin:{vertical:"bottom",horizontal:"left"}},a.createElement(l.Alert,{onClose:t,severity:"error",sx:{width:"100%"}},a.createElement(l.Typography,{component:"span",sx:{fontWeight:"bold"}},"We couldn’t complete the action.")," ","Please try again")),x={page:{labels:{singular_name:(0,i.__)("Page","elementor"),plural_name:(0,i.__)("Pages","elementor")},rest_base:"pages"}};var _=e=>["site-navigation","posts",e],w=e=>{if(!e)return e;const t=[];return e.pages.forEach((e=>{t.push(...e.data)})),t};var P=()=>{const e="/wp/v2/users/me?"+new URLSearchParams({_fields:["capabilities"].join(","),context:"edit"}).toString();return s({path:e})},b=()=>["site-navigation","user"];function I(){return(0,c.useQuery)({queryKey:b(),queryFn:()=>P()})}function T(){const{setEditMode:t}=v(),{data:n}=I();return a.createElement(l.Button,{size:"small",startIcon:a.createElement(e.PlusIcon,null),disabled:!n?.capabilities?.edit_pages,onClick:()=>{t({mode:"create",details:{}})},sx:{px:1.5}},(0,i.__)("Add New","elementor"))}var S=(0,l.styled)(e.ChevronDownIcon,{shouldForwardProp:e=>"isOpen"!==e})((({theme:e,isOpen:t})=>({transform:t?"rotate(0deg)":"rotate(-90deg)",transition:e.transitions.create("transform",{duration:e.transitions.duration.standard})}))),M=(0,l.styled)(l.ListItemIcon)((({theme:e})=>({minWidth:e.spacing(4)})));function C({label:e,Icon:t,isOpenByDefault:n=!1,children:o}){const[r,i]=(0,a.useState)(n);return a.createElement(a.Fragment,null,a.createElement(l.ListItem,null,a.createElement(M,{sx:{color:"text.secondary"}},a.createElement(l.IconButton,{onClick:()=>i((e=>!e)),size:"small",sx:{color:"inherit"}},a.createElement(S,{fontSize:"small",isOpen:r}))),a.createElement(M,{size:"small",sx:{color:"inherit"}},a.createElement(t,{fontSize:"small"})),a.createElement(l.ListItemText,{primaryTypographyProps:{variant:"subtitle2",component:"span"},primary:e})),a.createElement(l.Collapse,{in:r,timeout:"auto",unmountOnExit:!0},a.createElement(l.List,{dense:!0},o)),a.createElement(l.Divider,{sx:{mt:1}}))}function D(){return a.createElement(l.Box,{sx:{display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",pt:"40px",gap:"16px"}},a.createElement(e.Error404TemplateIcon,null),a.createElement(l.Box,{sx:{display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",gap:"8px"}},a.createElement(l.Typography,{variant:"body1",color:"text.primary"},(0,i.__)("We couldn’t display your pages.","elementor")),a.createElement(l.Box,null,a.createElement(l.Typography,{variant:"body2",color:"text.primary",sx:{textAlign:"center"}},(0,i.__)("It’s probably a temporary issue.","elementor")),a.createElement(l.Typography,{variant:"body2",color:"text.primary",sx:{textAlign:"center"}},(0,i.__)("If the problem persists,","elementor")," ",a.createElement(l.Link,{target:"_blank",href:"https://go.elementor.com/wp-editor-support-open-ticket/"},"Notify support")))))}var k=6,L=()=>{const e=`/elementor/v1/site-navigation/recent-posts?${new URLSearchParams({posts_per_page:`${k}`}).toString()}`;return s({path:e})},B=["site-navigation","recent-posts"];function A(e){const t=function(e){const t=(0,c.useQueryClient)();return(n={})=>{const o=_(e);return t.invalidateQueries({queryKey:B},n),t.invalidateQueries({queryKey:o},n)}}(e),n=()=>t({exact:!0}),o=(0,c.useMutation)({mutationFn:t=>((e,t)=>{const n=`/wp/v2/${x[e].rest_base}`;return s({path:n,method:"POST",data:t})})(e,t),onSuccess:n}),r=(0,c.useMutation)({mutationFn:t=>((e,t)=>{const n=`/wp/v2/${x[e].rest_base}`,{id:o,...r}=t;return s({path:`${n}/${o}`,method:"POST",data:r})})(e,t),onSuccess:n}),a=(0,c.useMutation)({mutationFn:t=>((e,t)=>{const n=`/wp/v2/${x[e].rest_base}`;return s({path:`${n}/${t}`,method:"DELETE"})})(e,t),onSuccess:n});return{createPost:o,updatePost:r,deletePost:a,duplicatePost:(0,c.useMutation)({mutationFn:e=>(e=>s({path:"/elementor/v1/site-navigation/duplicate-post",method:"POST",data:{post_id:e.id,title:e.title}}))(e),onSuccess:n})}}function O({postTitle:e,isLoading:t,callback:n}){const[o,r]=(0,a.useState)(e),[s,c]=(0,a.useState)(!1),[u,m]=(0,a.useState)(null),d=(0,a.useRef)(),p=e=>""!==e.trim(),y=()=>{p(o)&&n(o)};return a.createElement(a.Fragment,null,a.createElement(l.ListItem,{secondaryAction:a.createElement(F,{isLoading:t,closeButton:d})},a.createElement(l.Box,{width:"100%",component:"form",onSubmit:e=>{e.preventDefault(),y()}},a.createElement(l.TextField,{autoFocus:!0,fullWidth:!0,value:o,onChange:e=>{s||c(!0);const t=e.target.value;p(t)?m(null):m((0,i.__)("Name is required","elementor")),r(t)},disabled:t,error:!!u,onBlur:e=>{d.current!==e.relatedTarget&&y()},variant:"outlined",color:"secondary",size:"small"}))),u&&a.createElement(l.ListItem,null,a.createElement(l.ListItemText,{sx:{color:"error.main"}},u)))}function F({isLoading:t,closeButton:n}){const{resetEditMode:o}=v();return a.createElement(l.IconButton,{size:"small",color:"secondary",onClick:o,ref:n,disabled:t},t?a.createElement(l.CircularProgress,null):a.createElement(e.XIcon,{fontSize:"small"}))}function N(){const{type:e,resetEditMode:t}=v(),{createPost:n}=A(e),o=(0,u.__useNavigateToDocument)(),{setError:r}=v();return a.createElement(O,{postTitle:(0,i.__)("New Page","elementor"),isLoading:n.isPending,callback:async e=>{try{const{id:t}=await n.mutateAsync({title:e,status:"draft"});o(t)}catch{r()}finally{t()}}})}function z(){const{type:e,editMode:t,resetEditMode:n}=v(),o=(0,u.__useNavigateToDocument)(),{duplicatePost:r}=A(e),{setError:l}=v();return"duplicate"!==t.mode?null:a.createElement(O,{postTitle:`${t.details.title} ${(0,i.__)("copy","elementor")}`,isLoading:r.isPending,callback:async e=>{try{const{post_id:n}=await r.mutateAsync({id:t.details.postId,title:e});o(n)}catch{l()}finally{n()}}})}function q({post:e}){const{type:t,resetEditMode:n}=v(),{updatePost:o}=A(t),{setError:r}=v(),i=(0,u.__useActiveDocument)(),l=async e=>{const t=function(){const e=window.elementor?.documents;if(!e)throw new Error("Elementor Editor V1 documents manager not found");return e}().getCurrent().container;await(0,m.__privateRunCommand)("document/elements/settings",{container:t,settings:{post_title:e}})},s=i?.id===e.id,c=s?i?.title:e.title.rendered;return a.createElement(O,{postTitle:c,isLoading:o.isPending,callback:async t=>{t===c&&n();try{s?await l(t):await o.mutateAsync({id:e.id,title:t})}catch{r()}finally{n()}}})}function V(e=""){return(0,a.useMemo)((()=>{const t=document.createElement("textarea");t.innerHTML=e;const{value:n}=t;return t.remove(),n}),[e])}var j=({status:e})=>"publish"===e?null:a.createElement(l.Typography,{component:"span",variant:"body2",color:"text.secondary",sx:{textTransform:"capitalize",fontStyle:"italic",whiteSpace:"nowrap",flexBasis:"content"}},"(",e,")"),$=({title:e})=>{const t=V(e);return a.createElement(l.Typography,{component:"span",variant:"body2",color:"text.secondary",noWrap:!0,sx:{flexBasis:"auto"}},t)};function H({title:e,status:t}){return a.createElement(l.Box,{display:"flex"},a.createElement($,{title:e})," ",a.createElement(j,{status:t}))}function W({title:e,icon:t,MenuItemProps:n}){return a.createElement(l.MenuItem,{...n},a.createElement(l.ListItemIcon,{sx:{color:"inherit"}},a.createElement(t,null)),a.createElement(l.ListItemText,{primary:e}))}function Q({post:t}){const[n,o]=(0,a.useState)(!1),r=(0,u.__useActiveDocument)(),l=r?.id===t.id,s=!t.user_can.delete||t.isHome||l;return a.createElement(a.Fragment,null,a.createElement(W,{title:(0,i.__)("Delete","elementor"),icon:e.TrashIcon,MenuItemProps:{disabled:s,onClick:()=>o(!0),sx:{"&:hover":{color:"error.main"}}}}),n&&a.createElement(R,{post:t,setIsDialogOpen:o}))}function R({post:e,setIsDialogOpen:t}){const{type:n}=v(),{deletePost:o}=A(n),{setError:r}=v(),s=(0,i.sprintf)((0,i.__)('Delete "%s"?',"elementor"),e.title.rendered),c=()=>{o.isPending||t(!1)};return a.createElement(l.Dialog,{open:!0,onClose:c,"aria-labelledby":"delete-dialog"},a.createElement(l.DialogTitle,{noWrap:!0},s),a.createElement(l.Divider,null),a.createElement(l.DialogContent,null,a.createElement(l.DialogContentText,null,(0,i.__)("The page and its content will be deleted forever and we won’t be able to recover them.","elementor"))),a.createElement(l.DialogActions,null,a.createElement(l.Button,{variant:"contained",color:"secondary",onClick:c,disabled:o.isPending},(0,i.__)("Cancel","elementor")),a.createElement(l.Button,{variant:"contained",color:"error",onClick:async()=>{try{await o.mutateAsync(e.id)}catch{r(),t(!1)}},disabled:o.isPending},o.isPending?a.createElement(l.CircularProgress,null):(0,i.__)("Delete","elementor"))))}function K({post:t,popupState:n}){const{setEditMode:o}=v(),{data:r}=I(),l=!r?.capabilities?.edit_pages;return a.createElement(W,{title:(0,i.__)("Duplicate","elementor"),icon:e.CopyIcon,MenuItemProps:{disabled:l,onClick:()=>{n.close(),o({mode:"duplicate",details:{postId:t.id,title:t.title.rendered}})}}})}function U({post:t}){const{setEditMode:n}=v();return a.createElement(W,{title:(0,i.__)("Rename","elementor"),icon:e.EraseIcon,MenuItemProps:{disabled:!t.user_can.edit,onClick:()=>{n({mode:"rename",details:{postId:t.id}})}}})}function G({post:t,closeMenu:n}){const{updateSettingsMutation:o}=function(){const e=function(){const e=(0,c.useQueryClient)();return(t={})=>e.invalidateQueries({queryKey:["site-navigation","homepage"]},t)}();return{updateSettingsMutation:(0,c.useMutation)({mutationFn:e=>(e=>s({path:"/wp/v2/settings",method:"POST",data:e}))(e),onSuccess:async()=>e({exact:!0})})}}(),{setError:r}=v(),{data:u}=I(),m=!!u?.capabilities?.manage_options,d="publish"===t.status,p=!!t.isHome,y=!m||p||!d||o.isPending;return a.createElement(W,{title:(0,i.__)("Set as homepage","elementor"),icon:o.isPending?l.CircularProgress:e.HomeIcon,MenuItemProps:{disabled:y,onClick:async()=>{try{await o.mutateAsync({show_on_front:"page",page_on_front:t.id})}catch{r()}finally{n()}}}})}function X({post:t}){const{type:n}=v(),o=(0,i.__)("View %s","elementor").replace("%s",x[n].labels.singular_name);return a.createElement(W,{title:o,icon:e.EyeIcon,MenuItemProps:{onClick:()=>window.open(t.link,"_blank")}})}var Y=({children:e,isDisabled:t})=>{if(t){const t=a.createElement(l.Typography,{variant:"caption"},"You cannot edit this page.",a.createElement("br",null),"To edit it directly, contact the site owner");return a.createElement(l.Tooltip,{title:t,placement:"bottom",arrow:!1},e)}return a.createElement(a.Fragment,null,e)};function J({post:t}){const n=(0,u.__useActiveDocument)(),o=(0,u.__useNavigateToDocument)(),r=(0,l.usePopupState)({variant:"popover",popupId:"post-actions",disableAutoFocus:!0}),s=n?.id===t.id,c=s?n?.status.value:t.status,m=s?n?.title:t.title.rendered,d=!t.user_can.edit;return a.createElement(a.Fragment,null,a.createElement(Y,{isDisabled:d},a.createElement(l.ListItem,{disablePadding:!0,secondaryAction:a.createElement(l.IconButton,{value:!0,size:"small",...(0,l.bindTrigger)(r)},a.createElement(e.DotsVerticalIcon,{fontSize:"small"}))},a.createElement(l.ListItemButton,{selected:s,disabled:d,onClick:()=>{s||o(t.id)},dense:!0},a.createElement(l.ListItemText,{disableTypography:!0},a.createElement(H,{title:m,status:c})),t.isHome&&a.createElement(e.HomeIcon,{titleAccess:(0,i.__)("Homepage","elementor"),color:"disabled"})))),a.createElement(l.Menu,{PaperProps:{sx:{mt:2,width:200}},MenuListProps:{dense:!0},...(0,l.bindMenu)(r)},a.createElement(U,{post:t}),a.createElement(K,{post:t,popupState:r}),a.createElement(Q,{post:t}),a.createElement(X,{post:t}),a.createElement(l.Divider,null),a.createElement(G,{post:t,closeMenu:()=>r.close()})))}function Z({post:e}){const{editMode:t}=v();return"rename"===t.mode&&e?.id&&e?.id===t.details.postId?a.createElement(q,{post:e}):"create"!==t.mode||e?"duplicate"!==t.mode||e?e?a.createElement(J,{post:e}):null:a.createElement(z,null):a.createElement(N,null)}function ee({isOpenByDefault:t=!1}){const{type:n,editMode:o}=v(),{data:{posts:r,total:i},isLoading:u,isError:m,fetchNextPage:d,hasNextPage:p,isFetchingNextPage:y}=function(e){const t=(0,c.useInfiniteQuery)({queryKey:_(e),queryFn:({pageParam:t=1})=>(async(e,t)=>{const n=`/wp/v2/${x[e].rest_base}?`+new URLSearchParams({status:"any",order:"asc",page:t.toString(),per_page:10..toString(),_fields:["id","type","title","link","status","user_can"].join(",")}).toString(),o=await s({path:n,parse:!1});return{data:await o.json(),totalPages:Number(o.headers.get("x-wp-totalpages")),totalPosts:Number(o.headers.get("x-wp-total")),currentPage:t}})(e,t),initialPageParam:1,getNextPageParam:e=>e.currentPage<e.totalPages?e.currentPage+1:void 0});return{...t,data:{posts:w(t.data),total:t.data?.pages[0]?.totalPosts??0}}}(n),{data:g}=(0,c.useQuery)({queryKey:["site-navigation","homepage"],queryFn:()=>s({path:"/elementor/v1/site-navigation/homepage"})});if(m)return a.createElement(D,null);if(!r||u)return a.createElement(l.Box,{sx:{px:5}},a.createElement(l.Box,{display:"flex",justifyContent:"flex-end",alignItems:"center"},a.createElement(l.Skeleton,{sx:{my:4},animation:"wave",variant:"rounded",width:"110px",height:"28px"})),a.createElement(l.Box,null,a.createElement(l.Skeleton,{sx:{my:3},animation:"wave",variant:"rounded",width:"100%",height:"24px"}),a.createElement(l.Skeleton,{sx:{my:3},animation:"wave",variant:"rounded",width:"70%",height:"24px"}),a.createElement(l.Skeleton,{sx:{my:3},animation:"wave",variant:"rounded",width:"70%",height:"24px"}),a.createElement(l.Skeleton,{sx:{my:3},animation:"wave",variant:"rounded",width:"70%",height:"24px"})));const E=`${x[n].labels.plural_name} (${i.toString()})`,f=r.map((e=>e.id===g?{...e,isHome:!0}:e)).sort(((e,t)=>e.id===g?-1:t.id===g?1:0));return a.createElement(a.Fragment,null,a.createElement(l.Box,{display:"flex",justifyContent:"flex-end",alignItems:"center",sx:{py:1,px:2}},a.createElement(T,null)),a.createElement(l.List,{dense:!0},a.createElement(C,{label:E,Icon:e.PageTypeIcon,isOpenByDefault:t||!1},f.map((e=>a.createElement(Z,{key:e.id,post:e}))),["duplicate","create"].includes(o.mode)&&a.createElement(Z,null),p&&a.createElement(l.Box,{sx:{display:"flex",justifyContent:"center"}},a.createElement(l.Button,{onClick:d,color:"secondary"},y?a.createElement(l.CircularProgress,null):"Load More")))))}var{panel:te,usePanelStatus:ne,usePanelActions:oe}=(0,r.__createPanel)({id:"site-navigation-panel",component:()=>{const[e,t]=(0,a.useState)(!1);return a.createElement(r.Panel,null,a.createElement(r.PanelHeader,null,a.createElement(r.PanelHeaderTitle,null,(0,i.__)("Pages","elementor"))),a.createElement(r.PanelBody,null,a.createElement(f,{type:"page",setError:()=>t(!0)},a.createElement(ee,{isOpenByDefault:!0})),a.createElement(h,{open:e,onClose:()=>t(!1)})))}}),re="/elementor/v1/site-navigation/add-new-post";function ae({closePopup:t,...n}){const{create:o,isLoading:r}=function(){const[e,t]=(0,a.useState)(!1);return{create:()=>(t(!0),async function(){return await s({path:re,method:"POST",data:{post_type:"page"}})}().then((e=>e)).finally((()=>t(!1)))),isLoading:e}}(),c=(0,u.__useNavigateToDocument)(),{data:m}=I();return a.createElement(l.MenuItem,{disabled:r||!m?.capabilities?.edit_pages,onClick:async()=>{const{id:e}=await o();t(),await c(e)},...n},a.createElement(l.ListItemIcon,null,r?a.createElement(l.CircularProgress,{size:"1.25rem"}):a.createElement(e.PlusIcon,{fontSize:"small"})),a.createElement(l.ListItemText,{primaryTypographyProps:{variant:"body2"},primary:(0,i.__)("Add new page","elementor")}))}function ie({title:e,status:t}){return a.createElement(le,{title:e},a.createElement(l.Stack,{component:"span",direction:"row",alignItems:"center",spacing:.5},a.createElement(l.Typography,{component:"span",variant:"body2",sx:{maxWidth:"120px"},noWrap:!0},e),"publish"!==t.value&&a.createElement(l.Typography,{component:"span",variant:"body2",sx:{fontStyle:"italic"}},"(",t.label,")")))}function le(e){return a.createElement(l.Tooltip,{PopperProps:{sx:{"&.MuiTooltip-popper .MuiTooltip-tooltip.MuiTooltip-tooltipPlacementBottom":{mt:2.7}}},...e})}var se=p;function ce({postType:t,docType:n,label:o}){const r="elementor_library"===t?"global":"primary",i=se[n]||e.PostTypeIcon;return a.createElement(l.Chip,{component:"span",size:"small",variant:"outlined",label:o,"data-value":n,color:r,icon:a.createElement(i,null),sx:{ml:1,cursor:"inherit"}})}function ue({post:e,closePopup:t,...n}){const o=(0,u.__useNavigateToDocument)(),r=V(e.title);return a.createElement(l.MenuItem,{disabled:!e.user_can.edit,onClick:async()=>{t(),await o(e.id)},...n},a.createElement(l.ListItemText,{sx:{flexGrow:0},primaryTypographyProps:{variant:"body2",noWrap:!0},primary:r}),a.createElement(ce,{postType:e.type.post_type,docType:e.type.doc_type,label:e.type.label}))}function me(){const t=(0,u.__useActiveDocument)(),n=(0,u.__useHostDocument)(),o=t&&"kit"!==t.type.value?t:n,{data:r}=(0,c.useQuery)({queryKey:B,queryFn:()=>L()}),s=r?r.filter((e=>e.id!==o?.id)).splice(0,k-1):[],m=(0,l.usePopupState)({variant:"popover",popupId:"elementor-v2-top-bar-recently-edited"}),d=V(o?.title);if(!o)return null;const p=(0,l.bindTrigger)(m);return a.createElement(a.Fragment,null,a.createElement(l.Button,{color:"inherit",size:"small",endIcon:a.createElement(e.ChevronDownIcon,{fontSize:"small"}),...p,onClick:e=>{const t=window,n=t?.elementor?.editorEvents?.config;n&&t.elementor.editorEvents.dispatchEvent(n.names.topBar.documentNameDropdown,{location:n.locations.topBar,secondaryLocation:n.secondaryLocations.documentNameDropdown,trigger:n.triggers.dropdownClick,element:n.elements.dropdown}),p.onClick(e)}},a.createElement(ie,{title:d,status:o.status})),a.createElement(l.Menu,{MenuListProps:{subheader:a.createElement(l.ListSubheader,{color:"primary",sx:{fontStyle:"italic",fontWeight:"300"}},(0,i.__)("Recent","elementor"))},PaperProps:{sx:{mt:2.5,width:320}},...(0,l.bindMenu)(m)},s.map((e=>a.createElement(ue,{key:e.id,post:e,closePopup:m.close}))),0===s.length&&a.createElement(l.MenuItem,{disabled:!0},a.createElement(l.ListItemText,{primaryTypographyProps:{variant:"caption",fontStyle:"italic"},primary:(0,i.__)("There are no other pages or templates on this site yet.","elementor")})),a.createElement(l.Divider,{disabled:0===s.length}),a.createElement(ae,{closePopup:m.close})))}var{env:de}=(0,d.parseEnv)("@elementor/editor-site-navigation",(e=>e));function pe(){const{isOpen:t,isBlocked:n}=ne(),{open:o,close:r}=oe();return{title:(0,i.__)("Pages","elementor"),icon:e.PagesIcon,onClick:()=>t?r():o(),selected:t,disabled:n}}function ye(){(0,t.injectIntoPageIndication)({id:"document-recently-edited",component:me}),de.is_pages_panel_active&&((0,r.__registerPanel)(te),t.toolsMenu.registerToggleAction({id:"toggle-site-navigation-panel",priority:2,useProps:pe}))}}(),(window.elementorV2=window.elementorV2||{}).editorSiteNavigation=o}(),window.elementorV2.editorSiteNavigation?.init?.();