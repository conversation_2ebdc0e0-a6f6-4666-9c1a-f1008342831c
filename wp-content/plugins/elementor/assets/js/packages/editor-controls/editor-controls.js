/*! For license information please see editor-controls.js.LICENSE.txt */
!function(){"use strict";var e={react:function(e){e.exports=window.React},"react-dom":function(e){e.exports=window.ReactDOM},"@elementor/editor-current-user":function(e){e.exports=window.elementorV2.editorCurrentUser},"@elementor/editor-elements":function(e){e.exports=window.elementorV2.editorElements},"@elementor/editor-props":function(e){e.exports=window.elementorV2.editorProps},"@elementor/editor-ui":function(e){e.exports=window.elementorV2.editorUi},"@elementor/editor-v1-adapters":function(e){e.exports=window.elementorV2.editorV1Adapters},"@elementor/env":function(e){e.exports=window.elementorV2.env},"@elementor/http-client":function(e){e.exports=window.elementorV2.httpClient},"@elementor/icons":function(e){e.exports=window.elementorV2.icons},"@elementor/locations":function(e){e.exports=window.elementorV2.locations},"@elementor/query":function(e){e.exports=window.elementorV2.query},"@elementor/session":function(e){e.exports=window.elementorV2.session},"@elementor/ui":function(e){e.exports=window.elementorV2.ui},"@elementor/utils":function(e){e.exports=window.elementorV2.utils},"@elementor/wp-media":function(e){e.exports=window.elementorV2.wpMedia},"@wordpress/i18n":function(e){e.exports=window.wp.i18n},"./node_modules/@tanstack/react-virtual/dist/esm/index.js":function(e,t,n){n.r(t),n.d(t,{Virtualizer:function(){return o.Virtualizer},approxEqual:function(){return o.approxEqual},debounce:function(){return o.debounce},defaultKeyExtractor:function(){return o.defaultKeyExtractor},defaultRangeExtractor:function(){return o.defaultRangeExtractor},elementScroll:function(){return o.elementScroll},measureElement:function(){return o.measureElement},memo:function(){return o.memo},notUndefined:function(){return o.notUndefined},observeElementOffset:function(){return o.observeElementOffset},observeElementRect:function(){return o.observeElementRect},observeWindowOffset:function(){return o.observeWindowOffset},observeWindowRect:function(){return o.observeWindowRect},useVirtualizer:function(){return s},useWindowVirtualizer:function(){return c},windowScroll:function(){return o.windowScroll}});var l=n("react"),r=n("react-dom"),o=n("./node_modules/@tanstack/virtual-core/dist/esm/index.js");const a="undefined"!=typeof document?l.useLayoutEffect:l.useEffect;function i(e){const t=l.useReducer((()=>({})),{})[1],n={...e,onChange:(n,l)=>{var o;l?(0,r.flushSync)(t):t(),null==(o=e.onChange)||o.call(e,n,l)}},[i]=l.useState((()=>new o.Virtualizer(n)));return i.setOptions(n),a((()=>i._didMount()),[]),a((()=>i._willUpdate())),i}function s(e){return i({observeElementRect:o.observeElementRect,observeElementOffset:o.observeElementOffset,scrollToFn:o.elementScroll,...e})}function c(e){return i({getScrollElement:()=>"undefined"!=typeof document?window:null,observeElementRect:o.observeWindowRect,observeElementOffset:o.observeWindowOffset,scrollToFn:o.windowScroll,initialOffset:()=>"undefined"!=typeof document?window.scrollY:0,...e})}},"./node_modules/@tanstack/virtual-core/dist/esm/index.js":function(e,t,n){n.r(t),n.d(t,{Virtualizer:function(){return g},approxEqual:function(){return l.approxEqual},debounce:function(){return l.debounce},defaultKeyExtractor:function(){return r},defaultRangeExtractor:function(){return o},elementScroll:function(){return h},measureElement:function(){return m},memo:function(){return l.memo},notUndefined:function(){return l.notUndefined},observeElementOffset:function(){return u},observeElementRect:function(){return a},observeWindowOffset:function(){return d},observeWindowRect:function(){return s},windowScroll:function(){return p}});var l=n("./node_modules/@tanstack/virtual-core/dist/esm/utils.js");const r=e=>e,o=e=>{const t=Math.max(e.startIndex-e.overscan,0),n=Math.min(e.endIndex+e.overscan,e.count-1),l=[];for(let e=t;e<=n;e++)l.push(e);return l},a=(e,t)=>{const n=e.scrollElement;if(!n)return;const l=e.targetWindow;if(!l)return;const r=e=>{const{width:n,height:l}=e;t({width:Math.round(n),height:Math.round(l)})};if(r(n.getBoundingClientRect()),!l.ResizeObserver)return()=>{};const o=new l.ResizeObserver((t=>{const l=()=>{const e=t[0];if(null==e?void 0:e.borderBoxSize){const t=e.borderBoxSize[0];if(t)return void r({width:t.inlineSize,height:t.blockSize})}r(n.getBoundingClientRect())};e.options.useAnimationFrameWithResizeObserver?requestAnimationFrame(l):l()}));return o.observe(n,{box:"border-box"}),()=>{o.unobserve(n)}},i={passive:!0},s=(e,t)=>{const n=e.scrollElement;if(!n)return;const l=()=>{t({width:n.innerWidth,height:n.innerHeight})};return l(),n.addEventListener("resize",l,i),()=>{n.removeEventListener("resize",l)}},c="undefined"==typeof window||"onscrollend"in window,u=(e,t)=>{const n=e.scrollElement;if(!n)return;const r=e.targetWindow;if(!r)return;let o=0;const a=e.options.useScrollendEvent&&c?()=>{}:(0,l.debounce)(r,(()=>{t(o,!1)}),e.options.isScrollingResetDelay),s=l=>()=>{const{horizontal:r,isRtl:i}=e.options;o=r?n.scrollLeft*(i?-1:1):n.scrollTop,a(),t(o,l)},u=s(!0),d=s(!1);d(),n.addEventListener("scroll",u,i);const m=e.options.useScrollendEvent&&c;return m&&n.addEventListener("scrollend",d,i),()=>{n.removeEventListener("scroll",u),m&&n.removeEventListener("scrollend",d)}},d=(e,t)=>{const n=e.scrollElement;if(!n)return;const r=e.targetWindow;if(!r)return;let o=0;const a=e.options.useScrollendEvent&&c?()=>{}:(0,l.debounce)(r,(()=>{t(o,!1)}),e.options.isScrollingResetDelay),s=l=>()=>{o=n[e.options.horizontal?"scrollX":"scrollY"],a(),t(o,l)},u=s(!0),d=s(!1);d(),n.addEventListener("scroll",u,i);const m=e.options.useScrollendEvent&&c;return m&&n.addEventListener("scrollend",d,i),()=>{n.removeEventListener("scroll",u),m&&n.removeEventListener("scrollend",d)}},m=(e,t,n)=>{if(null==t?void 0:t.borderBoxSize){const e=t.borderBoxSize[0];if(e)return Math.round(e[n.options.horizontal?"inlineSize":"blockSize"])}return Math.round(e.getBoundingClientRect()[n.options.horizontal?"width":"height"])},p=(e,{adjustments:t=0,behavior:n},l)=>{var r,o;const a=e+t;null==(o=null==(r=l.scrollElement)?void 0:r.scrollTo)||o.call(r,{[l.options.horizontal?"left":"top"]:a,behavior:n})},h=(e,{adjustments:t=0,behavior:n},l)=>{var r,o;const a=e+t;null==(o=null==(r=l.scrollElement)?void 0:r.scrollTo)||o.call(r,{[l.options.horizontal?"left":"top"]:a,behavior:n})};class g{constructor(e){this.unsubs=[],this.scrollElement=null,this.targetWindow=null,this.isScrolling=!1,this.scrollToIndexTimeoutId=null,this.measurementsCache=[],this.itemSizeCache=new Map,this.pendingMeasuredCacheIndexes=[],this.scrollRect=null,this.scrollOffset=null,this.scrollDirection=null,this.scrollAdjustments=0,this.elementsCache=new Map,this.observer=(()=>{let e=null;const t=()=>e||(this.targetWindow&&this.targetWindow.ResizeObserver?e=new this.targetWindow.ResizeObserver((e=>{e.forEach((e=>{const t=()=>{this._measureElement(e.target,e)};this.options.useAnimationFrameWithResizeObserver?requestAnimationFrame(t):t()}))})):null);return{disconnect:()=>{var n;null==(n=t())||n.disconnect(),e=null},observe:e=>{var n;return null==(n=t())?void 0:n.observe(e,{box:"border-box"})},unobserve:e=>{var n;return null==(n=t())?void 0:n.unobserve(e)}}})(),this.range=null,this.setOptions=e=>{Object.entries(e).forEach((([t,n])=>{void 0===n&&delete e[t]})),this.options={debug:!1,initialOffset:0,overscan:1,paddingStart:0,paddingEnd:0,scrollPaddingStart:0,scrollPaddingEnd:0,horizontal:!1,getItemKey:r,rangeExtractor:o,onChange:()=>{},measureElement:m,initialRect:{width:0,height:0},scrollMargin:0,gap:0,indexAttribute:"data-index",initialMeasurementsCache:[],lanes:1,isScrollingResetDelay:150,enabled:!0,isRtl:!1,useScrollendEvent:!0,useAnimationFrameWithResizeObserver:!1,...e}},this.notify=e=>{var t,n;null==(n=(t=this.options).onChange)||n.call(t,this,e)},this.maybeNotify=(0,l.memo)((()=>(this.calculateRange(),[this.isScrolling,this.range?this.range.startIndex:null,this.range?this.range.endIndex:null])),(e=>{this.notify(e)}),{key:"maybeNotify",debug:()=>this.options.debug,initialDeps:[this.isScrolling,this.range?this.range.startIndex:null,this.range?this.range.endIndex:null]}),this.cleanup=()=>{this.unsubs.filter(Boolean).forEach((e=>e())),this.unsubs=[],this.observer.disconnect(),this.scrollElement=null,this.targetWindow=null},this._didMount=()=>()=>{this.cleanup()},this._willUpdate=()=>{var e;const t=this.options.enabled?this.options.getScrollElement():null;if(this.scrollElement!==t){if(this.cleanup(),!t)return void this.maybeNotify();this.scrollElement=t,this.scrollElement&&"ownerDocument"in this.scrollElement?this.targetWindow=this.scrollElement.ownerDocument.defaultView:this.targetWindow=(null==(e=this.scrollElement)?void 0:e.window)??null,this.elementsCache.forEach((e=>{this.observer.observe(e)})),this._scrollToOffset(this.getScrollOffset(),{adjustments:void 0,behavior:void 0}),this.unsubs.push(this.options.observeElementRect(this,(e=>{this.scrollRect=e,this.maybeNotify()}))),this.unsubs.push(this.options.observeElementOffset(this,((e,t)=>{this.scrollAdjustments=0,this.scrollDirection=t?this.getScrollOffset()<e?"forward":"backward":null,this.scrollOffset=e,this.isScrolling=t,this.maybeNotify()})))}},this.getSize=()=>this.options.enabled?(this.scrollRect=this.scrollRect??this.options.initialRect,this.scrollRect[this.options.horizontal?"width":"height"]):(this.scrollRect=null,0),this.getScrollOffset=()=>this.options.enabled?(this.scrollOffset=this.scrollOffset??("function"==typeof this.options.initialOffset?this.options.initialOffset():this.options.initialOffset),this.scrollOffset):(this.scrollOffset=null,0),this.getFurthestMeasurement=(e,t)=>{const n=new Map,l=new Map;for(let r=t-1;r>=0;r--){const t=e[r];if(n.has(t.lane))continue;const o=l.get(t.lane);if(null==o||t.end>o.end?l.set(t.lane,t):t.end<o.end&&n.set(t.lane,!0),n.size===this.options.lanes)break}return l.size===this.options.lanes?Array.from(l.values()).sort(((e,t)=>e.end===t.end?e.index-t.index:e.end-t.end))[0]:void 0},this.getMeasurementOptions=(0,l.memo)((()=>[this.options.count,this.options.paddingStart,this.options.scrollMargin,this.options.getItemKey,this.options.enabled]),((e,t,n,l,r)=>(this.pendingMeasuredCacheIndexes=[],{count:e,paddingStart:t,scrollMargin:n,getItemKey:l,enabled:r})),{key:!1}),this.getMeasurements=(0,l.memo)((()=>[this.getMeasurementOptions(),this.itemSizeCache]),(({count:e,paddingStart:t,scrollMargin:n,getItemKey:l,enabled:r},o)=>{if(!r)return this.measurementsCache=[],this.itemSizeCache.clear(),[];0===this.measurementsCache.length&&(this.measurementsCache=this.options.initialMeasurementsCache,this.measurementsCache.forEach((e=>{this.itemSizeCache.set(e.key,e.size)})));const a=this.pendingMeasuredCacheIndexes.length>0?Math.min(...this.pendingMeasuredCacheIndexes):0;this.pendingMeasuredCacheIndexes=[];const i=this.measurementsCache.slice(0,a);for(let r=a;r<e;r++){const e=l(r),a=1===this.options.lanes?i[r-1]:this.getFurthestMeasurement(i,r),s=a?a.end+this.options.gap:t+n,c=o.get(e),u="number"==typeof c?c:this.options.estimateSize(r),d=s+u,m=a?a.lane:r%this.options.lanes;i[r]={index:r,start:s,size:u,end:d,key:e,lane:m}}return this.measurementsCache=i,i}),{key:"getMeasurements",debug:()=>this.options.debug}),this.calculateRange=(0,l.memo)((()=>[this.getMeasurements(),this.getSize(),this.getScrollOffset(),this.options.lanes]),((e,t,n,l)=>this.range=e.length>0&&t>0?function({measurements:e,outerSize:t,scrollOffset:n,lanes:l}){const r=e.length-1;let o=f(0,r,(t=>e[t].start),n),a=o;if(1===l)for(;a<r&&e[a].end<n+t;)a++;else if(l>1){const i=Array(l).fill(0);for(;a<r&&i.some((e=>e<n+t));){const t=e[a];i[t.lane]=t.end,a++}const s=Array(l).fill(n+t);for(;o>0&&s.some((e=>e>=n));){const t=e[o];s[t.lane]=t.start,o--}o=Math.max(0,o-o%l),a=Math.min(r,a+(l-1-a%l))}return{startIndex:o,endIndex:a}}({measurements:e,outerSize:t,scrollOffset:n,lanes:l}):null),{key:"calculateRange",debug:()=>this.options.debug}),this.getVirtualIndexes=(0,l.memo)((()=>{let e=null,t=null;const n=this.calculateRange();return n&&(e=n.startIndex,t=n.endIndex),[this.options.rangeExtractor,this.options.overscan,this.options.count,e,t]}),((e,t,n,l,r)=>null===l||null===r?[]:e({startIndex:l,endIndex:r,overscan:t,count:n})),{key:"getVirtualIndexes",debug:()=>this.options.debug}),this.indexFromElement=e=>{const t=this.options.indexAttribute,n=e.getAttribute(t);return n?parseInt(n,10):(console.warn(`Missing attribute name '${t}={index}' on measured element.`),-1)},this._measureElement=(e,t)=>{const n=this.indexFromElement(e),l=this.measurementsCache[n];if(!l)return;const r=l.key,o=this.elementsCache.get(r);o!==e&&(o&&this.observer.unobserve(o),this.observer.observe(e),this.elementsCache.set(r,e)),e.isConnected&&this.resizeItem(n,this.options.measureElement(e,t,this))},this.resizeItem=(e,t)=>{const n=this.measurementsCache[e];if(!n)return;const l=t-(this.itemSizeCache.get(n.key)??n.size);0!==l&&((void 0!==this.shouldAdjustScrollPositionOnItemSizeChange?this.shouldAdjustScrollPositionOnItemSizeChange(n,l,this):n.start<this.getScrollOffset()+this.scrollAdjustments)&&(this.options.debug&&console.info("correction",l),this._scrollToOffset(this.getScrollOffset(),{adjustments:this.scrollAdjustments+=l,behavior:void 0})),this.pendingMeasuredCacheIndexes.push(n.index),this.itemSizeCache=new Map(this.itemSizeCache.set(n.key,t)),this.notify(!1))},this.measureElement=e=>{e?this._measureElement(e,void 0):this.elementsCache.forEach(((e,t)=>{e.isConnected||(this.observer.unobserve(e),this.elementsCache.delete(t))}))},this.getVirtualItems=(0,l.memo)((()=>[this.getVirtualIndexes(),this.getMeasurements()]),((e,t)=>{const n=[];for(let l=0,r=e.length;l<r;l++){const r=t[e[l]];n.push(r)}return n}),{key:"getVirtualItems",debug:()=>this.options.debug}),this.getVirtualItemForOffset=e=>{const t=this.getMeasurements();if(0!==t.length)return(0,l.notUndefined)(t[f(0,t.length-1,(e=>(0,l.notUndefined)(t[e]).start),e)])},this.getOffsetForAlignment=(e,t,n=0)=>{const l=this.getSize(),r=this.getScrollOffset();"auto"===t&&(t=e>=r+l?"end":"start"),"center"===t?e+=(n-l)/2:"end"===t&&(e-=l);const o=this.options.horizontal?"scrollWidth":"scrollHeight",a=(this.scrollElement?"document"in this.scrollElement?this.scrollElement.document.documentElement[o]:this.scrollElement[o]:0)-l;return Math.max(Math.min(a,e),0)},this.getOffsetForIndex=(e,t="auto")=>{e=Math.max(0,Math.min(e,this.options.count-1));const n=this.measurementsCache[e];if(!n)return;const l=this.getSize(),r=this.getScrollOffset();if("auto"===t)if(n.end>=r+l-this.options.scrollPaddingEnd)t="end";else{if(!(n.start<=r+this.options.scrollPaddingStart))return[r,t];t="start"}const o="end"===t?n.end+this.options.scrollPaddingEnd:n.start-this.options.scrollPaddingStart;return[this.getOffsetForAlignment(o,t,n.size),t]},this.isDynamicMode=()=>this.elementsCache.size>0,this.cancelScrollToIndex=()=>{null!==this.scrollToIndexTimeoutId&&this.targetWindow&&(this.targetWindow.clearTimeout(this.scrollToIndexTimeoutId),this.scrollToIndexTimeoutId=null)},this.scrollToOffset=(e,{align:t="start",behavior:n}={})=>{this.cancelScrollToIndex(),"smooth"===n&&this.isDynamicMode()&&console.warn("The `smooth` scroll behavior is not fully supported with dynamic size."),this._scrollToOffset(this.getOffsetForAlignment(e,t),{adjustments:void 0,behavior:n})},this.scrollToIndex=(e,{align:t="auto",behavior:n}={})=>{e=Math.max(0,Math.min(e,this.options.count-1)),this.cancelScrollToIndex(),"smooth"===n&&this.isDynamicMode()&&console.warn("The `smooth` scroll behavior is not fully supported with dynamic size.");const r=this.getOffsetForIndex(e,t);if(!r)return;const[o,a]=r;this._scrollToOffset(o,{adjustments:void 0,behavior:n}),"smooth"!==n&&this.isDynamicMode()&&this.targetWindow&&(this.scrollToIndexTimeoutId=this.targetWindow.setTimeout((()=>{if(this.scrollToIndexTimeoutId=null,this.elementsCache.has(this.options.getItemKey(e))){const[t]=(0,l.notUndefined)(this.getOffsetForIndex(e,a));(0,l.approxEqual)(t,this.getScrollOffset())||this.scrollToIndex(e,{align:a,behavior:n})}else this.scrollToIndex(e,{align:a,behavior:n})})))},this.scrollBy=(e,{behavior:t}={})=>{this.cancelScrollToIndex(),"smooth"===t&&this.isDynamicMode()&&console.warn("The `smooth` scroll behavior is not fully supported with dynamic size."),this._scrollToOffset(this.getScrollOffset()+e,{adjustments:void 0,behavior:t})},this.getTotalSize=()=>{var e;const t=this.getMeasurements();let n;if(0===t.length)n=this.options.paddingStart;else if(1===this.options.lanes)n=(null==(e=t[t.length-1])?void 0:e.end)??0;else{const e=Array(this.options.lanes).fill(null);let l=t.length-1;for(;l>0&&e.some((e=>null===e));){const n=t[l];null===e[n.lane]&&(e[n.lane]=n.end),l--}n=Math.max(...e.filter((e=>null!==e)))}return Math.max(n-this.options.scrollMargin+this.options.paddingEnd,0)},this._scrollToOffset=(e,{adjustments:t,behavior:n})=>{this.options.scrollToFn(e,{behavior:n,adjustments:t},this)},this.measure=()=>{this.itemSizeCache=new Map,this.notify(!1)},this.setOptions(e)}}const f=(e,t,n,l)=>{for(;e<=t;){const r=(e+t)/2|0,o=n(r);if(o<l)e=r+1;else{if(!(o>l))return r;t=r-1}}return e>0?e-1:0}},"./node_modules/@tanstack/virtual-core/dist/esm/utils.js":function(e,t,n){function l(e,t,n){let l,r=n.initialDeps??[];return()=>{var o,a,i,s;let c;n.key&&(null==(o=n.debug)?void 0:o.call(n))&&(c=Date.now());const u=e();if(u.length===r.length&&!u.some(((e,t)=>r[t]!==e)))return l;let d;if(r=u,n.key&&(null==(a=n.debug)?void 0:a.call(n))&&(d=Date.now()),l=t(...u),n.key&&(null==(i=n.debug)?void 0:i.call(n))){const e=Math.round(100*(Date.now()-c))/100,t=Math.round(100*(Date.now()-d))/100,l=t/16,r=(e,t)=>{for(e=String(e);e.length<t;)e=" "+e;return e};console.info(`%c⏱ ${r(t,5)} /${r(e,5)} ms`,`\n            font-size: .6rem;\n            font-weight: bold;\n            color: hsl(${Math.max(0,Math.min(120-120*l,120))}deg 100% 31%);`,null==n?void 0:n.key)}return null==(s=null==n?void 0:n.onChange)||s.call(n,l),l}}function r(e,t){if(void 0===e)throw new Error("Unexpected undefined"+(t?`: ${t}`:""));return e}n.r(t),n.d(t,{approxEqual:function(){return o},debounce:function(){return a},memo:function(){return l},notUndefined:function(){return r}});const o=(e,t)=>Math.abs(e-t)<1,a=(e,t,n)=>{let l;return function(...r){e.clearTimeout(l),l=e.setTimeout((()=>t.apply(this,r)),n)}}}},t={};function n(l){var r=t[l];if(void 0!==r)return r.exports;var o=t[l]={exports:{}};return e[l](o,o.exports,n),o.exports}n.d=function(e,t){for(var l in t)n.o(t,l)&&!n.o(e,l)&&Object.defineProperty(e,l,{enumerable:!0,get:t[l]})},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var l={};!function(){n.r(l),n.d(l,{AspectRatioControl:function(){return _t},BackgroundControl:function(){return xn},BoxShadowRepeaterControl:function(){return Ve},ColorControl:function(){return ce},ControlActionsProvider:function(){return N},ControlAdornments:function(){return ve},ControlAdornmentsProvider:function(){return fe},ControlFormLabel:function(){return U},ControlReplacementsProvider:function(){return R},ControlToggleButtonGroup:function(){return We},EqualUnequalSizesControl:function(){return Ye},FontFamilyControl:function(){return at},GapControl:function(){return yt},ImageControl:function(){return Q},LinkControl:function(){return gt},LinkedDimensionsControl:function(){return Ze},NumberControl:function(){return qe},PropKeyProvider:function(){return I},PropProvider:function(){return w},SelectControl:function(){return Y},SizeControl:function(){return re},StrokeControl:function(){return de},SvgMediaControl:function(){return Wt},SwitchControl:function(){return Cn},TextAreaControl:function(){return J},TextControl:function(){return Z},ToggleControl:function(){return je},UrlControl:function(){return it},createControlReplacementsRegistry:function(){return L},injectIntoRepeaterItemIcon:function(){return ye},injectIntoRepeaterItemLabel:function(){return Ce},useBoundProp:function(){return P},useControlActions:function(){return K},useSyncExternalState:function(){return ne}});var e=n("react"),t=n("@elementor/editor-props"),r=n("@elementor/ui"),o=n("@wordpress/i18n"),a=n("@elementor/utils"),i=n("@elementor/query"),s=n("@elementor/http-client"),c=n("@elementor/icons"),u=n("@elementor/wp-media"),d=n("@elementor/editor-ui"),m=n("@elementor/locations"),p=n("@elementor/editor-v1-adapters"),h=n("./node_modules/@tanstack/react-virtual/dist/esm/index.js"),g=n("@elementor/editor-elements"),f=n("@elementor/session"),E=n("@elementor/editor-current-user"),v=n("@elementor/env"),b=(0,a.createError)({code:"missing_prop_provider_prop_type",message:"Prop type is missing"}),y=(0,a.createError)({code:"unsupported_prop_provider_prop_type",message:"Parent prop type is not supported"}),x=(0,a.createError)({code:"hook_outside_provider",message:"Hook used outside of provider"}),C=(0,e.createContext)(null),w=({children:t,value:n,setValue:l,propType:r,placeholder:o,disabled:a})=>e.createElement(C.Provider,{value:{value:n,propType:r,setValue:l,placeholder:o,disabled:a}},t),_=()=>{const t=(0,e.useContext)(C);if(!t)throw new x({context:{hook:"usePropContext",provider:"PropProvider"}});return t},S=(0,e.createContext)(null),I=({children:t,bind:n})=>{const{propType:l}=_();if(!l)throw new b({context:{bind:n}});if("array"===l.kind)return e.createElement(z,{bind:n},t);if("object"===l.kind)return e.createElement(T,{bind:n},t);throw new y({context:{propType:l}})},T=({children:t,bind:n})=>{const l=_(),{path:r}=(0,e.useContext)(S)??{},o=l.value?.[n],a=l.placeholder?.[n],i=l.propType.shape[n];return e.createElement(S.Provider,{value:{...l,value:o,setValue:(e,t,r)=>{const o={...l.value,[n]:e};return l?.setValue(o,t,{...r,bind:n})},placeholder:a,bind:n,propType:i,path:[...r??[],n]}},t)},z=({children:t,bind:n})=>{const l=_(),{path:r}=(0,e.useContext)(S)??{},o=l.value?.[Number(n)],a=l.propType.item_prop_type;return e.createElement(S.Provider,{value:{...l,value:o,setValue:(e,t)=>{const r=[...l.value??[]];return r[Number(n)]=e,l?.setValue(r,t,{bind:n})},bind:n,propType:a,path:[...r??[],n]}},t)},k=()=>{const t=(0,e.useContext)(S);if(!t)throw new x({context:{hook:"usePropKeyContext",provider:"PropKeyProvider"}});return t};function P(e){const t=k(),{isValid:n,validate:l,restoreValue:r}=O(t.propType);if(!e)return t;const o=V(t.propType,e.key),a=e.extract(t.value??o.default??null),i=e.extract(t.placeholder??null);return{...t,propType:o,setValue:function(n,r,o){if(l(n))return null===n?t?.setValue(null,r,o):t?.setValue(e?.create(n,r),{},o)},value:n?a:null,restoreValue:r,placeholder:i}}var O=t=>{const[n,l]=(0,e.useState)(!0);return{isValid:n,setIsValid:l,validate:e=>{let n=!0;return t.settings.required&&null===e&&(n=!1),l(n),n},restoreValue:()=>l(!0)}},V=(e,t)=>{let n=e;if("union"===e.kind&&(n=e.prop_types[t]),!n)throw new b({context:{key:t}});return n},U=({children:t})=>e.createElement(r.FormLabel,{size:"tiny"},t),M=(0,e.createContext)([]),R=({replacements:t,children:n})=>e.createElement(M.Provider,{value:t},n),L=()=>{const e=[];return{registerControlReplacement:function(t){e.push(t)},getControlReplacements:function(){return e}}};function A(t){return n=>{const l=(t=>{const{value:n}=P(),l=(0,e.useContext)(M);try{const e=l.find((e=>e.condition({value:n})));return e?.component??t}catch{return t}})(t);return e.createElement(r.ErrorBoundary,{fallback:null},e.createElement(l,{...n}))}}Symbol("control");var G="elementor/v1/settings",F=e=>e.data.value,$="elementor_unfiltered_files_upload",W={queryKey:[$]},B=()=>(0,i.useQuery)({...W,queryFn:()=>{return(e=$,(0,s.httpService)().get(`${G}/${e}`).then((e=>F(e.data)))).then((e=>D(e)));var e},staleTime:1/0}),D=e=>Boolean("1"===e),j=(0,e.createContext)(null),N=({children:t,items:n})=>e.createElement(j.Provider,{value:{items:n}},t),K=()=>{const t=(0,e.useContext)(j);if(!t)throw new Error("useControlActions must be used within a ControlActionsProvider");return t},q=(0,r.styled)("span")`
	display: contents;

	.MuiFloatingActionBar-popper:has( .MuiFloatingActionBar-actions:empty ) {
		display: none;
	}

	.MuiFloatingActionBar-popper {
		z-index: 1000;
	}
`;function H({children:t}){const{items:n}=K(),{disabled:l}=P();if(0===n.length||l)return t;const o=n.map((({MenuItem:t,id:n})=>e.createElement(t,{key:n})));return e.createElement(q,null,e.createElement(r.UnstableFloatingActionBar,{actions:o},t))}var X=A((({mediaTypes:n=["image"]})=>{const{value:l,setValue:a}=P(t.imageSrcPropTypeUtil),{id:i,url:s}=l??{},{data:d,isFetching:m}=(0,u.useWpMediaAttachment)(i?.value||null),p=d?.url??s?.value??null,{open:h}=(0,u.useWpMediaFrame)({mediaTypes:n,multiple:!1,selected:i?.value||null,onSelect:e=>{a({id:{$$type:"image-attachment-id",value:e.id},url:null})}});return e.createElement(H,null,e.createElement(r.Card,{variant:"outlined"},e.createElement(r.CardMedia,{image:p,sx:{height:150}},m?e.createElement(r.Stack,{justifyContent:"center",alignItems:"center",width:"100%",height:"100%"},e.createElement(r.CircularProgress,null)):e.createElement(e.Fragment,null)),e.createElement(r.CardOverlay,null,e.createElement(r.Stack,{gap:1},e.createElement(r.Button,{size:"tiny",color:"inherit",variant:"outlined",onClick:()=>h({mode:"browse"})},(0,o.__)("Select image","elementor")),e.createElement(r.Button,{size:"tiny",variant:"text",color:"inherit",startIcon:e.createElement(c.UploadIcon,null),onClick:()=>h({mode:"upload"})},(0,o.__)("Upload","elementor"))))))})),Y=A((({options:n,onChange:l})=>{const{value:o,setValue:a,disabled:i}=P(t.stringPropTypeUtil);return e.createElement(H,null,e.createElement(r.Select,{sx:{overflow:"hidden"},displayEmpty:!0,size:"tiny",value:o??"",onChange:e=>{const t=e.target.value||null;l?.(t,o),a(t)},disabled:i,fullWidth:!0},n.map((({label:t,...n})=>e.createElement(d.MenuListItem,{key:n.value,...n,value:n.value??""},t)))))})),Q=A((({sizes:n,resolutionLabel:l=(0,o.__)("Image resolution","elementor"),showMode:a="all"})=>{const i=P(t.imagePropTypeUtil),{data:s}=B(),c=s?["image","svg"]:["image"];return e.createElement(w,{...i},e.createElement(r.Stack,{gap:1.5},["all","media"].includes(a)?e.createElement(I,{bind:"src"},e.createElement(U,null," ",(0,o.__)("Image","elementor")," "),e.createElement(X,{mediaTypes:c})):null,["all","sizes"].includes(a)?e.createElement(I,{bind:"size"},e.createElement(r.Grid,{container:!0,gap:1.5,alignItems:"center",flexWrap:"nowrap"},e.createElement(r.Grid,{item:!0,xs:6},e.createElement(U,null," ",l," ")),e.createElement(r.Grid,{item:!0,xs:6,sx:{overflow:"hidden"}},e.createElement(Y,{options:n})))):null))})),Z=A((({placeholder:n})=>{const{value:l,setValue:o,disabled:a}=P(t.stringPropTypeUtil);return e.createElement(H,null,e.createElement(r.TextField,{size:"tiny",fullWidth:!0,disabled:a,value:l??"",onChange:e=>o(e.target.value),placeholder:n}))})),J=A((({placeholder:n})=>{const{value:l,setValue:o,disabled:a}=P(t.stringPropTypeUtil);return e.createElement(H,null,e.createElement(r.TextField,{size:"tiny",multiline:!0,fullWidth:!0,minRows:5,disabled:a,value:l??"",onChange:e=>{o(e.target.value)},placeholder:n}))})),ee=(0,e.forwardRef)((({placeholder:t,type:n,value:l,onChange:o,onBlur:a,onKeyDown:i,onKeyUp:s,endAdornment:c,startAdornment:u,disabled:d},m)=>e.createElement(r.TextField,{ref:m,size:"tiny",fullWidth:!0,type:n,value:l,disabled:d,onChange:o,onKeyDown:i,onKeyUp:s,onBlur:a,placeholder:t,InputProps:{endAdornment:c,startAdornment:u}}))),te=({options:t,onClick:n,value:l,disabled:o})=>{const a=(0,r.usePopupState)({variant:"popover",popupId:(0,e.useId)()});return e.createElement(r.InputAdornment,{position:"end"},e.createElement(r.Button,{size:"small",color:"secondary",disabled:o,sx:{font:"inherit",minWidth:"initial",textTransform:"uppercase"},...(0,r.bindTrigger)(a)},l),e.createElement(r.Menu,{MenuListProps:{dense:!0},...(0,r.bindMenu)(a)},t.map(((l,r)=>e.createElement(d.MenuListItem,{key:l,onClick:()=>(e=>{n(t[e]),a.close()})(r)},l.toUpperCase())))))},ne=({external:t,setExternal:n,persistWhen:l,fallback:r})=>{function o(e,t){return e||r(t)}const[a,i]=(0,e.useState)(o(t,null));return(0,e.useEffect)((()=>{i((e=>o(t,e)))}),[t]),[a,e=>{const t=("function"==typeof e?e:()=>e)(a);var r;i(t),n(l(r=t)?r:null)}]},le=["px","%","em","rem","vw","vh"],re=A((({units:n=le,extendedValues:l=[],placeholder:r,startIcon:o})=>{const{value:a,setValue:i,restoreValue:s,disabled:c}=P(t.sizePropTypeUtil),[u,d]=ne({external:a,setExternal:i,persistWhen:e=>!!e?.size||0===e?.size,fallback:e=>({unit:e?.unit||"px",size:NaN})}),m=l?.length?oe:ie;return e.createElement(m,{disabled:c,size:u.size,unit:u.unit,placeholder:r,startIcon:o,units:n,extendedValues:l,handleSizeChange:e=>{const{value:t}=e.target;d((e=>({...e,size:t||"0"===t?parseFloat(t):NaN})))},handleUnitChange:e=>{d((t=>({size:t?.size??NaN,unit:e})))},onBlur:s})})),oe=n=>{const{value:l,setValue:r}=P(t.stringPropTypeUtil),{extendedValues:o=[]}=n,a=l??n.unit;return e.createElement(ie,{...n,units:[...n.units,...o],handleUnitChange:e=>{o.includes(e)?r(e):n.handleUnitChange(e)},unit:a})},ae=["e","E","+","-"],ie=({units:t,handleUnitChange:n,handleSizeChange:l,placeholder:o,startIcon:a,onBlur:i,size:s,unit:c,disabled:u})=>{const d=(0,e.useRef)("");return e.createElement(H,null,e.createElement(r.Box,null,e.createElement(ee,{disabled:u,endAdornment:e.createElement(te,{disabled:u,options:t,onClick:n,value:c??"px"}),placeholder:o,startAdornment:a?e.createElement(r.InputAdornment,{position:"start",disabled:u},a):void 0,type:"number",value:Number.isNaN(s)?"":s,onChange:l,onBlur:i,onKeyDown:e=>{ae.includes(e.key)&&e.preventDefault()},onKeyUp:e=>{const{key:l}=e;if(!/^[a-zA-Z%]$/.test(l))return;e.preventDefault();const r=l.toLowerCase(),o=(d.current+r).slice(-3);d.current=o;const a=t.find((e=>e.includes(o)))||t.find((e=>e.startsWith(r)))||t.find((e=>e.includes(r)));a&&n(a)}})))},se=({gap:t=2,sx:n,children:l})=>e.createElement(r.Stack,{gap:t,sx:{...n}},l),ce=A((({propTypeUtil:n=t.colorPropTypeUtil,anchorEl:l,slotProps:o={},...a})=>{const{value:i,setValue:s,disabled:c}=P(n);return e.createElement(H,null,e.createElement(r.UnstableColorField,{size:"tiny",fullWidth:!0,value:i??"",onChange:e=>{s(e||null)},...a,disabled:c,slotProps:{...o,colorPicker:{anchorEl:l,anchorOrigin:{vertical:"top",horizontal:"right"},transformOrigin:{vertical:"top",horizontal:-10}}}}))})),ue=["px","em","rem"],de=A((()=>{const n=P(t.strokePropTypeUtil);return e.createElement(w,{...n},e.createElement(se,null,e.createElement(me,{bind:"width",label:(0,o.__)("Stroke width","elementor")},e.createElement(re,{units:ue})),e.createElement(me,{bind:"color",label:(0,o.__)("Stroke color","elementor")},e.createElement(ce,null))))})),me=({bind:t,label:n,children:l})=>e.createElement(I,{bind:t},e.createElement(r.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},e.createElement(r.Grid,{item:!0,xs:6},e.createElement(U,null,n)),e.createElement(r.Grid,{item:!0,xs:6},l))),pe=({alignItems:t,gap:n=1.5,p:l,pt:o,pb:a,children:i})=>e.createElement(r.Stack,{alignItems:t,gap:n,p:l,pt:o,pb:a},i),he=({gap:t=1.5,alignItems:n="center",flexWrap:l="nowrap",children:o})=>e.createElement(r.Grid,{container:!0,gap:t,alignItems:n,flexWrap:l},o),ge=(0,e.createContext)(null),fe=({children:t,items:n})=>e.createElement(ge.Provider,{value:{items:n}},t),Ee=()=>{const t=(0,e.useContext)(ge);return t?.items??[]};function ve(){const t=Ee();return 0===t?.length?null:e.createElement(e.Fragment,null,t.map((({Adornment:t,id:n})=>e.createElement(t,{key:n}))))}var{Slot:be,inject:ye}=(0,m.createReplaceableLocation)(),{Slot:xe,inject:Ce}=(0,m.createReplaceableLocation)(),we=t=>e.createElement(r.List,{sx:{p:0,my:-.5,mx:0}},e.createElement(r.UnstableSortableProvider,{restrictAxis:!0,disableDragOverlay:!1,variant:"static",...t})),_e=({id:t,children:n})=>e.createElement(r.UnstableSortableItem,{id:t,render:({itemProps:t,triggerProps:l,itemStyle:r,triggerStyle:o,showDropIndication:a,dropIndicationStyle:i})=>e.createElement(Se,{...t,style:r},e.createElement(Ie,{...l,style:o}),n,a&&e.createElement(Te,{style:i}))}),Se=(0,r.styled)(r.ListItem)`
	position: relative;
	margin-inline: 0px;
	padding-inline: 0px;
	padding-block: ${({theme:e})=>e.spacing(.5)};

	& .class-item-sortable-trigger {
		color: ${({theme:e})=>e.palette.action.active};
		height: 100%;
		display: flex;
		align-items: center;
		visibility: hidden;
		position: absolute;
		top: 50%;
		padding-inline-end: ${({theme:e})=>e.spacing(.5)};
		transform: translate( -75%, -50% );
	}

	&[aria-describedby=''] > .MuiTag-root {
		background-color: ${({theme:e})=>e.palette.background.paper};
		box-shadow: ${({theme:e})=>e.shadows[3]};
	}

	&:hover {
		& .class-item-sortable-trigger {
			visibility: visible;
		}
	}
`,Ie=t=>e.createElement("div",{...t,role:"button",className:"class-item-sortable-trigger"},e.createElement(c.GripVerticalIcon,{fontSize:"tiny"})),Te=(0,r.styled)(r.Divider)`
	height: 0px;
	border: none;
	overflow: visible;

	&:after {
		--height: 2px;
		content: '';
		display: block;
		width: 100%;
		height: var( --height );
		margin-block: calc( -1 * var( --height ) / 2 );
		border-radius: ${({theme:e})=>e.spacing(.5)};
		background-color: ${({theme:e})=>e.palette.text.primary};
	}
`,ze="tiny",ke=({label:t,itemSettings:n,disabled:l=!1,openOnAdd:a=!1,addToBottom:i=!1,values:s=[],setValues:u})=>{const[d,m]=(0,e.useState)(-1),[p,h]=ne({external:s,setExternal:u,persistWhen:()=>!0}),[g,f]=(0,e.useState)(p.map(((e,t)=>t))),E=e=>1+Math.max(0,...e),v=l?e.Fragment:_e;return e.createElement(se,null,e.createElement(r.Stack,{direction:"row",justifyContent:"start",alignItems:"center",gap:1,sx:{marginInlineEnd:-.75}},e.createElement(r.Typography,{component:"label",variant:"caption",color:"text.secondary"},t),e.createElement(ve,null),e.createElement(r.IconButton,{size:ze,sx:{ml:"auto"},disabled:l,onClick:()=>{const e=structuredClone(n.initialValues),t=E(g);i?(h([...p,e]),f([...g,t])):(h([e,...p]),f([t,...g])),a&&m(t)},"aria-label":(0,o.__)("Add item","elementor")},e.createElement(c.PlusIcon,{fontSize:ze}))),0<g.length&&e.createElement(we,{value:g,onChange:e=>{f(e),h((t=>e.map((e=>{const n=g.indexOf(e);return t[n]}))))}},g.map(((t,r)=>{const o=p[r];return o?e.createElement(v,{id:t,key:`sortable-${t}`},e.createElement(Pe,{disabled:l,propDisabled:o?.disabled,label:e.createElement(xe,{value:o},e.createElement(n.Label,{value:o})),startIcon:e.createElement(be,{value:o},e.createElement(n.Icon,{value:o})),removeItem:()=>(e=>{f(g.filter(((t,n)=>n!==e))),h(p.filter(((t,n)=>n!==e)))})(r),duplicateItem:()=>(e=>{const t=structuredClone(p[e]),n=E(g),l=1+e;h([...p.slice(0,l),t,...p.slice(l)]),f([...g.slice(0,l),n,...g.slice(l)])})(r),toggleDisableItem:()=>(e=>{h(p.map(((t,n)=>{if(n===e){const{disabled:e,...n}=t;return{...n,...e?{}:{disabled:!0}}}return t})))})(r),openOnMount:a&&d===t,onOpen:()=>m(-1)},(t=>e.createElement(n.Content,{...t,value:o,bind:String(r)})))):null}))))},Pe=({label:t,propDisabled:n,startIcon:l,children:a,removeItem:i,duplicateItem:s,toggleDisableItem:u,openOnMount:d,onOpen:m,disabled:p})=>{const[h,g]=(0,e.useState)(null),{popoverState:f,popoverProps:E,ref:v,setRef:b}=Oe(d,m),y=(0,o.__)("Duplicate","elementor"),x=n?(0,o.__)("Show","elementor"):(0,o.__)("Hide","elementor"),C=(0,o.__)("Remove","elementor");return e.createElement(e.Fragment,null,e.createElement(r.UnstableTag,{disabled:p,label:t,showActionsOnHover:!0,fullWidth:!0,ref:b,variant:"outlined","aria-label":(0,o.__)("Open item","elementor"),...(0,r.bindTrigger)(f),startIcon:l,actions:e.createElement(e.Fragment,null,e.createElement(r.Tooltip,{title:y,placement:"top"},e.createElement(r.IconButton,{size:ze,onClick:s,"aria-label":y},e.createElement(c.CopyIcon,{fontSize:ze}))),e.createElement(r.Tooltip,{title:x,placement:"top"},e.createElement(r.IconButton,{size:ze,onClick:u,"aria-label":x},n?e.createElement(c.EyeOffIcon,{fontSize:ze}):e.createElement(c.EyeIcon,{fontSize:ze}))),e.createElement(r.Tooltip,{title:C,placement:"top"},e.createElement(r.IconButton,{size:ze,onClick:i,"aria-label":C},e.createElement(c.XIcon,{fontSize:ze}))))}),e.createElement(r.Popover,{disablePortal:!0,slotProps:{paper:{ref:g,sx:{mt:.5,width:v?.getBoundingClientRect().width}}},anchorOrigin:{vertical:"bottom",horizontal:"left"},...E,anchorEl:v},e.createElement(r.Box,null,a({anchorEl:h}))))},Oe=(t,n)=>{const[l,o]=(0,e.useState)(null),a=(0,r.usePopupState)({variant:"popover"}),i=(0,r.bindPopover)(a);return(0,e.useEffect)((()=>{t&&l&&(a.open(l),n?.())}),[l]),{popoverState:a,ref:l,setRef:o,popoverProps:i}},Ve=A((()=>{const{propType:n,value:l,setValue:r,disabled:a}=P(t.boxShadowPropTypeUtil);return e.createElement(w,{propType:n,value:l,setValue:r},e.createElement(ke,{openOnAdd:!0,disabled:a,values:l??[],setValues:r,label:(0,o.__)("Box shadow","elementor"),itemSettings:{Icon:Ue,Label:Ae,Content:Me,initialValues:Ge}}))})),Ue=({value:t})=>e.createElement(r.UnstableColorIndicator,{size:"inherit",component:"span",value:t.value.color?.value}),Me=({anchorEl:t,bind:n})=>e.createElement(I,{bind:n},e.createElement(Re,{anchorEl:t})),Re=({anchorEl:n})=>{const{propType:l,value:r,setValue:a}=P(t.shadowPropTypeUtil);return e.createElement(w,{propType:l,value:r,setValue:a},e.createElement(pe,{p:1.5},e.createElement(he,null,e.createElement(Le,{bind:"color",label:(0,o.__)("Color","elementor")},e.createElement(ce,{anchorEl:n})),e.createElement(Le,{bind:"position",label:(0,o.__)("Position","elementor"),sx:{overflow:"hidden"}},e.createElement(Y,{options:[{label:(0,o.__)("Inset","elementor"),value:"inset"},{label:(0,o.__)("Outset","elementor"),value:null}]}))),e.createElement(he,null,e.createElement(Le,{bind:"hOffset",label:(0,o.__)("Horizontal","elementor")},e.createElement(re,null)),e.createElement(Le,{bind:"vOffset",label:(0,o.__)("Vertical","elementor")},e.createElement(re,null))),e.createElement(he,null,e.createElement(Le,{bind:"blur",label:(0,o.__)("Blur","elementor")},e.createElement(re,null)),e.createElement(Le,{bind:"spread",label:(0,o.__)("Spread","elementor")},e.createElement(re,null)))))},Le=({label:t,bind:n,children:l,sx:o})=>e.createElement(I,{bind:n},e.createElement(r.Grid,{item:!0,xs:6,sx:o},e.createElement(r.Grid,{container:!0,gap:.75,alignItems:"center"},e.createElement(r.Grid,{item:!0,xs:12},e.createElement(r.FormLabel,{size:"tiny"},t)),e.createElement(r.Grid,{item:!0,xs:12},l)))),Ae=({value:t})=>{const{position:n,hOffset:l,vOffset:r,blur:o,spread:a}=t.value,{size:i="",unit:s=""}=o?.value||{},{size:c="",unit:u=""}=a?.value||{},{size:d="unset",unit:m=""}=l?.value||{},{size:p="unset",unit:h=""}=r?.value||{},g=n?.value||"outset",f=[d+m,p+h,i+s,c+u].join(" ");return e.createElement("span",{style:{textTransform:"capitalize"}},g,": ",f)},Ge={$$type:"shadow",value:{hOffset:{$$type:"size",value:{unit:"px",size:0}},vOffset:{$$type:"size",value:{unit:"px",size:0}},blur:{$$type:"size",value:{unit:"px",size:10}},spread:{$$type:"size",value:{unit:"px",size:0}},color:{$$type:"color",value:"rgba(0, 0, 0, 1)"},position:null}},Fe=({showTooltip:t,children:n,label:l})=>t&&l?e.createElement(r.Tooltip,{title:l,disableFocusListener:!0,placement:"top"},n):n,$e=(0,r.styled)(r.ToggleButtonGroup)`
	${({justify:e})=>`justify-content: ${e};`}
	button:not( :last-of-type ) {
		border-start-end-radius: 0;
		border-end-end-radius: 0;
	}
	button:not( :first-of-type ) {
		border-start-start-radius: 0;
		border-end-start-radius: 0;
	}
	button:last-of-type {
		border-start-end-radius: 8px;
		border-end-end-radius: 8px;
	}
`,We=({justify:t="end",size:n="tiny",value:l,onChange:o,items:a,maxItems:i,exclusive:s=!1,fullWidth:c=!1,disabled:u})=>{const d=s&&void 0!==i&&a.length>i,m=d?a.slice(i-1):[],p=d?a.slice(0,i-1):a,h="rtl"===(0,r.useTheme)().direction,g=(0,e.useMemo)((()=>{const e=m?.length;return`repeat(${e?p.length+1:p.length}, minmax(0, 25%)) ${e?"auto":""}`}),[m?.length,p.length]);return e.createElement(H,null,e.createElement($e,{justify:t,value:l,onChange:(e,t)=>{o(t)},exclusive:s,disabled:u,sx:{direction:h?"rtl /* @noflip */":"ltr /* @noflip */",display:"grid",gridTemplateColumns:g,width:"100%"}},p.map((({label:t,value:l,renderContent:o,showTooltip:a})=>e.createElement(Fe,{key:l,label:t,showTooltip:a||!1},e.createElement(r.ToggleButton,{value:l,"aria-label":t,size:n,fullWidth:c},e.createElement(o,{size:n}))))),m.length&&s&&e.createElement(Be,{size:n,value:l||null,onChange:o,items:m,fullWidth:c})))},Be=({size:t="tiny",onChange:n,items:l,fullWidth:o,value:a})=>{const i=De(l,a),[s,u]=(0,e.useState)(!1),d=(0,e.useRef)(null),m=e=>{u(!1),p(e)},p=e=>{n(e===a?null:e)};return e.createElement(e.Fragment,null,e.createElement(r.ToggleButton,{value:i.value,"aria-label":i.label,size:t,fullWidth:o,onClick:e=>{e.preventDefault(),m(i.value)},ref:d},i.renderContent({size:t})),e.createElement(r.ToggleButton,{size:t,"aria-expanded":s?"true":void 0,"aria-haspopup":"menu","aria-pressed":void 0,onClick:e=>{u((e=>!e)),e.preventDefault()},ref:d,value:"__chevron-icon-button__"},e.createElement(c.ChevronDownIcon,{fontSize:t})),e.createElement(r.Menu,{open:s,onClose:()=>u(!1),anchorEl:d.current,anchorOrigin:{vertical:"bottom",horizontal:"right"},transformOrigin:{vertical:"top",horizontal:"right"},sx:{mt:.5}},l.map((({label:t,value:n})=>e.createElement(r.MenuItem,{key:n,selected:n===a,onClick:()=>m(n)},e.createElement(r.ListItemText,null,e.createElement(r.Typography,{sx:{fontSize:"14px"}},t)))))))},De=(t,n)=>{const[l,r]=(0,e.useState)(t.find((e=>e.value===n))??t[0]);return(0,e.useEffect)((()=>{const e=t.find((e=>e.value===n));e&&r(e)}),[t,n]),l},je=A((({options:n,fullWidth:l=!1,size:r="tiny",exclusive:o=!0,maxItems:a})=>{const{value:i,setValue:s,placeholder:c,disabled:u}=P(t.stringPropTypeUtil),d=n.filter((e=>e.exclusive)).map((e=>e.value)),m={items:n,maxItems:a,fullWidth:l,size:r};return o?e.createElement(We,{...m,value:i??c??null,onChange:s,disabled:u,exclusive:!0}):e.createElement(We,{...m,value:(i??c)?.split(" ")??[],onChange:e=>{const t=e[e.length-1],n=d.includes(t)?[t]:e?.filter((e=>!d.includes(e)));s(n?.join(" ")||null)},disabled:u,exclusive:!1})})),Ne=e=>null==e||""===e||Number.isNaN(Number(e)),Ke=["e","E","+","-"],qe=A((({placeholder:n,max:l=Number.MAX_VALUE,min:o=-Number.MAX_VALUE,step:a=1,shouldForceInt:i=!1})=>{const{value:s,setValue:c,disabled:u}=P(t.numberPropTypeUtil);return e.createElement(H,null,e.createElement(r.TextField,{size:"tiny",type:"number",fullWidth:!0,disabled:u,value:Ne(s)?"":s,onChange:e=>{const t=e.target.value;if(Ne(t))return void c(null);const n=i?+parseInt(t):Number(t);c(Math.min(Math.max(n,o),l))},placeholder:n,inputProps:{step:a},onKeyDown:e=>{Ke.includes(e.key)&&e.preventDefault()}}))})),He=({children:t})=>e.createElement(r.Stack,{direction:"row",alignItems:"center",justifyItems:"start",gap:1},e.createElement(U,null,t),e.createElement(ve,null)),Xe=(e,t)=>{const n=Object.values(e);if(n.length!==t.length)return!1;const[l,...r]=n;return r.every((e=>e?.value?.size===l?.value?.size&&e?.value?.unit===l?.value?.unit))};function Ye({label:n,icon:l,tooltipLabel:a,items:i,multiSizePropTypeUtil:s}){const c=(0,e.useId)(),u=(0,e.useRef)(null),d=(0,r.usePopupState)({variant:"popover",popupId:c}),{propType:m,value:p,setValue:h,disabled:g}=P(s),{value:f,setValue:E,disabled:v}=P(t.sizePropTypeUtil),b=()=>f?i.reduce(((e,{bind:n})=>({...e,[n]:t.sizePropTypeUtil.create(f)})),{}):null,y=!!p;return e.createElement(e.Fragment,null,e.createElement(r.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap",ref:u},e.createElement(r.Grid,{item:!0,xs:6},e.createElement(He,null,n)),e.createElement(r.Grid,{item:!0,xs:6},e.createElement(r.Stack,{direction:"row",alignItems:"center",gap:1},e.createElement(re,{placeholder:y?(0,o.__)("Mixed","elementor"):void 0}),e.createElement(r.Tooltip,{title:a,placement:"top"},e.createElement(r.ToggleButton,{size:"tiny",value:"check",sx:{marginLeft:"auto"},...(0,r.bindToggle)(d),selected:d.isOpen,"aria-label":a,disabled:g||v},l))))),e.createElement(r.Popover,{disablePortal:!0,disableScrollLock:!0,anchorOrigin:{vertical:"bottom",horizontal:"right"},transformOrigin:{vertical:"top",horizontal:"right"},...(0,r.bindPopover)(d),slotProps:{paper:{sx:{mt:.5,width:u.current?.getBoundingClientRect().width}}}},e.createElement(w,{propType:m,value:p||(b()??null),setValue:e=>{const t={...p??b(),...e};if(Xe(t,i))return E(Object.values(t)[0]?.value);h(t)}},e.createElement(pe,{p:1.5,pt:2.5,pb:3},e.createElement(he,null,e.createElement(Qe,{item:i[0]}),e.createElement(Qe,{item:i[1]})),e.createElement(he,null,e.createElement(Qe,{item:i[2]}),e.createElement(Qe,{item:i[3]}))))))}var Qe=({item:t})=>{const n=(0,p.isExperimentActive)("e_v_3_30");return e.createElement(I,{bind:t.bind},e.createElement(r.Grid,{item:!0,xs:6},e.createElement(r.Grid,{container:!0,gap:.75,alignItems:"center"},e.createElement(r.Grid,{item:!0,xs:12},n?e.createElement(He,null,t.label):e.createElement(U,null,t.label)),e.createElement(r.Grid,{item:!0,xs:12},e.createElement(re,{startIcon:t.icon})))))},Ze=A((({label:n,isSiteRtl:l=!1,extendedValues:a})=>{const{value:i,setValue:s,disabled:u}=P(t.sizePropTypeUtil),{value:d,setValue:m,propType:h,disabled:g}=P(t.dimensionsPropTypeUtil),f=!d&&!i||!!i,E=(0,p.isExperimentActive)("e_v_3_30"),v=n.toLowerCase(),b=f?c.LinkIcon:c.DetachIcon,y=(0,o.__)("Link %s","elementor").replace("%s",v),x=(0,o.__)("Unlink %s","elementor").replace("%s",v);return e.createElement(w,{propType:h,value:d,setValue:m},e.createElement(r.Stack,{direction:"row",gap:2,flexWrap:"nowrap"},E?e.createElement(U,null,n):e.createElement(He,null,n),e.createElement(r.Tooltip,{title:f?x:y,placement:"top"},e.createElement(r.ToggleButton,{"aria-label":f?x:y,size:"tiny",value:"check",selected:f,sx:{marginLeft:"auto"},onChange:()=>{if(!f)return void s(d["block-start"]?.value??null);const e=i?t.sizePropTypeUtil.create(i):null;m({"block-start":e,"block-end":e,"inline-start":e,"inline-end":e})},disabled:u||g},e.createElement(b,{fontSize:"tiny"})))),e.createElement(r.Stack,{direction:"row",gap:2,flexWrap:"nowrap"},e.createElement(r.Grid,{container:!0,gap:.75,alignItems:"center"},e.createElement(r.Grid,{item:!0,xs:12},e.createElement(et,{bind:"block-start",label:(0,o.__)("Top","elementor")})),e.createElement(r.Grid,{item:!0,xs:12},e.createElement(Je,{bind:"block-start",startIcon:e.createElement(c.SideTopIcon,{fontSize:"tiny"}),isLinked:f,extendedValues:a}))),e.createElement(r.Grid,{container:!0,gap:.75,alignItems:"center"},e.createElement(r.Grid,{item:!0,xs:12},e.createElement(et,{bind:"inline-end",label:l?(0,o.__)("Left","elementor"):(0,o.__)("Right","elementor")})),e.createElement(r.Grid,{item:!0,xs:12},e.createElement(Je,{bind:"inline-end",startIcon:l?e.createElement(c.SideLeftIcon,{fontSize:"tiny"}):e.createElement(c.SideRightIcon,{fontSize:"tiny"}),isLinked:f,extendedValues:a})))),e.createElement(r.Stack,{direction:"row",gap:2,flexWrap:"nowrap"},e.createElement(r.Grid,{container:!0,gap:.75,alignItems:"center"},e.createElement(r.Grid,{item:!0,xs:12},e.createElement(et,{bind:"block-end",label:(0,o.__)("Bottom","elementor")})),e.createElement(r.Grid,{item:!0,xs:12},e.createElement(Je,{bind:"block-end",startIcon:e.createElement(c.SideBottomIcon,{fontSize:"tiny"}),isLinked:f,extendedValues:a}))),e.createElement(r.Grid,{container:!0,gap:.75,alignItems:"center"},e.createElement(r.Grid,{item:!0,xs:12},e.createElement(et,{bind:"inline-start",label:l?(0,o.__)("Right","elementor"):(0,o.__)("Left","elementor")})),e.createElement(r.Grid,{item:!0,xs:12},e.createElement(Je,{bind:"inline-start",startIcon:l?e.createElement(c.SideRightIcon,{fontSize:"tiny"}):e.createElement(c.SideLeftIcon,{fontSize:"tiny"}),isLinked:f,extendedValues:a})))))})),Je=({bind:t,startIcon:n,isLinked:l,extendedValues:r})=>l?e.createElement(re,{startIcon:n,extendedValues:r}):e.createElement(I,{bind:t},e.createElement(re,{startIcon:n,extendedValues:r})),et=({label:t,bind:n})=>(0,p.isExperimentActive)("e_v_3_30")?e.createElement(I,{bind:n},e.createElement(He,null,t)):e.createElement(U,null,t),tt="tiny",nt=({fontFamilies:t,fontFamily:n,onFontFamilyChange:l,onClose:a})=>{const[i,s]=(0,e.useState)(""),u=((e,t)=>e.reduce(((e,n)=>{const l=n.fonts.filter((e=>e.toLowerCase().includes(t.toLowerCase())));return l.length&&(e.push({type:"category",value:n.label}),l.forEach((t=>{e.push({type:"font",value:t})}))),e}),[]))(t,i),d=()=>{s(""),a()};return e.createElement(r.Stack,null,e.createElement(r.Stack,{direction:"row",alignItems:"center",pl:1.5,pr:.5,py:1.5},e.createElement(c.TextIcon,{fontSize:tt,sx:{mr:.5}}),e.createElement(r.Typography,{variant:"subtitle2"},(0,o.__)("Font Family","elementor")),e.createElement(r.IconButton,{size:tt,sx:{ml:"auto"},onClick:d},e.createElement(c.XIcon,{fontSize:tt}))),e.createElement(r.Box,{px:1.5,pb:1},e.createElement(r.TextField,{autoFocus:!0,fullWidth:!0,size:tt,value:i,placeholder:(0,o.__)("Search","elementor"),onChange:e=>{s(e.target.value)},InputProps:{startAdornment:e.createElement(r.InputAdornment,{position:"start"},e.createElement(c.SearchIcon,{fontSize:tt}))}})),e.createElement(r.Divider,null),u.length>0?e.createElement(lt,{fontListItems:u,setFontFamily:l,handleClose:d,fontFamily:n}):e.createElement(r.Box,{sx:{overflowY:"auto",height:260,width:220}},e.createElement(r.Stack,{alignItems:"center",p:2.5,gap:1.5,overflow:"hidden"},e.createElement(c.TextIcon,{fontSize:"large"}),e.createElement(r.Box,{sx:{maxWidth:160,overflow:"hidden"}},e.createElement(r.Typography,{align:"center",variant:"subtitle2",color:"text.secondary"},(0,o.__)("Sorry, nothing matched","elementor")),e.createElement(r.Typography,{variant:"subtitle2",color:"text.secondary",sx:{display:"flex",width:"100%",justifyContent:"center"}},e.createElement("span",null,"“"),e.createElement("span",{style:{maxWidth:"80%",overflow:"hidden",textOverflow:"ellipsis"}},i),e.createElement("span",null,"”."))),e.createElement(r.Typography,{align:"center",variant:"caption",color:"text.secondary"},(0,o.__)("Try something else.","elementor"),e.createElement(r.Link,{color:"secondary",variant:"caption",component:"button",onClick:()=>s("")},(0,o.__)("Clear & try again","elementor"))))))},lt=({fontListItems:t,setFontFamily:n,handleClose:l,fontFamily:o})=>{const a=(0,e.useRef)(null),i=t.find((e=>e.value===o)),s=ot((({getVirtualIndexes:e})=>{e().forEach((e=>{const n=t[e];n&&"font"===n.type&&((e,t="editor")=>{const n=window;n.elementor?.helpers?.enqueueFont?.(e,t)})(n.value)}))}),100),c=(0,h.useVirtualizer)({count:t.length,getScrollElement:()=>a.current,estimateSize:()=>36,overscan:6,onChange:s});return(0,e.useEffect)((()=>{c.scrollToIndex(t.findIndex((e=>e.value===o)))}),[o]),e.createElement(r.Box,{ref:a,sx:{overflowY:"auto",height:260,width:220}},e.createElement(rt,{role:"listbox",style:{height:`${c.getTotalSize()}px`},"data-testid":"font-list"},c.getVirtualItems().map((o=>{const a=t[o.index],s=o.index===t.length-1,c=1===o.index,u=i?.value===a.value,d=i?-1:0;return"category"===a.type?e.createElement(r.MenuSubheader,{key:o.key,style:{transform:`translateY(${o.start}px)`}},a.value):e.createElement("li",{key:o.key,role:"option","aria-selected":u,onClick:()=>{n(a.value),l()},onKeyDown:e=>{"Enter"===e.key&&(n(a.value),l()),"ArrowDown"===e.key&&s&&(e.preventDefault(),e.stopPropagation()),"ArrowUp"===e.key&&c&&(e.preventDefault(),e.stopPropagation())},tabIndex:u?0:d,style:{transform:`translateY(${o.start}px)`,fontFamily:a.value}},a.value)}))))},rt=(0,r.styled)(r.MenuList)((({theme:e})=>({"& > li":{height:36,position:"absolute",top:0,left:0,width:"100%",display:"flex",alignItems:"center"},'& > [role="option"]':{...e.typography.caption,lineHeight:"inherit",padding:e.spacing(.75,2,.75,4),"&:hover, &:focus":{backgroundColor:e.palette.action.hover},'&[aria-selected="true"]':{backgroundColor:e.palette.action.selected},cursor:"pointer",textOverflow:"ellipsis"},width:"100%",position:"relative"}))),ot=(t,n)=>{const[l]=(0,e.useState)((()=>(0,a.debounce)(t,n)));return(0,e.useEffect)((()=>()=>l.cancel()),[l]),l},at=A((({fontFamilies:n})=>{const{value:l,setValue:o,disabled:a}=P(t.stringPropTypeUtil),i=(0,r.usePopupState)({variant:"popover"});return e.createElement(e.Fragment,null,e.createElement(H,null,e.createElement(r.UnstableTag,{variant:"outlined",label:l,endIcon:e.createElement(c.ChevronDownIcon,{fontSize:"tiny"}),...(0,r.bindTrigger)(i),fullWidth:!0,disabled:a})),e.createElement(r.Popover,{disablePortal:!0,disableScrollLock:!0,anchorOrigin:{vertical:"bottom",horizontal:"left"},...(0,r.bindPopover)(i)},e.createElement(nt,{fontFamilies:n,fontFamily:l,onFontFamilyChange:o,onClose:i.close})))})),it=A((({placeholder:n})=>{const{value:l,setValue:o,disabled:a}=P(t.urlPropTypeUtil);return e.createElement(H,null,e.createElement(r.TextField,{size:"tiny",fullWidth:!0,value:l??"",disabled:a,onChange:e=>o(e.target.value),placeholder:n}))})),st=(0,e.forwardRef)(((t,n)=>{const{options:l,onOptionChange:o,onTextChange:a,allowCustomValues:i=!1,placeholder:s="",minInputLength:c=2,value:u="",...d}=t,m=function(e,t,n){if(null===e)return t;const l=String(e||"")?.toLowerCase();return l.length<n?new Array(0):t.filter((e=>String(e.id).toLowerCase().includes(l)||e.label.toLowerCase().includes(l)))}(u,l,c).map((({id:e})=>e)),p=!!u,h=i||u?.toString()?.length?void 0:()=>!0,g="number"==typeof u&&!!dt(l,u);return e.createElement(r.Autocomplete,{...d,ref:n,forcePopupIcon:!1,disableClearable:!0,freeSolo:i,value:u?.toString()||"",size:"tiny",onChange:(e,t)=>o(Number(t)),readOnly:g,options:m,getOptionKey:e=>dt(l,e)?.id||e,getOptionLabel:e=>dt(l,e)?.label||e.toString(),groupBy:mt(l)?e=>dt(l,e)?.groupLabel||e:void 0,isOptionEqualToValue:h,filterOptions:()=>m,renderOption:(t,n)=>e.createElement(r.Box,{component:"li",...t,key:t.id},dt(l,n)?.label??n),renderInput:t=>e.createElement(ct,{params:t,handleChange:e=>a?.(e),allowClear:p,placeholder:s,hasSelectedValue:g})})})),ct=({params:t,allowClear:n,placeholder:l,handleChange:o,hasSelectedValue:a})=>e.createElement(r.TextField,{...t,placeholder:l,onChange:e=>{o(e.target.value)},sx:{"& .MuiInputBase-input":{cursor:a?"default":void 0}},InputProps:{...t.InputProps,endAdornment:e.createElement(ut,{params:t,allowClear:n,handleChange:o})}}),ut=({allowClear:t,handleChange:n,params:l})=>e.createElement(r.InputAdornment,{position:"end"},t&&e.createElement(r.IconButton,{size:l.size,onClick:()=>n(null),sx:{cursor:"pointer"}},e.createElement(c.XIcon,{fontSize:l.size})));function dt(e,t=null){const n=(t||"").toString();return e.find((({id:e})=>n===e.toString()))}function mt(e){return e.every((e=>"groupLabel"in e))}var pt="tiny",ht={label:(0,o.__)("Learn More","elementor"),href:"https://go.elementor.com/element-link-inside-link-infotip"},gt=A((n=>{const{value:l,path:i,setValue:c,...u}=P(t.linkPropTypeUtil),[d,m]=(0,f.useSessionStorage)(i.join("/")),[p,h]=(0,e.useState)(!!l),{allowCustomValues:E,queryOptions:{endpoint:v="",requestParams:b={}},placeholder:y,minInputLength:x=2,context:{elementId:C}}=n||{},[_,S]=(0,e.useState)((0,g.getLinkInLinkRestriction)(C)),[T,z]=(0,e.useState)(function(e){const t=e?.destination?.value,n=e?.label?.value;return t&&n&&"number"===(e?.destination?.$$type||"url")?[{id:t.toString(),label:n}]:[]}(l)),k=!p&&_.shouldRestrict,O=e=>{c(e),m({...d,value:e})},V=(0,e.useMemo)((()=>(0,a.debounce)((e=>async function(e,t){if(!t||!e)return[];try{const{data:n}=await(0,s.httpService)().get(e,{params:t});return n.data.value}catch{return[]}}(v,e).then((e=>{z(function(e){const t=mt(e)?"groupLabel":"label";return e.sort(((e,n)=>e[t]&&n[t]?e[t].localeCompare(n[t]):0))}(e))}))),400)),[v]);return e.createElement(w,{...u,value:l,setValue:c},e.createElement(r.Stack,{gap:1.5},e.createElement(r.Stack,{direction:"row",sx:{justifyContent:"space-between",alignItems:"center",marginInlineEnd:-.75}},e.createElement(U,null,(0,o.__)("Link","elementor")),e.createElement(vt,{isVisible:!p,linkInLinkRestriction:_},e.createElement(ft,{disabled:k,active:p,onIconClick:()=>{if(S((0,g.getLinkInLinkRestriction)(C)),_.shouldRestrict&&!p)return;const e=!p;h(e),e||null===l||c(null),e&&d?.value&&c(d.value),m({value:d?.value,meta:{isEnabled:e}})},label:(0,o.__)("Toggle link","elementor")}))),e.createElement(r.Collapse,{in:p,timeout:"auto",unmountOnExit:!0},e.createElement(r.Stack,{gap:1.5},e.createElement(I,{bind:"destination"},e.createElement(H,null,e.createElement(st,{options:T,allowCustomValues:E,placeholder:y,value:l?.destination?.value?.settings?.label||l?.destination?.value,onOptionChange:e=>{const n=e?{...l,destination:t.numberPropTypeUtil.create(e),label:t.stringPropTypeUtil.create(dt(T,e)?.label||null)}:null;O(n)},onTextChange:e=>{const n=(e=e?.trim()||"")?{...l,destination:t.urlPropTypeUtil.create(e),label:t.stringPropTypeUtil.create("")}:null;O(n),(e=>{z([]),!e||!v||e.length<x||V({...b,term:e})})(e)},minInputLength:x}))),e.createElement(I,{bind:"isTargetBlank"},e.createElement(Et,{disabled:!l}))))))})),ft=({disabled:t,active:n,onIconClick:l,label:o})=>e.createElement(r.IconButton,{size:pt,onClick:l,"aria-label":o,disabled:t},n?e.createElement(c.MinusIcon,{fontSize:pt}):e.createElement(c.PlusIcon,{fontSize:pt})),Et=({disabled:n})=>{const{value:l=!1,setValue:a}=P(t.booleanPropTypeUtil),i=n?{style:{opacity:0}}:{};return e.createElement(r.Grid,{container:!0,alignItems:"center",flexWrap:"nowrap",justifyContent:"space-between"},e.createElement(r.Grid,{item:!0},e.createElement(U,null,(0,o.__)("Open in a new tab","elementor"))),e.createElement(r.Grid,{item:!0,sx:{marginInlineEnd:-1}},e.createElement(r.Switch,{checked:l,onClick:()=>{a(!l)},disabled:n,inputProps:i})))},vt=({linkInLinkRestriction:t,isVisible:n,children:l})=>{const{shouldRestrict:a,reason:i,elementId:s}=t;return a&&n?e.createElement(r.Infotip,{placement:"right",content:e.createElement(d.InfoTipCard,{content:bt[i],svgIcon:e.createElement(c.AlertTriangleIcon,null),learnMoreButton:ht,ctaButton:{label:(0,o.__)("Take me there","elementor"),onClick:()=>{s&&(0,g.selectElement)(s)}}})},e.createElement(r.Box,null,l)):e.createElement(e.Fragment,null,l)},bt={descendant:e.createElement(e.Fragment,null,(0,o.__)("To add a link to this container,","elementor"),e.createElement("br",null),(0,o.__)("first remove the link from the elements inside of it.","elementor")),ancestor:e.createElement(e.Fragment,null,(0,o.__)("To add a link to this element,","elementor"),e.createElement("br",null),(0,o.__)("first remove the link from its parent container.","elementor"))},yt=A((({label:n})=>{const{value:l,setValue:a,propType:i,disabled:s}=P(t.layoutDirectionPropTypeUtil),{value:u,setValue:d,disabled:m}=P(t.sizePropTypeUtil),p=!l&&!u||!!u,h=n.toLowerCase(),g=p?c.LinkIcon:c.DetachIcon,f=(0,o.__)("Link %s","elementor").replace("%s",h),E=(0,o.__)("Unlink %s","elementor").replace("%s",h);return e.createElement(w,{propType:i,value:l,setValue:a},e.createElement(r.Stack,{direction:"row",gap:2,flexWrap:"nowrap"},e.createElement(He,null,n),e.createElement(r.Tooltip,{title:p?E:f,placement:"top"},e.createElement(r.ToggleButton,{"aria-label":p?E:f,size:"tiny",value:"check",selected:p,sx:{marginLeft:"auto"},onChange:()=>{if(!p)return void d(l?.column?.value??null);const e=u?t.sizePropTypeUtil.create(u):null;a({row:e,column:e})},disabled:m||s},e.createElement(g,{fontSize:"tiny"})))),e.createElement(r.Stack,{direction:"row",gap:2,flexWrap:"nowrap"},e.createElement(r.Grid,{container:!0,gap:.75,alignItems:"center"},e.createElement(r.Grid,{item:!0,xs:12},e.createElement(U,null,(0,o.__)("Column","elementor"))),e.createElement(r.Grid,{item:!0,xs:12},e.createElement(xt,{bind:"column",isLinked:p}))),e.createElement(r.Grid,{container:!0,gap:.75,alignItems:"center"},e.createElement(r.Grid,{item:!0,xs:12},e.createElement(U,null,(0,o.__)("Row","elementor"))),e.createElement(r.Grid,{item:!0,xs:12},e.createElement(xt,{bind:"row",isLinked:p})))))})),xt=({bind:t,isLinked:n})=>n?e.createElement(re,null):e.createElement(I,{bind:t},e.createElement(re,null)),Ct=[{label:(0,o.__)("Auto","elementor"),value:"auto"},{label:"1/1",value:"1/1"},{label:"4/3",value:"4/3"},{label:"3/4",value:"3/4"},{label:"16/9",value:"16/9"},{label:"9/16",value:"9/16"},{label:"3/2",value:"3/2"},{label:"2/3",value:"2/3"}],wt="custom",_t=A((({label:n})=>{const{value:l,setValue:a,disabled:i}=P(t.stringPropTypeUtil),s=l&&!Ct.some((e=>e.value===l)),[u,m]=s?l.split("/"):["",""],[p,h]=(0,e.useState)(s),[g,f]=(0,e.useState)(u),[E,v]=(0,e.useState)(m),[b,y]=(0,e.useState)(s?wt:l||"");return e.createElement(H,null,e.createElement(r.Stack,{direction:"column",pt:2,gap:2},e.createElement(r.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},e.createElement(r.Grid,{item:!0,xs:6},e.createElement(He,null,n)),e.createElement(r.Grid,{item:!0,xs:6},e.createElement(r.Select,{size:"tiny",displayEmpty:!0,sx:{overflow:"hidden"},disabled:i,value:b,onChange:e=>{const t=e.target.value,n=t===wt;h(n),y(t),n||a(t)},fullWidth:!0},[...Ct,{label:(0,o.__)("Custom","elementor"),value:wt}].map((({label:t,...n})=>e.createElement(d.MenuListItem,{key:n.value,...n,value:n.value??""},t)))))),p&&e.createElement(r.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},e.createElement(r.Grid,{item:!0,xs:6},e.createElement(r.TextField,{size:"tiny",type:"number",fullWidth:!0,disabled:i,value:g,onChange:e=>{const t=e.target.value;f(t),t&&E&&a(`${t}/${E}`)},InputProps:{startAdornment:e.createElement(c.ArrowsMoveHorizontalIcon,{fontSize:"tiny"})}})),e.createElement(r.Grid,{item:!0,xs:6},e.createElement(r.TextField,{size:"tiny",type:"number",fullWidth:!0,disabled:i,value:E,onChange:e=>{const t=e.target.value;v(t),g&&t&&a(`${g}/${t}`)},InputProps:{startAdornment:e.createElement(c.ArrowsMoveVerticalIcon,{fontSize:"tiny"})}})))))})),St=(0,o.__)("Enable Unfiltered Uploads","elementor"),It=(0,o.__)("Before you enable unfiltered files upload, note that such files include a security risk. Elementor does run a process to remove possible malicious code, but there is still risk involved when using such files.","elementor"),Tt=(0,o.__)("Sorry, you can't upload that file yet","elementor"),zt=(0,o.__)("This is because this file type may pose a security risk. To upload them anyway, ask the site administrator to enable unfiltered file uploads.","elementor"),kt=(0,o.__)("Failed to enable unfiltered files upload.","elementor"),Pt=(0,o.__)("You can try again, if the problem persists, please contact support.","elementor"),Ot=t=>{const{mutateAsync:n,isPending:l}=function(){const e=(0,i.useQueryClient)();return(0,i.useMutation)({mutationFn:({allowUnfilteredFilesUpload:e})=>{return t=$,n=e?"1":"0",(0,s.httpService)().put(`${G}/${t}`,{value:n});var t,n},onSuccess:()=>e.invalidateQueries(W)})}(),{canUser:r}=(0,E.useCurrentUserCapabilities)(),[o,a]=(0,e.useState)(!1),c=r("manage_options"),u={...t,isPending:l,handleEnable:async()=>{try{const e=await n({allowUnfilteredFilesUpload:!0});!1===e?.data?.success?a(!0):t.onClose(!0)}catch{a(!0)}},isError:o,onClose:e=>{t.onClose(e),setTimeout((()=>a(!1)),300)}};return c?e.createElement(Vt,{...u}):e.createElement(Ut,{...u})},Vt=({open:t,onClose:n,handleEnable:l,isPending:a,isError:i})=>e.createElement(r.Dialog,{open:t,maxWidth:"sm",onClose:()=>n(!1)},e.createElement(r.DialogHeader,{logo:!1},e.createElement(r.DialogTitle,null,St)),e.createElement(r.Divider,null),e.createElement(r.DialogContent,null,e.createElement(r.DialogContentText,null,i?e.createElement(e.Fragment,null,kt," ",e.createElement("br",null)," ",Pt):It)),e.createElement(r.DialogActions,null,e.createElement(r.Button,{size:"medium",color:"secondary",onClick:()=>n(!1)},(0,o.__)("Cancel","elementor")),e.createElement(r.Button,{size:"medium",onClick:()=>l(),variant:"contained",color:"primary",disabled:a},a?e.createElement(r.CircularProgress,{size:24}):(0,o.__)("Enable","elementor")))),Ut=({open:t,onClose:n})=>e.createElement(r.Dialog,{open:t,maxWidth:"sm",onClose:()=>n(!1)},e.createElement(r.DialogHeader,{logo:!1},e.createElement(r.DialogTitle,null,Tt)),e.createElement(r.Divider,null),e.createElement(r.DialogContent,null,e.createElement(r.DialogContentText,null,zt)),e.createElement(r.DialogActions,null,e.createElement(r.Button,{size:"medium",onClick:()=>n(!1),variant:"contained",color:"primary"},(0,o.__)("Got it","elementor")))),Mt="transparent",Rt="#c1c1c1",Lt=`linear-gradient(45deg, ${Rt} 25%, ${Mt} 0, ${Mt} 75%, ${Rt} 0, ${Rt})`,At=(0,r.styled)(r.Card)`
	background-color: white;
	background-image: ${Lt}, ${Lt};
	background-size: ${8}px ${8}px;
	background-position:
		0 0,
		${4}px ${4}px;
	border: none;
`,Gt=(0,r.styled)(r.Stack)`
	position: relative;
	height: 140px;
	object-fit: contain;
	padding: 5px;
	justify-content: center;
	align-items: center;
	background-color: rgba( 255, 255, 255, 0.37 );
`,Ft={mode:"browse"},$t={mode:"upload"},Wt=A((()=>{const{value:n,setValue:l}=P(t.imageSrcPropTypeUtil),{id:a,url:i}=n??{},{data:s,isFetching:d}=(0,u.useWpMediaAttachment)(a?.value||null),m=s?.url??i?.value??null,{data:p}=B(),[h,g]=(0,e.useState)(!1),{open:f}=(0,u.useWpMediaFrame)({mediaTypes:["svg"],multiple:!1,selected:a?.value||null,onSelect:e=>{l({id:{$$type:"image-attachment-id",value:e.id},url:null})}}),E=e=>{p||e!==$t?f(e):g(!0)};return e.createElement(r.Stack,{gap:1},e.createElement(Ot,{open:h,onClose:e=>{g(!1),e&&f($t)}}),e.createElement(U,null," ",(0,o.__)("SVG","elementor")," "),e.createElement(H,null,e.createElement(At,{variant:"outlined"},e.createElement(Gt,null,d?e.createElement(r.CircularProgress,{role:"progressbar"}):e.createElement(r.CardMedia,{component:"img",image:m,alt:(0,o.__)("Preview SVG","elementor"),sx:{maxHeight:"140px",width:"50px"}})),e.createElement(r.CardOverlay,{sx:{"&:hover":{backgroundColor:"rgba( 0, 0, 0, 0.75 )"}}},e.createElement(r.Stack,{gap:1},e.createElement(r.Button,{size:"tiny",color:"inherit",variant:"outlined",onClick:()=>E(Ft)},(0,o.__)("Select SVG","elementor")),e.createElement(r.Button,{size:"tiny",variant:"text",color:"inherit",startIcon:e.createElement(c.UploadIcon,null),onClick:()=>E($t)},(0,o.__)("Upload","elementor")))))))})),{env:Bt}=(0,v.parseEnv)("@elementor/editor-controls"),Dt=A((()=>{const{value:n,setValue:l}=P(t.backgroundGradientOverlayPropTypeUtil);return e.createElement(H,null,e.createElement(r.UnstableGradientBox,{sx:{width:"auto",padding:1.5},value:(()=>{if(!n)return;const{type:e,angle:t,stops:l,positions:r}=n;return{type:e.value,angle:t.value,stops:l.value.map((({value:{color:e,offset:t}})=>({color:e.value,offset:t.value}))),positions:r?.value.split(" ")}})(),onChange:e=>{const n=(e=>({...e,type:t.stringPropTypeUtil.create(e.type),angle:t.numberPropTypeUtil.create(e.angle),stops:t.gradientColorStopPropTypeUtil.create(e.stops.map((({color:e,offset:n})=>t.colorStopPropTypeUtil.create({color:t.colorPropTypeUtil.create(e),offset:t.numberPropTypeUtil.create(n)}))))}))(e);n.positions&&(n.positions=t.stringPropTypeUtil.create(e.positions.join(" "))),l(n)}}))})),jt=t.backgroundGradientOverlayPropTypeUtil.create({type:t.stringPropTypeUtil.create("linear"),angle:t.numberPropTypeUtil.create(180),stops:t.gradientColorStopPropTypeUtil.create([t.colorStopPropTypeUtil.create({color:t.colorPropTypeUtil.create("rgb(0,0,0)"),offset:t.numberPropTypeUtil.create(0)}),t.colorStopPropTypeUtil.create({color:t.colorPropTypeUtil.create("rgb(255,255,255)"),offset:t.numberPropTypeUtil.create(100)})])}),Nt=[{value:"fixed",label:(0,o.__)("Fixed","elementor"),renderContent:({size:t})=>e.createElement(c.PinIcon,{fontSize:t}),showTooltip:!0},{value:"scroll",label:(0,o.__)("Scroll","elementor"),renderContent:({size:t})=>e.createElement(c.PinnedOffIcon,{fontSize:t}),showTooltip:!0}],Kt=()=>e.createElement(he,null,e.createElement(r.Grid,{item:!0,xs:6},e.createElement(U,null,(0,o.__)("Attachment","elementor"))),e.createElement(r.Grid,{item:!0,xs:6,sx:{display:"flex",justifyContent:"flex-end",overflow:"hidden"}},e.createElement(je,{options:Nt}))),qt=[{label:(0,o.__)("Center center","elementor"),value:"center center"},{label:(0,o.__)("Center left","elementor"),value:"center left"},{label:(0,o.__)("Center right","elementor"),value:"center right"},{label:(0,o.__)("Top center","elementor"),value:"top center"},{label:(0,o.__)("Top left","elementor"),value:"top left"},{label:(0,o.__)("Top right","elementor"),value:"top right"},{label:(0,o.__)("Bottom center","elementor"),value:"bottom center"},{label:(0,o.__)("Bottom left","elementor"),value:"bottom left"},{label:(0,o.__)("Bottom right","elementor"),value:"bottom right"},{label:(0,o.__)("Custom","elementor"),value:"custom"}],Ht=()=>{const n=P(t.backgroundImagePositionOffsetPropTypeUtil),l=P(t.stringPropTypeUtil),a=!!n.value;return e.createElement(r.Grid,{container:!0,spacing:1.5},e.createElement(r.Grid,{item:!0,xs:12},e.createElement(he,null,e.createElement(r.Grid,{item:!0,xs:6},e.createElement(U,null,(0,o.__)("Position","elementor"))),e.createElement(r.Grid,{item:!0,xs:6,sx:{display:"flex",justifyContent:"flex-end",overflow:"hidden"}},e.createElement(r.Select,{size:"tiny",value:(n.value?"custom":l.value)??"",onChange:e=>{const t=e.target.value||null;"custom"===t?n.setValue({x:null,y:null}):l.setValue(t)},fullWidth:!0},qt.map((({label:t,value:n})=>e.createElement(d.MenuListItem,{key:n,value:n??""},t))))))),a?e.createElement(w,{...n},e.createElement(r.Grid,{item:!0,xs:12},e.createElement(r.Grid,{container:!0,spacing:1.5},e.createElement(r.Grid,{item:!0,xs:6},e.createElement(I,{bind:"x"},e.createElement(re,{startIcon:e.createElement(c.LetterXIcon,{fontSize:"tiny"})}))),e.createElement(r.Grid,{item:!0,xs:6},e.createElement(I,{bind:"y"},e.createElement(re,{startIcon:e.createElement(c.LetterYIcon,{fontSize:"tiny"})})))))):null)},Xt=[{value:"repeat",label:(0,o.__)("Repeat","elementor"),renderContent:({size:t})=>e.createElement(c.GridDotsIcon,{fontSize:t}),showTooltip:!0},{value:"repeat-x",label:(0,o.__)("Repeat-x","elementor"),renderContent:({size:t})=>e.createElement(c.DotsHorizontalIcon,{fontSize:t}),showTooltip:!0},{value:"repeat-y",label:(0,o.__)("Repeat-y","elementor"),renderContent:({size:t})=>e.createElement(c.DotsVerticalIcon,{fontSize:t}),showTooltip:!0},{value:"no-repeat",label:(0,o.__)("No-repeat","elementor"),renderContent:({size:t})=>e.createElement(c.XIcon,{fontSize:t}),showTooltip:!0}],Yt=()=>e.createElement(he,null,e.createElement(r.Grid,{item:!0,xs:6},e.createElement(U,null,(0,o.__)("Repeat","elementor"))),e.createElement(r.Grid,{item:!0,xs:6,sx:{display:"flex",justifyContent:"flex-end"}},e.createElement(je,{options:Xt}))),Qt=[{value:"auto",label:(0,o.__)("Auto","elementor"),renderContent:({size:t})=>e.createElement(c.LetterAIcon,{fontSize:t}),showTooltip:!0},{value:"cover",label:(0,o.__)("Cover","elementor"),renderContent:({size:t})=>e.createElement(c.ArrowsMaximizeIcon,{fontSize:t}),showTooltip:!0},{value:"contain",label:(0,o.__)("Contain","elementor"),renderContent:({size:t})=>e.createElement(c.ArrowBarBothIcon,{fontSize:t}),showTooltip:!0},{value:"custom",label:(0,o.__)("Custom","elementor"),renderContent:({size:t})=>e.createElement(c.PencilIcon,{fontSize:t}),showTooltip:!0}],Zt=()=>{const n=P(t.backgroundImageSizeScalePropTypeUtil),l=P(t.stringPropTypeUtil),a=!!n.value;return e.createElement(r.Grid,{container:!0,spacing:1.5},e.createElement(r.Grid,{item:!0,xs:12},e.createElement(he,null,e.createElement(r.Grid,{item:!0,xs:6},e.createElement(U,null,(0,o.__)("Size","elementor"))),e.createElement(r.Grid,{item:!0,xs:6,sx:{display:"flex",justifyContent:"flex-end"}},e.createElement(We,{exclusive:!0,items:Qt,value:n.value?"custom":l.value,onChange:e=>{"custom"===e?n.setValue({width:null,height:null}):l.setValue(e)}})))),a?e.createElement(w,{...n},e.createElement(r.Grid,{item:!0,xs:12},e.createElement(he,null,e.createElement(r.Grid,{item:!0,xs:6},e.createElement(I,{bind:"width"},e.createElement(re,{startIcon:e.createElement(c.ArrowsMoveHorizontalIcon,{fontSize:"tiny"}),extendedValues:["auto"]}))),e.createElement(r.Grid,{item:!0,xs:6},e.createElement(I,{bind:"height"},e.createElement(re,{startIcon:e.createElement(c.ArrowsMoveVerticalIcon,{fontSize:"tiny"}),extendedValues:["auto"]})))))):null)},Jt=t.backgroundColorOverlayPropTypeUtil.create({color:t.colorPropTypeUtil.create("#00000033")}),en=()=>({$$type:"background-image-overlay",value:{image:{$$type:"image",value:{src:{$$type:"image-src",value:{url:{$$type:"url",value:Bt.background_placeholder_image},id:null}},size:{$$type:"string",value:"large"}}}}}),tn=[{label:(0,o.__)("Thumbnail - 150 x 150","elementor"),value:"thumbnail"},{label:(0,o.__)("Medium - 300 x 300","elementor"),value:"medium"},{label:(0,o.__)("Large 1024 x 1024","elementor"),value:"large"},{label:(0,o.__)("Full","elementor"),value:"full"}],nn=A((()=>{const{propType:n,value:l,setValue:r,disabled:a}=P(t.backgroundOverlayPropTypeUtil);return e.createElement(w,{propType:n,value:l,setValue:r},e.createElement(ke,{openOnAdd:!0,disabled:a,values:l??[],setValues:r,label:(0,o.__)("Overlay","elementor"),itemSettings:{Icon:on,Label:dn,Content:ln,initialValues:en()}}))})),ln=({anchorEl:t=null,bind:n})=>e.createElement(I,{bind:n},e.createElement(rn,{anchorEl:t})),rn=({anchorEl:n})=>{const{getTabsProps:l,getTabProps:a,getTabPanelProps:i}=(({color:n,image:l,gradient:o})=>{const{value:a,setValue:i}=P(t.backgroundImageOverlayPropTypeUtil),{value:s,setValue:c}=P(t.backgroundColorOverlayPropTypeUtil),{value:u,setValue:d}=P(t.backgroundGradientOverlayPropTypeUtil),{getTabsProps:m,getTabProps:p,getTabPanelProps:h}=(0,r.useTabs)(s?"color":u?"gradient":"image"),g=(0,e.useRef)({image:l,color:n,gradient:o}),f=(e,t)=>{t&&(g.current[e]=t)},E=(e,t)=>{switch(t){case"image":i(g.current.image),f("color",s),f("gradient",u);break;case"gradient":d(g.current.gradient),f("color",s),f("image",a);break;case"color":c(g.current.color),f("image",a),f("gradient",u)}return m().onChange(e,t)};return{getTabProps:p,getTabPanelProps:h,getTabsProps:()=>({...m(),onChange:E})}})({image:en().value,color:Jt.value,gradient:jt.value});return e.createElement(r.Box,{sx:{width:"100%"}},e.createElement(r.Box,{sx:{borderBottom:1,borderColor:"divider"}},e.createElement(r.Tabs,{size:"small",variant:"fullWidth",...l(),"aria-label":(0,o.__)("Background Overlay","elementor")},e.createElement(r.Tab,{label:(0,o.__)("Image","elementor"),...a("image")}),e.createElement(r.Tab,{label:(0,o.__)("Gradient","elementor"),...a("gradient")}),e.createElement(r.Tab,{label:(0,o.__)("Color","elementor"),...a("color")}))),e.createElement(r.TabPanel,{sx:{p:1.5},...i("image")},e.createElement(pe,null,e.createElement(fn,null))),e.createElement(r.TabPanel,{sx:{p:1.5},...i("gradient")},e.createElement(Dt,null)),e.createElement(r.TabPanel,{sx:{p:1.5},...i("color")},e.createElement(pe,null,e.createElement(gn,{anchorEl:n}))))},on=({value:t})=>{switch(t.$$type){case"background-image-overlay":return e.createElement(cn,{value:t});case"background-color-overlay":return e.createElement(sn,{value:t});case"background-gradient-overlay":return e.createElement(un,{value:t});default:return null}},an=e=>e?.value?.color?.value?e.value.color.value:"",sn=({value:t})=>{const n=an(t);return e.createElement(En,{size:"inherit",component:"span",value:n})},cn=({value:t})=>{const{imageUrl:n}=vn(t);return e.createElement(r.CardMedia,{image:n,sx:e=>({height:"1em",width:"1em",borderRadius:e.shape.borderRadius/2+"px",outline:`1px solid ${e.palette.action.disabled}`})})},un=({value:t})=>{const n=yn(t);return e.createElement(En,{size:"inherit",component:"span",value:n})},dn=({value:t})=>{switch(t.$$type){case"background-image-overlay":return e.createElement(pn,{value:t});case"background-color-overlay":return e.createElement(mn,{value:t});case"background-gradient-overlay":return e.createElement(hn,{value:t});default:return null}},mn=({value:t})=>{const n=an(t);return e.createElement("span",null,n)},pn=({value:t})=>{const{imageTitle:n}=vn(t);return e.createElement("span",null,n)},hn=({value:t})=>"linear"===t.value.type.value?e.createElement("span",null,(0,o.__)("Linear Gradient","elementor")):e.createElement("span",null,(0,o.__)("Radial Gradient","elementor")),gn=({anchorEl:n})=>{const l=P(t.backgroundColorOverlayPropTypeUtil);return e.createElement(w,{...l},e.createElement(I,{bind:"color"},e.createElement(ce,{anchorEl:n})))},fn=()=>{const n=P(t.backgroundImageOverlayPropTypeUtil);return e.createElement(w,{...n},e.createElement(I,{bind:"image"},e.createElement(r.Grid,{container:!0,spacing:1,alignItems:"center"},e.createElement(r.Grid,{item:!0,xs:12},e.createElement(Q,{resolutionLabel:(0,o.__)("Resolution","elementor"),sizes:tn})))),e.createElement(I,{bind:"position"},e.createElement(Ht,null)),e.createElement(I,{bind:"repeat"},e.createElement(Yt,null)),e.createElement(I,{bind:"size"},e.createElement(Zt,null)),e.createElement(I,{bind:"attachment"},e.createElement(Kt,null)))},En=(0,r.styled)(r.UnstableColorIndicator)((({theme:e})=>({borderRadius:e.shape.borderRadius/2+"px"}))),vn=e=>{let t,n=null;const l=e?.value.image.value?.src.value,{data:r}=(0,u.useWpMediaAttachment)(l.id?.value||null);if(l.id){const e=bn(r?.filename);t=`${r?.title}${e}`||null,n=r?.url||null}else l.url&&(n=l.url.value,t=n?.substring(n.lastIndexOf("/")+1)||null);return{imageTitle:t,imageUrl:n}},bn=e=>e?`.${e.substring(e.lastIndexOf(".")+1)}`:"",yn=e=>{const t=e.value,n=t.stops.value?.map((({value:{color:e,offset:t}})=>`${e.value} ${t.value??0}%`))?.join(",");return"linear"===t.type.value?`linear-gradient(${t.angle.value}deg, ${n})`:`radial-gradient(circle at ${t.positions.value}, ${n})`},xn=A((()=>{const n=P(t.backgroundPropTypeUtil),l=(0,p.isExperimentActive)("e_v_3_30"),a=(0,o.__)("Color","elementor");return e.createElement(w,{...n},e.createElement(I,{bind:"background-overlay"},e.createElement(nn,null)),e.createElement(I,{bind:"color"},e.createElement(r.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},e.createElement(r.Grid,{item:!0,xs:6},l?e.createElement(He,null,a):e.createElement(U,null,a)),e.createElement(r.Grid,{item:!0,xs:6},e.createElement(ce,null)))))})),Cn=A((()=>{const{value:n,setValue:l,disabled:o}=P(t.booleanPropTypeUtil);return e.createElement("div",{style:{display:"flex",justifyContent:"flex-end"}},e.createElement(r.Switch,{checked:!!n,onChange:e=>{l(e.target.checked)},size:"small",disabled:o}))}))}(),(window.elementorV2=window.elementorV2||{}).editorControls=l}(),window.elementorV2.editorControls?.init?.();