/*! For license information please see query.min.js.LICENSE.txt */
!function(){"use strict";var t={1020:function(t,e,s){var r=s(7557),i=Symbol.for("react.element"),n=(Symbol.for("react.fragment"),Object.prototype.hasOwnProperty),a=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,o={key:!0,ref:!0,__self:!0,__source:!0};e.jsx=function(t,e,s){var r,u={},c=null,h=null;for(r in void 0!==s&&(c=""+s),void 0!==e.key&&(c=""+e.key),void 0!==e.ref&&(h=e.ref),e)n.call(e,r)&&!o.hasOwnProperty(r)&&(u[r]=e[r]);if(t&&t.defaultProps)for(r in e=t.defaultProps)void 0===u[r]&&(u[r]=e[r]);return{$$typeof:i,type:t,key:c,ref:h,props:u,_owner:a.current}}},4848:function(t,e,s){t.exports=s(1020)},7557:function(t){t.exports=window.React}},e={};function s(r){var i=e[r];if(void 0!==i)return i.exports;var n=e[r]={exports:{}};return t[r](n,n.exports,s),n.exports}s.d=function(t,e){for(var r in e)s.o(e,r)&&!s.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:e[r]})},s.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},s.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})};var r={};s.r(r),s.d(r,{QueryClient:function(){return $},QueryClientProvider:function(){return X},createQueryClient:function(){return wt},useInfiniteQuery:function(){return gt},useMutation:function(){return Rt},useQuery:function(){return Ct},useQueryClient:function(){return Y}});var i="undefined"==typeof window||"Deno"in globalThis;function n(){}function a(t){return"number"==typeof t&&t>=0&&t!==1/0}function o(t,e){return Math.max(t+(e||0)-Date.now(),0)}function u(t,e){return"function"==typeof t?t(e):t}function c(t,e){return"function"==typeof t?t(e):t}function h(t,e){const{type:s="all",exact:r,fetchStatus:i,predicate:n,queryKey:a,stale:o}=t;if(a)if(r){if(e.queryHash!==d(a,e.options))return!1}else if(!p(e.queryKey,a))return!1;if("all"!==s){const t=e.isActive();if("active"===s&&!t)return!1;if("inactive"===s&&t)return!1}return!("boolean"==typeof o&&e.isStale()!==o||i&&i!==e.state.fetchStatus||n&&!n(e))}function l(t,e){const{exact:s,status:r,predicate:i,mutationKey:n}=t;if(n){if(!e.options.mutationKey)return!1;if(s){if(f(e.options.mutationKey)!==f(n))return!1}else if(!p(e.options.mutationKey,n))return!1}return!(r&&e.state.status!==r||i&&!i(e))}function d(t,e){return(e?.queryKeyHashFn||f)(t)}function f(t){return JSON.stringify(t,((t,e)=>b(e)?Object.keys(e).sort().reduce(((t,s)=>(t[s]=e[s],t)),{}):e))}function p(t,e){return t===e||typeof t==typeof e&&!(!t||!e||"object"!=typeof t||"object"!=typeof e)&&!Object.keys(e).some((s=>!p(t[s],e[s])))}function y(t,e){if(t===e)return t;const s=v(t)&&v(e);if(s||b(t)&&b(e)){const r=s?t:Object.keys(t),i=r.length,n=s?e:Object.keys(e),a=n.length,o=s?[]:{};let u=0;for(let i=0;i<a;i++){const a=s?i:n[i];(!s&&r.includes(a)||s)&&void 0===t[a]&&void 0===e[a]?(o[a]=void 0,u++):(o[a]=y(t[a],e[a]),o[a]===t[a]&&void 0!==t[a]&&u++)}return i===a&&u===i?t:o}return e}function m(t,e){if(!e||Object.keys(t).length!==Object.keys(e).length)return!1;for(const s in t)if(t[s]!==e[s])return!1;return!0}function v(t){return Array.isArray(t)&&t.length===Object.keys(t).length}function b(t){if(!g(t))return!1;const e=t.constructor;if(void 0===e)return!0;const s=e.prototype;return!!g(s)&&!!s.hasOwnProperty("isPrototypeOf")&&Object.getPrototypeOf(t)===Object.prototype}function g(t){return"[object Object]"===Object.prototype.toString.call(t)}function O(t,e,s){return"function"==typeof s.structuralSharing?s.structuralSharing(t,e):!1!==s.structuralSharing?y(t,e):e}function R(t,e,s=0){const r=[...t,e];return s&&r.length>s?r.slice(1):r}function C(t,e,s=0){const r=[e,...t];return s&&r.length>s?r.slice(0,-1):r}var w=Symbol();function P(t,e){return!t.queryFn&&e?.initialPromise?()=>e.initialPromise:t.queryFn&&t.queryFn!==w?t.queryFn:()=>Promise.reject(new Error(`Missing queryFn: '${t.queryHash}'`))}var S=function(){let t=[],e=0,s=t=>{t()},r=t=>{t()},i=t=>setTimeout(t,0);const n=r=>{e?t.push(r):i((()=>{s(r)}))};return{batch:n=>{let a;e++;try{a=n()}finally{e--,e||(()=>{const e=t;t=[],e.length&&i((()=>{r((()=>{e.forEach((t=>{s(t)}))}))}))})()}return a},batchCalls:t=>(...e)=>{n((()=>{t(...e)}))},schedule:n,setNotifyFunction:t=>{s=t},setBatchNotifyFunction:t=>{r=t},setScheduler:t=>{i=t}}}(),Q=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(t){return this.listeners.add(t),this.onSubscribe(),()=>{this.listeners.delete(t),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},q=new class extends Q{#t;#e;#s;constructor(){super(),this.#s=t=>{if(!i&&window.addEventListener){const e=()=>t();return window.addEventListener("visibilitychange",e,!1),()=>{window.removeEventListener("visibilitychange",e)}}}}onSubscribe(){this.#e||this.setEventListener(this.#s)}onUnsubscribe(){this.hasListeners()||(this.#e?.(),this.#e=void 0)}setEventListener(t){this.#s=t,this.#e?.(),this.#e=t((t=>{"boolean"==typeof t?this.setFocused(t):this.onFocus()}))}setFocused(t){this.#t!==t&&(this.#t=t,this.onFocus())}onFocus(){const t=this.isFocused();this.listeners.forEach((e=>{e(t)}))}isFocused(){return"boolean"==typeof this.#t?this.#t:"hidden"!==globalThis.document?.visibilityState}},E=new class extends Q{#r=!0;#e;#s;constructor(){super(),this.#s=t=>{if(!i&&window.addEventListener){const e=()=>t(!0),s=()=>t(!1);return window.addEventListener("online",e,!1),window.addEventListener("offline",s,!1),()=>{window.removeEventListener("online",e),window.removeEventListener("offline",s)}}}}onSubscribe(){this.#e||this.setEventListener(this.#s)}onUnsubscribe(){this.hasListeners()||(this.#e?.(),this.#e=void 0)}setEventListener(t){this.#s=t,this.#e?.(),this.#e=t(this.setOnline.bind(this))}setOnline(t){this.#r!==t&&(this.#r=t,this.listeners.forEach((e=>{e(t)})))}isOnline(){return this.#r}};function F(){let t,e;const s=new Promise(((s,r)=>{t=s,e=r}));function r(t){Object.assign(s,t),delete s.resolve,delete s.reject}return s.status="pending",s.catch((()=>{})),s.resolve=e=>{r({status:"fulfilled",value:e}),t(e)},s.reject=t=>{r({status:"rejected",reason:t}),e(t)},s}function x(t){return Math.min(1e3*2**t,3e4)}function M(t){return"online"!==(t??"online")||E.isOnline()}var D=class extends Error{constructor(t){super("CancelledError"),this.revert=t?.revert,this.silent=t?.silent}};function I(t){return t instanceof D}function T(t){let e,s=!1,r=0,n=!1;const a=F(),o=()=>q.isFocused()&&("always"===t.networkMode||E.isOnline())&&t.canRun(),u=()=>M(t.networkMode)&&t.canRun(),c=s=>{n||(n=!0,t.onSuccess?.(s),e?.(),a.resolve(s))},h=s=>{n||(n=!0,t.onError?.(s),e?.(),a.reject(s))},l=()=>new Promise((s=>{e=t=>{(n||o())&&s(t)},t.onPause?.()})).then((()=>{e=void 0,n||t.onContinue?.()})),d=()=>{if(n)return;let e;const a=0===r?t.initialPromise:void 0;try{e=a??t.fn()}catch(t){e=Promise.reject(t)}Promise.resolve(e).then(c).catch((e=>{if(n)return;const a=t.retry??(i?0:3),u=t.retryDelay??x,c="function"==typeof u?u(r,e):u,f=!0===a||"number"==typeof a&&r<a||"function"==typeof a&&a(r,e);var p;!s&&f?(r++,t.onFail?.(r,e),(p=c,new Promise((t=>{setTimeout(t,p)}))).then((()=>o()?void 0:l())).then((()=>{s?h(e):d()}))):h(e)}))};return{promise:a,cancel:e=>{n||(h(new D(e)),t.abort?.())},continue:()=>(e?.(),a),cancelRetry:()=>{s=!0},continueRetry:()=>{s=!1},canStart:u,start:()=>(u()?d():l().then(d),a)}}var A=class{#i;destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),a(this.gcTime)&&(this.#i=setTimeout((()=>{this.optionalRemove()}),this.gcTime))}updateGcTime(t){this.gcTime=Math.max(this.gcTime||0,t??(i?1/0:3e5))}clearGcTimeout(){this.#i&&(clearTimeout(this.#i),this.#i=void 0)}},U=class extends A{#n;#a;#o;#u;#c;#h;constructor(t){super(),this.#h=!1,this.#c=t.defaultOptions,this.setOptions(t.options),this.observers=[],this.#o=t.cache,this.queryKey=t.queryKey,this.queryHash=t.queryHash,this.#n=function(t){const e="function"==typeof t.initialData?t.initialData():t.initialData,s=void 0!==e,r=s?"function"==typeof t.initialDataUpdatedAt?t.initialDataUpdatedAt():t.initialDataUpdatedAt:0;return{data:e,dataUpdateCount:0,dataUpdatedAt:s?r??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:s?"success":"pending",fetchStatus:"idle"}}(this.options),this.state=t.state??this.#n,this.scheduleGc()}get meta(){return this.options.meta}get promise(){return this.#u?.promise}setOptions(t){this.options={...this.#c,...t},this.updateGcTime(this.options.gcTime)}optionalRemove(){this.observers.length||"idle"!==this.state.fetchStatus||this.#o.remove(this)}setData(t,e){const s=O(this.state.data,t,this.options);return this.#l({data:s,type:"success",dataUpdatedAt:e?.updatedAt,manual:e?.manual}),s}setState(t,e){this.#l({type:"setState",state:t,setStateOptions:e})}cancel(t){const e=this.#u?.promise;return this.#u?.cancel(t),e?e.then(n).catch(n):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(this.#n)}isActive(){return this.observers.some((t=>!1!==c(t.options.enabled,this)))}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===w||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStale(){return!!this.state.isInvalidated||(this.getObserversCount()>0?this.observers.some((t=>t.getCurrentResult().isStale)):void 0===this.state.data)}isStaleByTime(t=0){return this.state.isInvalidated||void 0===this.state.data||!o(this.state.dataUpdatedAt,t)}onFocus(){const t=this.observers.find((t=>t.shouldFetchOnWindowFocus()));t?.refetch({cancelRefetch:!1}),this.#u?.continue()}onOnline(){const t=this.observers.find((t=>t.shouldFetchOnReconnect()));t?.refetch({cancelRefetch:!1}),this.#u?.continue()}addObserver(t){this.observers.includes(t)||(this.observers.push(t),this.clearGcTimeout(),this.#o.notify({type:"observerAdded",query:this,observer:t}))}removeObserver(t){this.observers.includes(t)&&(this.observers=this.observers.filter((e=>e!==t)),this.observers.length||(this.#u&&(this.#h?this.#u.cancel({revert:!0}):this.#u.cancelRetry()),this.scheduleGc()),this.#o.notify({type:"observerRemoved",query:this,observer:t}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||this.#l({type:"invalidate"})}fetch(t,e){if("idle"!==this.state.fetchStatus)if(void 0!==this.state.data&&e?.cancelRefetch)this.cancel({silent:!0});else if(this.#u)return this.#u.continueRetry(),this.#u.promise;if(t&&this.setOptions(t),!this.options.queryFn){const t=this.observers.find((t=>t.options.queryFn));t&&this.setOptions(t.options)}const s=new AbortController,r=t=>{Object.defineProperty(t,"signal",{enumerable:!0,get:()=>(this.#h=!0,s.signal)})},i={fetchOptions:e,options:this.options,queryKey:this.queryKey,state:this.state,fetchFn:()=>{const t=P(this.options,e),s={queryKey:this.queryKey,meta:this.meta};return r(s),this.#h=!1,this.options.persister?this.options.persister(t,s,this):t(s)}};r(i),this.options.behavior?.onFetch(i,this),this.#a=this.state,"idle"!==this.state.fetchStatus&&this.state.fetchMeta===i.fetchOptions?.meta||this.#l({type:"fetch",meta:i.fetchOptions?.meta});const n=t=>{I(t)&&t.silent||this.#l({type:"error",error:t}),I(t)||(this.#o.config.onError?.(t,this),this.#o.config.onSettled?.(this.state.data,t,this)),this.scheduleGc()};return this.#u=T({initialPromise:e?.initialPromise,fn:i.fetchFn,abort:s.abort.bind(s),onSuccess:t=>{if(void 0!==t){try{this.setData(t)}catch(t){return void n(t)}this.#o.config.onSuccess?.(t,this),this.#o.config.onSettled?.(t,this.state.error,this),this.scheduleGc()}else n(new Error(`${this.queryHash} data is undefined`))},onError:n,onFail:(t,e)=>{this.#l({type:"failed",failureCount:t,error:e})},onPause:()=>{this.#l({type:"pause"})},onContinue:()=>{this.#l({type:"continue"})},retry:i.options.retry,retryDelay:i.options.retryDelay,networkMode:i.options.networkMode,canRun:()=>!0}),this.#u.start()}#l(t){this.state=(e=>{switch(t.type){case"failed":return{...e,fetchFailureCount:t.failureCount,fetchFailureReason:t.error};case"pause":return{...e,fetchStatus:"paused"};case"continue":return{...e,fetchStatus:"fetching"};case"fetch":return{...e,...k(e.data,this.options),fetchMeta:t.meta??null};case"success":return{...e,data:t.data,dataUpdateCount:e.dataUpdateCount+1,dataUpdatedAt:t.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!t.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const s=t.error;return I(s)&&s.revert&&this.#a?{...this.#a,fetchStatus:"idle"}:{...e,error:s,errorUpdateCount:e.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:e.fetchFailureCount+1,fetchFailureReason:s,fetchStatus:"idle",status:"error"};case"invalidate":return{...e,isInvalidated:!0};case"setState":return{...e,...t.state}}})(this.state),S.batch((()=>{this.observers.forEach((t=>{t.onQueryUpdate()})),this.#o.notify({query:this,type:"updated",action:t})}))}};function k(t,e){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:M(e.networkMode)?"fetching":"paused",...void 0===t&&{error:null,status:"pending"}}}var j=class extends Q{constructor(t={}){super(),this.config=t,this.#d=new Map}#d;build(t,e,s){const r=e.queryKey,i=e.queryHash??d(r,e);let n=this.get(i);return n||(n=new U({cache:this,queryKey:r,queryHash:i,options:t.defaultQueryOptions(e),state:s,defaultOptions:t.getQueryDefaults(r)}),this.add(n)),n}add(t){this.#d.has(t.queryHash)||(this.#d.set(t.queryHash,t),this.notify({type:"added",query:t}))}remove(t){const e=this.#d.get(t.queryHash);e&&(t.destroy(),e===t&&this.#d.delete(t.queryHash),this.notify({type:"removed",query:t}))}clear(){S.batch((()=>{this.getAll().forEach((t=>{this.remove(t)}))}))}get(t){return this.#d.get(t)}getAll(){return[...this.#d.values()]}find(t){const e={exact:!0,...t};return this.getAll().find((t=>h(e,t)))}findAll(t={}){const e=this.getAll();return Object.keys(t).length>0?e.filter((e=>h(t,e))):e}notify(t){S.batch((()=>{this.listeners.forEach((e=>{e(t)}))}))}onFocus(){S.batch((()=>{this.getAll().forEach((t=>{t.onFocus()}))}))}onOnline(){S.batch((()=>{this.getAll().forEach((t=>{t.onOnline()}))}))}},K=class extends A{#f;#p;#u;constructor(t){super(),this.mutationId=t.mutationId,this.#p=t.mutationCache,this.#f=[],this.state=t.state||{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0},this.setOptions(t.options),this.scheduleGc()}setOptions(t){this.options=t,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(t){this.#f.includes(t)||(this.#f.push(t),this.clearGcTimeout(),this.#p.notify({type:"observerAdded",mutation:this,observer:t}))}removeObserver(t){this.#f=this.#f.filter((e=>e!==t)),this.scheduleGc(),this.#p.notify({type:"observerRemoved",mutation:this,observer:t})}optionalRemove(){this.#f.length||("pending"===this.state.status?this.scheduleGc():this.#p.remove(this))}continue(){return this.#u?.continue()??this.execute(this.state.variables)}async execute(t){this.#u=T({fn:()=>this.options.mutationFn?this.options.mutationFn(t):Promise.reject(new Error("No mutationFn found")),onFail:(t,e)=>{this.#l({type:"failed",failureCount:t,error:e})},onPause:()=>{this.#l({type:"pause"})},onContinue:()=>{this.#l({type:"continue"})},retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#p.canRun(this)});const e="pending"===this.state.status,s=!this.#u.canStart();try{if(!e){this.#l({type:"pending",variables:t,isPaused:s}),await(this.#p.config.onMutate?.(t,this));const e=await(this.options.onMutate?.(t));e!==this.state.context&&this.#l({type:"pending",context:e,variables:t,isPaused:s})}const r=await this.#u.start();return await(this.#p.config.onSuccess?.(r,t,this.state.context,this)),await(this.options.onSuccess?.(r,t,this.state.context)),await(this.#p.config.onSettled?.(r,null,this.state.variables,this.state.context,this)),await(this.options.onSettled?.(r,null,t,this.state.context)),this.#l({type:"success",data:r}),r}catch(e){try{throw await(this.#p.config.onError?.(e,t,this.state.context,this)),await(this.options.onError?.(e,t,this.state.context)),await(this.#p.config.onSettled?.(void 0,e,this.state.variables,this.state.context,this)),await(this.options.onSettled?.(void 0,e,t,this.state.context)),e}finally{this.#l({type:"error",error:e})}}finally{this.#p.runNext(this)}}#l(t){this.state=(e=>{switch(t.type){case"failed":return{...e,failureCount:t.failureCount,failureReason:t.error};case"pause":return{...e,isPaused:!0};case"continue":return{...e,isPaused:!1};case"pending":return{...e,context:t.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:t.isPaused,status:"pending",variables:t.variables,submittedAt:Date.now()};case"success":return{...e,data:t.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...e,data:void 0,error:t.error,failureCount:e.failureCount+1,failureReason:t.error,isPaused:!1,status:"error"}}})(this.state),S.batch((()=>{this.#f.forEach((e=>{e.onMutationUpdate(t)})),this.#p.notify({mutation:this,type:"updated",action:t})}))}},_=class extends Q{constructor(t={}){super(),this.config=t,this.#y=new Map,this.#m=Date.now()}#y;#m;build(t,e,s){const r=new K({mutationCache:this,mutationId:++this.#m,options:t.defaultMutationOptions(e),state:s});return this.add(r),r}add(t){const e=L(t),s=this.#y.get(e)??[];s.push(t),this.#y.set(e,s),this.notify({type:"added",mutation:t})}remove(t){const e=L(t);if(this.#y.has(e)){const s=this.#y.get(e)?.filter((e=>e!==t));s&&(0===s.length?this.#y.delete(e):this.#y.set(e,s))}this.notify({type:"removed",mutation:t})}canRun(t){const e=this.#y.get(L(t))?.find((t=>"pending"===t.state.status));return!e||e===t}runNext(t){const e=this.#y.get(L(t))?.find((e=>e!==t&&e.state.isPaused));return e?.continue()??Promise.resolve()}clear(){S.batch((()=>{this.getAll().forEach((t=>{this.remove(t)}))}))}getAll(){return[...this.#y.values()].flat()}find(t){const e={exact:!0,...t};return this.getAll().find((t=>l(e,t)))}findAll(t={}){return this.getAll().filter((e=>l(t,e)))}notify(t){S.batch((()=>{this.listeners.forEach((e=>{e(t)}))}))}resumePausedMutations(){const t=this.getAll().filter((t=>t.state.isPaused));return S.batch((()=>Promise.all(t.map((t=>t.continue().catch(n))))))}};function L(t){return t.options.scope?.id??String(t.mutationId)}function H(t){return{onFetch:(e,s)=>{const r=e.options,i=e.fetchOptions?.meta?.fetchMore?.direction,n=e.state.data?.pages||[],a=e.state.data?.pageParams||[];let o={pages:[],pageParams:[]},u=0;const c=async()=>{let s=!1;const c=P(e.options,e.fetchOptions),h=async(t,r,i)=>{if(s)return Promise.reject();if(null==r&&t.pages.length)return Promise.resolve(t);const n={queryKey:e.queryKey,pageParam:r,direction:i?"backward":"forward",meta:e.options.meta};var a;a=n,Object.defineProperty(a,"signal",{enumerable:!0,get:()=>(e.signal.aborted?s=!0:e.signal.addEventListener("abort",(()=>{s=!0})),e.signal)});const o=await c(n),{maxPages:u}=e.options,h=i?C:R;return{pages:h(t.pages,o,u),pageParams:h(t.pageParams,r,u)}};if(i&&n.length){const t="backward"===i,e={pages:n,pageParams:a},s=(t?G:N)(r,e);o=await h(e,s,t)}else{const e=t??n.length;do{const t=0===u?a[0]??r.initialPageParam:N(r,o);if(u>0&&null==t)break;o=await h(o,t),u++}while(u<e)}return o};e.options.persister?e.fetchFn=()=>e.options.persister?.(c,{queryKey:e.queryKey,meta:e.options.meta,signal:e.signal},s):e.fetchFn=c}}}function N(t,{pages:e,pageParams:s}){const r=e.length-1;return e.length>0?t.getNextPageParam(e[r],e,s[r],s):void 0}function G(t,{pages:e,pageParams:s}){return e.length>0?t.getPreviousPageParam?.(e[0],e,s[0],s):void 0}function B(t,e){return!!e&&null!=N(t,e)}function W(t,e){return!(!e||!t.getPreviousPageParam)&&null!=G(t,e)}var $=class{#v;#p;#c;#b;#g;#O;#R;#C;constructor(t={}){this.#v=t.queryCache||new j,this.#p=t.mutationCache||new _,this.#c=t.defaultOptions||{},this.#b=new Map,this.#g=new Map,this.#O=0}mount(){this.#O++,1===this.#O&&(this.#R=q.subscribe((async t=>{t&&(await this.resumePausedMutations(),this.#v.onFocus())})),this.#C=E.subscribe((async t=>{t&&(await this.resumePausedMutations(),this.#v.onOnline())})))}unmount(){this.#O--,0===this.#O&&(this.#R?.(),this.#R=void 0,this.#C?.(),this.#C=void 0)}isFetching(t){return this.#v.findAll({...t,fetchStatus:"fetching"}).length}isMutating(t){return this.#p.findAll({...t,status:"pending"}).length}getQueryData(t){const e=this.defaultQueryOptions({queryKey:t});return this.#v.get(e.queryHash)?.state.data}ensureQueryData(t){const e=this.defaultQueryOptions(t),s=this.#v.build(this,e),r=s.state.data;return void 0===r?this.fetchQuery(t):(t.revalidateIfStale&&s.isStaleByTime(u(e.staleTime,s))&&this.prefetchQuery(e),Promise.resolve(r))}getQueriesData(t){return this.#v.findAll(t).map((({queryKey:t,state:e})=>[t,e.data]))}setQueryData(t,e,s){const r=this.defaultQueryOptions({queryKey:t}),i=this.#v.get(r.queryHash),n=i?.state.data,a=function(t,e){return"function"==typeof t?t(e):t}(e,n);if(void 0!==a)return this.#v.build(this,r).setData(a,{...s,manual:!0})}setQueriesData(t,e,s){return S.batch((()=>this.#v.findAll(t).map((({queryKey:t})=>[t,this.setQueryData(t,e,s)]))))}getQueryState(t){const e=this.defaultQueryOptions({queryKey:t});return this.#v.get(e.queryHash)?.state}removeQueries(t){const e=this.#v;S.batch((()=>{e.findAll(t).forEach((t=>{e.remove(t)}))}))}resetQueries(t,e){const s=this.#v,r={type:"active",...t};return S.batch((()=>(s.findAll(t).forEach((t=>{t.reset()})),this.refetchQueries(r,e))))}cancelQueries(t,e={}){const s={revert:!0,...e},r=S.batch((()=>this.#v.findAll(t).map((t=>t.cancel(s)))));return Promise.all(r).then(n).catch(n)}invalidateQueries(t,e={}){return S.batch((()=>{if(this.#v.findAll(t).forEach((t=>{t.invalidate()})),"none"===t?.refetchType)return Promise.resolve();const s={...t,type:t?.refetchType??t?.type??"active"};return this.refetchQueries(s,e)}))}refetchQueries(t,e={}){const s={...e,cancelRefetch:e.cancelRefetch??!0},r=S.batch((()=>this.#v.findAll(t).filter((t=>!t.isDisabled())).map((t=>{let e=t.fetch(void 0,s);return s.throwOnError||(e=e.catch(n)),"paused"===t.state.fetchStatus?Promise.resolve():e}))));return Promise.all(r).then(n)}fetchQuery(t){const e=this.defaultQueryOptions(t);void 0===e.retry&&(e.retry=!1);const s=this.#v.build(this,e);return s.isStaleByTime(u(e.staleTime,s))?s.fetch(e):Promise.resolve(s.state.data)}prefetchQuery(t){return this.fetchQuery(t).then(n).catch(n)}fetchInfiniteQuery(t){return t.behavior=H(t.pages),this.fetchQuery(t)}prefetchInfiniteQuery(t){return this.fetchInfiniteQuery(t).then(n).catch(n)}ensureInfiniteQueryData(t){return t.behavior=H(t.pages),this.ensureQueryData(t)}resumePausedMutations(){return E.isOnline()?this.#p.resumePausedMutations():Promise.resolve()}getQueryCache(){return this.#v}getMutationCache(){return this.#p}getDefaultOptions(){return this.#c}setDefaultOptions(t){this.#c=t}setQueryDefaults(t,e){this.#b.set(f(t),{queryKey:t,defaultOptions:e})}getQueryDefaults(t){const e=[...this.#b.values()],s={};return e.forEach((e=>{p(t,e.queryKey)&&Object.assign(s,e.defaultOptions)})),s}setMutationDefaults(t,e){this.#g.set(f(t),{mutationKey:t,defaultOptions:e})}getMutationDefaults(t){const e=[...this.#g.values()];let s={};return e.forEach((e=>{p(t,e.mutationKey)&&(s={...s,...e.defaultOptions})})),s}defaultQueryOptions(t){if(t._defaulted)return t;const e={...this.#c.queries,...this.getQueryDefaults(t.queryKey),...t,_defaulted:!0};return e.queryHash||(e.queryHash=d(e.queryKey,e)),void 0===e.refetchOnReconnect&&(e.refetchOnReconnect="always"!==e.networkMode),void 0===e.throwOnError&&(e.throwOnError=!!e.suspense),!e.networkMode&&e.persister&&(e.networkMode="offlineFirst"),e.queryFn===w&&(e.enabled=!1),e}defaultMutationOptions(t){return t?._defaulted?t:{...this.#c.mutations,...t?.mutationKey&&this.getMutationDefaults(t.mutationKey),...t,_defaulted:!0}}clear(){this.#v.clear(),this.#p.clear()}},z=s(7557),V=s(4848),J=z.createContext(void 0),Y=t=>{const e=z.useContext(J);if(t)return t;if(!e)throw new Error("No QueryClient set, use QueryClientProvider to set one");return e},X=({client:t,children:e})=>(z.useEffect((()=>(t.mount(),()=>{t.unmount()})),[t]),(0,V.jsx)(J.Provider,{value:t,children:e})),Z=class extends Q{constructor(t,e){super(),this.options=e,this.#w=t,this.#P=null,this.#S=F(),this.options.experimental_prefetchInRender||this.#S.reject(new Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(e)}#w;#Q=void 0;#q=void 0;#E=void 0;#F;#x;#S;#P;#M;#D;#I;#T;#A;#U;#k=new Set;bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){1===this.listeners.size&&(this.#Q.addObserver(this),tt(this.#Q,this.options)?this.#j():this.updateResult(),this.#K())}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return et(this.#Q,this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return et(this.#Q,this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,this.#_(),this.#L(),this.#Q.removeObserver(this)}setOptions(t,e){const s=this.options,r=this.#Q;if(this.options=this.#w.defaultQueryOptions(t),void 0!==this.options.enabled&&"boolean"!=typeof this.options.enabled&&"function"!=typeof this.options.enabled&&"boolean"!=typeof c(this.options.enabled,this.#Q))throw new Error("Expected enabled to be a boolean or a callback that returns a boolean");this.#H(),this.#Q.setOptions(this.options),s._defaulted&&!m(this.options,s)&&this.#w.getQueryCache().notify({type:"observerOptionsUpdated",query:this.#Q,observer:this});const i=this.hasListeners();i&&st(this.#Q,r,this.options,s)&&this.#j(),this.updateResult(e),!i||this.#Q===r&&c(this.options.enabled,this.#Q)===c(s.enabled,this.#Q)&&u(this.options.staleTime,this.#Q)===u(s.staleTime,this.#Q)||this.#N();const n=this.#G();!i||this.#Q===r&&c(this.options.enabled,this.#Q)===c(s.enabled,this.#Q)&&n===this.#U||this.#B(n)}getOptimisticResult(t){const e=this.#w.getQueryCache().build(this.#w,t),s=this.createResult(e,t);return r=s,!m(this.getCurrentResult(),r)&&(this.#E=s,this.#x=this.options,this.#F=this.#Q.state),s;var r}getCurrentResult(){return this.#E}trackResult(t,e){const s={};return Object.keys(t).forEach((r=>{Object.defineProperty(s,r,{configurable:!1,enumerable:!0,get:()=>(this.trackProp(r),e?.(r),t[r])})})),s}trackProp(t){this.#k.add(t)}getCurrentQuery(){return this.#Q}refetch({...t}={}){return this.fetch({...t})}fetchOptimistic(t){const e=this.#w.defaultQueryOptions(t),s=this.#w.getQueryCache().build(this.#w,e);return s.fetch().then((()=>this.createResult(s,e)))}fetch(t){return this.#j({...t,cancelRefetch:t.cancelRefetch??!0}).then((()=>(this.updateResult(),this.#E)))}#j(t){this.#H();let e=this.#Q.fetch(this.options,t);return t?.throwOnError||(e=e.catch(n)),e}#N(){this.#_();const t=u(this.options.staleTime,this.#Q);if(i||this.#E.isStale||!a(t))return;const e=o(this.#E.dataUpdatedAt,t)+1;this.#T=setTimeout((()=>{this.#E.isStale||this.updateResult()}),e)}#G(){return("function"==typeof this.options.refetchInterval?this.options.refetchInterval(this.#Q):this.options.refetchInterval)??!1}#B(t){this.#L(),this.#U=t,!i&&!1!==c(this.options.enabled,this.#Q)&&a(this.#U)&&0!==this.#U&&(this.#A=setInterval((()=>{(this.options.refetchIntervalInBackground||q.isFocused())&&this.#j()}),this.#U))}#K(){this.#N(),this.#B(this.#G())}#_(){this.#T&&(clearTimeout(this.#T),this.#T=void 0)}#L(){this.#A&&(clearInterval(this.#A),this.#A=void 0)}createResult(t,e){const s=this.#Q,r=this.options,i=this.#E,n=this.#F,a=this.#x,o=t!==s?t.state:this.#q,{state:u}=t;let c,h={...u},l=!1;if(e._optimisticResults){const i=this.hasListeners(),n=!i&&tt(t,e),a=i&&st(t,s,e,r);(n||a)&&(h={...h,...k(u.data,t.options)}),"isRestoring"===e._optimisticResults&&(h.fetchStatus="idle")}let{error:d,errorUpdatedAt:f,status:p}=h;if(e.select&&void 0!==h.data)if(i&&h.data===n?.data&&e.select===this.#M)c=this.#D;else try{this.#M=e.select,c=e.select(h.data),c=O(i?.data,c,e),this.#D=c,this.#P=null}catch(t){this.#P=t}else c=h.data;if(void 0!==e.placeholderData&&void 0===c&&"pending"===p){let t;if(i?.isPlaceholderData&&e.placeholderData===a?.placeholderData)t=i.data;else if(t="function"==typeof e.placeholderData?e.placeholderData(this.#I?.state.data,this.#I):e.placeholderData,e.select&&void 0!==t)try{t=e.select(t),this.#P=null}catch(t){this.#P=t}void 0!==t&&(p="success",c=O(i?.data,t,e),l=!0)}this.#P&&(d=this.#P,c=this.#D,f=Date.now(),p="error");const y="fetching"===h.fetchStatus,m="pending"===p,v="error"===p,b=m&&y,g=void 0!==c,R={status:p,fetchStatus:h.fetchStatus,isPending:m,isSuccess:"success"===p,isError:v,isInitialLoading:b,isLoading:b,data:c,dataUpdatedAt:h.dataUpdatedAt,error:d,errorUpdatedAt:f,failureCount:h.fetchFailureCount,failureReason:h.fetchFailureReason,errorUpdateCount:h.errorUpdateCount,isFetched:h.dataUpdateCount>0||h.errorUpdateCount>0,isFetchedAfterMount:h.dataUpdateCount>o.dataUpdateCount||h.errorUpdateCount>o.errorUpdateCount,isFetching:y,isRefetching:y&&!m,isLoadingError:v&&!g,isPaused:"paused"===h.fetchStatus,isPlaceholderData:l,isRefetchError:v&&g,isStale:rt(t,e),refetch:this.refetch,promise:this.#S};if(this.options.experimental_prefetchInRender){const e=t=>{"error"===R.status?t.reject(R.error):void 0!==R.data&&t.resolve(R.data)},r=()=>{const t=this.#S=R.promise=F();e(t)},i=this.#S;switch(i.status){case"pending":t.queryHash===s.queryHash&&e(i);break;case"fulfilled":"error"!==R.status&&R.data===i.value||r();break;case"rejected":"error"===R.status&&R.error===i.reason||r()}}return R}updateResult(t){const e=this.#E,s=this.createResult(this.#Q,this.options);if(this.#F=this.#Q.state,this.#x=this.options,void 0!==this.#F.data&&(this.#I=this.#Q),m(s,e))return;this.#E=s;const r={};!1!==t?.listeners&&(()=>{if(!e)return!0;const{notifyOnChangeProps:t}=this.options,s="function"==typeof t?t():t;if("all"===s||!s&&!this.#k.size)return!0;const r=new Set(s??this.#k);return this.options.throwOnError&&r.add("error"),Object.keys(this.#E).some((t=>{const s=t;return this.#E[s]!==e[s]&&r.has(s)}))})()&&(r.listeners=!0),this.#W({...r,...t})}#H(){const t=this.#w.getQueryCache().build(this.#w,this.options);if(t===this.#Q)return;const e=this.#Q;this.#Q=t,this.#q=t.state,this.hasListeners()&&(e?.removeObserver(this),t.addObserver(this))}onQueryUpdate(){this.updateResult(),this.hasListeners()&&this.#K()}#W(t){S.batch((()=>{t.listeners&&this.listeners.forEach((t=>{t(this.#E)})),this.#w.getQueryCache().notify({query:this.#Q,type:"observerResultsUpdated"})}))}};function tt(t,e){return function(t,e){return!1!==c(e.enabled,t)&&void 0===t.state.data&&!("error"===t.state.status&&!1===e.retryOnMount)}(t,e)||void 0!==t.state.data&&et(t,e,e.refetchOnMount)}function et(t,e,s){if(!1!==c(e.enabled,t)){const r="function"==typeof s?s(t):s;return"always"===r||!1!==r&&rt(t,e)}return!1}function st(t,e,s,r){return(t!==e||!1===c(r.enabled,t))&&(!s.suspense||"error"!==t.state.status)&&rt(t,s)}function rt(t,e){return!1!==c(e.enabled,t)&&t.isStaleByTime(u(e.staleTime,t))}var it=class extends Z{constructor(t,e){super(t,e)}bindMethods(){super.bindMethods(),this.fetchNextPage=this.fetchNextPage.bind(this),this.fetchPreviousPage=this.fetchPreviousPage.bind(this)}setOptions(t,e){super.setOptions({...t,behavior:H()},e)}getOptimisticResult(t){return t.behavior=H(),super.getOptimisticResult(t)}fetchNextPage(t){return this.fetch({...t,meta:{fetchMore:{direction:"forward"}}})}fetchPreviousPage(t){return this.fetch({...t,meta:{fetchMore:{direction:"backward"}}})}createResult(t,e){const{state:s}=t,r=super.createResult(t,e),{isFetching:i,isRefetching:n,isError:a,isRefetchError:o}=r,u=s.fetchMeta?.fetchMore?.direction,c=a&&"forward"===u,h=i&&"forward"===u,l=a&&"backward"===u,d=i&&"backward"===u;return{...r,fetchNextPage:this.fetchNextPage,fetchPreviousPage:this.fetchPreviousPage,hasNextPage:B(e,s.data),hasPreviousPage:W(e,s.data),isFetchNextPageError:c,isFetchingNextPage:h,isFetchPreviousPageError:l,isFetchingPreviousPage:d,isRefetchError:o&&!c&&!l,isRefetching:n&&!h&&!d}}};var nt=z.createContext(function(){let t=!1;return{clearReset:()=>{t=!1},reset:()=>{t=!0},isReset:()=>t}}()),at=()=>z.useContext(nt);function ot(t,e){return"function"==typeof t?t(...e):!!t}function ut(){}var ct=(t,e)=>{(t.suspense||t.throwOnError||t.experimental_prefetchInRender)&&(e.isReset()||(t.retryOnMount=!1))},ht=t=>{z.useEffect((()=>{t.clearReset()}),[t])},lt=({result:t,errorResetBoundary:e,throwOnError:s,query:r})=>t.isError&&!e.isReset()&&!t.isFetching&&r&&ot(s,[t.error,r]),dt=z.createContext(!1),ft=()=>z.useContext(dt),pt=(dt.Provider,t=>{t.suspense&&(void 0===t.staleTime&&(t.staleTime=1e3),"number"==typeof t.gcTime&&(t.gcTime=Math.max(t.gcTime,1e3)))}),yt=(t,e)=>t.isLoading&&t.isFetching&&!e,mt=(t,e)=>t?.suspense&&e.isPending,vt=(t,e,s)=>e.fetchOptimistic(t).catch((()=>{s.clearReset()}));function bt(t,e,s){const r=Y(s),n=ft(),a=at(),o=r.defaultQueryOptions(t);r.getDefaultOptions().queries?._experimental_beforeQuery?.(o),o._optimisticResults=n?"isRestoring":"optimistic",pt(o),ct(o,a),ht(a);const u=!r.getQueryCache().get(o.queryHash),[c]=z.useState((()=>new e(r,o))),h=c.getOptimisticResult(o);if(z.useSyncExternalStore(z.useCallback((t=>{const e=n?ut:c.subscribe(S.batchCalls(t));return c.updateResult(),e}),[c,n]),(()=>c.getCurrentResult()),(()=>c.getCurrentResult())),z.useEffect((()=>{c.setOptions(o,{listeners:!1})}),[o,c]),mt(o,h))throw vt(o,c,a);if(lt({result:h,errorResetBoundary:a,throwOnError:o.throwOnError,query:r.getQueryCache().get(o.queryHash)}))throw h.error;if(r.getDefaultOptions().queries?._experimental_afterQuery?.(o,h),o.experimental_prefetchInRender&&!i&&yt(h,n)){const t=u?vt(o,c,a):r.getQueryCache().get(o.queryHash)?.promise;t?.catch(ut).finally((()=>{c.updateResult()}))}return o.notifyOnChangeProps?h:c.trackResult(h)}function gt(t,e){return bt(t,it,e)}var Ot=class extends Q{#w;#E=void 0;#$;#z;constructor(t,e){super(),this.#w=t,this.setOptions(e),this.bindMethods(),this.#V()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(t){const e=this.options;this.options=this.#w.defaultMutationOptions(t),m(this.options,e)||this.#w.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.#$,observer:this}),e?.mutationKey&&this.options.mutationKey&&f(e.mutationKey)!==f(this.options.mutationKey)?this.reset():"pending"===this.#$?.state.status&&this.#$.setOptions(this.options)}onUnsubscribe(){this.hasListeners()||this.#$?.removeObserver(this)}onMutationUpdate(t){this.#V(),this.#W(t)}getCurrentResult(){return this.#E}reset(){this.#$?.removeObserver(this),this.#$=void 0,this.#V(),this.#W()}mutate(t,e){return this.#z=e,this.#$?.removeObserver(this),this.#$=this.#w.getMutationCache().build(this.#w,this.options),this.#$.addObserver(this),this.#$.execute(t)}#V(){const t=this.#$?.state??{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0};this.#E={...t,isPending:"pending"===t.status,isSuccess:"success"===t.status,isError:"error"===t.status,isIdle:"idle"===t.status,mutate:this.mutate,reset:this.reset}}#W(t){S.batch((()=>{if(this.#z&&this.hasListeners()){const e=this.#E.variables,s=this.#E.context;"success"===t?.type?(this.#z.onSuccess?.(t.data,e,s),this.#z.onSettled?.(t.data,null,e,s)):"error"===t?.type&&(this.#z.onError?.(t.error,e,s),this.#z.onSettled?.(void 0,t.error,e,s))}this.listeners.forEach((t=>{t(this.#E)}))}))}};function Rt(t,e){const s=Y(e),[r]=z.useState((()=>new Ot(s,t)));z.useEffect((()=>{r.setOptions(t)}),[r,t]);const i=z.useSyncExternalStore(z.useCallback((t=>r.subscribe(S.batchCalls(t))),[r]),(()=>r.getCurrentResult()),(()=>r.getCurrentResult())),n=z.useCallback(((t,e)=>{r.mutate(t,e).catch(ut)}),[r]);if(i.error&&ot(r.options.throwOnError,[i.error]))throw i.error;return{...i,mutate:n,mutateAsync:i.mutate}}function Ct(t,e){return bt(t,Z,e)}function wt(){return new $({defaultOptions:{queries:{refetchOnWindowFocus:!1,refetchOnReconnect:!1}}})}(window.elementorV2=window.elementorV2||{}).query=r}(),window.elementorV2.query?.init?.();