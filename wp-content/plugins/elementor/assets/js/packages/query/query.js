/*! For license information please see query.js.LICENSE.txt */
!function(){"use strict";var e={"./node_modules/react/cjs/react-jsx-runtime.development.js":function(e,t,r){!function(){var e,n=r("react"),s=Symbol.for("react.element"),i=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),a=Symbol.for("react.strict_mode"),u=Symbol.for("react.profiler"),c=Symbol.for("react.provider"),l=Symbol.for("react.context"),d=Symbol.for("react.forward_ref"),h=Symbol.for("react.suspense"),f=Symbol.for("react.suspense_list"),y=Symbol.for("react.memo"),p=Symbol.for("react.lazy"),m=Symbol.for("react.offscreen"),b=Symbol.iterator,v=n.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;function g(e){for(var t=arguments.length,r=new Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];!function(e,t,r){var n=v.ReactDebugCurrentFrame.getStackAddendum();""!==n&&(t+="%s",r=r.concat([n]));var s=r.map((function(e){return String(e)}));s.unshift("Warning: "+t),Function.prototype.apply.call(console.error,console,s)}(0,e,r)}function O(e){return e.displayName||"Context"}function q(e){if(null==e)return null;if("number"==typeof e.tag&&g("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."),"function"==typeof e)return e.displayName||e.name||null;if("string"==typeof e)return e;switch(e){case o:return"Fragment";case i:return"Portal";case u:return"Profiler";case a:return"StrictMode";case h:return"Suspense";case f:return"SuspenseList"}if("object"==typeof e)switch(e.$$typeof){case l:return O(e)+".Consumer";case c:return O(e._context)+".Provider";case d:return function(e,t,r){var n=e.displayName;if(n)return n;var s=t.displayName||t.name||"";return""!==s?r+"("+s+")":r}(e,e.render,"ForwardRef");case y:var t=e.displayName||null;return null!==t?t:q(e.type)||"Memo";case p:var r=e,n=r._payload,s=r._init;try{return q(s(n))}catch(e){return null}}return null}e=Symbol.for("react.module.reference");var k,R,j,S,C,w,_,Q=Object.assign,P=0;function E(){}E.__reactDisabledLog=!0;var M,T=v.ReactCurrentDispatcher;function F(e,t,r){if(void 0===M)try{throw Error()}catch(e){var n=e.stack.trim().match(/\n( *(at )?)/);M=n&&n[1]||""}return"\n"+M+e}var x,D=!1,I="function"==typeof WeakMap?WeakMap:Map;function A(e,t){if(!e||D)return"";var r,n=x.get(e);if(void 0!==n)return n;D=!0;var s,i=Error.prepareStackTrace;Error.prepareStackTrace=void 0,s=T.current,T.current=null,function(){if(0===P){k=console.log,R=console.info,j=console.warn,S=console.error,C=console.group,w=console.groupCollapsed,_=console.groupEnd;var e={configurable:!0,enumerable:!0,value:E,writable:!0};Object.defineProperties(console,{info:e,log:e,warn:e,error:e,group:e,groupCollapsed:e,groupEnd:e})}P++}();try{if(t){var o=function(){throw Error()};if(Object.defineProperty(o.prototype,"props",{set:function(){throw Error()}}),"object"==typeof Reflect&&Reflect.construct){try{Reflect.construct(o,[])}catch(e){r=e}Reflect.construct(e,[],o)}else{try{o.call()}catch(e){r=e}e.call(o.prototype)}}else{try{throw Error()}catch(e){r=e}e()}}catch(t){if(t&&r&&"string"==typeof t.stack){for(var a=t.stack.split("\n"),u=r.stack.split("\n"),c=a.length-1,l=u.length-1;c>=1&&l>=0&&a[c]!==u[l];)l--;for(;c>=1&&l>=0;c--,l--)if(a[c]!==u[l]){if(1!==c||1!==l)do{if(c--,--l<0||a[c]!==u[l]){var d="\n"+a[c].replace(" at new "," at ");return e.displayName&&d.includes("<anonymous>")&&(d=d.replace("<anonymous>",e.displayName)),"function"==typeof e&&x.set(e,d),d}}while(c>=1&&l>=0);break}}}finally{D=!1,T.current=s,function(){if(0==--P){var e={configurable:!0,enumerable:!0,writable:!0};Object.defineProperties(console,{log:Q({},e,{value:k}),info:Q({},e,{value:R}),warn:Q({},e,{value:j}),error:Q({},e,{value:S}),group:Q({},e,{value:C}),groupCollapsed:Q({},e,{value:w}),groupEnd:Q({},e,{value:_})})}P<0&&g("disabledDepth fell below zero. This is a bug in React. Please file an issue.")}(),Error.prepareStackTrace=i}var h=e?e.displayName||e.name:"",f=h?F(h):"";return"function"==typeof e&&x.set(e,f),f}function U(e,t,r){if(null==e)return"";if("function"==typeof e)return A(e,!(!(n=e.prototype)||!n.isReactComponent));var n;if("string"==typeof e)return F(e);switch(e){case h:return F("Suspense");case f:return F("SuspenseList")}if("object"==typeof e)switch(e.$$typeof){case d:return A(e.render,!1);case y:return U(e.type,t,r);case p:var s=e,i=s._payload,o=s._init;try{return U(o(i),t,r)}catch(e){}}return""}x=new I;var K=Object.prototype.hasOwnProperty,B={},N=v.ReactDebugCurrentFrame;function L(e){if(e){var t=e._owner,r=U(e.type,e._source,t?t.type:null);N.setExtraStackFrame(r)}else N.setExtraStackFrame(null)}var $=Array.isArray;function H(e){return $(e)}function W(e){return""+e}function G(e){if(function(e){try{return W(e),!1}catch(e){return!0}}(e))return g("The provided key is an unsupported type %s. This value must be coerced to a string before before using it here.",function(e){return"function"==typeof Symbol&&Symbol.toStringTag&&e[Symbol.toStringTag]||e.constructor.name||"Object"}(e)),W(e)}var z,V,J,Y=v.ReactCurrentOwner,X={key:!0,ref:!0,__self:!0,__source:!0};J={};var Z,ee=v.ReactCurrentOwner,te=v.ReactDebugCurrentFrame;function re(e){if(e){var t=e._owner,r=U(e.type,e._source,t?t.type:null);te.setExtraStackFrame(r)}else te.setExtraStackFrame(null)}function ne(e){return"object"==typeof e&&null!==e&&e.$$typeof===s}function se(){if(ee.current){var e=q(ee.current.type);if(e)return"\n\nCheck the render method of `"+e+"`."}return""}Z=!1;var ie={};function oe(e,t){if(e._store&&!e._store.validated&&null==e.key){e._store.validated=!0;var r=function(e){var t=se();if(!t){var r="string"==typeof e?e:e.displayName||e.name;r&&(t="\n\nCheck the top-level render call using <"+r+">.")}return t}(t);if(!ie[r]){ie[r]=!0;var n="";e&&e._owner&&e._owner!==ee.current&&(n=" It was passed a child from "+q(e._owner.type)+"."),re(e),g('Each child in a list should have a unique "key" prop.%s%s See https://reactjs.org/link/warning-keys for more information.',r,n),re(null)}}}function ae(e,t){if("object"==typeof e)if(H(e))for(var r=0;r<e.length;r++){var n=e[r];ne(n)&&oe(n,t)}else if(ne(e))e._store&&(e._store.validated=!0);else if(e){var s=function(e){if(null===e||"object"!=typeof e)return null;var t=b&&e[b]||e["@@iterator"];return"function"==typeof t?t:null}(e);if("function"==typeof s&&s!==e.entries)for(var i,o=s.call(e);!(i=o.next()).done;)ne(i.value)&&oe(i.value,t)}}var ue={};function ce(t,r,n,i,b,v){var O=function(t){return"string"==typeof t||"function"==typeof t||t===o||t===u||t===a||t===h||t===f||t===m||"object"==typeof t&&null!==t&&(t.$$typeof===p||t.$$typeof===y||t.$$typeof===c||t.$$typeof===l||t.$$typeof===d||t.$$typeof===e||void 0!==t.getModuleId)}(t);if(!O){var k="";(void 0===t||"object"==typeof t&&null!==t&&0===Object.keys(t).length)&&(k+=" You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.");var R,j=function(e){return void 0!==e?"\n\nCheck your code at "+e.fileName.replace(/^.*[\\\/]/,"")+":"+e.lineNumber+".":""}(b);k+=j||se(),null===t?R="null":H(t)?R="array":void 0!==t&&t.$$typeof===s?(R="<"+(q(t.type)||"Unknown")+" />",k=" Did you accidentally export a JSX literal instead of a component?"):R=typeof t,g("React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s",R,k)}var S=function(e,t,r,n,i){var o,a={},u=null,c=null;for(o in void 0!==r&&(G(r),u=""+r),function(e){if(K.call(e,"key")){var t=Object.getOwnPropertyDescriptor(e,"key").get;if(t&&t.isReactWarning)return!1}return void 0!==e.key}(t)&&(G(t.key),u=""+t.key),function(e){if(K.call(e,"ref")){var t=Object.getOwnPropertyDescriptor(e,"ref").get;if(t&&t.isReactWarning)return!1}return void 0!==e.ref}(t)&&(c=t.ref,function(e,t){if("string"==typeof e.ref&&Y.current&&t&&Y.current.stateNode!==t){var r=q(Y.current.type);J[r]||(g('Component "%s" contains the string ref "%s". Support for string refs will be removed in a future major release. This case cannot be automatically converted to an arrow function. We ask you to manually fix this case by using useRef() or createRef() instead. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-string-ref',q(Y.current.type),e.ref),J[r]=!0)}}(t,i)),t)K.call(t,o)&&!X.hasOwnProperty(o)&&(a[o]=t[o]);if(e&&e.defaultProps){var l=e.defaultProps;for(o in l)void 0===a[o]&&(a[o]=l[o])}if(u||c){var d="function"==typeof e?e.displayName||e.name||"Unknown":e;u&&function(e,t){var r=function(){z||(z=!0,g("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)",t))};r.isReactWarning=!0,Object.defineProperty(e,"key",{get:r,configurable:!0})}(a,d),c&&function(e,t){var r=function(){V||(V=!0,g("%s: `ref` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)",t))};r.isReactWarning=!0,Object.defineProperty(e,"ref",{get:r,configurable:!0})}(a,d)}return function(e,t,r,n,i,o,a){var u={$$typeof:s,type:e,key:t,ref:r,props:a,_owner:o,_store:{}};return Object.defineProperty(u._store,"validated",{configurable:!1,enumerable:!1,writable:!0,value:!1}),Object.defineProperty(u,"_self",{configurable:!1,enumerable:!1,writable:!1,value:n}),Object.defineProperty(u,"_source",{configurable:!1,enumerable:!1,writable:!1,value:i}),Object.freeze&&(Object.freeze(u.props),Object.freeze(u)),u}(e,u,c,i,n,Y.current,a)}(t,r,n,b,v);if(null==S)return S;if(O){var C=r.children;if(void 0!==C)if(i)if(H(C)){for(var w=0;w<C.length;w++)ae(C[w],t);Object.freeze&&Object.freeze(C)}else g("React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.");else ae(C,t)}if(K.call(r,"key")){var _=q(t),Q=Object.keys(r).filter((function(e){return"key"!==e})),P=Q.length>0?"{key: someKey, "+Q.join(": ..., ")+": ...}":"{key: someKey}";ue[_+P]||(g('A props object containing a "key" prop is being spread into JSX:\n  let props = %s;\n  <%s {...props} />\nReact keys must be passed directly to JSX without using spread:\n  let props = %s;\n  <%s key={someKey} {...props} />',P,_,Q.length>0?"{"+Q.join(": ..., ")+": ...}":"{}",_),ue[_+P]=!0)}return t===o?function(e){for(var t=Object.keys(e.props),r=0;r<t.length;r++){var n=t[r];if("children"!==n&&"key"!==n){re(e),g("Invalid prop `%s` supplied to `React.Fragment`. React.Fragment can only have `key` and `children` props.",n),re(null);break}}null!==e.ref&&(re(e),g("Invalid attribute `ref` supplied to `React.Fragment`."),re(null))}(S):function(e){var t,r=e.type;if(null!=r&&"string"!=typeof r){if("function"==typeof r)t=r.propTypes;else{if("object"!=typeof r||r.$$typeof!==d&&r.$$typeof!==y)return;t=r.propTypes}if(t){var n=q(r);!function(e,t,r,n,s){var i=Function.call.bind(K);for(var o in e)if(i(e,o)){var a=void 0;try{if("function"!=typeof e[o]){var u=Error((n||"React class")+": "+r+" type `"+o+"` is invalid; it must be a function, usually from the `prop-types` package, but received `"+typeof e[o]+"`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.");throw u.name="Invariant Violation",u}a=e[o](t,o,n,r,null,"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED")}catch(e){a=e}!a||a instanceof Error||(L(s),g("%s: type specification of %s `%s` is invalid; the type checker function must return `null` or an `Error` but returned a %s. You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument).",n||"React class",r,o,typeof a),L(null)),a instanceof Error&&!(a.message in B)&&(B[a.message]=!0,L(s),g("Failed %s type: %s",r,a.message),L(null))}}(t,e.props,"prop",n,e)}else void 0===r.PropTypes||Z||(Z=!0,g("Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?",q(r)||"Unknown"));"function"!=typeof r.getDefaultProps||r.getDefaultProps.isReactClassApproved||g("getDefaultProps is only used on classic React.createClass definitions. Use a static property named `defaultProps` instead.")}}(S),S}t.Fragment=o,t.jsx=function(e,t,r){return ce(e,t,r,!1)},t.jsxs=function(e,t,r){return ce(e,t,r,!0)}}()},"./node_modules/react/jsx-runtime.js":function(e,t,r){e.exports=r("./node_modules/react/cjs/react-jsx-runtime.development.js")},react:function(e){e.exports=window.React},"./node_modules/@tanstack/query-core/build/modern/focusManager.js":function(e,t,r){r.r(t),r.d(t,{FocusManager:function(){return i},focusManager:function(){return o}});var n=r("./node_modules/@tanstack/query-core/build/modern/subscribable.js"),s=r("./node_modules/@tanstack/query-core/build/modern/utils.js"),i=class extends n.Subscribable{#e;#t;#r;constructor(){super(),this.#r=e=>{if(!s.isServer&&window.addEventListener){const t=()=>e();return window.addEventListener("visibilitychange",t,!1),()=>{window.removeEventListener("visibilitychange",t)}}}}onSubscribe(){this.#t||this.setEventListener(this.#r)}onUnsubscribe(){this.hasListeners()||(this.#t?.(),this.#t=void 0)}setEventListener(e){this.#r=e,this.#t?.(),this.#t=e((e=>{"boolean"==typeof e?this.setFocused(e):this.onFocus()}))}setFocused(e){this.#e!==e&&(this.#e=e,this.onFocus())}onFocus(){const e=this.isFocused();this.listeners.forEach((t=>{t(e)}))}isFocused(){return"boolean"==typeof this.#e?this.#e:"hidden"!==globalThis.document?.visibilityState}},o=new i},"./node_modules/@tanstack/query-core/build/modern/infiniteQueryBehavior.js":function(e,t,r){r.r(t),r.d(t,{hasNextPage:function(){return a},hasPreviousPage:function(){return u},infiniteQueryBehavior:function(){return s}});var n=r("./node_modules/@tanstack/query-core/build/modern/utils.js");function s(e){return{onFetch:(t,r)=>{const s=t.options,a=t.fetchOptions?.meta?.fetchMore?.direction,u=t.state.data?.pages||[],c=t.state.data?.pageParams||[];let l={pages:[],pageParams:[]},d=0;const h=async()=>{let r=!1;const h=(0,n.ensureQueryFn)(t.options,t.fetchOptions),f=async(e,s,i)=>{if(r)return Promise.reject();if(null==s&&e.pages.length)return Promise.resolve(e);const o={queryKey:t.queryKey,pageParam:s,direction:i?"backward":"forward",meta:t.options.meta};var a;a=o,Object.defineProperty(a,"signal",{enumerable:!0,get:()=>(t.signal.aborted?r=!0:t.signal.addEventListener("abort",(()=>{r=!0})),t.signal)});const u=await h(o),{maxPages:c}=t.options,l=i?n.addToStart:n.addToEnd;return{pages:l(e.pages,u,c),pageParams:l(e.pageParams,s,c)}};if(a&&u.length){const e="backward"===a,t={pages:u,pageParams:c},r=(e?o:i)(s,t);l=await f(t,r,e)}else{const t=e??u.length;do{const e=0===d?c[0]??s.initialPageParam:i(s,l);if(d>0&&null==e)break;l=await f(l,e),d++}while(d<t)}return l};t.options.persister?t.fetchFn=()=>t.options.persister?.(h,{queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},r):t.fetchFn=h}}}function i(e,{pages:t,pageParams:r}){const n=t.length-1;return t.length>0?e.getNextPageParam(t[n],t,r[n],r):void 0}function o(e,{pages:t,pageParams:r}){return t.length>0?e.getPreviousPageParam?.(t[0],t,r[0],r):void 0}function a(e,t){return!!t&&null!=i(e,t)}function u(e,t){return!(!t||!e.getPreviousPageParam)&&null!=o(e,t)}},"./node_modules/@tanstack/query-core/build/modern/infiniteQueryObserver.js":function(e,t,r){r.r(t),r.d(t,{InfiniteQueryObserver:function(){return i}});var n=r("./node_modules/@tanstack/query-core/build/modern/queryObserver.js"),s=r("./node_modules/@tanstack/query-core/build/modern/infiniteQueryBehavior.js"),i=class extends n.QueryObserver{constructor(e,t){super(e,t)}bindMethods(){super.bindMethods(),this.fetchNextPage=this.fetchNextPage.bind(this),this.fetchPreviousPage=this.fetchPreviousPage.bind(this)}setOptions(e,t){super.setOptions({...e,behavior:(0,s.infiniteQueryBehavior)()},t)}getOptimisticResult(e){return e.behavior=(0,s.infiniteQueryBehavior)(),super.getOptimisticResult(e)}fetchNextPage(e){return this.fetch({...e,meta:{fetchMore:{direction:"forward"}}})}fetchPreviousPage(e){return this.fetch({...e,meta:{fetchMore:{direction:"backward"}}})}createResult(e,t){const{state:r}=e,n=super.createResult(e,t),{isFetching:i,isRefetching:o,isError:a,isRefetchError:u}=n,c=r.fetchMeta?.fetchMore?.direction,l=a&&"forward"===c,d=i&&"forward"===c,h=a&&"backward"===c,f=i&&"backward"===c;return{...n,fetchNextPage:this.fetchNextPage,fetchPreviousPage:this.fetchPreviousPage,hasNextPage:(0,s.hasNextPage)(t,r.data),hasPreviousPage:(0,s.hasPreviousPage)(t,r.data),isFetchNextPageError:l,isFetchingNextPage:d,isFetchPreviousPageError:h,isFetchingPreviousPage:f,isRefetchError:u&&!l&&!h,isRefetching:o&&!d&&!f}}}},"./node_modules/@tanstack/query-core/build/modern/mutation.js":function(e,t,r){r.r(t),r.d(t,{Mutation:function(){return o},getDefaultState:function(){return a}});var n=r("./node_modules/@tanstack/query-core/build/modern/notifyManager.js"),s=r("./node_modules/@tanstack/query-core/build/modern/removable.js"),i=r("./node_modules/@tanstack/query-core/build/modern/retryer.js"),o=class extends s.Removable{#n;#s;#i;constructor(e){super(),this.mutationId=e.mutationId,this.#s=e.mutationCache,this.#n=[],this.state=e.state||{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0},this.setOptions(e.options),this.scheduleGc()}setOptions(e){this.options=e,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(e){this.#n.includes(e)||(this.#n.push(e),this.clearGcTimeout(),this.#s.notify({type:"observerAdded",mutation:this,observer:e}))}removeObserver(e){this.#n=this.#n.filter((t=>t!==e)),this.scheduleGc(),this.#s.notify({type:"observerRemoved",mutation:this,observer:e})}optionalRemove(){this.#n.length||("pending"===this.state.status?this.scheduleGc():this.#s.remove(this))}continue(){return this.#i?.continue()??this.execute(this.state.variables)}async execute(e){this.#i=(0,i.createRetryer)({fn:()=>this.options.mutationFn?this.options.mutationFn(e):Promise.reject(new Error("No mutationFn found")),onFail:(e,t)=>{this.#o({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#o({type:"pause"})},onContinue:()=>{this.#o({type:"continue"})},retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#s.canRun(this)});const t="pending"===this.state.status,r=!this.#i.canStart();try{if(!t){this.#o({type:"pending",variables:e,isPaused:r}),await(this.#s.config.onMutate?.(e,this));const t=await(this.options.onMutate?.(e));t!==this.state.context&&this.#o({type:"pending",context:t,variables:e,isPaused:r})}const n=await this.#i.start();return await(this.#s.config.onSuccess?.(n,e,this.state.context,this)),await(this.options.onSuccess?.(n,e,this.state.context)),await(this.#s.config.onSettled?.(n,null,this.state.variables,this.state.context,this)),await(this.options.onSettled?.(n,null,e,this.state.context)),this.#o({type:"success",data:n}),n}catch(t){try{throw await(this.#s.config.onError?.(t,e,this.state.context,this)),await(this.options.onError?.(t,e,this.state.context)),await(this.#s.config.onSettled?.(void 0,t,this.state.variables,this.state.context,this)),await(this.options.onSettled?.(void 0,t,e,this.state.context)),t}finally{this.#o({type:"error",error:t})}}finally{this.#s.runNext(this)}}#o(e){this.state=(t=>{switch(e.type){case"failed":return{...t,failureCount:e.failureCount,failureReason:e.error};case"pause":return{...t,isPaused:!0};case"continue":return{...t,isPaused:!1};case"pending":return{...t,context:e.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:e.isPaused,status:"pending",variables:e.variables,submittedAt:Date.now()};case"success":return{...t,data:e.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...t,data:void 0,error:e.error,failureCount:t.failureCount+1,failureReason:e.error,isPaused:!1,status:"error"}}})(this.state),n.notifyManager.batch((()=>{this.#n.forEach((t=>{t.onMutationUpdate(e)})),this.#s.notify({mutation:this,type:"updated",action:e})}))}};function a(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}},"./node_modules/@tanstack/query-core/build/modern/mutationCache.js":function(e,t,r){r.r(t),r.d(t,{MutationCache:function(){return a}});var n=r("./node_modules/@tanstack/query-core/build/modern/notifyManager.js"),s=r("./node_modules/@tanstack/query-core/build/modern/mutation.js"),i=r("./node_modules/@tanstack/query-core/build/modern/utils.js"),o=r("./node_modules/@tanstack/query-core/build/modern/subscribable.js"),a=class extends o.Subscribable{constructor(e={}){super(),this.config=e,this.#a=new Map,this.#u=Date.now()}#a;#u;build(e,t,r){const n=new s.Mutation({mutationCache:this,mutationId:++this.#u,options:e.defaultMutationOptions(t),state:r});return this.add(n),n}add(e){const t=u(e),r=this.#a.get(t)??[];r.push(e),this.#a.set(t,r),this.notify({type:"added",mutation:e})}remove(e){const t=u(e);if(this.#a.has(t)){const r=this.#a.get(t)?.filter((t=>t!==e));r&&(0===r.length?this.#a.delete(t):this.#a.set(t,r))}this.notify({type:"removed",mutation:e})}canRun(e){const t=this.#a.get(u(e))?.find((e=>"pending"===e.state.status));return!t||t===e}runNext(e){const t=this.#a.get(u(e))?.find((t=>t!==e&&t.state.isPaused));return t?.continue()??Promise.resolve()}clear(){n.notifyManager.batch((()=>{this.getAll().forEach((e=>{this.remove(e)}))}))}getAll(){return[...this.#a.values()].flat()}find(e){const t={exact:!0,...e};return this.getAll().find((e=>(0,i.matchMutation)(t,e)))}findAll(e={}){return this.getAll().filter((t=>(0,i.matchMutation)(e,t)))}notify(e){n.notifyManager.batch((()=>{this.listeners.forEach((t=>{t(e)}))}))}resumePausedMutations(){const e=this.getAll().filter((e=>e.state.isPaused));return n.notifyManager.batch((()=>Promise.all(e.map((e=>e.continue().catch(i.noop))))))}};function u(e){return e.options.scope?.id??String(e.mutationId)}},"./node_modules/@tanstack/query-core/build/modern/mutationObserver.js":function(e,t,r){r.r(t),r.d(t,{MutationObserver:function(){return a}});var n=r("./node_modules/@tanstack/query-core/build/modern/mutation.js"),s=r("./node_modules/@tanstack/query-core/build/modern/notifyManager.js"),i=r("./node_modules/@tanstack/query-core/build/modern/subscribable.js"),o=r("./node_modules/@tanstack/query-core/build/modern/utils.js"),a=class extends i.Subscribable{#c;#l=void 0;#d;#h;constructor(e,t){super(),this.#c=e,this.setOptions(t),this.bindMethods(),this.#f()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(e){const t=this.options;this.options=this.#c.defaultMutationOptions(e),(0,o.shallowEqualObjects)(this.options,t)||this.#c.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.#d,observer:this}),t?.mutationKey&&this.options.mutationKey&&(0,o.hashKey)(t.mutationKey)!==(0,o.hashKey)(this.options.mutationKey)?this.reset():"pending"===this.#d?.state.status&&this.#d.setOptions(this.options)}onUnsubscribe(){this.hasListeners()||this.#d?.removeObserver(this)}onMutationUpdate(e){this.#f(),this.#y(e)}getCurrentResult(){return this.#l}reset(){this.#d?.removeObserver(this),this.#d=void 0,this.#f(),this.#y()}mutate(e,t){return this.#h=t,this.#d?.removeObserver(this),this.#d=this.#c.getMutationCache().build(this.#c,this.options),this.#d.addObserver(this),this.#d.execute(e)}#f(){const e=this.#d?.state??(0,n.getDefaultState)();this.#l={...e,isPending:"pending"===e.status,isSuccess:"success"===e.status,isError:"error"===e.status,isIdle:"idle"===e.status,mutate:this.mutate,reset:this.reset}}#y(e){s.notifyManager.batch((()=>{if(this.#h&&this.hasListeners()){const t=this.#l.variables,r=this.#l.context;"success"===e?.type?(this.#h.onSuccess?.(e.data,t,r),this.#h.onSettled?.(e.data,null,t,r)):"error"===e?.type&&(this.#h.onError?.(e.error,t,r),this.#h.onSettled?.(void 0,e.error,t,r))}this.listeners.forEach((e=>{e(this.#l)}))}))}}},"./node_modules/@tanstack/query-core/build/modern/notifyManager.js":function(e,t,r){function n(){let e=[],t=0,r=e=>{e()},n=e=>{e()},s=e=>setTimeout(e,0);const i=n=>{t?e.push(n):s((()=>{r(n)}))};return{batch:i=>{let o;t++;try{o=i()}finally{t--,t||(()=>{const t=e;e=[],t.length&&s((()=>{n((()=>{t.forEach((e=>{r(e)}))}))}))})()}return o},batchCalls:e=>(...t)=>{i((()=>{e(...t)}))},schedule:i,setNotifyFunction:e=>{r=e},setBatchNotifyFunction:e=>{n=e},setScheduler:e=>{s=e}}}r.r(t),r.d(t,{createNotifyManager:function(){return n},notifyManager:function(){return s}});var s=n()},"./node_modules/@tanstack/query-core/build/modern/onlineManager.js":function(e,t,r){r.r(t),r.d(t,{OnlineManager:function(){return i},onlineManager:function(){return o}});var n=r("./node_modules/@tanstack/query-core/build/modern/subscribable.js"),s=r("./node_modules/@tanstack/query-core/build/modern/utils.js"),i=class extends n.Subscribable{#p=!0;#t;#r;constructor(){super(),this.#r=e=>{if(!s.isServer&&window.addEventListener){const t=()=>e(!0),r=()=>e(!1);return window.addEventListener("online",t,!1),window.addEventListener("offline",r,!1),()=>{window.removeEventListener("online",t),window.removeEventListener("offline",r)}}}}onSubscribe(){this.#t||this.setEventListener(this.#r)}onUnsubscribe(){this.hasListeners()||(this.#t?.(),this.#t=void 0)}setEventListener(e){this.#r=e,this.#t?.(),this.#t=e(this.setOnline.bind(this))}setOnline(e){this.#p!==e&&(this.#p=e,this.listeners.forEach((t=>{t(e)})))}isOnline(){return this.#p}},o=new i},"./node_modules/@tanstack/query-core/build/modern/query.js":function(e,t,r){r.r(t),r.d(t,{Query:function(){return a},fetchState:function(){return u}});var n=r("./node_modules/@tanstack/query-core/build/modern/utils.js"),s=r("./node_modules/@tanstack/query-core/build/modern/notifyManager.js"),i=r("./node_modules/@tanstack/query-core/build/modern/retryer.js"),o=r("./node_modules/@tanstack/query-core/build/modern/removable.js"),a=class extends o.Removable{#m;#b;#v;#i;#g;#O;constructor(e){super(),this.#O=!1,this.#g=e.defaultOptions,this.setOptions(e.options),this.observers=[],this.#v=e.cache,this.queryKey=e.queryKey,this.queryHash=e.queryHash,this.#m=function(e){const t="function"==typeof e.initialData?e.initialData():e.initialData,r=void 0!==t,n=r?"function"==typeof e.initialDataUpdatedAt?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:r?n??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:r?"success":"pending",fetchStatus:"idle"}}(this.options),this.state=e.state??this.#m,this.scheduleGc()}get meta(){return this.options.meta}get promise(){return this.#i?.promise}setOptions(e){this.options={...this.#g,...e},this.updateGcTime(this.options.gcTime)}optionalRemove(){this.observers.length||"idle"!==this.state.fetchStatus||this.#v.remove(this)}setData(e,t){const r=(0,n.replaceData)(this.state.data,e,this.options);return this.#o({data:r,type:"success",dataUpdatedAt:t?.updatedAt,manual:t?.manual}),r}setState(e,t){this.#o({type:"setState",state:e,setStateOptions:t})}cancel(e){const t=this.#i?.promise;return this.#i?.cancel(e),t?t.then(n.noop).catch(n.noop):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(this.#m)}isActive(){return this.observers.some((e=>!1!==(0,n.resolveEnabled)(e.options.enabled,this)))}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===n.skipToken||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStale(){return!!this.state.isInvalidated||(this.getObserversCount()>0?this.observers.some((e=>e.getCurrentResult().isStale)):void 0===this.state.data)}isStaleByTime(e=0){return this.state.isInvalidated||void 0===this.state.data||!(0,n.timeUntilStale)(this.state.dataUpdatedAt,e)}onFocus(){const e=this.observers.find((e=>e.shouldFetchOnWindowFocus()));e?.refetch({cancelRefetch:!1}),this.#i?.continue()}onOnline(){const e=this.observers.find((e=>e.shouldFetchOnReconnect()));e?.refetch({cancelRefetch:!1}),this.#i?.continue()}addObserver(e){this.observers.includes(e)||(this.observers.push(e),this.clearGcTimeout(),this.#v.notify({type:"observerAdded",query:this,observer:e}))}removeObserver(e){this.observers.includes(e)&&(this.observers=this.observers.filter((t=>t!==e)),this.observers.length||(this.#i&&(this.#O?this.#i.cancel({revert:!0}):this.#i.cancelRetry()),this.scheduleGc()),this.#v.notify({type:"observerRemoved",query:this,observer:e}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||this.#o({type:"invalidate"})}fetch(e,t){if("idle"!==this.state.fetchStatus)if(void 0!==this.state.data&&t?.cancelRefetch)this.cancel({silent:!0});else if(this.#i)return this.#i.continueRetry(),this.#i.promise;if(e&&this.setOptions(e),!this.options.queryFn){const e=this.observers.find((e=>e.options.queryFn));e&&this.setOptions(e.options)}Array.isArray(this.options.queryKey)||console.error("As of v4, queryKey needs to be an Array. If you are using a string like 'repoData', please change it to an Array, e.g. ['repoData']");const r=new AbortController,s=e=>{Object.defineProperty(e,"signal",{enumerable:!0,get:()=>(this.#O=!0,r.signal)})},o={fetchOptions:t,options:this.options,queryKey:this.queryKey,state:this.state,fetchFn:()=>{const e=(0,n.ensureQueryFn)(this.options,t),r={queryKey:this.queryKey,meta:this.meta};return s(r),this.#O=!1,this.options.persister?this.options.persister(e,r,this):e(r)}};s(o),this.options.behavior?.onFetch(o,this),this.#b=this.state,"idle"!==this.state.fetchStatus&&this.state.fetchMeta===o.fetchOptions?.meta||this.#o({type:"fetch",meta:o.fetchOptions?.meta});const a=e=>{(0,i.isCancelledError)(e)&&e.silent||this.#o({type:"error",error:e}),(0,i.isCancelledError)(e)||(this.#v.config.onError?.(e,this),this.#v.config.onSettled?.(this.state.data,e,this)),this.scheduleGc()};return this.#i=(0,i.createRetryer)({initialPromise:t?.initialPromise,fn:o.fetchFn,abort:r.abort.bind(r),onSuccess:e=>{if(void 0===e)return console.error(`Query data cannot be undefined. Please make sure to return a value other than undefined from your query function. Affected query key: ${this.queryHash}`),void a(new Error(`${this.queryHash} data is undefined`));try{this.setData(e)}catch(e){return void a(e)}this.#v.config.onSuccess?.(e,this),this.#v.config.onSettled?.(e,this.state.error,this),this.scheduleGc()},onError:a,onFail:(e,t)=>{this.#o({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#o({type:"pause"})},onContinue:()=>{this.#o({type:"continue"})},retry:o.options.retry,retryDelay:o.options.retryDelay,networkMode:o.options.networkMode,canRun:()=>!0}),this.#i.start()}#o(e){this.state=(t=>{switch(e.type){case"failed":return{...t,fetchFailureCount:e.failureCount,fetchFailureReason:e.error};case"pause":return{...t,fetchStatus:"paused"};case"continue":return{...t,fetchStatus:"fetching"};case"fetch":return{...t,...u(t.data,this.options),fetchMeta:e.meta??null};case"success":return{...t,data:e.data,dataUpdateCount:t.dataUpdateCount+1,dataUpdatedAt:e.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!e.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const r=e.error;return(0,i.isCancelledError)(r)&&r.revert&&this.#b?{...this.#b,fetchStatus:"idle"}:{...t,error:r,errorUpdateCount:t.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:t.fetchFailureCount+1,fetchFailureReason:r,fetchStatus:"idle",status:"error"};case"invalidate":return{...t,isInvalidated:!0};case"setState":return{...t,...e.state}}})(this.state),s.notifyManager.batch((()=>{this.observers.forEach((e=>{e.onQueryUpdate()})),this.#v.notify({query:this,type:"updated",action:e})}))}};function u(e,t){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:(0,i.canFetch)(t.networkMode)?"fetching":"paused",...void 0===e&&{error:null,status:"pending"}}}},"./node_modules/@tanstack/query-core/build/modern/queryCache.js":function(e,t,r){r.r(t),r.d(t,{QueryCache:function(){return a}});var n=r("./node_modules/@tanstack/query-core/build/modern/utils.js"),s=r("./node_modules/@tanstack/query-core/build/modern/query.js"),i=r("./node_modules/@tanstack/query-core/build/modern/notifyManager.js"),o=r("./node_modules/@tanstack/query-core/build/modern/subscribable.js"),a=class extends o.Subscribable{constructor(e={}){super(),this.config=e,this.#q=new Map}#q;build(e,t,r){const i=t.queryKey,o=t.queryHash??(0,n.hashQueryKeyByOptions)(i,t);let a=this.get(o);return a||(a=new s.Query({cache:this,queryKey:i,queryHash:o,options:e.defaultQueryOptions(t),state:r,defaultOptions:e.getQueryDefaults(i)}),this.add(a)),a}add(e){this.#q.has(e.queryHash)||(this.#q.set(e.queryHash,e),this.notify({type:"added",query:e}))}remove(e){const t=this.#q.get(e.queryHash);t&&(e.destroy(),t===e&&this.#q.delete(e.queryHash),this.notify({type:"removed",query:e}))}clear(){i.notifyManager.batch((()=>{this.getAll().forEach((e=>{this.remove(e)}))}))}get(e){return this.#q.get(e)}getAll(){return[...this.#q.values()]}find(e){const t={exact:!0,...e};return this.getAll().find((e=>(0,n.matchQuery)(t,e)))}findAll(e={}){const t=this.getAll();return Object.keys(e).length>0?t.filter((t=>(0,n.matchQuery)(e,t))):t}notify(e){i.notifyManager.batch((()=>{this.listeners.forEach((t=>{t(e)}))}))}onFocus(){i.notifyManager.batch((()=>{this.getAll().forEach((e=>{e.onFocus()}))}))}onOnline(){i.notifyManager.batch((()=>{this.getAll().forEach((e=>{e.onOnline()}))}))}}},"./node_modules/@tanstack/query-core/build/modern/queryClient.js":function(e,t,r){r.r(t),r.d(t,{QueryClient:function(){return l}});var n=r("./node_modules/@tanstack/query-core/build/modern/utils.js"),s=r("./node_modules/@tanstack/query-core/build/modern/queryCache.js"),i=r("./node_modules/@tanstack/query-core/build/modern/mutationCache.js"),o=r("./node_modules/@tanstack/query-core/build/modern/focusManager.js"),a=r("./node_modules/@tanstack/query-core/build/modern/onlineManager.js"),u=r("./node_modules/@tanstack/query-core/build/modern/notifyManager.js"),c=r("./node_modules/@tanstack/query-core/build/modern/infiniteQueryBehavior.js"),l=class{#k;#s;#g;#R;#j;#S;#C;#w;constructor(e={}){this.#k=e.queryCache||new s.QueryCache,this.#s=e.mutationCache||new i.MutationCache,this.#g=e.defaultOptions||{},this.#R=new Map,this.#j=new Map,this.#S=0}mount(){this.#S++,1===this.#S&&(this.#C=o.focusManager.subscribe((async e=>{e&&(await this.resumePausedMutations(),this.#k.onFocus())})),this.#w=a.onlineManager.subscribe((async e=>{e&&(await this.resumePausedMutations(),this.#k.onOnline())})))}unmount(){this.#S--,0===this.#S&&(this.#C?.(),this.#C=void 0,this.#w?.(),this.#w=void 0)}isFetching(e){return this.#k.findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return this.#s.findAll({...e,status:"pending"}).length}getQueryData(e){const t=this.defaultQueryOptions({queryKey:e});return this.#k.get(t.queryHash)?.state.data}ensureQueryData(e){const t=this.defaultQueryOptions(e),r=this.#k.build(this,t),s=r.state.data;return void 0===s?this.fetchQuery(e):(e.revalidateIfStale&&r.isStaleByTime((0,n.resolveStaleTime)(t.staleTime,r))&&this.prefetchQuery(t),Promise.resolve(s))}getQueriesData(e){return this.#k.findAll(e).map((({queryKey:e,state:t})=>[e,t.data]))}setQueryData(e,t,r){const s=this.defaultQueryOptions({queryKey:e}),i=this.#k.get(s.queryHash),o=i?.state.data,a=(0,n.functionalUpdate)(t,o);if(void 0!==a)return this.#k.build(this,s).setData(a,{...r,manual:!0})}setQueriesData(e,t,r){return u.notifyManager.batch((()=>this.#k.findAll(e).map((({queryKey:e})=>[e,this.setQueryData(e,t,r)]))))}getQueryState(e){const t=this.defaultQueryOptions({queryKey:e});return this.#k.get(t.queryHash)?.state}removeQueries(e){const t=this.#k;u.notifyManager.batch((()=>{t.findAll(e).forEach((e=>{t.remove(e)}))}))}resetQueries(e,t){const r=this.#k,n={type:"active",...e};return u.notifyManager.batch((()=>(r.findAll(e).forEach((e=>{e.reset()})),this.refetchQueries(n,t))))}cancelQueries(e,t={}){const r={revert:!0,...t},s=u.notifyManager.batch((()=>this.#k.findAll(e).map((e=>e.cancel(r)))));return Promise.all(s).then(n.noop).catch(n.noop)}invalidateQueries(e,t={}){return u.notifyManager.batch((()=>{if(this.#k.findAll(e).forEach((e=>{e.invalidate()})),"none"===e?.refetchType)return Promise.resolve();const r={...e,type:e?.refetchType??e?.type??"active"};return this.refetchQueries(r,t)}))}refetchQueries(e,t={}){const r={...t,cancelRefetch:t.cancelRefetch??!0},s=u.notifyManager.batch((()=>this.#k.findAll(e).filter((e=>!e.isDisabled())).map((e=>{let t=e.fetch(void 0,r);return r.throwOnError||(t=t.catch(n.noop)),"paused"===e.state.fetchStatus?Promise.resolve():t}))));return Promise.all(s).then(n.noop)}fetchQuery(e){const t=this.defaultQueryOptions(e);void 0===t.retry&&(t.retry=!1);const r=this.#k.build(this,t);return r.isStaleByTime((0,n.resolveStaleTime)(t.staleTime,r))?r.fetch(t):Promise.resolve(r.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(n.noop).catch(n.noop)}fetchInfiniteQuery(e){return e.behavior=(0,c.infiniteQueryBehavior)(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(n.noop).catch(n.noop)}ensureInfiniteQueryData(e){return e.behavior=(0,c.infiniteQueryBehavior)(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return a.onlineManager.isOnline()?this.#s.resumePausedMutations():Promise.resolve()}getQueryCache(){return this.#k}getMutationCache(){return this.#s}getDefaultOptions(){return this.#g}setDefaultOptions(e){this.#g=e}setQueryDefaults(e,t){this.#R.set((0,n.hashKey)(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){const t=[...this.#R.values()],r={};return t.forEach((t=>{(0,n.partialMatchKey)(e,t.queryKey)&&Object.assign(r,t.defaultOptions)})),r}setMutationDefaults(e,t){this.#j.set((0,n.hashKey)(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){const t=[...this.#j.values()];let r={};return t.forEach((t=>{(0,n.partialMatchKey)(e,t.mutationKey)&&(r={...r,...t.defaultOptions})})),r}defaultQueryOptions(e){if(e._defaulted)return e;const t={...this.#g.queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=(0,n.hashQueryKeyByOptions)(t.queryKey,t)),void 0===t.refetchOnReconnect&&(t.refetchOnReconnect="always"!==t.networkMode),void 0===t.throwOnError&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t.queryFn===n.skipToken&&(t.enabled=!1),t}defaultMutationOptions(e){return e?._defaulted?e:{...this.#g.mutations,...e?.mutationKey&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){this.#k.clear(),this.#s.clear()}}},"./node_modules/@tanstack/query-core/build/modern/queryObserver.js":function(e,t,r){r.r(t),r.d(t,{QueryObserver:function(){return c}});var n=r("./node_modules/@tanstack/query-core/build/modern/focusManager.js"),s=r("./node_modules/@tanstack/query-core/build/modern/notifyManager.js"),i=r("./node_modules/@tanstack/query-core/build/modern/query.js"),o=r("./node_modules/@tanstack/query-core/build/modern/subscribable.js"),a=r("./node_modules/@tanstack/query-core/build/modern/thenable.js"),u=r("./node_modules/@tanstack/query-core/build/modern/utils.js"),c=class extends o.Subscribable{constructor(e,t){super(),this.options=t,this.#c=e,this.#_=null,this.#Q=(0,a.pendingThenable)(),this.options.experimental_prefetchInRender||this.#Q.reject(new Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(t)}#c;#P=void 0;#E=void 0;#l=void 0;#M;#T;#Q;#_;#F;#x;#D;#I;#A;#U;#K=new Set;bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){1===this.listeners.size&&(this.#P.addObserver(this),l(this.#P,this.options)?this.#B():this.updateResult(),this.#N())}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return d(this.#P,this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return d(this.#P,this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,this.#L(),this.#$(),this.#P.removeObserver(this)}setOptions(e,t){const r=this.options,n=this.#P;if(this.options=this.#c.defaultQueryOptions(e),void 0!==this.options.enabled&&"boolean"!=typeof this.options.enabled&&"function"!=typeof this.options.enabled&&"boolean"!=typeof(0,u.resolveEnabled)(this.options.enabled,this.#P))throw new Error("Expected enabled to be a boolean or a callback that returns a boolean");this.#H(),this.#P.setOptions(this.options),r._defaulted&&!(0,u.shallowEqualObjects)(this.options,r)&&this.#c.getQueryCache().notify({type:"observerOptionsUpdated",query:this.#P,observer:this});const s=this.hasListeners();s&&h(this.#P,n,this.options,r)&&this.#B(),this.updateResult(t),!s||this.#P===n&&(0,u.resolveEnabled)(this.options.enabled,this.#P)===(0,u.resolveEnabled)(r.enabled,this.#P)&&(0,u.resolveStaleTime)(this.options.staleTime,this.#P)===(0,u.resolveStaleTime)(r.staleTime,this.#P)||this.#W();const i=this.#G();!s||this.#P===n&&(0,u.resolveEnabled)(this.options.enabled,this.#P)===(0,u.resolveEnabled)(r.enabled,this.#P)&&i===this.#U||this.#z(i)}getOptimisticResult(e){const t=this.#c.getQueryCache().build(this.#c,e),r=this.createResult(t,e);return n=this,s=r,!(0,u.shallowEqualObjects)(n.getCurrentResult(),s)&&(this.#l=r,this.#T=this.options,this.#M=this.#P.state),r;var n,s}getCurrentResult(){return this.#l}trackResult(e,t){const r={};return Object.keys(e).forEach((n=>{Object.defineProperty(r,n,{configurable:!1,enumerable:!0,get:()=>(this.trackProp(n),t?.(n),e[n])})})),r}trackProp(e){this.#K.add(e)}getCurrentQuery(){return this.#P}refetch({...e}={}){return this.fetch({...e})}fetchOptimistic(e){const t=this.#c.defaultQueryOptions(e),r=this.#c.getQueryCache().build(this.#c,t);return r.fetch().then((()=>this.createResult(r,t)))}fetch(e){return this.#B({...e,cancelRefetch:e.cancelRefetch??!0}).then((()=>(this.updateResult(),this.#l)))}#B(e){this.#H();let t=this.#P.fetch(this.options,e);return e?.throwOnError||(t=t.catch(u.noop)),t}#W(){this.#L();const e=(0,u.resolveStaleTime)(this.options.staleTime,this.#P);if(u.isServer||this.#l.isStale||!(0,u.isValidTimeout)(e))return;const t=(0,u.timeUntilStale)(this.#l.dataUpdatedAt,e)+1;this.#I=setTimeout((()=>{this.#l.isStale||this.updateResult()}),t)}#G(){return("function"==typeof this.options.refetchInterval?this.options.refetchInterval(this.#P):this.options.refetchInterval)??!1}#z(e){this.#$(),this.#U=e,!u.isServer&&!1!==(0,u.resolveEnabled)(this.options.enabled,this.#P)&&(0,u.isValidTimeout)(this.#U)&&0!==this.#U&&(this.#A=setInterval((()=>{(this.options.refetchIntervalInBackground||n.focusManager.isFocused())&&this.#B()}),this.#U))}#N(){this.#W(),this.#z(this.#G())}#L(){this.#I&&(clearTimeout(this.#I),this.#I=void 0)}#$(){this.#A&&(clearInterval(this.#A),this.#A=void 0)}createResult(e,t){const r=this.#P,n=this.options,s=this.#l,o=this.#M,c=this.#T,d=e!==r?e.state:this.#E,{state:y}=e;let p,m={...y},b=!1;if(t._optimisticResults){const s=this.hasListeners(),o=!s&&l(e,t),a=s&&h(e,r,t,n);(o||a)&&(m={...m,...(0,i.fetchState)(y.data,e.options)}),"isRestoring"===t._optimisticResults&&(m.fetchStatus="idle")}let{error:v,errorUpdatedAt:g,status:O}=m;if(t.select&&void 0!==m.data)if(s&&m.data===o?.data&&t.select===this.#F)p=this.#x;else try{this.#F=t.select,p=t.select(m.data),p=(0,u.replaceData)(s?.data,p,t),this.#x=p,this.#_=null}catch(e){this.#_=e}else p=m.data;if(void 0!==t.placeholderData&&void 0===p&&"pending"===O){let e;if(s?.isPlaceholderData&&t.placeholderData===c?.placeholderData)e=s.data;else if(e="function"==typeof t.placeholderData?t.placeholderData(this.#D?.state.data,this.#D):t.placeholderData,t.select&&void 0!==e)try{e=t.select(e),this.#_=null}catch(e){this.#_=e}void 0!==e&&(O="success",p=(0,u.replaceData)(s?.data,e,t),b=!0)}this.#_&&(v=this.#_,p=this.#x,g=Date.now(),O="error");const q="fetching"===m.fetchStatus,k="pending"===O,R="error"===O,j=k&&q,S=void 0!==p,C={status:O,fetchStatus:m.fetchStatus,isPending:k,isSuccess:"success"===O,isError:R,isInitialLoading:j,isLoading:j,data:p,dataUpdatedAt:m.dataUpdatedAt,error:v,errorUpdatedAt:g,failureCount:m.fetchFailureCount,failureReason:m.fetchFailureReason,errorUpdateCount:m.errorUpdateCount,isFetched:m.dataUpdateCount>0||m.errorUpdateCount>0,isFetchedAfterMount:m.dataUpdateCount>d.dataUpdateCount||m.errorUpdateCount>d.errorUpdateCount,isFetching:q,isRefetching:q&&!k,isLoadingError:R&&!S,isPaused:"paused"===m.fetchStatus,isPlaceholderData:b,isRefetchError:R&&S,isStale:f(e,t),refetch:this.refetch,promise:this.#Q};if(this.options.experimental_prefetchInRender){const t=e=>{"error"===C.status?e.reject(C.error):void 0!==C.data&&e.resolve(C.data)},n=()=>{const e=this.#Q=C.promise=(0,a.pendingThenable)();t(e)},s=this.#Q;switch(s.status){case"pending":e.queryHash===r.queryHash&&t(s);break;case"fulfilled":"error"!==C.status&&C.data===s.value||n();break;case"rejected":"error"===C.status&&C.error===s.reason||n()}}return C}updateResult(e){const t=this.#l,r=this.createResult(this.#P,this.options);if(this.#M=this.#P.state,this.#T=this.options,void 0!==this.#M.data&&(this.#D=this.#P),(0,u.shallowEqualObjects)(r,t))return;this.#l=r;const n={};!1!==e?.listeners&&(()=>{if(!t)return!0;const{notifyOnChangeProps:e}=this.options,r="function"==typeof e?e():e;if("all"===r||!r&&!this.#K.size)return!0;const n=new Set(r??this.#K);return this.options.throwOnError&&n.add("error"),Object.keys(this.#l).some((e=>{const r=e;return this.#l[r]!==t[r]&&n.has(r)}))})()&&(n.listeners=!0),this.#y({...n,...e})}#H(){const e=this.#c.getQueryCache().build(this.#c,this.options);if(e===this.#P)return;const t=this.#P;this.#P=e,this.#E=e.state,this.hasListeners()&&(t?.removeObserver(this),e.addObserver(this))}onQueryUpdate(){this.updateResult(),this.hasListeners()&&this.#N()}#y(e){s.notifyManager.batch((()=>{e.listeners&&this.listeners.forEach((e=>{e(this.#l)})),this.#c.getQueryCache().notify({query:this.#P,type:"observerResultsUpdated"})}))}};function l(e,t){return function(e,t){return!1!==(0,u.resolveEnabled)(t.enabled,e)&&void 0===e.state.data&&!("error"===e.state.status&&!1===t.retryOnMount)}(e,t)||void 0!==e.state.data&&d(e,t,t.refetchOnMount)}function d(e,t,r){if(!1!==(0,u.resolveEnabled)(t.enabled,e)){const n="function"==typeof r?r(e):r;return"always"===n||!1!==n&&f(e,t)}return!1}function h(e,t,r,n){return(e!==t||!1===(0,u.resolveEnabled)(n.enabled,e))&&(!r.suspense||"error"!==e.state.status)&&f(e,r)}function f(e,t){return!1!==(0,u.resolveEnabled)(t.enabled,e)&&e.isStaleByTime((0,u.resolveStaleTime)(t.staleTime,e))}},"./node_modules/@tanstack/query-core/build/modern/removable.js":function(e,t,r){r.r(t),r.d(t,{Removable:function(){return s}});var n=r("./node_modules/@tanstack/query-core/build/modern/utils.js"),s=class{#V;destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),(0,n.isValidTimeout)(this.gcTime)&&(this.#V=setTimeout((()=>{this.optionalRemove()}),this.gcTime))}updateGcTime(e){this.gcTime=Math.max(this.gcTime||0,e??(n.isServer?1/0:3e5))}clearGcTimeout(){this.#V&&(clearTimeout(this.#V),this.#V=void 0)}}},"./node_modules/@tanstack/query-core/build/modern/retryer.js":function(e,t,r){r.r(t),r.d(t,{CancelledError:function(){return c},canFetch:function(){return u},createRetryer:function(){return d},isCancelledError:function(){return l}});var n=r("./node_modules/@tanstack/query-core/build/modern/focusManager.js"),s=r("./node_modules/@tanstack/query-core/build/modern/onlineManager.js"),i=r("./node_modules/@tanstack/query-core/build/modern/thenable.js"),o=r("./node_modules/@tanstack/query-core/build/modern/utils.js");function a(e){return Math.min(1e3*2**e,3e4)}function u(e){return"online"!==(e??"online")||s.onlineManager.isOnline()}var c=class extends Error{constructor(e){super("CancelledError"),this.revert=e?.revert,this.silent=e?.silent}};function l(e){return e instanceof c}function d(e){let t,r=!1,l=0,d=!1;const h=(0,i.pendingThenable)(),f=()=>n.focusManager.isFocused()&&("always"===e.networkMode||s.onlineManager.isOnline())&&e.canRun(),y=()=>u(e.networkMode)&&e.canRun(),p=r=>{d||(d=!0,e.onSuccess?.(r),t?.(),h.resolve(r))},m=r=>{d||(d=!0,e.onError?.(r),t?.(),h.reject(r))},b=()=>new Promise((r=>{t=e=>{(d||f())&&r(e)},e.onPause?.()})).then((()=>{t=void 0,d||e.onContinue?.()})),v=()=>{if(d)return;let t;const n=0===l?e.initialPromise:void 0;try{t=n??e.fn()}catch(e){t=Promise.reject(e)}Promise.resolve(t).then(p).catch((t=>{if(d)return;const n=e.retry??(o.isServer?0:3),s=e.retryDelay??a,i="function"==typeof s?s(l,t):s,u=!0===n||"number"==typeof n&&l<n||"function"==typeof n&&n(l,t);!r&&u?(l++,e.onFail?.(l,t),(0,o.sleep)(i).then((()=>f()?void 0:b())).then((()=>{r?m(t):v()}))):m(t)}))};return{promise:h,cancel:t=>{d||(m(new c(t)),e.abort?.())},continue:()=>(t?.(),h),cancelRetry:()=>{r=!0},continueRetry:()=>{r=!1},canStart:y,start:()=>(y()?v():b().then(v),h)}}},"./node_modules/@tanstack/query-core/build/modern/subscribable.js":function(e,t,r){r.r(t),r.d(t,{Subscribable:function(){return n}});var n=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}}},"./node_modules/@tanstack/query-core/build/modern/thenable.js":function(e,t,r){function n(){let e,t;const r=new Promise(((r,n)=>{e=r,t=n}));function n(e){Object.assign(r,e),delete r.resolve,delete r.reject}return r.status="pending",r.catch((()=>{})),r.resolve=t=>{n({status:"fulfilled",value:t}),e(t)},r.reject=e=>{n({status:"rejected",reason:e}),t(e)},r}r.r(t),r.d(t,{pendingThenable:function(){return n}})},"./node_modules/@tanstack/query-core/build/modern/utils.js":function(e,t,r){r.r(t),r.d(t,{addToEnd:function(){return R},addToStart:function(){return j},ensureQueryFn:function(){return C},functionalUpdate:function(){return i},hashKey:function(){return f},hashQueryKeyByOptions:function(){return h},isPlainArray:function(){return b},isPlainObject:function(){return v},isServer:function(){return n},isValidTimeout:function(){return o},keepPreviousData:function(){return k},matchMutation:function(){return d},matchQuery:function(){return l},noop:function(){return s},partialMatchKey:function(){return y},replaceData:function(){return q},replaceEqualDeep:function(){return p},resolveEnabled:function(){return c},resolveStaleTime:function(){return u},shallowEqualObjects:function(){return m},skipToken:function(){return S},sleep:function(){return O},timeUntilStale:function(){return a}});var n="undefined"==typeof window||"Deno"in globalThis;function s(){}function i(e,t){return"function"==typeof e?e(t):e}function o(e){return"number"==typeof e&&e>=0&&e!==1/0}function a(e,t){return Math.max(e+(t||0)-Date.now(),0)}function u(e,t){return"function"==typeof e?e(t):e}function c(e,t){return"function"==typeof e?e(t):e}function l(e,t){const{type:r="all",exact:n,fetchStatus:s,predicate:i,queryKey:o,stale:a}=e;if(o)if(n){if(t.queryHash!==h(o,t.options))return!1}else if(!y(t.queryKey,o))return!1;if("all"!==r){const e=t.isActive();if("active"===r&&!e)return!1;if("inactive"===r&&e)return!1}return!("boolean"==typeof a&&t.isStale()!==a||s&&s!==t.state.fetchStatus||i&&!i(t))}function d(e,t){const{exact:r,status:n,predicate:s,mutationKey:i}=e;if(i){if(!t.options.mutationKey)return!1;if(r){if(f(t.options.mutationKey)!==f(i))return!1}else if(!y(t.options.mutationKey,i))return!1}return!(n&&t.state.status!==n||s&&!s(t))}function h(e,t){return(t?.queryKeyHashFn||f)(e)}function f(e){return JSON.stringify(e,((e,t)=>v(t)?Object.keys(t).sort().reduce(((e,r)=>(e[r]=t[r],e)),{}):t))}function y(e,t){return e===t||typeof e==typeof t&&!(!e||!t||"object"!=typeof e||"object"!=typeof t)&&!Object.keys(t).some((r=>!y(e[r],t[r])))}function p(e,t){if(e===t)return e;const r=b(e)&&b(t);if(r||v(e)&&v(t)){const n=r?e:Object.keys(e),s=n.length,i=r?t:Object.keys(t),o=i.length,a=r?[]:{};let u=0;for(let s=0;s<o;s++){const o=r?s:i[s];(!r&&n.includes(o)||r)&&void 0===e[o]&&void 0===t[o]?(a[o]=void 0,u++):(a[o]=p(e[o],t[o]),a[o]===e[o]&&void 0!==e[o]&&u++)}return s===o&&u===s?e:a}return t}function m(e,t){if(!t||Object.keys(e).length!==Object.keys(t).length)return!1;for(const r in e)if(e[r]!==t[r])return!1;return!0}function b(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function v(e){if(!g(e))return!1;const t=e.constructor;if(void 0===t)return!0;const r=t.prototype;return!!g(r)&&!!r.hasOwnProperty("isPrototypeOf")&&Object.getPrototypeOf(e)===Object.prototype}function g(e){return"[object Object]"===Object.prototype.toString.call(e)}function O(e){return new Promise((t=>{setTimeout(t,e)}))}function q(e,t,r){if("function"==typeof r.structuralSharing)return r.structuralSharing(e,t);if(!1!==r.structuralSharing){try{return p(e,t)}catch(e){console.error(`Structural sharing requires data to be JSON serializable. To fix this, turn off structuralSharing or return JSON-serializable data from your queryFn. [${r.queryHash}]: ${e}`)}return p(e,t)}return t}function k(e){return e}function R(e,t,r=0){const n=[...e,t];return r&&n.length>r?n.slice(1):n}function j(e,t,r=0){const n=[t,...e];return r&&n.length>r?n.slice(0,-1):n}var S=Symbol();function C(e,t){return e.queryFn===S&&console.error(`Attempted to invoke queryFn when set to skipToken. This is likely a configuration error. Query hash: '${e.queryHash}'`),!e.queryFn&&t?.initialPromise?()=>t.initialPromise:e.queryFn&&e.queryFn!==S?e.queryFn:()=>Promise.reject(new Error(`Missing queryFn: '${e.queryHash}'`))}},"./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js":function(e,t,r){r.r(t),r.d(t,{QueryClientContext:function(){return i},QueryClientProvider:function(){return a},useQueryClient:function(){return o}});var n=r("react"),s=r("./node_modules/react/jsx-runtime.js"),i=n.createContext(void 0),o=e=>{const t=n.useContext(i);if(e)return e;if(!t)throw new Error("No QueryClient set, use QueryClientProvider to set one");return t},a=({client:e,children:t})=>(n.useEffect((()=>(e.mount(),()=>{e.unmount()})),[e]),(0,s.jsx)(i.Provider,{value:e,children:t}))},"./node_modules/@tanstack/react-query/build/modern/QueryErrorResetBoundary.js":function(e,t,r){r.r(t),r.d(t,{QueryErrorResetBoundary:function(){return u},useQueryErrorResetBoundary:function(){return a}});var n=r("react"),s=r("./node_modules/react/jsx-runtime.js");function i(){let e=!1;return{clearReset:()=>{e=!1},reset:()=>{e=!0},isReset:()=>e}}var o=n.createContext(i()),a=()=>n.useContext(o),u=({children:e})=>{const[t]=n.useState((()=>i()));return(0,s.jsx)(o.Provider,{value:t,children:"function"==typeof e?e(t):e})}},"./node_modules/@tanstack/react-query/build/modern/errorBoundaryUtils.js":function(e,t,r){r.r(t),r.d(t,{ensurePreventErrorBoundaryRetry:function(){return i},getHasError:function(){return a},useClearResetErrorBoundary:function(){return o}});var n=r("react"),s=r("./node_modules/@tanstack/react-query/build/modern/utils.js"),i=(e,t)=>{(e.suspense||e.throwOnError||e.experimental_prefetchInRender)&&(t.isReset()||(e.retryOnMount=!1))},o=e=>{n.useEffect((()=>{e.clearReset()}),[e])},a=({result:e,errorResetBoundary:t,throwOnError:r,query:n})=>e.isError&&!t.isReset()&&!e.isFetching&&n&&(0,s.shouldThrowError)(r,[e.error,n])},"./node_modules/@tanstack/react-query/build/modern/isRestoring.js":function(e,t,r){r.r(t),r.d(t,{IsRestoringProvider:function(){return o},useIsRestoring:function(){return i}});var n=r("react"),s=n.createContext(!1),i=()=>n.useContext(s),o=s.Provider},"./node_modules/@tanstack/react-query/build/modern/suspense.js":function(e,t,r){r.r(t),r.d(t,{defaultThrowOnError:function(){return n},ensureSuspenseTimers:function(){return s},fetchOptimistic:function(){return a},shouldSuspend:function(){return o},willFetch:function(){return i}});var n=(e,t)=>void 0===t.state.data,s=e=>{e.suspense&&(void 0===e.staleTime&&(e.staleTime=1e3),"number"==typeof e.gcTime&&(e.gcTime=Math.max(e.gcTime,1e3)))},i=(e,t)=>e.isLoading&&e.isFetching&&!t,o=(e,t)=>e?.suspense&&t.isPending,a=(e,t,r)=>t.fetchOptimistic(e).catch((()=>{r.clearReset()}))},"./node_modules/@tanstack/react-query/build/modern/useBaseQuery.js":function(e,t,r){r.r(t),r.d(t,{useBaseQuery:function(){return h}});var n=r("react"),s=r("./node_modules/@tanstack/query-core/build/modern/notifyManager.js"),i=r("./node_modules/@tanstack/query-core/build/modern/utils.js"),o=r("./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js"),a=r("./node_modules/@tanstack/react-query/build/modern/QueryErrorResetBoundary.js"),u=r("./node_modules/@tanstack/react-query/build/modern/errorBoundaryUtils.js"),c=r("./node_modules/@tanstack/react-query/build/modern/isRestoring.js"),l=r("./node_modules/@tanstack/react-query/build/modern/suspense.js"),d=r("./node_modules/@tanstack/react-query/build/modern/utils.js");function h(e,t,r){if("object"!=typeof e||Array.isArray(e))throw new Error('Bad argument type. Starting with v5, only the "Object" form is allowed when calling query related functions. Please use the error stack to find the culprit call. More info here: https://tanstack.com/query/latest/docs/react/guides/migrating-to-v5#supports-a-single-signature-one-object');const h=(0,o.useQueryClient)(r),f=(0,c.useIsRestoring)(),y=(0,a.useQueryErrorResetBoundary)(),p=h.defaultQueryOptions(e);h.getDefaultOptions().queries?._experimental_beforeQuery?.(p),p._optimisticResults=f?"isRestoring":"optimistic",(0,l.ensureSuspenseTimers)(p),(0,u.ensurePreventErrorBoundaryRetry)(p,y),(0,u.useClearResetErrorBoundary)(y);const m=!h.getQueryCache().get(p.queryHash),[b]=n.useState((()=>new t(h,p))),v=b.getOptimisticResult(p);if(n.useSyncExternalStore(n.useCallback((e=>{const t=f?d.noop:b.subscribe(s.notifyManager.batchCalls(e));return b.updateResult(),t}),[b,f]),(()=>b.getCurrentResult()),(()=>b.getCurrentResult())),n.useEffect((()=>{b.setOptions(p,{listeners:!1})}),[p,b]),(0,l.shouldSuspend)(p,v))throw(0,l.fetchOptimistic)(p,b,y);if((0,u.getHasError)({result:v,errorResetBoundary:y,throwOnError:p.throwOnError,query:h.getQueryCache().get(p.queryHash)}))throw v.error;if(h.getDefaultOptions().queries?._experimental_afterQuery?.(p,v),p.experimental_prefetchInRender&&!i.isServer&&(0,l.willFetch)(v,f)){const e=m?(0,l.fetchOptimistic)(p,b,y):h.getQueryCache().get(p.queryHash)?.promise;e?.catch(d.noop).finally((()=>{b.updateResult()}))}return p.notifyOnChangeProps?v:b.trackResult(v)}},"./node_modules/@tanstack/react-query/build/modern/useInfiniteQuery.js":function(e,t,r){r.r(t),r.d(t,{useInfiniteQuery:function(){return i}});var n=r("./node_modules/@tanstack/query-core/build/modern/infiniteQueryObserver.js"),s=r("./node_modules/@tanstack/react-query/build/modern/useBaseQuery.js");function i(e,t){return(0,s.useBaseQuery)(e,n.InfiniteQueryObserver,t)}},"./node_modules/@tanstack/react-query/build/modern/useMutation.js":function(e,t,r){r.r(t),r.d(t,{useMutation:function(){return u}});var n=r("react"),s=r("./node_modules/@tanstack/query-core/build/modern/mutationObserver.js"),i=r("./node_modules/@tanstack/query-core/build/modern/notifyManager.js"),o=r("./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js"),a=r("./node_modules/@tanstack/react-query/build/modern/utils.js");function u(e,t){const r=(0,o.useQueryClient)(t),[u]=n.useState((()=>new s.MutationObserver(r,e)));n.useEffect((()=>{u.setOptions(e)}),[u,e]);const c=n.useSyncExternalStore(n.useCallback((e=>u.subscribe(i.notifyManager.batchCalls(e))),[u]),(()=>u.getCurrentResult()),(()=>u.getCurrentResult())),l=n.useCallback(((e,t)=>{u.mutate(e,t).catch(a.noop)}),[u]);if(c.error&&(0,a.shouldThrowError)(u.options.throwOnError,[c.error]))throw c.error;return{...c,mutate:l,mutateAsync:c.mutate}}},"./node_modules/@tanstack/react-query/build/modern/useQuery.js":function(e,t,r){r.r(t),r.d(t,{useQuery:function(){return i}});var n=r("./node_modules/@tanstack/query-core/build/modern/queryObserver.js"),s=r("./node_modules/@tanstack/react-query/build/modern/useBaseQuery.js");function i(e,t){return(0,s.useBaseQuery)(e,n.QueryObserver,t)}},"./node_modules/@tanstack/react-query/build/modern/utils.js":function(e,t,r){function n(e,t){return"function"==typeof e?e(...t):!!e}function s(){}r.r(t),r.d(t,{noop:function(){return s},shouldThrowError:function(){return n}})}},t={};function r(n){var s=t[n];if(void 0!==s)return s.exports;var i=t[n]={exports:{}};return e[n](i,i.exports,r),i.exports}r.d=function(e,t){for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var n={};!function(){r.r(n),r.d(n,{QueryClient:function(){return e.QueryClient},QueryClientProvider:function(){return t.QueryClientProvider},createQueryClient:function(){return a},useInfiniteQuery:function(){return s.useInfiniteQuery},useMutation:function(){return i.useMutation},useQuery:function(){return o.useQuery},useQueryClient:function(){return t.useQueryClient}});var e=r("./node_modules/@tanstack/query-core/build/modern/queryClient.js"),t=r("./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js"),s=r("./node_modules/@tanstack/react-query/build/modern/useInfiniteQuery.js"),i=r("./node_modules/@tanstack/react-query/build/modern/useMutation.js"),o=r("./node_modules/@tanstack/react-query/build/modern/useQuery.js");function a(){return new e.QueryClient({defaultOptions:{queries:{refetchOnWindowFocus:!1,refetchOnReconnect:!1}}})}}(),(window.elementorV2=window.elementorV2||{}).query=n}(),window.elementorV2.query?.init?.();