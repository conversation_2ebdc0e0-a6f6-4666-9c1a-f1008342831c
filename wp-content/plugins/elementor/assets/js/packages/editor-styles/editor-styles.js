/*! For license information please see editor-styles.js.LICENSE.txt */
!function(){"use strict";var e={d:function(t,n){for(var r in n)e.o(n,r)&&!e.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:n[r]})},o:function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r:function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};function n(e="",t=[]){let n;do{n=e+Math.random().toString(16).slice(2,9)}while(t.includes(n));return n}e.r(t),e.d(t,{generateId:function(){return n},getStylesSchema:function(){return r},getVariantByMeta:function(){return o}});var r=()=>{const e=(()=>{const e=window;return e.elementor?.config??{}})();return e?.atomic?.styles_schema??{}};function o(e,t){return e.variants.find((e=>e.meta.breakpoint===t.breakpoint&&e.meta.state===t.state))}(window.elementorV2=window.elementorV2||{}).editorStyles=t}(),window.elementorV2.editorStyles?.init?.();