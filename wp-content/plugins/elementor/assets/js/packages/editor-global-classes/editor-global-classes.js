/*! For license information please see editor-global-classes.js.LICENSE.txt */
!function(){"use strict";var e={react:function(e){e.exports=window.React},"@elementor/editor":function(e){e.exports=window.elementorV2.editor},"@elementor/editor-current-user":function(e){e.exports=window.elementorV2.editorCurrentUser},"@elementor/editor-documents":function(e){e.exports=window.elementorV2.editorDocuments},"@elementor/editor-editing-panel":function(e){e.exports=window.elementorV2.editorEditingPanel},"@elementor/editor-panels":function(e){e.exports=window.elementorV2.editorPanels},"@elementor/editor-props":function(e){e.exports=window.elementorV2.editorProps},"@elementor/editor-styles":function(e){e.exports=window.elementorV2.editorStyles},"@elementor/editor-styles-repository":function(e){e.exports=window.elementorV2.editorStylesRepository},"@elementor/editor-ui":function(e){e.exports=window.elementorV2.editorUi},"@elementor/editor-v1-adapters":function(e){e.exports=window.elementorV2.editorV1Adapters},"@elementor/http-client":function(e){e.exports=window.elementorV2.httpClient},"@elementor/icons":function(e){e.exports=window.elementorV2.icons},"@elementor/query":function(e){e.exports=window.elementorV2.query},"@elementor/store":function(e){e.exports=window.elementorV2.store},"@elementor/ui":function(e){e.exports=window.elementorV2.ui},"@elementor/utils":function(e){e.exports=window.elementorV2.utils},"@wordpress/i18n":function(e){e.exports=window.wp.i18n}},t={};function n(r){var o=t[r];if(void 0!==o)return o.exports;var a=t[r]={exports:{}};return e[r](a,a.exports,n),a.exports}n.d=function(e,t){for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var r={};!function(){n.r(r),n.d(r,{init:function(){return Ee}});var e=n("@elementor/editor"),t=n("@elementor/editor-editing-panel"),o=n("@elementor/editor-panels"),a=n("@elementor/editor-styles-repository"),l=n("@elementor/editor-v1-adapters"),i=n("@elementor/store"),s=n("react"),c=n("@elementor/editor-documents"),d=n("@elementor/ui"),m=n("@wordpress/i18n"),p=n("@elementor/editor-styles"),u=n("@elementor/utils"),g=n("@elementor/editor-props"),y=n("@elementor/editor-ui"),h=n("@elementor/icons"),_=n("@elementor/query"),b=n("@elementor/http-client"),f=n("@elementor/editor-current-user"),v="elementor_global_classes_update_class",E=(0,u.createError)({code:"global_class_not_found",message:"Global class not found."}),w=(0,u.createError)({code:"global_class_label_already_exists",message:"Class with this name already exists."}),x="globalClasses",S=(0,i.__createSlice)({name:x,initialState:{data:{items:{},order:[]},initialData:{frontend:{items:{},order:[]},preview:{items:{},order:[]}},isDirty:!1},reducers:{load(e,{payload:{frontend:t,preview:n}}){e.initialData.frontend=t,e.initialData.preview=n,e.data=n,e.isDirty=!1},add(e,{payload:t}){e.data.items[t.id]=t,e.data.order.unshift(t.id),e.isDirty=!0},delete(e,{payload:t}){e.data.items=Object.fromEntries(Object.entries(e.data.items).filter((([e])=>e!==t))),e.data.order=e.data.order.filter((e=>e!==t)),e.isDirty=!0},setOrder(e,{payload:t}){e.data.order=t,e.isDirty=!0},update(e,{payload:t}){const n={...e.data.items[t.style.id],...t.style};e.data.items[t.style.id]=n,e.isDirty=!0},updateProps(e,{payload:t}){const n=e.data.items[t.id];if(!n)throw new E({context:{styleId:t.id}});const r=(0,p.getVariantByMeta)(n,t.meta);r?r.props=(0,g.mergeProps)(r.props,t.props):n.variants.push({meta:t.meta,props:t.props}),e.isDirty=!0},reset(e,{payload:{context:t}}){"frontend"===t&&(e.initialData.frontend=e.data,e.isDirty=!1),e.initialData.preview=e.data}}}),C=e=>e[x].data,D=e=>e[x].initialData.frontend,P=e=>e[x].initialData.preview,T=(0,i.__createSelector)(C,(({order:e})=>e)),A=(0,i.__createSelector)(C,(({items:e})=>e)),I=e=>e[x].isDirty,k=(0,i.__createSelector)(A,T,((e,t)=>t.map((t=>e[t])))),M=(e,t)=>e[x].data.items[t]??null,V=(0,a.createStylesProvider)({key:"global-classes",priority:30,limit:50,labels:{singular:(0,m.__)("class","elementor"),plural:(0,m.__)("classes","elementor")},subscribe:e=>(0,i.__subscribeWithSelector)((e=>e.globalClasses),e),capabilities:(()=>{if((0,l.isExperimentActive)("global_classes_should_enforce_capabilities"))return{update:v,create:v,delete:v,updateProps:v}})(),actions:{all:()=>k((0,i.__getState)()),get:e=>M((0,i.__getState)(),e),resolveCssName:e=>(0,l.isExperimentActive)("e_v_3_30")?M((0,i.__getState)(),e)?.label??e:e,create:e=>{const t=A((0,i.__getState)());if(Object.values(t).map((e=>e.label)).includes(e))throw new w({context:{label:e}});const n=Object.keys(t),r=(0,p.generateId)("g-",n);return(0,i.__dispatch)(S.actions.add({id:r,type:"class",label:e,variants:[]})),r},update:e=>{(0,i.__dispatch)(S.actions.update({style:e}))},delete:e=>{(0,i.__dispatch)(S.actions.delete(e))},updateProps:e=>{(0,i.__dispatch)(S.actions.updateProps({id:e.id,meta:e.meta,props:e.props}))}}}),O=()=>(0,i.__useSelector)(I),B="/global-classes",j={all:(e="preview")=>(0,b.httpService)().get("elementor/v1"+B,{params:{context:e}}),publish:e=>(0,b.httpService)().put("elementor/v1"+B,e,{params:{context:"frontend"}}),saveDraft:e=>(0,b.httpService)().put("elementor/v1"+B,e,{params:{context:"preview"}})};async function z({context:e}){const t=C((0,i.__getState)());"preview"===e?await j.saveDraft({items:t.items,order:t.order,changes:R(t,P((0,i.__getState)()))}):await j.publish({items:t.items,order:t.order,changes:R(t,D((0,i.__getState)()))}),(0,i.__dispatch)(S.actions.reset({context:e}))}function R(e,t){const n=Object.keys(e.items),r=Object.keys(t.items);return{added:n.filter((e=>!r.includes(e))),deleted:r.filter((e=>!n.includes(e))),modified:n.filter((n=>n in t.items&&L(e.items[n])!==L(t.items[n])))}}function L(e){return JSON.stringify(e,((e,t)=>function(e){return!!e&&"object"==typeof e&&!Array.isArray(e)}(t)?Object.keys(t).sort().reduce(((e,n)=>(e[n]=t[n],e)),{}):t))}var W=()=>{const[e,t]=(0,f.useSuppressedMessage)("global-class-manager"),[n,r]=(0,s.useState)(!e);return s.createElement(y.IntroductionModal,{open:n,title:(0,m.__)("Class Manager","elementor"),handleClose:e=>{e||t(),r(!1)}},s.createElement(d.Image,{sx:{width:"100%",aspectRatio:"16 / 9"},src:"https://assets.elementor.com/packages/v1/images/class-manager-intro.svg",alt:""}),s.createElement(F,null))},F=()=>s.createElement(d.Box,{p:3},s.createElement(d.Typography,{variant:"body2"},(0,m.__)("The Class Manager lets you see all the classes you've created, plus adjust their priority, rename them, and delete unused classes to keep your CSS structured.","elementor")),s.createElement("br",null),s.createElement(d.Typography,{variant:"body2"},(0,m.__)("Remember, when editing an item within a specific class, any changes you make will apply across all elements in that class.","elementor"))),$=!1,N=({sx:e,...t})=>s.createElement(h.ColorSwatchIcon,{sx:{transform:"rotate(90deg)",...e},...t}),U=(0,s.createContext)(null),G=({children:e})=>{const[t,n]=(0,s.useState)(null);return s.createElement(U.Provider,{value:{openDialog:e=>{n(e)},closeDialog:()=>{n(null)},dialogProps:t}},e,!!t&&s.createElement(Y,{...t}))},H="delete-class-dialog",Y=({label:e,id:t})=>{const{closeDialog:n}=q();return s.createElement(d.Dialog,{open:!0,onClose:n,"aria-labelledby":H,maxWidth:"xs"},s.createElement(d.DialogTitle,{id:H,display:"flex",alignItems:"center",gap:1,sx:{lineHeight:1}},s.createElement(h.AlertOctagonFilledIcon,{color:"error"}),(0,m.__)("Delete this class?","elementor")),s.createElement(d.DialogContent,null,s.createElement(d.DialogContentText,{variant:"body2",color:"textPrimary"},(0,m.__)("Deleting","elementor"),s.createElement(d.Typography,{variant:"subtitle2",component:"span"}," ",e," "),(0,m.__)("will permanently remove it from your project and may affect the design across all elements using it. This action cannot be undone.","elementor"))),s.createElement(d.DialogActions,null,s.createElement(d.Button,{color:"secondary",onClick:n},(0,m.__)("Not now","elementor")),s.createElement(d.Button,{variant:"contained",color:"error",onClick:()=>{(e=>{(0,i.__dispatch)(S.actions.delete(e)),$=!0})(t),n()}},(0,m.__)("Delete","elementor"))))},q=()=>{const e=(0,s.useContext)(U);if(!e)throw new Error("useDeleteConfirmation must be used within a DeleteConfirmationProvider");return e},J=e=>s.createElement(d.UnstableSortableProvider,{restrictAxis:!0,variant:"static",dragPlaceholderStyle:{opacity:"1"},...e}),K=e=>s.createElement(Q,{...e,role:"button",className:"class-item-sortable-trigger"},s.createElement(h.GripVerticalIcon,{fontSize:"tiny"})),X=({children:e,id:t,...n})=>s.createElement(d.UnstableSortableItem,{...n,id:t,render:({itemProps:t,isDragged:n,triggerProps:r,itemStyle:o,triggerStyle:a,dropIndicationStyle:l,showDropIndication:i,isDragOverlay:c,isDragPlaceholder:m})=>s.createElement(d.Box,{...t,style:o,component:"li",role:"listitem",sx:{backgroundColor:c?"background.paper":void 0}},e({itemProps:t,isDragged:n,triggerProps:r,itemStyle:o,triggerStyle:a,isDragPlaceholder:m}),i&&s.createElement(Z,{style:l}))}),Q=(0,d.styled)("div")((({theme:e})=>({position:"absolute",left:0,top:"50%",transform:`translate( -${e.spacing(1.5)}, -50% )`,color:e.palette.action.active}))),Z=(0,d.styled)(d.Box)`
	width: 100%;
	height: 1px;
	background-color: ${({theme:e})=>e.palette.text.primary};
`,ee=({disabled:e})=>{const t=(0,i.__useSelector)(k),n=(0,i.__useDispatch)(),[r,o]=te();return t?.length?s.createElement(G,null,s.createElement(d.List,{sx:{display:"flex",flexDirection:"column",gap:.5}},s.createElement(J,{value:r,onChange:o},t?.map((({id:t,label:r})=>{const o=e=>{n(S.actions.update({style:{id:t,label:e}}))};return s.createElement(X,{key:t,id:t},(({isDragged:n,isDragPlaceholder:a,triggerProps:l,triggerStyle:i})=>s.createElement(ne,{id:t,label:r,renameClass:o,selected:n,disabled:e||a,sortableTriggerProps:{...l,style:i}})))}))))):s.createElement(oe,null)},te=()=>{const e=(0,i.__useDispatch)();return[(0,i.__useSelector)(T),t=>{e(S.actions.setOrder(t))}]},ne=({id:e,label:t,renameClass:n,selected:r,disabled:o,sortableTriggerProps:a})=>{const l=(0,s.useRef)(null),{ref:i,openEditMode:c,isEditing:p,error:u,getProps:g}=(0,y.useEditable)({value:t,onSubmit:n,validation:se}),{openDialog:_}=q(),b=(0,d.usePopupState)({variant:"popover",disableAutoFocus:!0}),f=(r||b.isOpen)&&!o;return s.createElement(s.Fragment,null,s.createElement(d.Stack,{p:0},s.createElement(y.WarningInfotip,{open:Boolean(u),text:u??"",placement:"bottom",width:l.current?.getBoundingClientRect().width,offset:[0,-15]},s.createElement(re,{ref:l,dense:!0,disableGutters:!0,showActions:f||p,shape:"rounded",onDoubleClick:c,selected:f,disabled:o,focusVisibleClassName:"visible-class-item"},s.createElement(K,{...a}),s.createElement(le,{isActive:p,isError:!!u},p?s.createElement(y.EditableField,{ref:i,as:d.Typography,variant:"caption",...g()}):s.createElement(y.EllipsisWithTooltip,{title:t,as:d.Typography,variant:"caption"})),s.createElement(d.Tooltip,{placement:"top",className:"class-item-more-actions",title:(0,m.__)("More actions","elementor")},s.createElement(d.IconButton,{size:"tiny",...(0,d.bindTrigger)(b),"aria-label":"More actions"},s.createElement(h.DotsVerticalIcon,{fontSize:"tiny"})))))),s.createElement(d.Menu,{...(0,d.bindMenu)(b),anchorOrigin:{vertical:"bottom",horizontal:"right"},transformOrigin:{vertical:"top",horizontal:"right"}},s.createElement(y.MenuListItem,{sx:{minWidth:"160px"},onClick:()=>{b.close(),c()}},s.createElement(d.Typography,{variant:"caption",sx:{color:"text.primary"}},(0,m.__)("Rename","elementor"))),s.createElement(y.MenuListItem,{onClick:()=>{b.close(),_({id:e,label:t})}},s.createElement(d.Typography,{variant:"caption",sx:{color:"error.light"}},(0,m.__)("Delete","elementor")))))},re=(0,d.styled)(d.ListItemButton,{shouldForwardProp:e=>!["showActions"].includes(e)})((({showActions:e})=>`\n\tmin-height: 36px;\n\n\t&.visible-class-item {\n\t\tbox-shadow: none !important;\n\t}\n\n\t.class-item-more-actions, .class-item-sortable-trigger {\n\t\tvisibility: ${e?"visible":"hidden"};\n\t}\n\n\t.class-item-sortable-trigger {\n\t\tvisibility: ${e?"visible":"hidden"};\n\t}\n\n\t&:hover&:not(:disabled) {\n\t\t.class-item-more-actions, .class-item-sortable-trigger  {\n\t\t\tvisibility: visible;\n\t\t}\n\t}\n`)),oe=()=>s.createElement(d.Stack,{alignItems:"center",gap:1.5,pt:10,px:.5,maxWidth:"260px",margin:"auto"},s.createElement(N,{fontSize:"large"}),s.createElement(ae,{variant:"subtitle2",component:"h2",color:"text.secondary"},(0,m.__)("There are no global classes yet.","elementor")),s.createElement(d.Typography,{align:"center",variant:"caption",color:"text.secondary"},(0,m.__)("CSS classes created in the editor panel will appear here. Once they are available, you can arrange their hierarchy, rename them, or delete them as needed.","elementor"))),ae=(0,d.styled)(d.Typography)((({theme:e,variant:t})=>({"&.MuiTypography-root":{...e.typography[t]}}))),le=(0,d.styled)(d.Box,{shouldForwardProp:e=>!["isActive","isError"].includes(e)})((({theme:e,isActive:t,isError:n})=>({display:"flex",width:"100%",flexGrow:1,borderRadius:e.spacing(.5),border:ie({isActive:t,isError:n,theme:e}),padding:`0 ${e.spacing(1)}`,marginLeft:t?e.spacing(1):0,minWidth:0}))),ie=({isActive:e,isError:t,theme:n})=>t?`2px solid ${n.palette.error.main}`:e?`2px solid ${n.palette.secondary.main}`:"none",se=e=>{const t=(0,a.validateStyleLabel)(e,"rename");return t.isValid?null:t.errorMessage},ce="save-changes-dialog",de=({children:e,onClose:t})=>s.createElement(d.Dialog,{open:!0,onClose:t,"aria-labelledby":ce,maxWidth:"xs"},e);de.Title=({children:e})=>s.createElement(d.DialogTitle,{id:ce,display:"flex",alignItems:"center",gap:1,sx:{lineHeight:1}},s.createElement(h.AlertTriangleFilledIcon,{color:"secondary"}),e),de.Content=({children:e})=>s.createElement(d.DialogContent,null,e),de.ContentText=e=>s.createElement(d.DialogContentText,{variant:"body2",color:"textPrimary",display:"flex",flexDirection:"column",...e}),de.Actions=({actions:e})=>{const[t,n]=(0,s.useState)(!1),{cancel:r,confirm:o}=e;return s.createElement(d.DialogActions,null,s.createElement(d.Button,{variant:"text",color:"secondary",onClick:r.action},r.label),s.createElement(d.Button,{variant:"contained",color:"secondary",onClick:async()=>{n(!0),await o.action(),n(!1)},loading:t},o.label))};var me=()=>{const[e,t]=(0,s.useState)(!1);return{isOpen:e,open:()=>t(!0),close:()=>t(!1)}},pe="global-classes-manager",{panel:ue,usePanelActions:ge}=(0,o.__createPanel)({id:pe,component:function(){const e=O(),{close:t}=ge(),{open:n,close:r,isOpen:a}=me(),{mutateAsync:l,isPending:i}=be();return _e(),s.createElement(y.ThemeProvider,null,s.createElement(d.ErrorBoundary,{fallback:s.createElement(he,null)},s.createElement(o.Panel,null,s.createElement(o.PanelHeader,null,s.createElement(d.Stack,{p:1,pl:2,width:"100%",direction:"row",alignItems:"center"},s.createElement(o.PanelHeaderTitle,{sx:{display:"flex",alignItems:"center",gap:.5}},s.createElement(N,{fontSize:"inherit"}),(0,m.__)("Class Manager","elementor")),s.createElement(ye,{sx:{marginLeft:"auto"},disabled:i,onClose:()=>{e?n():t()}}))),s.createElement(o.PanelBody,{px:2},s.createElement(ee,{disabled:i})),s.createElement(o.PanelFooter,null,s.createElement(d.Button,{fullWidth:!0,size:"small",color:"global",variant:"contained",onClick:l,disabled:!e,loading:i},(0,m.__)("Save changes","elementor"))))),s.createElement(W,null),a&&s.createElement(de,null,s.createElement(de.Title,null,(0,m.__)("You have unsaved changes","elementor")),s.createElement(de.Content,null,s.createElement(de.ContentText,null,(0,m.__)("You have unsaved changes in the Class Manager.","elementor")),s.createElement(de.ContentText,null,(0,m.__)("To avoid losing your updates, save your changes before leaving.","elementor"))),s.createElement(de.Actions,{actions:{cancel:{label:(0,m.__)("Cancel","elementor"),action:r},confirm:{label:(0,m.__)("Save & Continue","elementor"),action:async()=>{await l(),r(),t()}}}})))},allowedEditModes:["edit",pe],onOpen:()=>{(0,l.changeEditMode)(pe),function(){const e=window;e.$e?.components?.get?.("panel")?.blockUserInteractions?.()}()},onClose:()=>{(0,l.changeEditMode)("edit"),function(){const e=window;e.$e?.components?.get?.("panel")?.unblockUserInteractions?.()}()}}),ye=({onClose:e,...t})=>s.createElement(d.IconButton,{size:"small",color:"secondary",onClick:e,"aria-label":"Close",...t},s.createElement(h.XIcon,{fontSize:"small"})),he=()=>s.createElement(d.Box,{role:"alert",sx:{minHeight:"100%",p:2}},s.createElement(d.Alert,{severity:"error",sx:{mb:2,maxWidth:400,textAlign:"center"}},s.createElement("strong",null,(0,m.__)("Something went wrong","elementor")))),_e=()=>{const e=O();(0,s.useEffect)((()=>{const t=t=>{e&&t.preventDefault()};return window.addEventListener("beforeunload",t),()=>{window.removeEventListener("beforeunload",t)}}),[e])},be=()=>(0,_.useMutation)({mutationFn:()=>z({context:"frontend"}),onSuccess:async()=>{(0,c.setDocumentModifiedStatus)(!1),$&&await(async()=>{await(()=>{const e=(0,c.getCurrentDocument)();return(0,c.getV1DocumentsManager)().invalidateCache(),(0,l.__privateRunCommand)("editor/documents/switch",{id:e?.id,shouldScroll:!1,shouldNavigateToDefaultRoute:!1})})(),$=!1})()}}),fe=()=>{const e=(0,c.__useActiveDocument)(),{open:t}=ge(),{save:n}=(0,c.__useActiveDocumentActions)(),{open:r,close:o,isOpen:l}=me(),{userCan:i}=(0,a.useUserStylesCapability)();return i(V.getKey()).update?s.createElement(s.Fragment,null,s.createElement(d.Tooltip,{title:(0,m.__)("Class Manager","elementor"),placement:"top"},s.createElement(d.IconButton,{size:"tiny",onClick:()=>{e?.isDirty?r():t()},sx:{marginInlineEnd:-.75}},s.createElement(N,{fontSize:"tiny"}))),l&&s.createElement(de,null,s.createElement(de.Title,null,(0,m.__)("You have unsaved changes","elementor")),s.createElement(de.Content,null,s.createElement(de.ContentText,{sx:{mb:2}},(0,m.__)("To open the Class Manager, save your page first. You can't continue without saving.","elementor"))),s.createElement(de.Actions,{actions:{cancel:{label:(0,m.__)("Stay here","elementor"),action:o},confirm:{label:(0,m.__)("Save & Continue","elementor"),action:async()=>{await n(),o(),t()}}}}))):null};function ve(){const e=(0,i.__useDispatch)();return(0,s.useEffect)((()=>{Promise.all([j.all("preview"),j.all("frontend")]).then((([t,n])=>{const{data:r}=t,{data:o}=n;e(S.actions.load({preview:{items:r.data,order:r.meta.order},frontend:{items:o.data,order:o.meta.order}}))}))}),[e]),null}function Ee(){(0,i.__registerSlice)(S),(0,o.__registerPanel)(ue),a.stylesRepository.register(V),(0,e.injectIntoLogic)({id:"global-classes-populate-store",component:ve}),(0,t.injectIntoClassSelectorActions)({id:"global-classes-manager-button",component:fe}),(0,l.__privateListenTo)((0,l.v1ReadyEvent)(),(()=>{!function(){const e=(0,i.__subscribeWithSelector)(I,(()=>{I((0,i.__getState)())&&(0,c.setDocumentModifiedStatus)(!0)}));(0,l.registerDataHook)("after","document/save/save",(e=>z({context:"publish"===e.status?"frontend":"preview"})))}()}))}}(),(window.elementorV2=window.elementorV2||{}).editorGlobalClasses=r}(),window.elementorV2.editorGlobalClasses?.init?.();