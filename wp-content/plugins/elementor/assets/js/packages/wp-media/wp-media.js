/*! For license information please see wp-media.js.LICENSE.txt */
!function(){"use strict";var e={react:function(e){e.exports=window.React},"@elementor/query":function(e){e.exports=window.elementorV2.query},"@elementor/utils":function(e){e.exports=window.elementorV2.utils}},t={};function n(r){var o=t[r];if(void 0!==o)return o.exports;var i=t[r]={exports:{}};return e[r](i,i.exports,n),i.exports}n.d=function(e,t){for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var r={};!function(){n.r(r),n.d(r,{getMediaAttachment:function(){return c},useWpMediaAttachment:function(){return p},useWpMediaFrame:function(){return f}});var e=n("@elementor/query"),t=n("@elementor/utils"),o=n("react"),i=(0,t.createError)({code:"wp_media_not_available",message:"`wp.media` is not available, make sure the `media-models` handle is set in the dependencies array"}),a=(0,t.createError)({code:"wp_plupload_settings_not_available",message:"`_wpPluploadSettings` is not available, make sure a wp media uploader is open"}),u=window,s=()=>{if(!u.wp?.media)throw new i;return u.wp.media};function l(e){const{filesizeInBytes:t,filesizeHumanReadable:n,author:r,authorName:o,...i}=e;return{...i,filesize:{inBytes:t,humanReadable:n},author:{id:parseInt(r),name:o}}}async function c({id:e}){if(!e)return null;const t=s().attachment(e),n=t.toJSON();if("url"in n)return l(n);try{return l(await t.fetch())}catch{return null}}function p(t){return(0,e.useQuery)({queryKey:["wp-attachment",t],queryFn:()=>c({id:t}),enabled:!!t})}var d=window,m=()=>{if(!d._wpPluploadSettings)throw new a;return d._wpPluploadSettings};function f(e){const t=(0,o.useRef)();return(0,o.useEffect)((()=>()=>{w(t.current)}),[]),{open:(n={})=>{w(t.current),t.current=function({onSelect:e,multiple:t,mediaTypes:n,selected:r,title:o,mode:i="browse"}){const a=s()({title:o,multiple:t,library:{type:g(n)}}).on("open",(()=>{!function(e){e.uploader.uploader.param("uploadTypeCaller","elementor-wp-media-upload")}(a),function(e,t="browse"){e.content.mode(t)}(a,i),function(e,t){const n=("number"==typeof t?[t]:t)?.filter((e=>!!e)).map((e=>s().attachment(e)));e.state().get("selection").set(n||[])}(a,r)})).on("close",(()=>w(a))).on("insert select",(()=>function(e,t,n){const r=e.state().get("selection").toJSON().map(l);n(t?r:r[0])}(a,t,e)));return function(e,t){const n=m().defaults.filters.mime_types?.[0]?.extensions;e.on("ready",(()=>{m().defaults.filters.mime_types=[{extensions:b(t)}]})),e.on("close",(()=>{m().defaults.filters.mime_types=n?[{extensions:n}]:[]}))}(a,n),a}({...e,...n}),t.current?.open()}}}function w(e){e?.detach(),e?.remove()}var y=["avif","bmp","gif","ico","jpe","jpeg","jpg","png","webp"];function g(e){const t={image:y.map((e=>`image/${e}`)),svg:["image/svg+xml"]};return e.reduce(((e,n)=>e.concat(t[n])),[])}function b(e){const t={image:y,svg:["svg"]};return e.reduce(((e,n)=>e.concat(t[n])),[]).join(",")}}(),(window.elementorV2=window.elementorV2||{}).wpMedia=r}(),window.elementorV2.wpMedia?.init?.();