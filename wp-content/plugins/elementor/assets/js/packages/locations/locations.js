/*! For license information please see locations.js.LICENSE.txt */
!function(){"use strict";var e={react:function(e){e.exports=window.React}},n={};function t(r){var o=n[r];if(void 0!==o)return o.exports;var i=n[r]={exports:{}};return e[r](i,i.exports,t),i.exports}t.d=function(e,n){for(var r in n)t.o(n,r)&&!t.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:n[r]})},t.o=function(e,n){return Object.prototype.hasOwnProperty.call(e,n)},t.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var r={};!function(){t.r(r),t.d(r,{__flushAllInjections:function(){return u},createLocation:function(){return p},createReplaceableLocation:function(){return f}});var e=t("react"),n=class extends e.Component{state={hasError:!1};static getDerivedStateFromError(){return{hasError:!0}}render(){return this.state.hasError?this.props.fallback:this.props.children}};function o({children:t}){return e.createElement(n,{fallback:null},e.createElement(e.Suspense,{fallback:null},t))}var i=10,c=[];function u(){c.forEach((e=>e()))}function a(e){return()=>[...e.values()].sort(((e,n)=>e.priority-n.priority))}function s(n){return()=>(0,e.useMemo)((()=>n()),[])}function l(n){return t=>e.createElement(o,null,e.createElement(n,{...t}))}function p(){const n=new Map,t=a(n),r=s(t),o=function(n){return t=>{const r=n();return e.createElement(e.Fragment,null,r.map((({id:n,component:r})=>e.createElement(r,{...t,key:n}))))}}(r),u=function(e){return({component:n,id:t,options:r={}})=>{!e.has(t)||r?.overwrite?e.set(t,{id:t,component:l(n),priority:r.priority??i}):console.warn(`An injection with the id "${t}" already exists. Did you mean to use "options.overwrite"?`)}}(n);return c.push((()=>n.clear())),{inject:u,getInjections:t,useInjections:r,Slot:o}}function f(){const n=new Map,t=a(n),r=s(t),o=function(n){return t=>{const r=n(),{component:o}=r.find((({condition:e})=>e?.(t)))??{};return o?e.createElement(o,{...t}):t.children}}(r),u=function(e){return({component:n,id:t,condition:r=()=>!0,options:o={}})=>{e.set(t,{id:t,component:l(n),condition:r,priority:o.priority??i})}}(n);return c.push((()=>n.clear())),{getInjections:t,useInjections:r,inject:u,Slot:o}}}(),(window.elementorV2=window.elementorV2||{}).locations=r}(),window.elementorV2.locations?.init?.();