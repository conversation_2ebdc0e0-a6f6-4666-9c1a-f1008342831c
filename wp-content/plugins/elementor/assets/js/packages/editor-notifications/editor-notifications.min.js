!function(){"use strict";var e={n:function(n){var t=n&&n.__esModule?function(){return n.default}:function(){return n};return e.d(t,{a:t}),t},d:function(n,t){for(var r in t)e.o(t,r)&&!e.o(n,r)&&Object.defineProperty(n,r,{enumerable:!0,get:t[r]})},o:function(e,n){return Object.prototype.hasOwnProperty.call(e,n)},r:function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},n={};e.r(n),e.d(n,{NotifyReact:function(){return Fe},init:function(){return Xe},notify:function(){return Be}});var t=window.elementorV2.editor,r=window.elementorV2.store,i=window.React,o=e.n(i),a=window.ReactDOM;function s(e){var n,t,r="";if("string"==typeof e||"number"==typeof e)r+=e;else if("object"==typeof e)if(Array.isArray(e))for(n=0;n<e.length;n++)e[n]&&(t=s(e[n]))&&(r&&(r+=" "),r+=t);else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}var u=function(){for(var e,n,t=0,r="";t<arguments.length;)(e=arguments[t++])&&(n=s(e))&&(r&&(r+=" "),r+=n);return r};let c={data:""},l=e=>"object"==typeof window?((e?e.querySelector("#_goober"):window._goober)||Object.assign((e||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:e||c,d=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,f=/\/\*[^]*?\*\/|  +/g,p=/\n+/g,m=(e,n)=>{let t="",r="",i="";for(let o in e){let a=e[o];"@"==o[0]?"i"==o[1]?t=o+" "+a+";":r+="f"==o[1]?m(a,o):o+"{"+m(a,"k"==o[1]?"":n)+"}":"object"==typeof a?r+=m(a,n?n.replace(/([^,])+/g,(e=>o.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,(n=>/&/.test(n)?n.replace(/&/g,e):e?e+" "+n:n)))):o):null!=a&&(o=/^--/.test(o)?o:o.replace(/[A-Z]/g,"-$&").toLowerCase(),i+=m.p?m.p(o,a):o+":"+a+";")}return t+(n&&i?n+"{"+i+"}":i)+r},h={},g=e=>{if("object"==typeof e){let n="";for(let t in e)n+=t+g(e[t]);return n}return e},v=(e,n,t,r,i)=>{let o=g(e),a=h[o]||(h[o]=(e=>{let n=0,t=11;for(;n<e.length;)t=101*t+e.charCodeAt(n++)>>>0;return"go"+t})(o));if(!h[a]){let n=o!==e?e:(e=>{let n,t,r=[{}];for(;n=d.exec(e.replace(f,""));)n[4]?r.shift():n[3]?(t=n[3].replace(p," ").trim(),r.unshift(r[0][t]=r[0][t]||{})):r[0][n[1]]=n[2].replace(p," ").trim();return r[0]})(e);h[a]=m(i?{["@keyframes "+a]:n}:n,t?"":"."+a)}let s=t&&h.g?h.g:null;return t&&(h.g=h[a]),((e,n,t,r)=>{r?n.data=n.data.replace(r,e):-1===n.data.indexOf(e)&&(n.data=t?e+n.data:n.data+e)})(h[a],n,r,s),a};function x(e){let n=this||{},t=e.call?e(n.p):e;return v(t.unshift?t.raw?((e,n,t)=>e.reduce(((e,r,i)=>{let o=n[i];if(o&&o.call){let e=o(t),n=e&&e.props&&e.props.className||/^go/.test(e)&&e;o=n?"."+n:e&&"object"==typeof e?e.props?"":m(e,""):!1===e?"":e}return e+r+(null==o?"":o)}),""))(t,[].slice.call(arguments,1),n.p):t.reduce(((e,t)=>Object.assign(e,t&&t.call?t(n.p):t)),{}):t,l(n.target),n.g,n.o,n.k)}function E(e,n){for(var t=0;t<n.length;t++){var r=n[t];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function b(e,n,t){return n&&E(e.prototype,n),t&&E(e,t),e}function y(){return y=Object.assign||function(e){for(var n=1;n<arguments.length;n++){var t=arguments[n];for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])}return e},y.apply(this,arguments)}function k(e,n){e.prototype=Object.create(n.prototype),e.prototype.constructor=e,e.__proto__=n}function w(e,n){if(null==e)return{};var t,r,i={},o=Object.keys(e);for(r=0;r<o.length;r++)t=o[r],n.indexOf(t)>=0||(i[t]=e[t]);return i}function C(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}x.bind({g:1}),x.bind({k:1});var S=function(){return""},O=o().createContext({enqueueSnackbar:S,closeSnackbar:S}),T="@media (max-width:599.95px)",D="@media (min-width:600px)",L=function(e){return e.charAt(0).toUpperCase()+e.slice(1)},j=function(e){return""+L(e.vertical)+L(e.horizontal)},N=function(e){return!!e||0===e},M="unmounted",R="exited",H="entering",P="entered",V="exiting",A=function(e){function n(n){var t;t=e.call(this,n)||this;var r,i=n.appear;return t.appearStatus=null,n.in?i?(r=R,t.appearStatus=H):r=P:r=n.unmountOnExit||n.mountOnEnter?M:R,t.state={status:r},t.nextCallback=null,t}k(n,e),n.getDerivedStateFromProps=function(e,n){return e.in&&n.status===M?{status:R}:null};var t=n.prototype;return t.componentDidMount=function(){this.updateStatus(!0,this.appearStatus)},t.componentDidUpdate=function(e){var n=null;if(e!==this.props){var t=this.state.status;this.props.in?t!==H&&t!==P&&(n=H):t!==H&&t!==P||(n=V)}this.updateStatus(!1,n)},t.componentWillUnmount=function(){this.cancelNextCallback()},t.getTimeouts=function(){var e=this.props.timeout,n=e,t=e;return null!=e&&"number"!=typeof e&&"string"!=typeof e&&(t=e.exit,n=e.enter),{exit:t,enter:n}},t.updateStatus=function(e,n){void 0===e&&(e=!1),null!==n?(this.cancelNextCallback(),n===H?this.performEnter(e):this.performExit()):this.props.unmountOnExit&&this.state.status===R&&this.setState({status:M})},t.performEnter=function(e){var n=this,t=this.props.enter,r=e,i=this.getTimeouts();e||t?(this.props.onEnter&&this.props.onEnter(this.node,r),this.safeSetState({status:H},(function(){n.props.onEntering&&n.props.onEntering(n.node,r),n.onTransitionEnd(i.enter,(function(){n.safeSetState({status:P},(function(){n.props.onEntered&&n.props.onEntered(n.node,r)}))}))}))):this.safeSetState({status:P},(function(){n.props.onEntered&&n.props.onEntered(n.node,r)}))},t.performExit=function(){var e=this,n=this.props.exit,t=this.getTimeouts();n?(this.props.onExit&&this.props.onExit(this.node),this.safeSetState({status:V},(function(){e.props.onExiting&&e.props.onExiting(e.node),e.onTransitionEnd(t.exit,(function(){e.safeSetState({status:R},(function(){e.props.onExited&&e.props.onExited(e.node)}))}))}))):this.safeSetState({status:R},(function(){e.props.onExited&&e.props.onExited(e.node)}))},t.cancelNextCallback=function(){null!==this.nextCallback&&this.nextCallback.cancel&&(this.nextCallback.cancel(),this.nextCallback=null)},t.safeSetState=function(e,n){n=this.setNextCallback(n),this.setState(e,n)},t.setNextCallback=function(e){var n=this,t=!0;return this.nextCallback=function(){t&&(t=!1,n.nextCallback=null,e())},this.nextCallback.cancel=function(){t=!1},this.nextCallback},t.onTransitionEnd=function(e,n){this.setNextCallback(n);var t=null==e&&!this.props.addEndListener;this.node&&!t?(this.props.addEndListener&&this.props.addEndListener(this.node,this.nextCallback),null!=e&&setTimeout(this.nextCallback,e)):setTimeout(this.nextCallback,0)},t.render=function(){var e=this.state.status;if(e===M)return null;var n=this.props;return(0,n.children)(e,w(n,["children","in","mountOnEnter","unmountOnExit","appear","enter","exit","timeout","addEndListener","onEnter","onEntering","onEntered","onExit","onExiting","onExited","nodeRef"]))},b(n,[{key:"node",get:function(){var e,n=null===(e=this.props.nodeRef)||void 0===e?void 0:e.current;if(!n)throw new Error("notistack - Custom snackbar is not refForwarding");return n}}]),n}(o().Component);function q(){}function _(e,n){"function"==typeof e?e(n):e&&(e.current=n)}function I(e,n){return(0,i.useMemo)((function(){return null==e&&null==n?null:function(t){_(e,t),_(n,t)}}),[e,n])}function W(e){var n=e.timeout,t=e.style,r=void 0===t?{}:t,i=e.mode;return{duration:"object"==typeof n?n[i]||0:n,easing:r.transitionTimingFunction,delay:r.transitionDelay}}A.defaultProps={in:!1,mountOnEnter:!1,unmountOnExit:!1,appear:!1,enter:!0,exit:!0,onEnter:q,onEntering:q,onEntered:q,onExit:q,onExiting:q,onExited:q};var z=function(e){e.scrollTop=e.scrollTop},B=function(e){return Math.round(e)+"ms"};function F(e,n){void 0===e&&(e=["all"]);var t=n||{},r=t.duration,i=void 0===r?300:r,o=t.easing,a=void 0===o?"cubic-bezier(0.4, 0, 0.2, 1)":o,s=t.delay,u=void 0===s?0:s;return(Array.isArray(e)?e:[e]).map((function(e){var n="string"==typeof i?i:B(i),t="string"==typeof u?u:B(u);return e+" "+n+" "+a+" "+t})).join(",")}function Z(e){var n=function(e){return e&&e.ownerDocument||document}(e);return n.defaultView||window}function X(e,n){if(n){var t=function(e,n){var t,r=n.getBoundingClientRect(),i=Z(n);if(n.fakeTransform)t=n.fakeTransform;else{var o=i.getComputedStyle(n);t=o.getPropertyValue("-webkit-transform")||o.getPropertyValue("transform")}var a=0,s=0;if(t&&"none"!==t&&"string"==typeof t){var u=t.split("(")[1].split(")")[0].split(",");a=parseInt(u[4],10),s=parseInt(u[5],10)}switch(e){case"left":return"translateX("+(i.innerWidth+a-r.left)+"px)";case"right":return"translateX(-"+(r.left+r.width-a)+"px)";case"up":return"translateY("+(i.innerHeight+s-r.top)+"px)";default:return"translateY(-"+(r.top+r.height-s)+"px)"}}(e,n);t&&(n.style.webkitTransform=t,n.style.transform=t)}}var Q=(0,i.forwardRef)((function(e,n){var t=e.children,r=e.direction,o=void 0===r?"down":r,a=e.in,s=e.style,u=e.timeout,c=void 0===u?0:u,l=e.onEnter,d=e.onEntered,f=e.onExit,p=e.onExited,m=w(e,["children","direction","in","style","timeout","onEnter","onEntered","onExit","onExited"]),h=(0,i.useRef)(null),g=I(t.ref,h),v=I(g,n),x=(0,i.useCallback)((function(){h.current&&X(o,h.current)}),[o]);return(0,i.useEffect)((function(){if(!a&&"down"!==o&&"right"!==o){var e=function(e,n){var t;function r(){for(var r=this,i=arguments.length,o=new Array(i),a=0;a<i;a++)o[a]=arguments[a];clearTimeout(t),t=setTimeout((function(){e.apply(r,o)}),n)}return void 0===n&&(n=166),r.clear=function(){clearTimeout(t)},r}((function(){h.current&&X(o,h.current)})),n=Z(h.current);return n.addEventListener("resize",e),function(){e.clear(),n.removeEventListener("resize",e)}}}),[o,a]),(0,i.useEffect)((function(){a||x()}),[a,x]),(0,i.createElement)(A,Object.assign({appear:!0,nodeRef:h,onEnter:function(e,n){X(o,e),z(e),l&&l(e,n)},onEntered:d,onEntering:function(e){var n=(null==s?void 0:s.transitionTimingFunction)||"cubic-bezier(0.0, 0, 0.2, 1)",t=W({timeout:c,mode:"enter",style:y({},s,{transitionTimingFunction:n})});e.style.webkitTransition=F("-webkit-transform",t),e.style.transition=F("transform",t),e.style.webkitTransform="none",e.style.transform="none"},onExit:function(e){var n=(null==s?void 0:s.transitionTimingFunction)||"cubic-bezier(0.4, 0, 0.6, 1)",t=W({timeout:c,mode:"exit",style:y({},s,{transitionTimingFunction:n})});e.style.webkitTransition=F("-webkit-transform",t),e.style.transition=F("transform",t),X(o,e),f&&f(e)},onExited:function(e){e.style.webkitTransition="",e.style.transition="",p&&p(e)},in:a,timeout:c},m),(function(e,n){return(0,i.cloneElement)(t,y({ref:v,style:y({visibility:"exited"!==e||a?void 0:"hidden"},s,{},t.props.style)},n))}))}));Q.displayName="Slide";var U=function(e){return o().createElement("svg",Object.assign({viewBox:"0 0 24 24",focusable:"false",style:{fontSize:20,marginInlineEnd:8,userSelect:"none",width:"1em",height:"1em",display:"inline-block",fill:"currentColor",flexShrink:0}},e))},G=function(){return o().createElement(U,null,o().createElement("path",{d:"M12 2C6.5 2 2 6.5 2 12S6.5 22 12 22 22 17.5 22 12 17.5 2 12 2M10 17L5 12L6.41\n        10.59L10 14.17L17.59 6.58L19 8L10 17Z"}))},Y=function(){return o().createElement(U,null,o().createElement("path",{d:"M13,14H11V10H13M13,18H11V16H13M1,21H23L12,2L1,21Z"}))},$=function(){return o().createElement(U,null,o().createElement("path",{d:"M12,2C17.53,2 22,6.47 22,12C22,17.53 17.53,22 12,22C6.47,22 2,17.53 2,12C2,\n        6.47 6.47,2 12,2M15.59,7L12,10.59L8.41,7L7,8.41L10.59,12L7,15.59L8.41,17L12,\n        13.41L15.59,17L17,15.59L13.41,12L17,8.41L15.59,7Z"}))},J=function(){return o().createElement(U,null,o().createElement("path",{d:"M13,9H11V7H13M13,17H11V11H13M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,\n        0 22,12A10,10 0 0,0 12,2Z"}))},K={maxSnack:3,persist:!1,hideIconVariant:!1,disableWindowBlurListener:!1,variant:"default",autoHideDuration:5e3,iconVariant:{default:void 0,success:o().createElement(G,null),warning:o().createElement(Y,null),error:o().createElement($,null),info:o().createElement(J,null)},anchorOrigin:{vertical:"bottom",horizontal:"left"},TransitionComponent:Q,transitionDuration:{enter:225,exit:195}};function ee(e){return Object.entries(e).reduce((function(e,n){var t,r=n[0],i=n[1];return y({},e,((t={})[r]=x(i),t))}),{})}var ne="notistack-CollapseWrapper",te=function(e){return"notistack-MuiContent-"+e},re=ee({root:{height:0},entered:{height:"auto"}}),ie="0px",oe=(0,i.forwardRef)((function(e,n){var t=e.children,r=e.in,o=e.onExited,a=(0,i.useRef)(null),s=(0,i.useRef)(null),c=I(n,s),l=function(){return a.current?a.current.clientHeight:0};return(0,i.createElement)(A,{in:r,unmountOnExit:!0,onEnter:function(e){e.style.height=ie},onEntered:function(e){e.style.height="auto"},onEntering:function(e){var n=l(),t=W({timeout:175,mode:"enter"}),r=t.duration,i=t.easing;e.style.transitionDuration="string"==typeof r?r:r+"ms",e.style.height=n+"px",e.style.transitionTimingFunction=i||""},onExit:function(e){e.style.height=l()+"px"},onExited:o,onExiting:function(e){z(e);var n=W({timeout:175,mode:"exit"}),t=n.duration,r=n.easing;e.style.transitionDuration="string"==typeof t?t:t+"ms",e.style.height=ie,e.style.transitionTimingFunction=r||""},nodeRef:s,timeout:175},(function(e,n){return(0,i.createElement)("div",Object.assign({ref:c,className:u(re.root,"entered"===e&&re.entered),style:y({pointerEvents:"all",overflow:"hidden",minHeight:ie,transition:F("height")},"entered"===e&&{overflow:"visible"},{},"exited"===e&&!r&&{visibility:"hidden"})},n),(0,i.createElement)("div",{ref:a,className:ne,style:{display:"flex",width:"100%"}},t))}))}));oe.displayName="Collapse";var ae={right:"left",left:"right",bottom:"up",top:"down"},se=function(e){return"anchorOrigin"+j(e)},ue=function(){};function ce(e,n){return e.reduce((function(e,t){return null==t?e:function(){for(var r=arguments.length,i=new Array(r),o=0;o<r;o++)i[o]=arguments[o];var a=[].concat(i);n&&-1===a.indexOf(n)&&a.push(n),e.apply(this,a),t.apply(this,a)}}),ue)}var le="undefined"!=typeof window?i.useLayoutEffect:i.useEffect;function de(e){var n=(0,i.useRef)(e);return le((function(){n.current=e})),(0,i.useCallback)((function(){return n.current.apply(void 0,arguments)}),[])}var fe,pe=(0,i.forwardRef)((function(e,n){var t=e.children,r=e.className,o=e.autoHideDuration,a=e.disableWindowBlurListener,s=void 0!==a&&a,c=e.onClose,l=e.id,d=e.open,f=e.SnackbarProps,p=void 0===f?{}:f,m=(0,i.useRef)(),h=de((function(){c&&c.apply(void 0,arguments)})),g=de((function(e){c&&null!=e&&(m.current&&clearTimeout(m.current),m.current=setTimeout((function(){h(null,"timeout",l)}),e))}));(0,i.useEffect)((function(){return d&&g(o),function(){m.current&&clearTimeout(m.current)}}),[d,o,g]);var v=function(){m.current&&clearTimeout(m.current)},x=(0,i.useCallback)((function(){null!=o&&g(.5*o)}),[o,g]);return(0,i.useEffect)((function(){if(!s&&d)return window.addEventListener("focus",x),window.addEventListener("blur",v),function(){window.removeEventListener("focus",x),window.removeEventListener("blur",v)}}),[s,x,d]),(0,i.createElement)("div",Object.assign({ref:n},p,{className:u("notistack-Snackbar",r),onMouseEnter:function(e){p.onMouseEnter&&p.onMouseEnter(e),v()},onMouseLeave:function(e){p.onMouseLeave&&p.onMouseLeave(e),x()}}),t)}));pe.displayName="Snackbar";var me=ee({root:(fe={display:"flex",flexWrap:"wrap",flexGrow:1},fe[D]={flexGrow:"initial",minWidth:"288px"},fe)}),he=(0,i.forwardRef)((function(e,n){var t=e.className,r=w(e,["className"]);return o().createElement("div",Object.assign({ref:n,className:u(me.root,t)},r))}));he.displayName="SnackbarContent";var ge=ee({root:{backgroundColor:"#313131",fontSize:"0.875rem",lineHeight:1.43,letterSpacing:"0.01071em",color:"#fff",alignItems:"center",padding:"6px 16px",borderRadius:"4px",boxShadow:"0px 3px 5px -1px rgba(0,0,0,0.2),0px 6px 10px 0px rgba(0,0,0,0.14),0px 1px 18px 0px rgba(0,0,0,0.12)"},lessPadding:{paddingLeft:"20px"},default:{backgroundColor:"#313131"},success:{backgroundColor:"#43a047"},error:{backgroundColor:"#d32f2f"},warning:{backgroundColor:"#ff9800"},info:{backgroundColor:"#2196f3"},message:{display:"flex",alignItems:"center",padding:"8px 0"},action:{display:"flex",alignItems:"center",marginLeft:"auto",paddingLeft:"16px",marginRight:"-8px"}}),ve="notistack-snackbar",xe=(0,i.forwardRef)((function(e,n){var t=e.id,r=e.message,i=e.action,a=e.iconVariant,s=e.variant,c=e.hideIconVariant,l=e.style,d=e.className,f=a[s],p=i;return"function"==typeof p&&(p=p(t)),o().createElement(he,{ref:n,role:"alert","aria-describedby":ve,style:l,className:u("notistack-MuiContent",te(s),ge.root,ge[s],d,!c&&f&&ge.lessPadding)},o().createElement("div",{id:ve,className:ge.message},c?null:f,r),p&&o().createElement("div",{className:ge.action},p))}));xe.displayName="MaterialDesignContent";var Ee,be,ye,ke,we,Ce,Se=(0,i.memo)(xe),Oe=ee({wrappedRoot:{width:"100%",position:"relative",transform:"translateX(0)",top:0,right:0,bottom:0,left:0,minWidth:"288px"}}),Te=function(e){var n=(0,i.useRef)(),t=(0,i.useState)(!0),r=t[0],a=t[1],s=ce([e.snack.onClose,e.onClose]),c=(0,i.useCallback)((function(){n.current=setTimeout((function(){a((function(e){return!e}))}),125)}),[]);(0,i.useEffect)((function(){return function(){n.current&&clearTimeout(n.current)}}),[]);var l,d=e.snack,f=e.classes,p=e.Component,m=void 0===p?Se:p,h=(0,i.useMemo)((function(){return function(e){void 0===e&&(e={});var n={containerRoot:!0,containerAnchorOriginTopCenter:!0,containerAnchorOriginBottomCenter:!0,containerAnchorOriginTopRight:!0,containerAnchorOriginBottomRight:!0,containerAnchorOriginTopLeft:!0,containerAnchorOriginBottomLeft:!0};return Object.keys(e).filter((function(e){return!n[e]})).reduce((function(n,t){var r;return y({},n,((r={})[t]=e[t],r))}),{})}(f)}),[f]),g=d.open,v=d.SnackbarProps,x=d.TransitionComponent,E=d.TransitionProps,b=d.transitionDuration,k=d.disableWindowBlurListener,C=d.content,S=w(d,["open","SnackbarProps","TransitionComponent","TransitionProps","transitionDuration","disableWindowBlurListener","content","entered","requestClose","onEnter","onEntered","onExit","onExited"]),O=y({direction:(l=S.anchorOrigin,"center"!==l.horizontal?ae[l.horizontal]:ae[l.vertical]),timeout:b},E),T=C;"function"==typeof T&&(T=T(S.id,S.message));var D=["onEnter","onEntered","onExit","onExited"].reduce((function(n,t){var r;return y({},n,((r={})[t]=ce([e.snack[t],e[t]],S.id),r))}),{});return o().createElement(oe,{in:r,onExited:D.onExited},o().createElement(pe,{open:g,id:S.id,disableWindowBlurListener:k,autoHideDuration:S.autoHideDuration,className:u(Oe.wrappedRoot,h.root,h[se(S.anchorOrigin)]),SnackbarProps:v,onClose:s},o().createElement(x,Object.assign({},O,{appear:!0,in:g,onExit:D.onExit,onExited:c,onEnter:D.onEnter,onEntered:ce([D.onEntered,function(){e.snack.requestClose&&s(null,"instructed",e.snack.id)}],S.id)}),T||o().createElement(m,Object.assign({},S)))))},De=20,Le=6,je=2,Ne="."+ne,Me=ee({root:(Ee={boxSizing:"border-box",display:"flex",maxHeight:"100%",position:"fixed",zIndex:1400,height:"auto",width:"auto",transition:F(["top","right","bottom","left","max-width"],{duration:300,easing:"ease"}),pointerEvents:"none"},Ee[Ne]={padding:Le+"px 0px",transition:"padding 300ms ease 0ms"},Ee.maxWidth="calc(100% - "+2*De+"px)",Ee[T]={width:"100%",maxWidth:"calc(100% - 32px)"},Ee),rootDense:(be={},be[Ne]={padding:je+"px 0px"},be),top:{top:De-Le+"px",flexDirection:"column"},bottom:{bottom:De-Le+"px",flexDirection:"column-reverse"},left:(ye={left:De+"px"},ye[D]={alignItems:"flex-start"},ye[T]={left:"16px"},ye),right:(ke={right:De+"px"},ke[D]={alignItems:"flex-end"},ke[T]={right:"16px"},ke),center:(we={left:"50%",transform:"translateX(-50%)"},we[D]={alignItems:"center"},we)}),Re=function(e){var n=e.classes,t=void 0===n?{}:n,r=e.anchorOrigin,i=e.dense,a=e.children,s=u("notistack-SnackbarContainer",Me[r.vertical],Me[r.horizontal],Me.root,t.containerRoot,t["containerAnchorOrigin"+j(r)],i&&Me.rootDense);return o().createElement("div",{className:s},a)},He=(0,i.memo)(Re),Pe=function(e){return!("string"==typeof e||(0,i.isValidElement)(e))},Ve=function(e){function n(n){var t;return(t=e.call(this,n)||this).enqueueSnackbar=function(e,n){if(void 0===n&&(n={}),null==e)throw new Error("enqueueSnackbar called with invalid argument");var r=Pe(e)?e:n,i=Pe(e)?e.message:e,o=r.key,a=r.preventDuplicate,s=w(r,["key","preventDuplicate"]),c=N(o),l=c?o:(new Date).getTime()+Math.random(),d=function(e,n){return function(t,r){return void 0===r&&(r=!1),r?y({},K[t],{},n[t],{},e[t]):"autoHideDuration"===t?(i=e.autoHideDuration,o=n.autoHideDuration,(a=function(e){return"number"==typeof e||null===e})(i)?i:a(o)?o:K.autoHideDuration):"transitionDuration"===t?function(e,n){var t=function(e,n){return n.some((function(n){return typeof e===n}))};return t(e,["string","number"])?e:t(e,["object"])?y({},K.transitionDuration,{},t(n,["object"])&&n,{},e):t(n,["string","number"])?n:t(n,["object"])?y({},K.transitionDuration,{},n):K.transitionDuration}(e.transitionDuration,n.transitionDuration):e[t]||n[t]||K[t];var i,o,a}}(s,t.props),f=y({id:l},s,{message:i,open:!0,entered:!1,requestClose:!1,persist:d("persist"),action:d("action"),content:d("content"),variant:d("variant"),anchorOrigin:d("anchorOrigin"),disableWindowBlurListener:d("disableWindowBlurListener"),autoHideDuration:d("autoHideDuration"),hideIconVariant:d("hideIconVariant"),TransitionComponent:d("TransitionComponent"),transitionDuration:d("transitionDuration"),TransitionProps:d("TransitionProps",!0),iconVariant:d("iconVariant",!0),style:d("style",!0),SnackbarProps:d("SnackbarProps",!0),className:u(t.props.className,s.className)});return f.persist&&(f.autoHideDuration=void 0),t.setState((function(e){if(void 0===a&&t.props.preventDuplicate||a){var n=function(e){return c?e.id===l:e.message===i},r=e.queue.findIndex(n)>-1,o=e.snacks.findIndex(n)>-1;if(r||o)return e}return t.handleDisplaySnack(y({},e,{queue:[].concat(e.queue,[f])}))})),l},t.handleDisplaySnack=function(e){return e.snacks.length>=t.maxSnack?t.handleDismissOldest(e):t.processQueue(e)},t.processQueue=function(e){var n=e.queue,t=e.snacks;return n.length>0?y({},e,{snacks:[].concat(t,[n[0]]),queue:n.slice(1,n.length)}):e},t.handleDismissOldest=function(e){if(e.snacks.some((function(e){return!e.open||e.requestClose})))return e;var n=!1,r=!1;e.snacks.reduce((function(e,n){return e+(n.open&&n.persist?1:0)}),0)===t.maxSnack&&(r=!0);var i=e.snacks.map((function(e){return n||e.persist&&!r?y({},e):(n=!0,e.entered?(e.onClose&&e.onClose(null,"maxsnack",e.id),t.props.onClose&&t.props.onClose(null,"maxsnack",e.id),y({},e,{open:!1})):y({},e,{requestClose:!0}))}));return y({},e,{snacks:i})},t.handleEnteredSnack=function(e,n,r){if(!N(r))throw new Error("handleEnteredSnack Cannot be called with undefined key");t.setState((function(e){return{snacks:e.snacks.map((function(e){return e.id===r?y({},e,{entered:!0}):y({},e)}))}}))},t.handleCloseSnack=function(e,n,r){t.props.onClose&&t.props.onClose(e,n,r);var i=void 0===r;t.setState((function(e){var n=e.snacks,t=e.queue;return{snacks:n.map((function(e){return i||e.id===r?e.entered?y({},e,{open:!1}):y({},e,{requestClose:!0}):y({},e)})),queue:t.filter((function(e){return e.id!==r}))}}))},t.closeSnackbar=function(e){var n=t.state.snacks.find((function(n){return n.id===e}));N(e)&&n&&n.onClose&&n.onClose(null,"instructed",e),t.handleCloseSnack(null,"instructed",e)},t.handleExitedSnack=function(e,n){if(!N(n))throw new Error("handleExitedSnack Cannot be called with undefined key");t.setState((function(e){var r=t.processQueue(y({},e,{snacks:e.snacks.filter((function(e){return e.id!==n}))}));return 0===r.queue.length?r:t.handleDismissOldest(r)}))},t.enqueueSnackbar,Ce=t.closeSnackbar,t.state={snacks:[],queue:[],contextValue:{enqueueSnackbar:t.enqueueSnackbar.bind(C(t)),closeSnackbar:t.closeSnackbar.bind(C(t))}},t}return k(n,e),n.prototype.render=function(){var e=this,n=this.state.contextValue,t=this.props,r=t.domRoot,i=t.children,s=t.dense,u=void 0!==s&&s,c=t.Components,l=void 0===c?{}:c,d=t.classes,f=this.state.snacks.reduce((function(e,n){var t,r=j(n.anchorOrigin),i=e[r]||[];return y({},e,((t={})[r]=[].concat(i,[n]),t))}),{}),p=Object.keys(f).map((function(n){var t=f[n],r=t[0];return o().createElement(He,{key:n,dense:u,anchorOrigin:r.anchorOrigin,classes:d},t.map((function(n){return o().createElement(Te,{key:n.id,snack:n,classes:d,Component:l[n.variant],onClose:e.handleCloseSnack,onEnter:e.props.onEnter,onExit:e.props.onExit,onExited:ce([e.handleExitedSnack,e.props.onExited],n.id),onEntered:ce([e.handleEnteredSnack,e.props.onEntered],n.id)})})))}));return o().createElement(O.Provider,{value:n},i,r?(0,a.createPortal)(p,r):p)},b(n,[{key:"maxSnack",get:function(){return this.props.maxSnack||K.maxSnack}}]),n}(i.Component),Ae=window.elementorV2.ui,qe=(0,r.__createSlice)({name:"notifications",initialState:{},reducers:{notifyAction:(e,n)=>{const t={...e};return t[n.payload.id]||(t[n.payload.id]=n.payload),t},clearAction:(e,n)=>{const t={...e};return t[n.payload.id]&&delete t[n.payload.id],t}}}),{notifyAction:_e,clearAction:Ie}=qe.actions,We={default:(0,i.forwardRef)(((e,n)=>{const t=function(e){const n=["autoHideDuration","persist","hideIconVariant","iconVariant","anchorOrigin"];return Object.entries(e).reduce(((e,[t,r])=>(n.includes(t)||(e[t]=r),e)),{})}(e),r=document.querySelector(".elementor-panel")?.clientWidth||0;return i.createElement(Ae.ThemeProvider,{palette:"unstable"},i.createElement(Ae.SnackbarContent,{ref:n,...t,sx:{"&.MuiPaper-root":{minWidth:"max-content"},ml:r+"px"}}))}))},ze=()=>((e=>{const{enqueueSnackbar:n}=(0,i.useContext)(O),t=(0,r.__useDispatch)();(0,i.useEffect)((()=>{Object.values(e).forEach((e=>{const r=()=>i.createElement(i.Fragment,{key:e.id},e.additionalActionProps?.map(((e,n)=>i.createElement(Ae.Button,{key:`${n}`,...e}))),i.createElement(Ae.CloseButton,{"aria-label":"close",color:"inherit",onClick:()=>{Ce(e.id),t(Ie({id:e.id}))}}));n(e.message,{persist:!0,variant:e.type,key:e.id,onClose:()=>t(Ie({id:e.id})),preventDuplicate:!0,action:i.createElement(r,null)})}))}),[e,n,t])})((0,r.__useSelector)((e=>e.notifications))),null);function Be(e){const n=(0,r.__getStore)();n?.dispatch(_e(e))}function Fe(e){(0,r.__useDispatch)()(_e(e))}var Ze=()=>i.createElement(Ve,{maxSnack:3,autoHideDuration:8e3,anchorOrigin:{horizontal:"center",vertical:"bottom"},Components:We},i.createElement(ze,null));function Xe(){(0,r.__registerSlice)(qe),(0,t.injectIntoTop)({id:"notifications",component:Ze})}(window.elementorV2=window.elementorV2||{}).editorNotifications=n}(),window.elementorV2.editorNotifications?.init?.();