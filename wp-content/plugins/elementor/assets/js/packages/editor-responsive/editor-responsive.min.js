!function(){"use strict";var e={d:function(t,n){for(var i in n)e.o(n,i)&&!e.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:n[i]})},o:function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r:function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{getBreakpoints:function(){return d},getBreakpointsTree:function(){return f},useActivateBreakpoint:function(){return a},useActiveBreakpoint:function(){return c},useBreakpoints:function(){return u},useBreakpointsMap:function(){return l}});var n=window.elementorV2.editorV1Adapters,i=window.wp.i18n,r=window.React;function o(){const{breakpoints:e}=window.elementor?.config?.responsive||{};if(!e||0===Object.entries(e).length)return{minWidth:[],defaults:[],maxWidth:[]};const t=[],n=[],r=[{id:"desktop",label:(0,i.__)("Desktop","elementor")}];Object.entries(e).forEach((([e,i])=>{if(!i.is_enabled)return;const o={id:e,label:i.label,width:i.value,type:"min"===i.direction?"min-width":"max-width"};o.width?"min-width"===o.type?t.push(o):"max-width"===o.type&&n.push(o):r.push(o)}));const o=(e,t)=>e.width&&t.width?t.width-e.width:0;return{minWidth:t.sort(o),defaults:r,maxWidth:n.sort(o)}}function d(){const{minWidth:e,defaults:t,maxWidth:n}=o();return[...e,...t,...n]}function u(){return(0,n.__privateUseListenTo)((0,n.v1ReadyEvent)(),d)}function c(){return(0,n.__privateUseListenTo)((0,n.windowEvent)("elementor/device-mode/change"),s)}function s(){const e=window;return e.elementor?.channels?.deviceMode?.request?.("currentMode")||null}function a(){return(0,r.useCallback)((e=>(0,n.__privateRunCommand)("panel/change-device-mode",{device:e})),[])}function l(){const e=u().map((e=>[e.id,e]));return Object.fromEntries(e)}function f(){const{minWidth:e,defaults:t,maxWidth:n}=o(),[i]=t,r={...i,children:[]},d=e=>{let t=r;e.forEach((e=>{const n={...e,children:[]};t.children.push(n),t=n}))};return d(e),d(n),r}(window.elementorV2=window.elementorV2||{}).editorResponsive=t}(),window.elementorV2.editorResponsive?.init?.();