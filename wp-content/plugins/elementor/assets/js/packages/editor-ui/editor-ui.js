/*! For license information please see editor-ui.js.LICENSE.txt */
!function(){"use strict";var e={react:function(e){e.exports=window.React},"@elementor/editor-v1-adapters":function(e){e.exports=window.elementorV2.editorV1Adapters},"@elementor/icons":function(e){e.exports=window.elementorV2.icons},"@elementor/ui":function(e){e.exports=window.elementorV2.ui},"@wordpress/i18n":function(e){e.exports=window.wp.i18n}},t={};function n(r){var o=t[r];if(void 0!==o)return o.exports;var i=t[r]={exports:{}};return e[r](i,i.exports,n),i.exports}n.d=function(e,t){for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var r={};!function(){n.r(r),n.d(r,{EditableField:function(){return u},EllipsisWithTooltip:function(){return l},InfoTipCard:function(){return w},IntroductionModal:function(){return d},MenuItemInfotip:function(){return h},MenuListItem:function(){return v},ThemeProvider:function(){return g},WarningInfotip:function(){return x},useEditable:function(){return y}});var e=n("react"),t=n("@elementor/ui"),o=n("@wordpress/i18n"),i=n("@elementor/editor-v1-adapters"),a=n("@elementor/icons"),l=({maxWidth:n,title:r,as:o,...i})=>{const[a,l]=s();return l?e.createElement(t.Tooltip,{title:r,placement:"top"},e.createElement(c,{maxWidth:n,ref:a,as:o,...i},r)):e.createElement(c,{maxWidth:n,ref:a,as:o,...i},r)},c=e.forwardRef((({maxWidth:n,as:r=t.Box,...o},i)=>e.createElement(r,{ref:i,position:"relative",...o,style:{overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap",maxWidth:n}}))),s=()=>{const[t,n]=(0,e.useState)(null),[r,o]=(0,e.useState)(!1);return(0,e.useEffect)((()=>{const e=new ResizeObserver((([{target:e}])=>{o(e.scrollWidth>e.clientWidth)}));return t&&e.observe(t),()=>{e.disconnect()}}),[t]),[n,r]},u=(0,e.forwardRef)((({value:n,error:r,as:o="span",sx:i,...a},l)=>e.createElement(t.Tooltip,{title:r,open:!!r,placement:"top"},e.createElement(m,{ref:l,component:o,...a},n)))),m=(0,t.styled)(t.Box)`
	width: 100%;
	&:focus {
		outline: none;
	}
`,d=({open:n,handleClose:r,title:i,children:a})=>{const[l,c]=(0,e.useState)(!0);return e.createElement(t.Dialog,{open:n,onClose:r,maxWidth:"sm",TransitionComponent:p},i&&e.createElement(t.DialogHeader,{logo:!1},e.createElement(t.DialogTitle,null,i)),a,e.createElement(t.DialogActions,null,e.createElement(t.FormControlLabel,{sx:{marginRight:"auto"},control:e.createElement(t.Checkbox,{checked:!l,onChange:()=>c(!l)}),label:e.createElement(t.Typography,{variant:"body2"},(0,o.__)("Don't show this again","elementor"))}),e.createElement(t.Button,{size:"medium",variant:"contained",sx:{minWidth:"135px"},onClick:()=>r(l)},(0,o.__)("Got it","elementor"))))},p=e.forwardRef(((n,r)=>e.createElement(t.Fade,{ref:r,...n,timeout:{enter:1e3,exit:200}})));function f(){return window.elementor?.getPreferences?.("ui_theme")||"auto"}var E="unstable";function g({children:n}){const r=function(){const[t,n]=(0,e.useState)((()=>f()));return(0,e.useEffect)((()=>(0,i.__privateListenTo)((0,i.v1ReadyEvent)(),(()=>n(f())))),[]),(0,e.useEffect)((()=>(0,i.__privateListenTo)((0,i.commandEndEvent)("document/elements/settings"),(e=>{const t=e;t.args?.settings&&"ui_theme"in t.args.settings&&n(f())}))),[]),t}();return e.createElement(t.ThemeProvider,{colorScheme:r,palette:E},n)}var v=({children:n,...r})=>e.createElement(t.MenuItem,{dense:!0,...r,sx:{...r.sx??{}}},e.createElement(t.MenuItemText,{primary:n,primaryTypographyProps:{variant:"caption"}})),h=(0,e.forwardRef)((({showInfoTip:n=!1,children:r,content:o},i)=>n?e.createElement(t.Infotip,{ref:i,placement:"right",arrow:!1,content:e.createElement(t.Paper,{color:"secondary",sx:{display:"flex",gap:.5,p:2,maxWidth:325},elevation:0},e.createElement(a.InfoCircleFilledIcon,{fontSize:"small",color:"secondary"}),e.createElement(t.Stack,null,e.createElement(t.Typography,{variant:"caption",color:"text.primary"},o)))},e.createElement("div",{style:{pointerEvents:"initial",width:"100%"},onClick:e=>e.stopPropagation()},r)):e.createElement(e.Fragment,null,r))),w=({content:n,svgIcon:r,learnMoreButton:o,ctaButton:i})=>e.createElement(t.Card,{elevation:0,sx:{width:320}},e.createElement(t.CardContent,{sx:{pb:0}},e.createElement(t.Box,{display:"flex",alignItems:"start"},e.createElement(t.SvgIcon,{fontSize:"tiny",sx:{mr:.5}},r),e.createElement(t.Typography,{variant:"body2"},n))),(i||o)&&e.createElement(t.CardActions,null,o&&e.createElement(t.Button,{size:"small",color:"warning",href:o.href,target:"_blank"},o.label),i&&e.createElement(t.Button,{size:"small",color:"warning",variant:"contained",onClick:i.onClick},i.label))),x=(0,e.forwardRef)((({children:n,open:r,title:o,text:i,placement:a,width:l,offset:c},s)=>e.createElement(t.Infotip,{ref:s,open:r,placement:a,PopperProps:{sx:{width:l||"initial",".MuiTooltip-tooltip":{marginLeft:0,marginRight:0}},modifiers:c?[{name:"offset",options:{offset:c}}]:[]},arrow:!1,content:e.createElement(t.Alert,{color:"error",severity:"warning",variant:"standard",sx:e=>({".MuiAlert-icon":{fontSize:"1.25rem",marginRight:e.spacing(.5)}})},o?e.createElement(t.AlertTitle,null,o):null,e.createElement(t.Typography,{variant:"caption",sx:{color:"text.primary"}},i))},n))),y=({value:t,onSubmit:n,validation:r,onClick:o,onError:i})=>{const[a,l]=(0,e.useState)(!1),[c,s]=(0,e.useState)(null),u=b(a),m=e=>e!==t,d=()=>{u.current?.blur(),s(null),i?.(null),l(!1)},p={onClick:e=>{a&&e.stopPropagation(),o?.(e)},onKeyDown:e=>(e.stopPropagation(),["Escape"].includes(e.key)?d():["Enter"].includes(e.key)?(e.preventDefault(),(e=>{if(m(e)&&!c)try{n(e)}finally{d()}})(e.target.innerText)):void 0),onInput:e=>{const{innerText:t}=e.target;if(r){const e=m(t)?r(t):null;s(e),i?.(e)}},onBlur:d},f={value:t,role:"textbox",contentEditable:a,...a&&{suppressContentEditableWarning:!0}};return{ref:u,isEditing:a,openEditMode:()=>{l(!0)},closeEditMode:d,value:t,error:c,getProps:()=>({...p,...f})}},b=t=>{const n=(0,e.useRef)(null);return(0,e.useEffect)((()=>{t&&T(n.current)}),[t]),n},T=e=>{const t=getSelection();if(!t||!e)return;const n=document.createRange();n.selectNodeContents(e),t.removeAllRanges(),t.addRange(n)}}(),(window.elementorV2=window.elementorV2||{}).editorUi=r}(),window.elementorV2.editorUi?.init?.();