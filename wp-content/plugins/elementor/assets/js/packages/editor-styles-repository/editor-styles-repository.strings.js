__( 'Class name is too long. Please keep it under 50 characters.', 'elementor' );
__( 'Class names must start with a letter.', 'elementor' );
__( 'Class names can’t contain spaces.', 'elementor' );
__( 'Class names can only use letters, numbers, dashes (-), and underscores (_).', 'elementor' );
__( 'Double hyphens are reserved for custom properties.', 'elementor' );
__( 'Class names can’t start with a hyphen followed by a number.', 'elementor' );
__( 'This name is reserved and can’t be used. Try something more specific.', 'elementor' );
__( 'Class name is too short. Use at least 2 characters.', 'elementor' );
__( 'This class name already exists. Please choose a unique name.', 'elementor' );