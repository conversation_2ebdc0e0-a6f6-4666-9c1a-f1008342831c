/*! For license information please see editor-styles-repository.js.LICENSE.txt */
!function(){"use strict";var e={react:function(e){e.exports=window.React},"@elementor/editor-current-user":function(e){e.exports=window.elementorV2.editorCurrentUser},"@elementor/editor-elements":function(e){e.exports=window.elementorV2.editorElements},"@elementor/editor-v1-adapters":function(e){e.exports=window.elementorV2.editorV1Adapters},"@elementor/schema":function(e){e.exports=window.elementorV2.schema},"@elementor/utils":function(e){e.exports=window.elementorV2.utils},"@wordpress/i18n":function(e){e.exports=window.wp.i18n}},t={};function r(n){var s=t[n];if(void 0!==s)return s.exports;var o=t[n]={exports:{}};return e[n](o,o.exports,r),o.exports}r.d=function(e,t){for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var n={};!function(){r.r(n),r.d(n,{ELEMENTS_BASE_STYLES_PROVIDER_KEY:function(){return R},ELEMENTS_STYLES_PROVIDER_KEY_PREFIX:function(){return v},ELEMENTS_STYLES_RESERVED_LABEL:function(){return w},createStylesProvider:function(){return b},init:function(){return I},isElementsStylesProvider:function(){return C},stylesRepository:function(){return u},useGetStylesRepositoryCreateAction:function(){return p},useProviders:function(){return c},useUserStylesCapability:function(){return m},validateStyleLabel:function(){return P}});var e=r("react"),t=r("@elementor/editor-current-user"),s=r("@elementor/schema"),o=r("@wordpress/i18n"),i=r("@elementor/editor-elements"),a=r("@elementor/editor-v1-adapters"),l=r("@elementor/utils"),u=(()=>{const e=[],t=()=>e.slice(0).sort(((e,t)=>e.priority>t.priority?-1:1));return{all:(e={})=>t().flatMap((t=>t.actions.all(e))),register:t=>{e.push(t)},subscribe:t=>{const r=e.map((e=>e.subscribe(t)));return()=>{r.forEach((e=>e()))}},getProviders:t,getProviderByKey:t=>e.find((e=>e.getKey()===t))}})();function c(){const[,t]=(0,e.useReducer)((e=>!e),!1);return(0,e.useEffect)((()=>u.subscribe(t)),[]),u.getProviders()}var d={create:!0,delete:!0,update:!0,updateProps:!0},m=()=>{const{capabilities:e}=(0,t.useCurrentUserCapabilities)();return{userCan:t=>{const r=u.getProviderByKey(t);return r?.capabilities?Object.entries(r.capabilities).reduce(((t,[r,n])=>({...t,[r]:e?.includes(n)??!0})),d):d}}};function p(){const{userCan:t}=m();return(0,e.useMemo)((()=>{const e=u.getProviders().map((e=>e.actions.create&&t(e.getKey()).create?[e,e.actions.create]:null)).filter(Boolean);if(1===e.length)return e[0];if(0===e.length)return null;throw new Error("Multiple providers with create action found in styles repository.")}),[])}var f=(0,l.createError)({code:"invalid_elements_style_provider_meta",message:"Invalid elements style provider meta."}),y=(0,l.createError)({code:"active_document_must_exist",message:"Active document must exist."}),g=1e4,_=10;function b({key:e,priority:t=_,limit:r=g,subscribe:n=()=>()=>{},labels:s,actions:o,capabilities:i}){return{getKey:"string"==typeof e?()=>e:e,priority:t,limit:r,capabilities:i,subscribe:n,labels:{singular:s?.singular??null,plural:s?.plural??null},actions:{all:o.all,get:o.get,resolveCssName:o.resolveCssName??(e=>e),create:o.create,delete:o.delete,update:o.update,updateProps:o.updateProps}}}var v="document-elements-",w="local",E=b({key:()=>{const e=(0,i.getCurrentDocumentId)();if(!e)throw new y;return`${v}${e}`},priority:50,subscribe:e=>(0,a.__privateListenTo)(i.styleRerenderEvents,e),actions:{all:(e={})=>{let t=(0,i.getElements)();return h(e)&&(t=t.filter((t=>t.id===e.elementId))),t.flatMap((e=>Object.values(e.model.get("styles")??{})))},get:(e,t={})=>{if(!h(t))throw new f({context:{meta:t}});return((0,i.getElementStyles)(t.elementId)??{})[e]??null},updateProps:(e,t={})=>{if(!h(t))throw new f({context:{meta:t}});(0,i.updateElementStyle)({elementId:t.elementId,styleId:e.id,meta:e.meta,props:e.props})}}});function h(e){return"elementId"in e&&"string"==typeof e.elementId&&!!e.elementId}var S=["container"],x=s.z.string().max(50,(0,o.__)("Class name is too long. Please keep it under 50 characters.","elementor")).regex(/^(|[^0-9].*)$/,(0,o.__)("Class names must start with a letter.","elementor")).regex(/^\S*$/,(0,o.__)("Class names can’t contain spaces.","elementor")).regex(/^(|[a-zA-Z0-9_-]+)$/,(0,o.__)("Class names can only use letters, numbers, dashes (-), and underscores (_).","elementor")).regex(/^(?!--).*/,(0,o.__)("Double hyphens are reserved for custom properties.","elementor")).regex(/^(?!-[0-9])/,(0,o.__)("Class names can’t start with a hyphen followed by a number.","elementor")).refine((e=>!S.includes(e)),{message:(0,o.__)("This name is reserved and can’t be used. Try something more specific.","elementor")});function P(e,t){const r=new Set([w,...u.all().map((e=>e.label.toLowerCase()))]),n=["create","rename"].includes(t),s=x.refine((e=>!(n&&e.length<2)),{message:(0,o.__)("Class name is too short. Use at least 2 characters.","elementor")}).refine((e=>!(n&&r.has(e))),{message:(0,o.__)("This class name already exists. Please choose a unique name.","elementor")}).safeParse(e.toLowerCase());return s.success?{isValid:!0,errorMessage:null}:{isValid:!1,errorMessage:s.error.format()._errors[0]}}function C(e){return new RegExp(`^${v}\\d+$`).test(e)}var R="element-base-styles",V=b({key:R,actions:{all(){const e=(0,i.getWidgetsCache)();return Object.values(e??{}).flatMap((e=>Object.values(e.base_styles??{})))},get(e){return this.all().find((t=>t.id===e))??null}}});function I(){u.register(E),u.register(V)}}(),(window.elementorV2=window.elementorV2||{}).editorStylesRepository=n}(),window.elementorV2.editorStylesRepository?.init?.();