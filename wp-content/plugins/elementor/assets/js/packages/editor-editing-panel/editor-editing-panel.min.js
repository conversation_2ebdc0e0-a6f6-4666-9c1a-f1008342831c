!function(){"use strict";var e={d:function(t,n){for(var l in n)e.o(n,l)&&!e.o(t,l)&&Object.defineProperty(t,l,{enumerable:!0,get:n[l]})},o:function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r:function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{controlActionsMenu:function(){return he},init:function(){return Fr},injectIntoClassSelectorActions:function(){return Ee},registerControlReplacement:function(){return C},useBoundProp:function(){return n.useBoundProp},usePanelActions:function(){return Yl},usePanelStatus:function(){return Xl}});var n=window.elementorV2.editorControls,l=window.React,r=window.elementorV2.editorElements,o=window.elementorV2.editorStylesRepository,a=window.elementorV2.editorUi,i=window.elementorV2.icons,c=window.elementorV2.locations,s=window.elementorV2.ui,m=window.wp.i18n,u=window.elementorV2.utils,d=window.elementorV2.editorDocuments,p=window.elementorV2.editorProps,E=window.elementorV2.editorV1Adapters,f=window.elementorV2.editorPanels,g=window.elementorV2.session,b=window.elementorV2.menus,v=window.elementorV2.editorResponsive,_=window.elementorV2.editorStyles,h=window.elementorV2.editorCanvas,y=window.elementorV2.editor,x=window.elementorV2.editorCurrentUser,w=window.elementorV2.schema,S=window.elementorV2.wpMedia,{registerControlReplacement:C,getControlReplacements:I}=(0,n.createControlReplacementsRegistry)(),T=(0,l.createContext)(null);function k({children:e,prop:t}){return l.createElement(T.Provider,{value:{prop:t}},e)}function z(){const e=(0,l.useContext)(T);if(!e)throw new Error("useClassesProp must be used within a ClassesPropProvider");return e.prop}var P=(0,l.createContext)(null);function G({children:e,element:t,elementType:n}){return l.createElement(P.Provider,{value:{element:t,elementType:n}},e)}function L(){const e=(0,l.useContext)(P);if(!e)throw new Error("useElement must be used within a ElementProvider");return e}var R=(0,u.createError)({code:"control_type_not_found",message:"Control type not found."}),B=(0,u.createError)({code:"provider_not_found",message:"Styles provider not found."}),V=(0,u.createError)({code:"provider_cannot_update_props",message:"Styles provider doesn't support updating props."}),A=(0,u.createError)({code:"style_not_found_under_provider",message:"Style not found under the provider."}),D=(0,l.createContext)(null);function M({children:e,...t}){const n=null===t.id?null:W(t.id),{userCan:r}=(0,o.useUserStylesCapability)();if(t.id&&!n)throw new B({context:{styleId:t.id}});const a=r(n?.getKey()??"").updateProps;return l.createElement(D.Provider,{value:{...t,provider:n,canEdit:a}},e)}function O(){const e=(0,l.useContext)(D);if(!e)throw new Error("useStyle must be used within a StyleProvider");return e}function W(e){return o.stylesRepository.getProviders().find((t=>t.actions.all().find((t=>t.id===e))))??null}function j(e){const{_group:t,_action:n,...l}=e;return l}var F=l.forwardRef((function({selected:e,options:t,entityName:n,onSelect:r,placeholder:o,onCreate:a,validate:i,renderEmptyState:c,...m},u){const{inputValue:d,setInputValue:p,error:E,setError:f,inputHandlers:g}=function(e){const[t,n]=(0,l.useState)(""),[r,o]=(0,l.useState)(null);return{inputValue:t,setInputValue:n,error:r,setError:o,inputHandlers:{onChange:t=>{const{value:l}=t.target;if(n(l),!e)return;if(!l)return void o(null);const{isValid:r,errorMessage:a}=e(l,"inputChange");o(r?null:a)},onBlur:()=>{n(""),o(null)}}}}(i),{open:b,openDropdown:v,closeDropdown:_}=function(e=!1){const[t,n]=(0,l.useState)(e);return{open:t,openDropdown:()=>n(!0),closeDropdown:()=>n(!1)}}(m.open),{createOption:h,loading:y}=function(e){const{onCreate:t,validate:n,setInputValue:r,setError:o,closeDropdown:a}=e,[i,c]=(0,l.useState)(!1);return t?{createOption:async e=>{if(c(!0),n){const{isValid:t,errorMessage:l}=n(e,"create");if(!t)return o(l),void c(!1)}try{r(""),a(),await t(e)}catch{}finally{c(!1)}},loading:i}:{createOption:null,loading:!1}}({onCreate:a,validate:i,setInputValue:p,setError:f,closeDropdown:_}),[x,w]=(0,l.useMemo)((()=>[t,e].map((e=>function(e,t){return e.map((e=>({...e,_group:`Existing ${t??"options"}`})))}(e,n?.plural)))),[t,e,n?.plural]),S=function(e){const{options:t,onSelect:n,createOption:l,setInputValue:r,closeDropdown:o}=e;if(n||l)return async(e,n,i,c)=>{const s=c?.option;if(!s||"object"==typeof s&&s.fixed)return;const m=n.filter((e=>"string"!=typeof e));switch(i){case"removeOption":a(m,"removeOption",s);break;case"selectOption":{const e=s;if("create"===e._action){const t=e.value;return l?.(t)}a(m,"selectOption",e);break}case"createOption":{const e=s,n=t.find((t=>t.label.toLocaleLowerCase()===e.toLocaleLowerCase()));if(!n)return l?.(e);m.push(n),a(m,"selectOption",n);break}}r(""),o()};function a(e,t,l){n?.(e.map((e=>j(e))),t,j(l))}}({options:x,onSelect:r,createOption:h,setInputValue:p,closeDropdown:_}),C=function(e){const{options:t,selected:n,onCreate:l,entityName:r}=e,o=(0,s.createFilterOptions)();return(e,a)=>{const i=n.map((e=>e.value)),c=o(e.filter((e=>!i.includes(e.value))),a),s=t.some((e=>a.inputValue===e.label));return Boolean(l)&&""!==a.inputValue&&!i.includes(a.inputValue)&&!s&&c.unshift({label:`Create "${a.inputValue}"`,value:a.inputValue,_group:`Create a new ${r?.singular??"option"}`,key:`create-${a.inputValue}`,_action:"create"}),c}}({options:t,selected:e,onCreate:a,entityName:n}),I=Boolean(a)||d.length<2||void 0;return l.createElement(s.Autocomplete,{renderTags:(e,t)=>e.map(((e,n)=>l.createElement(s.Chip,{size:"tiny",...t({index:n}),key:e.key??e.value??e.label,label:e.label}))),...m,ref:u,freeSolo:I,forcePopupIcon:!1,multiple:!0,clearOnBlur:!0,selectOnFocus:!0,disableClearable:!0,handleHomeEndKeys:!0,disabled:y,open:b,onOpen:v,onClose:_,disableCloseOnSelect:!0,value:w,options:x,ListboxComponent:E?l.forwardRef(((e,t)=>l.createElement(U,{ref:t,error:E}))):void 0,renderGroup:e=>l.createElement($,{...e}),inputValue:d,renderInput:e=>l.createElement(s.TextField,{...e,error:Boolean(E),placeholder:o,...g,sx:e=>({".MuiAutocomplete-inputRoot.MuiInputBase-adornedStart":{paddingLeft:e.spacing(.25),paddingRight:e.spacing(.25)}})}),onChange:S,getOptionLabel:e=>"string"==typeof e?e:e.label,getOptionKey:e=>"string"==typeof e?e:e.key??e.value??e.label,filterOptions:C,groupBy:e=>e._group??"",renderOption:(e,t)=>{const{_group:n,label:r}=t;return l.createElement("li",{...e,style:{display:"block",textOverflow:"ellipsis"},"data-group":n},r)},noOptionsText:c?.({searchValue:d,onClear:()=>{p(""),_()}}),isOptionEqualToValue:(e,t)=>"string"==typeof e?e===t:e.value===t.value})})),$=e=>{const t=`combobox-group-${(0,l.useId)().replace(/:/g,"_")}`;return l.createElement(N,{role:"group","aria-labelledby":t},l.createElement(K,{id:t}," ",e.group),l.createElement(H,{role:"listbox"},e.children))},U=l.forwardRef((({error:e="error"},t)=>l.createElement(s.Box,{ref:t,sx:e=>({padding:e.spacing(2)})},l.createElement(s.Typography,{variant:"caption",sx:{color:"error.main",display:"inline-block"}},e)))),N=(0,s.styled)("li")`
	&:not( :last-of-type ) {
		border-bottom: 1px solid ${({theme:e})=>e.palette.divider};
	}
`,K=(0,s.styled)(s.Box)((({theme:e})=>({position:"sticky",top:"-8px",padding:e.spacing(1,2),color:e.palette.text.tertiary,backgroundColor:e.palette.primary.contrastText}))),H=(0,s.styled)("ul")`
	padding: 0;
`,J=(0,l.createContext)(null),Y=()=>{const e=(0,l.useContext)(J);if(!e)throw new Error("useCssClass must be used within a CssClassProvider");return e};function X({children:e,...t}){return l.createElement(J.Provider,{value:t},e)}var q=(0,s.styled)("div",{shouldForwardProp:e=>"variant"!==e})`
	width: 5px;
	height: 5px;
	border-radius: 50%;
	background-color: ${({theme:e,variant:t})=>{switch(t){case"overridden":return e.palette.warning.light;case"global":return e.palette.global.dark;case"local":return e.palette.accent.main;default:return e.palette.text.disabled}}};
`,Z={V_3_30:"e_v_3_30"};function Q(){const{id:e,setId:t}=O(),{element:n}=L(),o=(0,E.isExperimentActive)(Z.V_3_30),a=ee(),i=te(),c=(0,l.useMemo)((()=>(0,E.undoable)({do:({classId:t})=>{const n=e;return i(t),n},undo:({classId:e},n)=>{a(e),t(n)}},{title:(0,r.getElementLabel)(n.id),subtitle:({classLabel:e})=>(0,m.__)("class %s removed","elementor").replace("%s",e)})),[e,a,n.id,i,t]),s=(0,l.useCallback)((({classId:e})=>{i(e)}),[i]);return o?c:s}function ee(){const{element:e}=L(),{setId:t}=O(),{setClasses:n,getAppliedClasses:r}=ne();return(0,l.useCallback)((l=>{const o=r();if(o.includes(l))throw new Error(`Class ${l} is already applied to element ${e.id}, cannot re-apply.`);const a=[...o,l];n(a),t(l)}),[e.id,r,t,n])}function te(){const{element:e}=L(),{id:t,setId:n}=O(),{setClasses:r,getAppliedClasses:o}=ne();return(0,l.useCallback)((l=>{const a=o();if(!a.includes(l))throw new Error(`Class ${l} is not applied to element ${e.id}, cannot unapply it.`);const i=a.filter((e=>e!==l));r(i),t===l&&n(i[0]??null)}),[t,e.id,o,n,r])}function ne(){const{element:e}=L(),t=z(),n=(0,E.isExperimentActive)(Z.V_3_30);return(0,l.useMemo)((()=>({setClasses:l=>{(0,r.updateElementSettings)({id:e.id,props:{[t]:p.classesPropTypeUtil.create(l)},withHistory:!n}),n&&(0,d.setDocumentModifiedStatus)(!0)},getAppliedClasses:()=>(0,r.getElementSetting)(e.id,t)?.value||[]})),[t,e.id,n])}var le=[{key:"normal",value:null},{key:"hover",value:"hover"},{key:"focus",value:"focus"},{key:"active",value:"active"}];function re({popupState:e,anchorEl:t,fixed:n}){const{provider:r}=Y();return l.createElement(s.Menu,{MenuListProps:{dense:!0,sx:{minWidth:"160px"}},...(0,s.bindMenu)(e),anchorEl:t,anchorOrigin:{vertical:"bottom",horizontal:"left"},transformOrigin:{horizontal:"left",vertical:-4},onKeyDown:e=>{e.stopPropagation()},disableAutoFocusItem:!0},function({provider:e,closeMenu:t,fixed:n}){if(!e)return[];const r=o.stylesRepository.getProviderByKey(e),a=r?.actions,i=a?.update,c=!n,m=[i&&l.createElement(ie,{key:"rename-class",closeMenu:t}),c&&l.createElement(ae,{key:"unapply-class",closeMenu:t})].filter(Boolean);return m.length&&(m.unshift(l.createElement(s.MenuSubheader,{key:"provider-label",sx:{typography:"caption",color:"text.secondary",pb:.5,pt:1,textTransform:"capitalize"}},r?.labels?.singular)),m.push(l.createElement(s.Divider,{key:"provider-actions-divider"}))),m}({provider:r,closeMenu:e.close,fixed:n}),l.createElement(s.MenuSubheader,{sx:{typography:"caption",color:"text.secondary",pb:.5,pt:1}},(0,m.__)("States","elementor")),le.map((t=>l.createElement(oe,{key:t.key,state:t.value,closeMenu:e.close}))))}function oe({state:e,closeMenu:t,...n}){const{id:r,provider:i}=Y(),{id:c,setId:u,setMetaState:d,meta:p}=O(),{state:E}=p,{userCan:f}=(0,o.useUserStylesCapability)(),g=function(e){const{meta:t}=O(),n=o.stylesRepository.all().find((t=>t.id===e));return Object.fromEntries(n?.variants.filter((e=>t.breakpoint===e.meta.breakpoint)).map((e=>[e.meta.state??"normal",!0]))??[])}(r),b=!e||f(i??"").updateProps,v=!i||(0,o.isElementsStylesProvider)(i)?"local":"global",_=g[e??"normal"]??!1,h=!b&&!_,y=r===c,x=e===E&&y;return l.createElement(a.MenuListItem,{...n,selected:x,disabled:h,sx:{textTransform:"capitalize"},onClick:()=>{y||u(r),d(e),t()}},l.createElement(a.MenuItemInfotip,{showInfoTip:h,content:(0,m.__)("With your current role, you can only use existing states.","elementor")},l.createElement(s.Stack,{gap:.75,direction:"row",alignItems:"center"},_&&l.createElement(q,{"aria-label":(0,m.__)("Has style","elementor"),variant:v}),e??"normal")))}function ae({closeMenu:e,...t}){const{id:n,label:r}=Y(),o=Q();return n?l.createElement(a.MenuListItem,{...t,onClick:()=>{o({classId:n,classLabel:r}),e()}},(0,m.__)("Remove","elementor")):null}function ie({closeMenu:e}){const{handleRename:t,provider:n}=Y(),{userCan:r}=(0,o.useUserStylesCapability)();if(!n)return null;const i=r(n).update;return l.createElement(a.MenuListItem,{disabled:!i,onClick:()=>{e(),t()}},l.createElement(a.MenuItemInfotip,{showInfoTip:!i,content:(0,m.__)("With your current role, you can use existing classes but can’t modify them.","elementor")},(0,m.__)("Rename","elementor")))}var ce="tiny";function se(e){const{chipProps:t,icon:n,color:r,fixed:c,...u}=e,{id:d,provider:p,label:E,isActive:f,onClickActive:g,renameLabel:b,setError:v}=u,{meta:_,setMetaState:h}=O(),y=(0,s.usePopupState)({variant:"popover"}),[x,w]=(0,l.useState)(null),{onDelete:S,...C}=t,{userCan:I}=(0,o.useUserStylesCapability)(),{ref:T,isEditing:k,openEditMode:z,error:P,getProps:G}=(0,a.useEditable)({value:E,onSubmit:b,validation:me,onError:v}),L=P?"error":r,R=p?o.stylesRepository.getProviderByKey(p)?.actions:null,B=Boolean(R?.update)&&I(p??"")?.update,V=f&&_.state;return l.createElement(l.Fragment,null,l.createElement(s.UnstableChipGroup,{ref:w,...C,"aria-label":`Edit ${E}`,role:"group",sx:e=>({"&.MuiChipGroup-root.MuiAutocomplete-tag":{margin:e.spacing(.125)}})},l.createElement(s.Chip,{size:ce,label:k?l.createElement(a.EditableField,{ref:T,...G()}):l.createElement(a.EllipsisWithTooltip,{maxWidth:"10ch",title:E,as:"div"}),variant:!f||_.state||k?"standard":"filled",shape:"rounded",icon:n,color:L,onClick:()=>{V?h(null):B&&f?z():g(d)},"aria-pressed":f,sx:e=>({lineHeight:1,cursor:f&&B&&!V?"text":"pointer",borderRadius:.75*e.shape.borderRadius+"px","&.Mui-focusVisible":{boxShadow:"none !important"}})}),!k&&l.createElement(s.Chip,{icon:V?void 0:l.createElement(i.DotsVerticalIcon,{fontSize:"tiny"}),size:ce,label:V?l.createElement(s.Stack,{direction:"row",gap:.5,alignItems:"center"},l.createElement(s.Typography,{variant:"inherit"},_.state),l.createElement(i.DotsVerticalIcon,{fontSize:"tiny"})):void 0,variant:"filled",shape:"rounded",color:L,...(0,s.bindTrigger)(y),"aria-label":(0,m.__)("Open CSS Class Menu","elementor"),sx:e=>({borderRadius:.75*e.shape.borderRadius+"px",paddingRight:0,...V?{}:{paddingLeft:0},".MuiChip-label":V?{paddingRight:0}:{padding:0}})})),l.createElement(X,{...u,handleRename:z},l.createElement(re,{popupState:y,anchorEl:x,fixed:c})))}var me=e=>{const t=(0,o.validateStyleLabel)(e,"rename");return t.isValid?null:t.errorMessage},ue="elementor-css-class-selector",de={label:(0,m.__)("local","elementor"),value:null,fixed:!0,color:"accent",icon:l.createElement(i.MapPinIcon,null),provider:null},{Slot:pe,inject:Ee}=(0,c.createLocation)();function fe(){const e=function(){const{element:e}=L();return(0,o.useProviders)().filter((e=>!!e.actions.updateProps)).flatMap((t=>{const n=(0,o.isElementsStylesProvider)(t.getKey()),r=t.actions.all({elementId:e.id});return n&&0===r.length?[de]:r.map((e=>({label:e.label,value:e.id,fixed:n,color:n?"accent":"global",icon:n?l.createElement(i.MapPinIcon,null):null,provider:t.getKey()})))}))}(),{id:t,setId:n}=O(),c=(0,l.useRef)(null),[u,d]=(0,l.useState)(null),p=function(){const e=function(){const{id:e,setId:t}=O(),{element:n}=L(),o=(0,E.isExperimentActive)(Z.V_3_30),a=ee(),i=te(),c=(0,l.useMemo)((()=>(0,E.undoable)({do:({classId:t})=>{const n=e;return a(t),n},undo:({classId:e},n)=>{i(e),t(n)}},{title:(0,r.getElementLabel)(n.id),subtitle:({classLabel:e})=>(0,m.__)("class %s applied","elementor").replace("%s",e)})),[e,a,n.id,i,t]),s=(0,l.useCallback)((({classId:e})=>{a(e)}),[a]);return o?c:s}(),t=Q();return(n,l,r)=>{if(r.value)switch(l){case"selectOption":e({classId:r.value,classLabel:r.label});break;case"removeOption":t({classId:r.value,classLabel:r.label})}}}(),{create:f,validate:g,entityName:b}=function(){const[e,t]=function(){const{id:e,setId:t}=O(),n=(0,E.isExperimentActive)(Z.V_3_30),[r,a]=(0,o.useGetStylesRepositoryCreateAction)()??[null,null],i=r?.actions.delete,c=ee(),s=te(),u=(0,l.useMemo)((()=>{if(r&&a)return(0,E.undoable)({do:({classLabel:t})=>{const n=e,l=a(t);return c(l),{prevActiveId:n,createdId:l}},undo:(e,{prevActiveId:n,createdId:l})=>{s(l),i?.(l),t(n)}},{title:(0,m.__)("Class","elementor"),subtitle:({classLabel:e})=>(0,m.__)("%s created","elementor").replace("%s",e)})}),[e,c,a,i,r,t,s]),d=(0,l.useCallback)((({classLabel:e})=>{if(!a)return;const t=a(e);c(t)}),[c,a]);return r&&u?n?[r,u]:[r,d]:[null,null]}();if(!e||!t)return{};return{create:e=>{t({classLabel:e})},validate:(t,n)=>function(e){return e.actions.all().length>=e.limit}(e)?{isValid:!1,errorMessage:(0,m.__)("You’ve reached the limit of 50 classes. Please remove an existing one to create a new class.","elementor")}:(0,o.validateStyleLabel)(t,n),entityName:e.labels.singular&&e.labels.plural?e.labels:void 0}}(),v=function(e){const{element:t}=L(),n=z(),l=(0,r.useElementSetting)(t.id,n)?.value||[],a=e.filter((e=>e.value&&l.includes(e.value)));return a.some((e=>e.provider&&(0,o.isElementsStylesProvider)(e.provider)))||a.unshift(de),a}(e),_=v.find((e=>e.value===t))??de,h=v.every((({fixed:e})=>e)),{userCan:y}=(0,o.useUserStylesCapability)(),x=!_.provider||y(_.provider).updateProps;return l.createElement(s.Stack,{p:2},l.createElement(s.Stack,{direction:"row",gap:1,alignItems:"center",justifyContent:"space-between"},l.createElement(s.FormLabel,{htmlFor:ue,size:"small"},(0,m.__)("Classes","elementor")),l.createElement(s.Stack,{direction:"row",gap:1},l.createElement(pe,null))),l.createElement(a.WarningInfotip,{open:Boolean(u),text:u??"",placement:"bottom",width:c.current?.getBoundingClientRect().width,offset:[0,-15]},l.createElement(F,{id:ue,ref:c,size:"tiny",placeholder:h?(0,m.__)("Type class name","elementor"):void 0,options:e,selected:v,entityName:b,onSelect:p,onCreate:f??void 0,validate:g??void 0,limitTags:50,renderEmptyState:ge,getLimitTagsText:e=>l.createElement(s.Chip,{size:"tiny",variant:"standard",label:`+${e}`,clickable:!0}),renderTags:(e,t)=>e.map(((e,r)=>{const o=t({index:r}),a=e.value===_?.value;return l.createElement(se,{key:o.key,fixed:e.fixed,label:e.label,provider:e.provider,id:e.value,isActive:a,color:a&&e.color?e.color:"default",icon:e.icon,chipProps:o,onClickActive:()=>n(e.value),renameLabel:t=>{if(!e.value)throw new Error("Cannot rename a class without style id");return be(e.provider,{label:t,id:e.value})},setError:d})}))})),!x&&l.createElement(a.InfoAlert,{content:(0,m.__)("With your current role, you can use existing classes but can’t modify them.","elementor"),sx:{mt:1}}))}var ge=({searchValue:e,onClear:t})=>l.createElement(s.Box,{sx:{py:4}},l.createElement(s.Stack,{gap:1,alignItems:"center",color:"text.secondary",justifyContent:"center",sx:{px:2,m:"auto",maxWidth:"236px"}},l.createElement(i.ColorSwatchIcon,{sx:{transform:"rotate(90deg)"},fontSize:"large"}),l.createElement(s.Typography,{align:"center",variant:"subtitle2"},(0,m.__)("Sorry, nothing matched","elementor"),l.createElement("br",null),"“",e,"”."),l.createElement(s.Typography,{align:"center",variant:"caption",sx:{mb:2}},(0,m.__)("With your current role,","elementor"),l.createElement("br",null),(0,m.__)("you can only use existing classes.","elementor")),l.createElement(s.Link,{color:"text.secondary",variant:"caption",component:"button",onClick:t},(0,m.__)("Clear & try again","elementor")))),be=(e,t)=>{if(!e)return;const n=o.stylesRepository.getProviderByKey(e);return n?n.actions.update?.(t):void 0},ve="tiny",_e="tiny",he=(0,b.createMenu)({components:{Action:function({title:e,visible:t=!0,icon:n,onClick:r}){return t?l.createElement(s.Tooltip,{placement:"bottom",title:e,arrow:!0},l.createElement(s.IconButton,{"aria-label":e,size:ve,onClick:r},l.createElement(n,{fontSize:ve}))):null},PopoverAction:function({title:e,visible:t=!0,icon:n,popoverContent:r}){const o=(0,l.useId)(),a=(0,s.usePopupState)({variant:"popover",popupId:`elementor-popover-action-${o}`});return t?l.createElement(l.Fragment,null,l.createElement(s.Tooltip,{placement:"top",title:e},l.createElement(s.IconButton,{"aria-label":e,key:o,size:_e,...(0,s.bindToggle)(a)},l.createElement(n,{fontSize:_e}))),l.createElement(s.Popover,{disablePortal:!0,disableScrollLock:!0,anchorOrigin:{vertical:"bottom",horizontal:"center"},...(0,s.bindPopover)(a)},l.createElement(s.Stack,{direction:"row",alignItems:"center",pl:1.5,pr:.5,py:1.5},l.createElement(n,{fontSize:_e,sx:{mr:.5}}),l.createElement(s.Typography,{variant:"subtitle2"},e),l.createElement(s.IconButton,{sx:{ml:"auto"},size:_e,onClick:a.close},l.createElement(i.XIcon,{fontSize:_e}))),l.createElement(r,{closePopover:a.close}))):null}}});function ye(){return l.createElement(s.Box,{role:"alert",sx:{minHeight:"100%",p:2}},l.createElement(s.Alert,{severity:"error",sx:{mb:2,maxWidth:400,textAlign:"center"}},l.createElement("strong",null,"Something went wrong")))}var xe=(0,l.createContext)(void 0),we=(0,s.styled)("div")`
	height: 100%;
	overflow-y: auto;
`;function Se({children:e}){const[t,n]=(0,l.useState)("up"),r=(0,l.useRef)(null),o=(0,l.useRef)(0);return(0,l.useEffect)((()=>{const e=r.current;if(!e)return;const t=()=>{const{scrollTop:t}=e;t>o.current?n("down"):t<o.current&&n("up"),o.current=t};return e.addEventListener("scroll",t),()=>{e.removeEventListener("scroll",t)}})),l.createElement(xe.Provider,{value:{direction:t}},l.createElement(we,{ref:r},e))}var Ce={defaultSectionsExpanded:{settings:["Content","Settings"],style:[]},defaultTab:"settings"},Ie=(0,l.createContext)({"e-div-block":{defaultSectionsExpanded:Ce.defaultSectionsExpanded,defaultTab:"style"},"e-flexbox":{defaultSectionsExpanded:Ce.defaultSectionsExpanded,defaultTab:"style"}}),Te=()=>{const{element:e}=L();return(0,l.useContext)(Ie)[e.type]||Ce},ke=(e,t)=>{const{element:n}=L(),r=(0,E.isExperimentActive)(Z.V_3_30),o=`elementor/editor-state/${n.id}/${e}`,a=r?(0,g.getSessionStorageItem)(o):t,[i,c]=(0,l.useState)(a??t);return[i,e=>{(0,g.setSessionStorageItem)(o,e),c(e)}]},ze={image:{component:n.ImageControl,layout:"full"},"svg-media":{component:n.SvgMediaControl,layout:"full"},text:{component:n.TextControl,layout:"full"},textarea:{component:n.TextAreaControl,layout:"full"},size:{component:n.SizeControl,layout:"two-columns"},select:{component:n.SelectControl,layout:"two-columns"},link:{component:n.LinkControl,layout:"full"},url:{component:n.UrlControl,layout:"full"},switch:{component:n.SwitchControl,layout:"two-columns"}},Pe=e=>ze[e]?.component,Ge=({props:e,type:t})=>{const n=Pe(t),{element:r}=L();if(!n)throw new R({context:{controlType:t}});return l.createElement(n,{...e,context:{elementId:r.id}})},Le=({children:e,layout:t})=>l.createElement(Re,{layout:t},e),Re=(0,s.styled)(s.Box,{shouldForwardProp:e=>!["layout"].includes(e)})((({layout:e,theme:t})=>({display:"grid",gridGap:t.spacing(1),...Be(e)}))),Be=e=>({justifyContent:"space-between",gridTemplateColumns:{full:"minmax(0, 1fr)","two-columns":"repeat(2, minmax(0, 1fr))"}[e]}),Ve=({schema:e})=>({key:"",kind:"object",meta:{},settings:{},default:null,shape:e}),Ae=({bind:e,children:t})=>{const{element:o,elementType:a}=L(),i=(0,r.useElementSetting)(o.id,e),c={[e]:i},s=Ve({schema:a.propsSchema});return l.createElement(n.PropProvider,{propType:s,value:c,setValue:e=>{(0,r.updateElementSettings)({id:o.id,props:{...e}})}},l.createElement(n.PropKeyProvider,{bind:e},t))},De=(0,s.styled)(i.ChevronDownIcon,{shouldForwardProp:e=>"open"!==e})((({theme:e,open:t})=>({transform:t?"rotate(180deg)":"rotate(0deg)",transition:e.transitions.create("transform",{duration:e.transitions.duration.standard})})));function Me({title:e,children:t,defaultExpanded:n=!1}){const[r,o]=ke(e,!!n),a=(0,l.useId)(),i=`label-${a}`,c=`content-${a}`;return l.createElement(l.Fragment,null,l.createElement(s.ListItemButton,{id:i,"aria-controls":c,onClick:()=>o(!r),sx:{"&:hover":{backgroundColor:"transparent"}}},l.createElement(s.ListItemText,{secondary:e,secondaryTypographyProps:{color:"text.primary",variant:"caption",fontWeight:"bold"}}),l.createElement(De,{open:r,color:"secondary",fontSize:"tiny"})),l.createElement(s.Collapse,{id:c,"aria-labelledby":i,in:r,timeout:"auto",unmountOnExit:!0},l.createElement(s.Stack,{gap:2.5,p:2},t)),l.createElement(s.Divider,null))}function Oe(e){return l.createElement(s.List,{disablePadding:!0,component:"div",...e})}var We=()=>{const{elementType:e,element:t}=L(),n=Te();return l.createElement(g.SessionStorageProvider,{prefix:t.id},l.createElement(Oe,null,e.controls.map((({type:e,value:t},r)=>{return"control"===e?l.createElement(je,{key:t.bind,control:t}):"section"===e?l.createElement(Me,{title:t.label,key:e+"."+r,defaultExpanded:(o=t.label,!(0,E.isExperimentActive)(Z.V_3_30)||n.defaultSectionsExpanded.settings?.includes(o))},t.items?.map((e=>"control"===e.type?l.createElement(je,{key:e.value.bind,control:e.value}):null))):null;var o}))))},je=({control:e})=>{if(!Pe(e.type))return null;const t=e.meta?.layout||(r=e.type,ze[r].layout);var r;return l.createElement(Ae,{bind:e.bind},e.meta?.topDivider&&l.createElement(s.Divider,null),l.createElement(Le,{layout:t},e.label?l.createElement(n.ControlFormLabel,null,e.label):null,l.createElement(Ge,{type:e.type,props:e.props})))},Fe=()=>{const{provider:e}=O(),[,t]=(0,l.useReducer)((e=>!e),!1);(0,l.useEffect)((()=>e?.subscribe(t)),[e])},$e="normal",Ue=e=>e??$e,Ne=e=>e??"desktop";function Ke(e,t){const n=function(e){const t={},n=(e,l)=>{const{id:r,children:o}=e;t[r]=l?[...l]:[],o?.forEach((e=>{n(e,[...t[r]??[],r])}))};return n(e),t}(t),l={};return t=>{const{breakpoint:r,state:o}=t,a=Ue(o),i=Ne(r);if(l[i]?.[a])return l[i][a].snapshot;const c=[...n[i],r];return c.forEach(((t,n)=>{const r=n>0?c[n-1]:null;((t,n,r)=>{const o=Ne(t),a=Ue(r);l[o]||(l[o]={[$e]:He(e({breakpoint:t,state:null}),n,{},null)}),r&&!l[o][a]&&(l[o][a]=He(e({breakpoint:t,state:r}),n,l[o],r))})(t,r?l[r]:void 0,o)})),l[i]?.[a]?.snapshot}}function He(e,t,n,l){const r=function(e){const t={};return e.forEach((e=>{const{variant:{props:n}}=e;Object.entries(n).forEach((([n,l])=>{const r=(0,p.filterEmptyValues)(l);if(null===r)return;t[n]||(t[n]=[]);const o={...e,value:r};t[n].push(o)}))})),{snapshot:t,stateSpecificSnapshot:t}}(e);return l?{snapshot:Je([r.snapshot,t?.[l]?.stateSpecificSnapshot,n[$e]?.snapshot]),stateSpecificSnapshot:Je([r.stateSpecificSnapshot,t?.[l]?.stateSpecificSnapshot])}:{snapshot:Je([r.snapshot,t?.[$e]?.snapshot]),stateSpecificSnapshot:void 0}}function Je(e){const t={};return e.filter(Boolean).forEach((e=>Object.entries(e).forEach((([e,n])=>{t[e]||(t[e]=[]),t[e]=t[e].concat(n)})))),t}function Ye(e,t,n){return e&&"object"==typeof e?function(e,t){return!!e&&(0,p.isTransformable)(t)&&e.key!==t.$$type}(n,e)?e:t.reduce(((e,t)=>e?(0,p.isTransformable)(e)?e.value?.[t]??null:"object"==typeof e?e[t]??null:null:null),e):null}var Xe=(e,t)=>e&&"union"===e.kind?Object.values(e.prop_types).find((e=>!!t.reduce(((e,t)=>{if("object"!==e?.kind)return null;const{shape:n}=e;return n[t]?n[t]:null}),e)))??null:null,qe=(0,l.createContext)(null);function Ze({children:e}){const t=et(),n=(0,v.getBreakpointsTree)(),{getSnapshot:r,getInheritanceChain:o}=function(e,t){const n=function(e){const t={};return e.forEach((e=>{const n=W(e.id)?.getKey()??null;e.variants.forEach((l=>{const{meta:r}=l,{state:o,breakpoint:a}=r,i=Ne(a),c=Ue(o);t[i]||(t[i]={});const s=t[i];s[c]||(s[c]=[]),s[c].push({style:e,variant:l,provider:n})}))})),t}(e);return{getSnapshot:Ke((({breakpoint:e,state:t})=>n?.[Ne(e)]?.[Ue(t)]??[]),t),getInheritanceChain:(e,t,n)=>{const[l,...r]=t;let o=e[l]??[];if(r.length>0){const e=Xe(n,r);o=o.map((({value:t,...n})=>({...n,value:Ye(t,r,e)}))).filter((({value:e})=>!(0,p.isEmpty)(e)))}return o}}}(t,n);return l.createElement(qe.Provider,{value:{getSnapshot:r,getInheritanceChain:o}},e)}function Qe(e){const t=(0,l.useContext)(qe);if(!t)throw new Error("useStylesInheritanceChain must be used within a StyleInheritanceProvider");const n=(0,_.getStylesSchema)(),r=n?.[e[0]],o=function(){const e=(0,l.useContext)(qe),{meta:t}=O();if(!e)throw new Error("useStylesInheritanceSnapshot must be used within a StyleInheritanceProvider");return t?e.getSnapshot(t)??null:null}();return o?t.getInheritanceChain(o,e,r):[]}var et=()=>{const{element:e}=L(),t=z(),n=tt();Fe();const l=(0,r.useElementSetting)(e.id,t),a=p.classesPropTypeUtil.extract(l)??[];return o.stylesRepository.all().filter((e=>[...n,...a].includes(e.id)))},tt=()=>{const{elementType:e}=L(),t=(0,r.getWidgetsCache)(),n=t?.[e.key];return Object.keys(n?.base_styles??{})};function nt(e){const{element:t}=L(),{id:n,meta:a,provider:i}=O(),c=z(),s=(0,l.useMemo)((()=>(0,E.undoable)({do:({elementId:e,styleId:t,provider:n,meta:l,props:r})=>{if(!n.actions.updateProps)throw new V({context:{providerKey:n.getKey()}});const o=function(e,t){if(!e)return{};const n=(0,_.getVariantByMeta)(e,t);return structuredClone(n?.props??{})}(n.actions.get(t,{elementId:e}),l);return n.actions.updateProps({id:t,meta:l,props:r},{elementId:e}),o},undo:({elementId:e,styleId:t,meta:n,provider:l},r)=>{l.actions.updateProps?.({id:t,meta:n,props:r},{elementId:e})}},{title:({elementId:e})=>(0,r.getElementLabel)(e),subtitle:(0,m.__)("Style edited","elementor")})),[]),u=(0,l.useMemo)((()=>(0,E.undoable)({do:e=>(0,r.createElementStyle)({...e,label:o.ELEMENTS_STYLES_RESERVED_LABEL}),undo:({elementId:e},t)=>{(0,r.deleteElementStyle)(e,t)},redo:(e,t)=>(0,r.createElementStyle)({...e,styleId:t,label:o.ELEMENTS_STYLES_RESERVED_LABEL})},{title:({elementId:e})=>(0,r.getElementLabel)(e),subtitle:(0,m.__)("Style edited","elementor")})),[]);Fe();const d=function({styleId:e,elementId:t,provider:n,meta:l,propNames:r}){if(!n||!e)return null;const o=n.actions.get(e,{elementId:t});if(!o)throw new A({context:{styleId:e,providerKey:n.getKey()}});const a=(0,_.getVariantByMeta)(o,l);return Object.fromEntries(r.map((e=>[e,a?.props[e]??null])))}({elementId:t.id,styleId:n,provider:i,meta:a,propNames:e});return[d,e=>{null!==n?s({elementId:t.id,styleId:n,provider:i,meta:a,props:e}):u({elementId:t.id,classesProp:c,meta:a,props:e})}]}function lt(e){const[t,n]=nt([e]);return[t?.[e]??null,t=>{n({[e]:t})}]}var rt=new Set(["background-color-overlay","background-image-overlay","background-gradient-overlay","gradient-color-stop","color-stop","background-image-position-offset","background-image-size-scale","image-src","image","background-overlay"]),ot=()=>(0,E.isExperimentActive)("e_v_3_30"),at=(0,l.createContext)(null),it=({gap:e=2,sx:t,children:n})=>{const r=(0,l.useRef)(null);return l.createElement(at.Provider,{value:r},l.createElement(s.Stack,{gap:e,sx:{...t},ref:r},n))};function ct(){const e="rtl"===(0,s.useTheme)().direction;return{isSiteRtl:!!(()=>{const e=window;return e.elementorFrontend?.config??{}})()?.is_rtl,isUiRtl:e}}var st={widescreen:i.WidescreenIcon,desktop:i.DesktopIcon,laptop:i.LaptopIcon,tablet_extra:i.TabletLandscapeIcon,tablet:i.TabletPortraitIcon,mobile_extra:i.MobileLandscapeIcon,mobile:i.MobilePortraitIcon},mt=({breakpoint:e})=>{const t=(0,v.useBreakpoints)(),n=e||"desktop",r=st[n];if(!r)return null;const o=t.find((e=>e.id===n))?.label;return l.createElement(s.Tooltip,{title:o,placement:"top"},l.createElement(r,{fontSize:"tiny",sx:{mt:"2px"}}))},ut="tiny",dt=({displayLabel:e,provider:t,chipColor:n})=>{const r=t===o.ELEMENTS_BASE_STYLES_PROVIDER_KEY?l.createElement(s.Tooltip,{title:(0,m.__)("Inherited from base styles","elementor"),placement:"top"},l.createElement(i.InfoCircleIcon,{fontSize:ut})):void 0;return l.createElement(s.Chip,{label:e,size:ut,color:n,variant:"standard",state:"enabled",icon:r,sx:e=>({lineHeight:1,flexWrap:"nowrap",alignItems:"center",borderRadius:.75*e.shape.borderRadius+"px",flexDirection:"row-reverse",".MuiChip-label":{overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"}})})},pt=({index:e,value:t})=>l.createElement(s.Typography,{variant:"caption",color:"text.tertiary",sx:{mt:"1px",textDecoration:0===e?"none":"line-through",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"}},t),Et=()=>l.createElement(s.Box,{display:"flex",gap:.5,alignItems:"center"}),ft=async(e,t,n)=>{try{const r=await n({props:{[t]:e.value}}),o=r?.[t]??r;return(0,l.isValidElement)(o)?o:"object"==typeof o?JSON.stringify(o):String(o)}catch{return""}},gt=e=>{const{provider:t="",style:n}=e;return t===o.ELEMENTS_BASE_STYLES_PROVIDER_KEY?"default":"local"===n?.label?"accent":"global"},bt=(0,h.createTransformersRegistry)(),vt=({inheritanceChain:e,propType:t,path:n,label:r,children:a})=>{const[i,c]=(0,l.useState)(!1),u=()=>c(!1),d=n.join("."),p=(0,l.useContext)(at),E=p?.current?.offsetWidth??320,f=(0,l.useMemo)((()=>(0,h.createPropsResolver)({transformers:bt,schema:{[d]:t}})),[d,t]),g=((e,t,n)=>{const[r,a]=(0,l.useState)([]);return(0,l.useEffect)((()=>{(async()=>{const l=(await Promise.all(e.filter((({style:e})=>e)).map(((e,l)=>(async(e,t,n,l)=>{const{variant:{meta:{state:r,breakpoint:o}},style:{label:a,id:i}}=e,c=`${a}${r?":"+r:""}`;return{id:i?i+(r??""):t,provider:e.provider||"",breakpoint:o??"desktop",displayLabel:c,value:await ft(e,n,l),chipColor:gt(e)}})(e,l,t,n))))).map((e=>({...e,displayLabel:o.ELEMENTS_BASE_STYLES_PROVIDER_KEY!==e.provider?e.displayLabel:(0,m.__)("Base","elementor")}))).filter((e=>!e.value||""!==e.displayLabel)).slice(0,2);a(l)})()}),[e,t,n]),r})(e,d,f),b=l.createElement(s.ClickAwayListener,{onClickAway:u},l.createElement(s.Card,{elevation:0,sx:{width:`${E}px`,maxWidth:500,overflowX:"hidden"}},l.createElement(s.CardContent,{sx:{display:"flex",gap:.5,flexDirection:"column",p:0,"&:last-child":{pb:0}}},l.createElement(s.Stack,{direction:"row",alignItems:"center",sx:{pl:1.5,pr:.5,minHeight:36,py:.5}},l.createElement(s.Typography,{variant:"subtitle2",color:"secondary",sx:{fontSize:12,fontWeight:"500"}},(0,m.__)("Style origin","elementor")),l.createElement(s.CloseButton,{slotProps:{icon:{fontSize:"tiny"}},sx:{ml:"auto"},onClick:u})),l.createElement(s.Stack,{gap:1.5,sx:{pl:2,pr:1,pb:2,overflowX:"hidden",overflowY:"auto"},role:"list"},g.map(((e,t)=>l.createElement(s.Box,{key:e.id,display:"flex",gap:.5,role:"listitem","aria-label":(0,m.__)("Inheritance item: %s","elementor").replace("%s",e.displayLabel)},l.createElement(s.Box,{display:"flex",gap:.5,sx:{flexWrap:"wrap",width:"100%"}},l.createElement(mt,{breakpoint:e.breakpoint}),l.createElement(dt,{displayLabel:e.displayLabel,provider:e.provider,chipColor:e.chipColor}),l.createElement(pt,{index:t,value:e.value})),l.createElement(Et,null))))))));return l.createElement(_t,{showInfotip:i,onClose:u,infotipContent:b},l.createElement(s.IconButton,{onClick:()=>c((e=>!e)),"aria-label":r,sx:{my:"-1px"}},a))};function _t({children:e,showInfotip:t,onClose:n,infotipContent:r}){const{isSiteRtl:o}=ct(),a=o?9999999:-9999999;return t?l.createElement(l.Fragment,null,l.createElement(s.Backdrop,{open:t,onClick:n,sx:{backgroundColor:"transparent",zIndex:e=>e.zIndex.modal-1}}),l.createElement(s.Infotip,{placement:"top",content:r,open:t,onClose:n,disableHoverListener:!0,componentsProps:{tooltip:{sx:{mx:2}}},slotProps:{popper:{modifiers:[{name:"offset",options:{offset:[a,0]}}]}}},e)):l.createElement(s.Tooltip,{title:(0,m.__)("Style origin","elementor"),placement:"top"},e)}var ht=()=>{const{path:e,propType:t}=(0,n.useBoundProp)(),{id:r,provider:a,meta:i}=O(),c=(0,E.isExperimentActive)(Z.V_3_30)?e:e.slice(0,1),u=Qe(c);if(!u.length)return null;const d=u.find((({style:e,variant:{meta:{breakpoint:t,state:n}}})=>e.id===r&&t===i.breakpoint&&n===i.state)),f=!(0,p.isEmpty)(d?.value),[g]=u;if(g.provider===o.ELEMENTS_BASE_STYLES_PROVIDER_KEY)return null;const b=d===g,v=yt({isFinalValue:b,hasValue:f}),_=xt({isFinalValue:b,hasValue:f,currentStyleProvider:a});return ot()?l.createElement(vt,{inheritanceChain:u,path:c,propType:t,label:v},l.createElement(q,{variant:_})):l.createElement(s.Tooltip,{title:(0,m.__)("Style origin","elementor"),placement:"top"},l.createElement(q,{variant:_,"aria-label":v}))},yt=({isFinalValue:e,hasValue:t})=>e?(0,m.__)("This is the final value","elementor"):t?(0,m.__)("This value is overridden by another style","elementor"):(0,m.__)("This has value from another style","elementor"),xt=({isFinalValue:e,hasValue:t,currentStyleProvider:n})=>e?(0,o.isElementsStylesProvider)(n?.getKey?.())?"local":"global":t?"overridden":void 0,wt=({bind:e,placeholder:t,children:r})=>{const[o,a]=lt(e),{canEdit:i}=O(),c=(0,_.getStylesSchema)(),s=Ve({schema:c}),m={[e]:o},u={[e]:t};return l.createElement(n.ControlAdornmentsProvider,{items:[{id:"styles-inheritance",Adornment:ht}]},l.createElement(n.PropProvider,{propType:s,value:m,setValue:t=>{a(t[e])},placeholder:u,disabled:!i},l.createElement(n.PropKeyProvider,{bind:e},r)))},St=()=>l.createElement(it,null,l.createElement(wt,{bind:"background"},l.createElement(n.BackgroundControl,null))),Ct=()=>l.createElement(s.Divider,{sx:{my:.5}}),It=({children:e})=>l.createElement(s.Stack,{direction:"row",alignItems:"center",justifyItems:"start",gap:1},l.createElement(n.ControlFormLabel,null,e),l.createElement(n.ControlAdornments,null)),Tt="tiny",kt=({isAdded:e,label:t,onAdd:n,onRemove:r,children:o,disabled:a})=>l.createElement(it,null,l.createElement(s.Stack,{direction:"row",sx:{justifyContent:"space-between",alignItems:"center",marginInlineEnd:-.75}},l.createElement(It,null,t),e?l.createElement(s.IconButton,{size:Tt,onClick:r,"aria-label":"Remove",disabled:a},l.createElement(i.MinusIcon,{fontSize:Tt})):l.createElement(s.IconButton,{size:Tt,onClick:n,"aria-label":"Add",disabled:a},l.createElement(i.PlusIcon,{fontSize:Tt}))),l.createElement(s.Collapse,{in:e,unmountOnExit:!0},l.createElement(it,null,o))),zt=()=>l.createElement(wt,{bind:"border-color"},l.createElement(s.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},l.createElement(s.Grid,{item:!0,xs:6},l.createElement(It,null,(0,m.__)("Border color","elementor"))),l.createElement(s.Grid,{item:!0,xs:6},l.createElement(n.ColorControl,null)))),Pt=[{value:"none",label:(0,m.__)("None","elementor")},{value:"solid",label:(0,m.__)("Solid","elementor")},{value:"dashed",label:(0,m.__)("Dashed","elementor")},{value:"dotted",label:(0,m.__)("Dotted","elementor")},{value:"double",label:(0,m.__)("Double","elementor")},{value:"groove",label:(0,m.__)("Groove","elementor")},{value:"ridge",label:(0,m.__)("Ridge","elementor")},{value:"inset",label:(0,m.__)("Inset","elementor")},{value:"outset",label:(0,m.__)("Outset","elementor")}],Gt=()=>l.createElement(wt,{bind:"border-style"},l.createElement(s.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},l.createElement(s.Grid,{item:!0,xs:6},l.createElement(It,null,(0,m.__)("Border type","elementor"))),l.createElement(s.Grid,{item:!0,xs:6,sx:{overflow:"hidden"}},l.createElement(n.SelectControl,{options:Pt})))),Lt=(0,s.withDirection)(i.SideRightIcon),Rt=(0,s.withDirection)(i.SideLeftIcon),Bt=e=>[{label:(0,m.__)("Top","elementor"),icon:l.createElement(i.SideTopIcon,{fontSize:"tiny"}),bind:"block-start"},{label:e?(0,m.__)("Left","elementor"):(0,m.__)("Right","elementor"),icon:l.createElement(Lt,{fontSize:"tiny"}),bind:"inline-end"},{label:(0,m.__)("Bottom","elementor"),icon:l.createElement(i.SideBottomIcon,{fontSize:"tiny"}),bind:"block-end"},{label:e?(0,m.__)("Right","elementor"):(0,m.__)("Left","elementor"),icon:l.createElement(Rt,{fontSize:"tiny"}),bind:"inline-start"}],Vt=()=>{const{isSiteRtl:e}=ct();return l.createElement(wt,{bind:"border-width"},l.createElement(n.EqualUnequalSizesControl,{items:Bt(e),label:(0,m.__)("Border width","elementor"),icon:l.createElement(i.SideAllIcon,{fontSize:"tiny"}),tooltipLabel:(0,m.__)("Adjust borders","elementor"),multiSizePropTypeUtil:p.borderWidthPropTypeUtil}))},At={"border-width":{$$type:"size",value:{size:1,unit:"px"}},"border-color":{$$type:"color",value:"#000000"},"border-style":{$$type:"string",value:"solid"}},Dt=()=>{const{canEdit:e}=O(),[t,n]=nt(Object.keys(At)),r=Object.values(t??{}).some(Boolean);return l.createElement(kt,{label:(0,m.__)("Border","elementor"),isAdded:r,onAdd:()=>{n(At)},onRemove:()=>{n({"border-width":null,"border-color":null,"border-style":null})},disabled:!e},l.createElement(Vt,null),l.createElement(zt,null),l.createElement(Gt,null))},Mt=(0,s.withDirection)(i.RadiusTopLeftIcon),Ot=(0,s.withDirection)(i.RadiusTopRightIcon),Wt=(0,s.withDirection)(i.RadiusBottomLeftIcon),jt=(0,s.withDirection)(i.RadiusBottomRightIcon),Ft=e=>e?(0,m.__)("Top right","elementor"):(0,m.__)("Top left","elementor"),$t=e=>e?(0,m.__)("Top left","elementor"):(0,m.__)("Top right","elementor"),Ut=e=>e?(0,m.__)("Bottom right","elementor"):(0,m.__)("Bottom left","elementor"),Nt=e=>e?(0,m.__)("Bottom left","elementor"):(0,m.__)("Bottom right","elementor"),Kt=e=>[{label:Ft(e),icon:l.createElement(Mt,{fontSize:"tiny"}),bind:"start-start"},{label:$t(e),icon:l.createElement(Ot,{fontSize:"tiny"}),bind:"start-end"},{label:Ut(e),icon:l.createElement(Wt,{fontSize:"tiny"}),bind:"end-start"},{label:Nt(e),icon:l.createElement(jt,{fontSize:"tiny"}),bind:"end-end"}],Ht=()=>{const{isSiteRtl:e}=ct();return l.createElement(wt,{bind:"border-radius"},l.createElement(n.EqualUnequalSizesControl,{items:Kt(e),label:(0,m.__)("Border radius","elementor"),icon:l.createElement(i.BorderCornersIcon,{fontSize:"tiny"}),tooltipLabel:(0,m.__)("Adjust corners","elementor"),multiSizePropTypeUtil:p.borderRadiusPropTypeUtil}))},Jt=()=>l.createElement(it,null,l.createElement(Ht,null),l.createElement(Ct,null),l.createElement(Dt,null)),Yt=()=>l.createElement(it,null,l.createElement(wt,{bind:"box-shadow"},l.createElement(n.BoxShadowRepeaterControl,null))),Xt={row:0,column:90,"row-reverse":180,"column-reverse":270},qt={row:0,column:-90,"row-reverse":-180,"column-reverse":-270},Zt=({icon:e,size:t,isClockwise:n=!0,offset:r=0,disableRotationForReversed:o=!1})=>{const a=(0,l.useRef)(Qt(n,r,o));return a.current=Qt(n,r,o,a),l.createElement(e,{fontSize:t,sx:{transition:".3s",rotate:`${a.current}deg`}})},Qt=(e,t,n,l)=>{const[r]=lt("flex-direction"),o="rtl"===(0,s.useTheme)().direction?-1:1,a=e?Xt:qt,i=r?.value||"row",c=l?l.current*o:a[i]+t,m=((a[i]+t-c+360)%360+180)%360-180;return n&&["row-reverse","column-reverse"].includes(i)?0:(c+m)*o},en=(0,s.withDirection)(i.JustifyTopIcon),tn=(0,s.withDirection)(i.JustifyBottomIcon),nn={isClockwise:!1,offset:0,disableRotationForReversed:!0},ln=[{value:"start",label:(0,m.__)("Start","elementor"),renderContent:({size:e})=>l.createElement(Zt,{icon:en,size:e,...nn}),showTooltip:!0},{value:"center",label:(0,m.__)("Center","elementor"),renderContent:({size:e})=>l.createElement(Zt,{icon:i.JustifyCenterIcon,size:e,...nn}),showTooltip:!0},{value:"end",label:(0,m.__)("End","elementor"),renderContent:({size:e})=>l.createElement(Zt,{icon:tn,size:e,...nn}),showTooltip:!0},{value:"space-between",label:(0,m.__)("Space between","elementor"),renderContent:({size:e})=>l.createElement(Zt,{icon:i.JustifySpaceBetweenVerticalIcon,size:e,...nn}),showTooltip:!0},{value:"space-around",label:(0,m.__)("Space around","elementor"),renderContent:({size:e})=>l.createElement(Zt,{icon:i.JustifySpaceAroundVerticalIcon,size:e,...nn}),showTooltip:!0},{value:"space-evenly",label:(0,m.__)("Space evenly","elementor"),renderContent:({size:e})=>l.createElement(Zt,{icon:i.JustifyDistributeVerticalIcon,size:e,...nn}),showTooltip:!0}],rn=()=>{const{isSiteRtl:e}=ct();return l.createElement(s.DirectionProvider,{rtl:e},l.createElement(s.ThemeProvider,null,l.createElement(wt,{bind:"align-content"},l.createElement(s.Stack,{gap:1},l.createElement(It,null,(0,m.__)("Align content","elementor")),l.createElement(n.ToggleControl,{options:ln,fullWidth:!0})))))},on=(0,s.withDirection)(i.LayoutAlignLeftIcon),an=(0,s.withDirection)(i.LayoutAlignRightIcon),cn={isClockwise:!1,offset:90},sn=[{value:"start",label:(0,m.__)("Start","elementor"),renderContent:({size:e})=>l.createElement(Zt,{icon:on,size:e,...cn}),showTooltip:!0},{value:"center",label:(0,m.__)("Center","elementor"),renderContent:({size:e})=>l.createElement(Zt,{icon:i.LayoutAlignCenterIcon,size:e,...cn}),showTooltip:!0},{value:"end",label:(0,m.__)("End","elementor"),renderContent:({size:e})=>l.createElement(Zt,{icon:an,size:e,...cn}),showTooltip:!0},{value:"stretch",label:(0,m.__)("Stretch","elementor"),renderContent:({size:e})=>l.createElement(Zt,{icon:i.LayoutDistributeVerticalIcon,size:e,...cn}),showTooltip:!0}],mn=()=>{const{isSiteRtl:e}=ct();return l.createElement(s.DirectionProvider,{rtl:e},l.createElement(s.ThemeProvider,null,l.createElement(wt,{bind:"align-items"},l.createElement(s.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},l.createElement(s.Grid,{item:!0,xs:6},l.createElement(It,null,(0,m.__)("Align items","elementor"))),l.createElement(s.Grid,{item:!0,xs:6,sx:{display:"flex",justifyContent:"end"}},l.createElement(n.ToggleControl,{options:sn}))))))},un={row:90,"row-reverse":90,column:0,"column-reverse":0},dn=(0,s.withDirection)(i.LayoutAlignLeftIcon),pn=(0,s.withDirection)(i.LayoutAlignRightIcon),En={isClockwise:!1},fn=e=>[{value:"start",label:(0,m.__)("Start","elementor"),renderContent:({size:t})=>l.createElement(Zt,{icon:dn,size:t,offset:un[e],...En}),showTooltip:!0},{value:"center",label:(0,m.__)("Center","elementor"),renderContent:({size:t})=>l.createElement(Zt,{icon:i.LayoutAlignCenterIcon,size:t,offset:un[e],...En}),showTooltip:!0},{value:"end",label:(0,m.__)("End","elementor"),renderContent:({size:t})=>l.createElement(Zt,{icon:pn,size:t,offset:un[e],...En}),showTooltip:!0},{value:"stretch",label:(0,m.__)("Stretch","elementor"),renderContent:({size:t})=>l.createElement(Zt,{icon:i.LayoutDistributeVerticalIcon,size:t,offset:un[e],...En}),showTooltip:!0}],gn=({parentStyleDirection:e})=>{const{isSiteRtl:t}=ct();return l.createElement(s.DirectionProvider,{rtl:t},l.createElement(s.ThemeProvider,null,l.createElement(wt,{bind:"align-self"},l.createElement(s.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},l.createElement(s.Grid,{item:!0,xs:6},l.createElement(It,null,(0,m.__)("Align self","elementor"))),l.createElement(s.Grid,{item:!0,xs:6,sx:{display:"flex",justifyContent:"flex-end"}},l.createElement(n.ToggleControl,{options:fn(e)}))))))},bn=[{value:"block",renderContent:()=>(0,m.__)("Block","elementor"),label:(0,m.__)("Block","elementor"),showTooltip:!0},{value:"flex",renderContent:()=>(0,m.__)("Flex","elementor"),label:(0,m.__)("Flex","elementor"),showTooltip:!0},{value:"inline-block",renderContent:()=>(0,m.__)("In-blk","elementor"),label:(0,m.__)("Inline-block","elementor"),showTooltip:!0}],vn=()=>{const e=(0,E.isExperimentActive)(Z.V_3_30),t=[...bn];e&&t.push({value:"none",renderContent:()=>(0,m.__)("None","elementor"),label:(0,m.__)("None","elementor"),showTooltip:!0}),t.push({value:"inline-flex",renderContent:()=>(0,m.__)("In-flx","elementor"),label:(0,m.__)("Inline-flex","elementor"),showTooltip:!0});const r=_n();return l.createElement(wt,{bind:"display",placeholder:r},l.createElement(s.Stack,{gap:.75},l.createElement(It,null,(0,m.__)("Display","elementor")),l.createElement(n.ToggleControl,{options:t,maxItems:4,fullWidth:!0})))},_n=()=>Qe(["display"])[0]?.value??void 0,hn=[{value:"row",label:(0,m.__)("Row","elementor"),renderContent:({size:e})=>{const t=(0,s.withDirection)(i.ArrowRightIcon);return l.createElement(t,{fontSize:e})},showTooltip:!0},{value:"column",label:(0,m.__)("Column","elementor"),renderContent:({size:e})=>l.createElement(i.ArrowDownSmallIcon,{fontSize:e}),showTooltip:!0},{value:"row-reverse",label:(0,m.__)("Reversed row","elementor"),renderContent:({size:e})=>{const t=(0,s.withDirection)(i.ArrowLeftIcon);return l.createElement(t,{fontSize:e})},showTooltip:!0},{value:"column-reverse",label:(0,m.__)("Reversed column","elementor"),renderContent:({size:e})=>l.createElement(i.ArrowUpSmallIcon,{fontSize:e}),showTooltip:!0}],yn=()=>{const{isSiteRtl:e}=ct();return l.createElement(s.DirectionProvider,{rtl:e},l.createElement(s.ThemeProvider,null,l.createElement(wt,{bind:"flex-direction"},l.createElement(s.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},l.createElement(s.Grid,{item:!0,xs:6},l.createElement(It,null,(0,m.__)("Direction","elementor"))),l.createElement(s.Grid,{item:!0,xs:6,sx:{display:"flex",justifyContent:"end"}},l.createElement(n.ToggleControl,{options:hn}))))))},xn=-99999,wn="first",Sn="last",Cn="custom",In={[wn]:xn,[Sn]:99999},Tn=[{value:wn,label:(0,m.__)("First","elementor"),renderContent:({size:e})=>l.createElement(i.ArrowUpSmallIcon,{fontSize:e}),showTooltip:!0},{value:Sn,label:(0,m.__)("Last","elementor"),renderContent:({size:e})=>l.createElement(i.ArrowDownSmallIcon,{fontSize:e}),showTooltip:!0},{value:Cn,label:(0,m.__)("Custom","elementor"),renderContent:({size:e})=>l.createElement(i.PencilIcon,{fontSize:e}),showTooltip:!0}],kn=()=>{const{isSiteRtl:e}=ct(),[t,r]=lt("order"),{canEdit:o}=O(),[a,i]=(0,l.useState)(zn(t?.value||null));return l.createElement(s.DirectionProvider,{rtl:e},l.createElement(s.ThemeProvider,null,l.createElement(wt,{bind:"order"},l.createElement(it,null,l.createElement(s.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},l.createElement(s.Grid,{item:!0,xs:6},l.createElement(It,null,(0,m.__)("Order","elementor"))),l.createElement(s.Grid,{item:!0,xs:6,sx:{display:"flex",justifyContent:"end"}},l.createElement(n.ControlToggleButtonGroup,{items:Tn,value:a,onChange:e=>{i(e),r(e&&e!==Cn?{$$type:"number",value:In[e]}:null)},exclusive:!0,disabled:!o}))),Cn===a&&l.createElement(s.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},l.createElement(s.Grid,{item:!0,xs:6},l.createElement(It,null,(0,m.__)("Custom order","elementor"))),l.createElement(s.Grid,{item:!0,xs:6,sx:{display:"flex",justifyContent:"end"}},l.createElement(n.NumberControl,{min:-99998,max:99998,shouldForceInt:!0})))))))},zn=e=>99999===e?Sn:xn===e?wn:0===e||e?Cn:null,Pn=[{value:"flex-grow",label:(0,m.__)("Grow","elementor"),renderContent:({size:e})=>l.createElement(i.ExpandIcon,{fontSize:e}),showTooltip:!0},{value:"flex-shrink",label:(0,m.__)("Shrink","elementor"),renderContent:({size:e})=>l.createElement(i.ShrinkIcon,{fontSize:e}),showTooltip:!0},{value:"custom",label:(0,m.__)("Custom","elementor"),renderContent:({size:e})=>l.createElement(i.PencilIcon,{fontSize:e}),showTooltip:!0}],Gn=()=>{const{isSiteRtl:e}=ct(),{canEdit:t}=O(),[r,o]=nt(["flex-grow","flex-shrink","flex-basis"]),a=r?.["flex-grow"]?.value||null,i=r?.["flex-shrink"]?.value||null,c=r?.["flex-basis"]?.value||null,u=(0,l.useMemo)((()=>Rn({grow:a,shrink:i,basis:c})),[a,i,c]),[d,E]=(0,l.useState)(u);return l.createElement(s.DirectionProvider,{rtl:e},l.createElement(s.ThemeProvider,null,l.createElement(it,null,l.createElement(s.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},l.createElement(wt,{bind:d??""},l.createElement(s.Grid,{item:!0,xs:6},l.createElement(It,null,(0,m.__)("Size","elementor"))),l.createElement(s.Grid,{item:!0,xs:6,sx:{display:"flex",justifyContent:"end"}},l.createElement(n.ControlToggleButtonGroup,{value:d,onChange:(e=null)=>{E(e),o(e&&"custom"!==e?"flex-grow"!==e?{"flex-basis":null,"flex-grow":null,"flex-shrink":p.numberPropTypeUtil.create(1)}:{"flex-basis":null,"flex-grow":p.numberPropTypeUtil.create(1),"flex-shrink":null}:{"flex-basis":null,"flex-grow":null,"flex-shrink":null})},disabled:!t,items:Pn,exclusive:!0})))),"custom"===d&&l.createElement(Ln,null))))},Ln=()=>l.createElement(l.Fragment,null,l.createElement(wt,{bind:"flex-grow"},l.createElement(s.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},l.createElement(s.Grid,{item:!0,xs:6},l.createElement(It,null,(0,m.__)("Grow","elementor"))),l.createElement(s.Grid,{item:!0,xs:6,sx:{display:"flex",justifyContent:"end"}},l.createElement(n.NumberControl,{min:0,shouldForceInt:!0})))),l.createElement(wt,{bind:"flex-shrink"},l.createElement(s.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},l.createElement(s.Grid,{item:!0,xs:6},l.createElement(It,null,(0,m.__)("Shrink","elementor"))),l.createElement(s.Grid,{item:!0,xs:6,sx:{display:"flex",justifyContent:"end"}},l.createElement(n.NumberControl,{min:0,shouldForceInt:!0})))),l.createElement(wt,{bind:"flex-basis"},l.createElement(s.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},l.createElement(s.Grid,{item:!0,xs:6},l.createElement(It,null,(0,m.__)("Basis","elementor"))),l.createElement(s.Grid,{item:!0,xs:6,sx:{display:"flex",justifyContent:"end"}},l.createElement(n.SizeControl,{extendedValues:["auto"]}))))),Rn=({grow:e,shrink:t,basis:n})=>null!==e||null!==t||n?t&&e||n?"custom":1===e?"flex-grow":1===t?"flex-shrink":"custom":null,Bn=()=>l.createElement(s.Stack,{gap:1},l.createElement(wt,{bind:"gap"},l.createElement(n.GapControl,{label:(0,m.__)("Gaps","elementor")}))),Vn=(0,s.withDirection)(i.JustifyTopIcon),An=(0,s.withDirection)(i.JustifyBottomIcon),Dn={isClockwise:!0,offset:-90},Mn=[{value:"flex-start",label:(0,m.__)("Start","elementor"),renderContent:({size:e})=>l.createElement(Zt,{icon:Vn,size:e,...Dn}),showTooltip:!0},{value:"center",label:(0,m.__)("Center","elementor"),renderContent:({size:e})=>l.createElement(Zt,{icon:i.JustifyCenterIcon,size:e,...Dn}),showTooltip:!0},{value:"flex-end",label:(0,m.__)("End","elementor"),renderContent:({size:e})=>l.createElement(Zt,{icon:An,size:e,...Dn}),showTooltip:!0},{value:"space-between",label:(0,m.__)("Space between","elementor"),renderContent:({size:e})=>l.createElement(Zt,{icon:i.JustifySpaceBetweenVerticalIcon,size:e,...Dn}),showTooltip:!0},{value:"space-around",label:(0,m.__)("Space around","elementor"),renderContent:({size:e})=>l.createElement(Zt,{icon:i.JustifySpaceAroundVerticalIcon,size:e,...Dn}),showTooltip:!0},{value:"space-evenly",label:(0,m.__)("Space evenly","elementor"),renderContent:({size:e})=>l.createElement(Zt,{icon:i.JustifyDistributeVerticalIcon,size:e,...Dn}),showTooltip:!0}],On=()=>{const{isSiteRtl:e}=ct();return l.createElement(s.DirectionProvider,{rtl:e},l.createElement(s.ThemeProvider,null,l.createElement(wt,{bind:"justify-content"},l.createElement(s.Stack,{gap:.75},l.createElement(It,null,(0,m.__)("Justify content","elementor")),l.createElement(n.ToggleControl,{options:Mn,fullWidth:!0})))))},Wn=[{value:"nowrap",label:(0,m.__)("No wrap","elementor"),renderContent:({size:e})=>l.createElement(i.ArrowRightIcon,{fontSize:e}),showTooltip:!0},{value:"wrap",label:(0,m.__)("Wrap","elementor"),renderContent:({size:e})=>l.createElement(i.ArrowBackIcon,{fontSize:e}),showTooltip:!0},{value:"wrap-reverse",label:(0,m.__)("Reversed wrap","elementor"),renderContent:({size:e})=>l.createElement(i.ArrowForwardIcon,{fontSize:e}),showTooltip:!0}],jn=()=>{const{isSiteRtl:e}=ct();return l.createElement(s.DirectionProvider,{rtl:e},l.createElement(s.ThemeProvider,null,l.createElement(wt,{bind:"flex-wrap"},l.createElement(s.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},l.createElement(s.Grid,{item:!0,xs:6},l.createElement(It,null,(0,m.__)("Wrap","elementor"))),l.createElement(s.Grid,{item:!0,xs:6,sx:{display:"flex",justifyContent:"flex-end"}},l.createElement(n.ToggleControl,{options:Wn}))))))},Fn=()=>{const[e]=lt("display"),t=_n(),n=Nn(e,t),{element:o}=L(),a=(0,r.useParentElement)(o.id),i=(c=a?.id||null,(0,E.__privateUseListenTo)([(0,E.windowEvent)("elementor/device-mode/change"),(0,E.commandEndEvent)("document/elements/reset-style"),(0,E.commandEndEvent)("document/elements/settings"),(0,E.commandEndEvent)("document/elements/paste-style")],(()=>{if(!c)return null;const e=window,t=e.elementor?.getContainer?.(c);return t?.view?.el?window.getComputedStyle(t.view.el):null})));var c;const s=i?.flexDirection??"row";return l.createElement(it,null,l.createElement(vn,null),n&&l.createElement($n,null),"flex"===i?.display&&l.createElement(Un,{parentStyleDirection:s}))},$n=()=>{const[e]=lt("flex-wrap");return l.createElement(l.Fragment,null,l.createElement(yn,null),l.createElement(On,null),l.createElement(mn,null),l.createElement(Ct,null),l.createElement(Bn,null),l.createElement(jn,null),["wrap","wrap-reverse"].includes(e?.value)&&l.createElement(rn,null))},Un=({parentStyleDirection:e})=>l.createElement(l.Fragment,null,l.createElement(Ct,null),l.createElement(n.ControlFormLabel,null,(0,m.__)("Flex child","elementor")),l.createElement(gn,{parentStyleDirection:e}),l.createElement(kn,null),l.createElement(Gn,null)),Nn=(e,t)=>{const n=e?.value??t?.value;return!!n&&("flex"===n||"inline-flex"===n)},Kn=(0,s.withDirection)(i.SideLeftIcon),Hn=(0,s.withDirection)(i.SideRightIcon),Jn={"inset-block-start":l.createElement(i.SideTopIcon,{fontSize:"tiny"}),"inset-block-end":l.createElement(i.SideBottomIcon,{fontSize:"tiny"}),"inset-inline-start":l.createElement(Zt,{icon:Kn,size:"tiny"}),"inset-inline-end":l.createElement(Zt,{icon:Hn,size:"tiny"})},Yn=e=>e?(0,m.__)("Right","elementor"):(0,m.__)("Left","elementor"),Xn=e=>e?(0,m.__)("Left","elementor"):(0,m.__)("Right","elementor"),qn=()=>{const{isSiteRtl:e}=ct();return l.createElement(l.Fragment,null,l.createElement(s.Stack,{direction:"row",gap:2,flexWrap:"nowrap"},l.createElement(Zn,{side:"inset-block-start",label:(0,m.__)("Top","elementor")}),l.createElement(Zn,{side:"inset-inline-end",label:Xn(e)})),l.createElement(s.Stack,{direction:"row",gap:2,flexWrap:"nowrap"},l.createElement(Zn,{side:"inset-block-end",label:(0,m.__)("Bottom","elementor")}),l.createElement(Zn,{side:"inset-inline-start",label:Yn(e)})))},Zn=({side:e,label:t})=>l.createElement(s.Grid,{container:!0,gap:.75,alignItems:"center"},l.createElement(s.Grid,{item:!0,xs:12},l.createElement(It,null,t)),l.createElement(s.Grid,{item:!0,xs:12},l.createElement(wt,{bind:e},l.createElement(n.SizeControl,{startIcon:Jn[e],extendedValues:["auto"]})))),Qn=()=>l.createElement(wt,{bind:"scroll-margin-top"},l.createElement(s.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},l.createElement(s.Grid,{item:!0,xs:6},l.createElement(It,null,(0,m.__)("Anchor offset","elementor"))),l.createElement(s.Grid,{item:!0,xs:6},l.createElement(n.SizeControl,{units:["px","em","rem","vw","vh"]})))),el=[{label:(0,m.__)("Static","elementor"),value:"static"},{label:(0,m.__)("Relative","elementor"),value:"relative"},{label:(0,m.__)("Absolute","elementor"),value:"absolute"},{label:(0,m.__)("Fixed","elementor"),value:"fixed"},{label:(0,m.__)("Sticky","elementor"),value:"sticky"}],tl=({onChange:e})=>l.createElement(wt,{bind:"position"},l.createElement(s.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},l.createElement(s.Grid,{item:!0,xs:6},l.createElement(It,null,(0,m.__)("Position","elementor"))),l.createElement(s.Grid,{item:!0,xs:6,sx:{overflow:"hidden"}},l.createElement(n.SelectControl,{options:el,onChange:e})))),nl=()=>l.createElement(wt,{bind:"z-index"},l.createElement(s.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},l.createElement(s.Grid,{item:!0,xs:6},l.createElement(It,null,(0,m.__)("Z-index","elementor"))),l.createElement(s.Grid,{item:!0,xs:6},l.createElement(n.NumberControl,null)))),ll=()=>{const[e]=lt("position"),[t,n]=nt(["inset-block-start","inset-block-end","inset-inline-start","inset-inline-end"]),[r,o,a]=rl(),i=(0,E.isExperimentActive)("e_v_3_30"),c=e&&"static"!==e?.value;return l.createElement(it,null,l.createElement(tl,{onChange:(e,l)=>{"static"===e?t&&(o(t),n({"inset-block-start":void 0,"inset-block-end":void 0,"inset-inline-start":void 0,"inset-inline-end":void 0})):"static"===l&&r&&(n(r),a())}}),c?l.createElement(l.Fragment,null,l.createElement(qn,null),l.createElement(nl,null)):null,i&&l.createElement(l.Fragment,null,l.createElement(Ct,null),l.createElement(Qn,null)))},rl=()=>{const{id:e,meta:t}=O(),n=`styles/${e}/${t.breakpoint||"desktop"}/${t.state||"null"}/dimensions`;return(0,g.useSessionStorage)(n)},ol=({children:e,defaultOpen:t=!1})=>{const[n,r]=(0,l.useState)(t);return l.createElement(s.Stack,null,l.createElement(s.Button,{fullWidth:!0,size:"small",color:"secondary",variant:"outlined",onClick:()=>{r((e=>!e))},endIcon:l.createElement(De,{open:n}),sx:{my:.5}},n?(0,m.__)("Show less","elementor"):(0,m.__)("Show more","elementor")),l.createElement(s.Collapse,{in:n,timeout:"auto",unmountOnExit:!0},e))},al=[{label:(0,m.__)("Fill","elementor"),value:"fill"},{label:(0,m.__)("Cover","elementor"),value:"cover"},{label:(0,m.__)("Contain","elementor"),value:"contain"},{label:(0,m.__)("None","elementor"),value:"none"},{label:(0,m.__)("Scale down","elementor"),value:"scale-down"}],il=()=>l.createElement(wt,{bind:"object-fit"},l.createElement(s.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},l.createElement(s.Grid,{item:!0,xs:6},l.createElement(It,null,(0,m.__)("Object fit","elementor"))),l.createElement(s.Grid,{item:!0,xs:6,sx:{overflow:"hidden"}},l.createElement(n.SelectControl,{options:al})))),cl=[{label:(0,m.__)("Center center","elementor"),value:"center center"},{label:(0,m.__)("Center left","elementor"),value:"center left"},{label:(0,m.__)("Center right","elementor"),value:"center right"},{label:(0,m.__)("Top center","elementor"),value:"top center"},{label:(0,m.__)("Top left","elementor"),value:"top left"},{label:(0,m.__)("Top right","elementor"),value:"top right"},{label:(0,m.__)("Bottom center","elementor"),value:"bottom center"},{label:(0,m.__)("Bottom left","elementor"),value:"bottom left"},{label:(0,m.__)("Bottom right","elementor"),value:"bottom right"}],sl=()=>l.createElement(wt,{bind:"object-position"},l.createElement(s.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},l.createElement(s.Grid,{item:!0,xs:6},l.createElement(It,null,(0,m.__)("Object position","elementor"))),l.createElement(s.Grid,{item:!0,xs:6,sx:{overflow:"hidden"}},l.createElement(n.SelectControl,{options:cl})))),ml=[{value:"visible",label:(0,m.__)("Visible","elementor"),renderContent:({size:e})=>l.createElement(i.EyeIcon,{fontSize:e}),showTooltip:!0},{value:"hidden",label:(0,m.__)("Hidden","elementor"),renderContent:({size:e})=>l.createElement(i.EyeOffIcon,{fontSize:e}),showTooltip:!0},{value:"auto",label:(0,m.__)("Auto","elementor"),renderContent:({size:e})=>l.createElement(i.LetterAIcon,{fontSize:e}),showTooltip:!0}],ul=()=>l.createElement(wt,{bind:"overflow"},l.createElement(s.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},l.createElement(s.Grid,{item:!0,xs:6},l.createElement(It,null,(0,m.__)("Overflow","elementor"))),l.createElement(s.Grid,{item:!0,xs:6,sx:{display:"flex",justifyContent:"flex-end"}},l.createElement(n.ToggleControl,{options:ml})))),dl=()=>{const[e]=lt("object-fit"),t=e&&"fill"!==e?.value,r=(0,E.isExperimentActive)("e_v_3_30");return l.createElement(it,null,l.createElement(s.Grid,{container:!0,gap:2,flexWrap:"nowrap"},l.createElement(s.Grid,{item:!0,xs:6},l.createElement(pl,{bind:"width",label:(0,m.__)("Width","elementor"),extendedValues:["auto"]})),l.createElement(s.Grid,{item:!0,xs:6},l.createElement(pl,{bind:"height",label:(0,m.__)("Height","elementor"),extendedValues:["auto"]}))),l.createElement(s.Grid,{container:!0,gap:2,flexWrap:"nowrap"},l.createElement(s.Grid,{item:!0,xs:6},l.createElement(pl,{bind:"min-width",label:(0,m.__)("Min width","elementor"),extendedValues:["auto"]})),l.createElement(s.Grid,{item:!0,xs:6},l.createElement(pl,{bind:"min-height",label:(0,m.__)("Min height","elementor"),extendedValues:["auto"]}))),l.createElement(s.Grid,{container:!0,gap:2,flexWrap:"nowrap"},l.createElement(s.Grid,{item:!0,xs:6},l.createElement(pl,{bind:"max-width",label:(0,m.__)("Max width","elementor")})),l.createElement(s.Grid,{item:!0,xs:6},l.createElement(pl,{bind:"max-height",label:(0,m.__)("Max height","elementor")}))),l.createElement(Ct,null),l.createElement(s.Stack,null,l.createElement(ul,null)),r&&l.createElement(ol,null,l.createElement(s.Stack,{gap:2},l.createElement(wt,{bind:"aspect-ratio"},l.createElement(n.AspectRatioControl,{label:(0,m.__)("Aspect Ratio","elementor")})),l.createElement(Ct,null),l.createElement(il,null),t&&l.createElement(s.Grid,{item:!0,xs:6},l.createElement(sl,null)))))},pl=({label:e,bind:t,extendedValues:r})=>l.createElement(wt,{bind:t},l.createElement(s.Grid,{container:!0,gap:.75,alignItems:"center"},l.createElement(s.Grid,{item:!0,xs:12},l.createElement(It,null,e)),l.createElement(s.Grid,{item:!0,xs:12},l.createElement(n.SizeControl,{extendedValues:r})))),El=()=>{const{isSiteRtl:e}=ct();return l.createElement(it,null,l.createElement(wt,{bind:"margin"},l.createElement(n.LinkedDimensionsControl,{label:(0,m.__)("Margin","elementor"),isSiteRtl:e,extendedValues:["auto"]})),l.createElement(Ct,null),l.createElement(wt,{bind:"padding"},l.createElement(n.LinkedDimensionsControl,{label:(0,m.__)("Padding","elementor"),isSiteRtl:e})))},fl=()=>l.createElement(wt,{bind:"column-count"},l.createElement(s.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},l.createElement(s.Grid,{item:!0,xs:6},l.createElement(It,null,(0,m.__)("Columns","elementor"))),l.createElement(s.Grid,{item:!0,xs:6},l.createElement(n.NumberControl,{shouldForceInt:!0,min:0,step:1})))),gl=()=>l.createElement(wt,{bind:"column-gap"},l.createElement(s.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},l.createElement(s.Grid,{item:!0,xs:6},l.createElement(It,null,(0,m.__)("Column gap","elementor"))),l.createElement(s.Grid,{item:!0,xs:6},l.createElement(n.SizeControl,null)))),bl={system:(0,m.__)("System","elementor"),custom:(0,m.__)("Custom Fonts","elementor"),googlefonts:(0,m.__)("Google Fonts","elementor")},vl=()=>{const e=(()=>{const e=(()=>{const{controls:e}=(()=>{const e=window;return e.elementor?.config??{}})(),t=e?.font?.options;return t||null})();return(0,l.useMemo)((()=>{const t=["system","custom","googlefonts"];return Object.entries(e||{}).reduce(((e,[n,l])=>{if(!bl[l])return e;const r=t.indexOf(l);return e[r]||(e[r]={label:bl[l],fonts:[]}),e[r].fonts.push(n),e}),[]).filter(Boolean)}),[e])})();return 0===e.length?null:l.createElement(wt,{bind:"font-family"},l.createElement(s.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},l.createElement(s.Grid,{item:!0,xs:6},l.createElement(It,null,(0,m.__)("Font family","elementor"))),l.createElement(s.Grid,{item:!0,xs:6,sx:{minWidth:0}},l.createElement(n.FontFamilyControl,{fontFamilies:e}))))},_l=()=>l.createElement(wt,{bind:"font-size"},l.createElement(s.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},l.createElement(s.Grid,{item:!0,xs:6},l.createElement(It,null,(0,m.__)("Font size","elementor"))),l.createElement(s.Grid,{item:!0,xs:6},l.createElement(n.SizeControl,null)))),hl=[{value:"normal",label:(0,m.__)("Normal","elementor"),renderContent:({size:e})=>l.createElement(i.MinusIcon,{fontSize:e}),showTooltip:!0},{value:"italic",label:(0,m.__)("Italic","elementor"),renderContent:({size:e})=>l.createElement(i.ItalicIcon,{fontSize:e}),showTooltip:!0}],yl=()=>l.createElement(wt,{bind:"font-style"},l.createElement(s.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},l.createElement(s.Grid,{item:!0,xs:6},l.createElement(n.ControlFormLabel,null,(0,m.__)("Font style","elementor"))),l.createElement(s.Grid,{item:!0,xs:6,display:"flex",justifyContent:"end"},l.createElement(n.ToggleControl,{options:hl})))),xl=[{value:"100",label:(0,m.__)("100 - Thin","elementor")},{value:"200",label:(0,m.__)("200 - Extra light","elementor")},{value:"300",label:(0,m.__)("300 - Light","elementor")},{value:"400",label:(0,m.__)("400 - Normal","elementor")},{value:"500",label:(0,m.__)("500 - Medium","elementor")},{value:"600",label:(0,m.__)("600 - Semi bold","elementor")},{value:"700",label:(0,m.__)("700 - Bold","elementor")},{value:"800",label:(0,m.__)("800 - Extra bold","elementor")},{value:"900",label:(0,m.__)("900 - Black","elementor")}],wl=()=>l.createElement(wt,{bind:"font-weight"},l.createElement(s.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},l.createElement(s.Grid,{item:!0,xs:6},l.createElement(It,null,(0,m.__)("Font weight","elementor"))),l.createElement(s.Grid,{item:!0,xs:6,sx:{overflow:"hidden"}},l.createElement(n.SelectControl,{options:xl})))),Sl=()=>l.createElement(wt,{bind:"letter-spacing"},l.createElement(s.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},l.createElement(s.Grid,{item:!0,xs:6},l.createElement(It,null,(0,m.__)("Letter spacing","elementor"))),l.createElement(s.Grid,{item:!0,xs:6},l.createElement(n.SizeControl,null)))),Cl=()=>l.createElement(wt,{bind:"line-height"},l.createElement(s.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},l.createElement(s.Grid,{item:!0,xs:6},l.createElement(It,null,(0,m.__)("Line height","elementor"))),l.createElement(s.Grid,{item:!0,xs:6},l.createElement(n.SizeControl,null)))),Il=(0,s.withDirection)(i.AlignLeftIcon),Tl=(0,s.withDirection)(i.AlignRightIcon),kl=[{value:"start",label:(0,m.__)("Start","elementor"),renderContent:({size:e})=>l.createElement(Il,{fontSize:e}),showTooltip:!0},{value:"center",label:(0,m.__)("Center","elementor"),renderContent:({size:e})=>l.createElement(i.AlignCenterIcon,{fontSize:e}),showTooltip:!0},{value:"end",label:(0,m.__)("End","elementor"),renderContent:({size:e})=>l.createElement(Tl,{fontSize:e}),showTooltip:!0},{value:"justify",label:(0,m.__)("Justify","elementor"),renderContent:({size:e})=>l.createElement(i.AlignJustifiedIcon,{fontSize:e}),showTooltip:!0}],zl=()=>l.createElement(wt,{bind:"text-align"},l.createElement(s.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},l.createElement(s.Grid,{item:!0,xs:6},l.createElement(It,null,(0,m.__)("Text align","elementor"))),l.createElement(s.Grid,{item:!0,xs:6,display:"flex",justifyContent:"end"},l.createElement(n.ToggleControl,{options:kl})))),Pl=()=>l.createElement(wt,{bind:"color"},l.createElement(s.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},l.createElement(s.Grid,{item:!0,xs:6},l.createElement(It,null,(0,m.__)("Text color","elementor"))),l.createElement(s.Grid,{item:!0,xs:6},l.createElement(n.ColorControl,null)))),Gl=[{value:"none",label:(0,m.__)("None","elementor"),renderContent:({size:e})=>l.createElement(i.MinusIcon,{fontSize:e}),showTooltip:!0,exclusive:!0},{value:"underline",label:(0,m.__)("Underline","elementor"),renderContent:({size:e})=>l.createElement(i.UnderlineIcon,{fontSize:e}),showTooltip:!0},{value:"line-through",label:(0,m.__)("Line-through","elementor"),renderContent:({size:e})=>l.createElement(i.StrikethroughIcon,{fontSize:e}),showTooltip:!0},{value:"overline",label:(0,m.__)("Overline","elementor"),renderContent:({size:e})=>l.createElement(i.OverlineIcon,{fontSize:e}),showTooltip:!0}],Ll=()=>l.createElement(wt,{bind:"text-decoration"},l.createElement(s.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},l.createElement(s.Grid,{item:!0,xs:6},l.createElement(It,null,(0,m.__)("Line decoration","elementor"))),l.createElement(s.Grid,{item:!0,xs:6,display:"flex",justifyContent:"end"},l.createElement(n.ToggleControl,{options:Gl,exclusive:!1})))),Rl=[{value:"ltr",label:(0,m.__)("Left to right","elementor"),renderContent:({size:e})=>l.createElement(i.TextDirectionLtrIcon,{fontSize:e}),showTooltip:!0},{value:"rtl",label:(0,m.__)("Right to left","elementor"),renderContent:({size:e})=>l.createElement(i.TextDirectionRtlIcon,{fontSize:e}),showTooltip:!0}],Bl=()=>l.createElement(wt,{bind:"direction"},l.createElement(s.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},l.createElement(s.Grid,{item:!0,xs:6},l.createElement(It,null,(0,m.__)("Direction","elementor"))),l.createElement(s.Grid,{item:!0,xs:6,display:"flex",justifyContent:"end"},l.createElement(n.ToggleControl,{options:Rl})))),Vl={$$type:"stroke",value:{color:{$$type:"color",value:"#000000"},width:{$$type:"size",value:{unit:"px",size:1}}}},Al=()=>{const{canEdit:e}=O(),[t,r]=lt("stroke"),o=Boolean(t);return l.createElement(wt,{bind:"stroke"},l.createElement(kt,{label:(0,m.__)("Text stroke","elementor"),isAdded:o,onAdd:()=>{r(Vl)},onRemove:()=>{r(null)},disabled:!e},l.createElement(n.StrokeControl,null)))},Dl=[{value:"none",label:(0,m.__)("None","elementor"),renderContent:({size:e})=>l.createElement(i.MinusIcon,{fontSize:e}),showTooltip:!0},{value:"capitalize",label:(0,m.__)("Capitalize","elementor"),renderContent:({size:e})=>l.createElement(i.LetterCaseIcon,{fontSize:e}),showTooltip:!0},{value:"uppercase",label:(0,m.__)("Uppercase","elementor"),renderContent:({size:e})=>l.createElement(i.LetterCaseUpperIcon,{fontSize:e}),showTooltip:!0},{value:"lowercase",label:(0,m.__)("Lowercase","elementor"),renderContent:({size:e})=>l.createElement(i.LetterCaseLowerIcon,{fontSize:e}),showTooltip:!0}],Ml=()=>l.createElement(wt,{bind:"text-transform"},l.createElement(s.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},l.createElement(s.Grid,{item:!0,xs:6},l.createElement(It,null,(0,m.__)("Text transform","elementor"))),l.createElement(s.Grid,{item:!0,xs:6,display:"flex",justifyContent:"end"},l.createElement(n.ToggleControl,{options:Dl})))),Ol=()=>l.createElement(wt,{bind:"word-spacing"},l.createElement(s.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},l.createElement(s.Grid,{item:!0,xs:6},l.createElement(It,null,(0,m.__)("Word spacing","elementor"))),l.createElement(s.Grid,{item:!0,xs:6},l.createElement(n.SizeControl,null)))),Wl=()=>{const[e]=lt("column-count"),t=(0,E.isExperimentActive)("e_v_3_30"),n=!!(e?.value&&e?.value>1);return l.createElement(it,null,l.createElement(vl,null),l.createElement(wl,null),l.createElement(_l,null),l.createElement(Ct,null),l.createElement(zl,null),l.createElement(Pl,null),l.createElement(ol,null,l.createElement(it,{sx:{pt:2}},l.createElement(Cl,null),l.createElement(Sl,null),l.createElement(Ol,null),t&&l.createElement(l.Fragment,null,l.createElement(fl,null),n&&l.createElement(gl,null)),l.createElement(Ct,null),l.createElement(Ll,null),l.createElement(Ml,null),l.createElement(Bl,null),l.createElement(yl,null),l.createElement(Al,null))))},jl={position:"sticky",zIndex:1,opacity:1,backgroundColor:"background.default",transition:"top 300ms ease"},Fl=({section:e})=>{const{component:t,name:n,title:r}=e,o=Te(),a=t,i=!!(0,E.isExperimentActive)(Z.V_3_30)&&o.defaultSectionsExpanded.style?.includes(n);return l.createElement(Me,{title:r,defaultExpanded:i},l.createElement(a,null))},$l=()=>{const e=function(){const{elementType:e}=L(),t=Object.entries(e.propsSchema).find((([,e])=>"plain"===e.kind&&e.key===p.CLASSES_PROP_KEY));if(!t)throw new Error("Element does not have a classes prop");return t[0]}(),[t,n]=function(e){const[t,n]=ke("active-style-id",null),l=function(e){const{element:t}=L();return(0,r.useElementSetting)(t.id,e)}(e)?.value||[],o=function(e){const{element:t}=L(),n=(0,r.getElementStyles)(t.id)??{};return Object.values(n).find((t=>e.includes(t.id)))}(l);return[function(e,t){return e&&t.includes(e)?e:null}(t,l)||o?.id||null,n]}(e),[o,a]=(0,l.useState)(null),i=(0,v.useActiveBreakpoint)();return l.createElement(k,{prop:e},l.createElement(M,{meta:{breakpoint:i,state:o},id:t,setId:e=>{n(e),a(null)},setMetaState:a},l.createElement(g.SessionStorageProvider,{prefix:t??""},l.createElement(Ze,null,l.createElement(Ul,null,l.createElement(fe,null),l.createElement(s.Divider,null)),l.createElement(Oe,null,l.createElement(Fl,{section:{component:Fn,name:"Layout",title:(0,m.__)("Layout","elementor")}}),l.createElement(Fl,{section:{component:El,name:"Spacing",title:(0,m.__)("Spacing","elementor")}}),l.createElement(Fl,{section:{component:dl,name:"Size",title:(0,m.__)("Size","elementor")}}),l.createElement(Fl,{section:{component:ll,name:"Position",title:(0,m.__)("Position","elementor")}}),l.createElement(Fl,{section:{component:Wl,name:"Typography",title:(0,m.__)("Typography","elementor")}}),l.createElement(Fl,{section:{component:St,name:"Background",title:(0,m.__)("Background","elementor")}}),l.createElement(Fl,{section:{component:Jt,name:"Border",title:(0,m.__)("Border","elementor")}}),l.createElement(Fl,{section:{component:Yt,name:"Effects",title:(0,m.__)("Effects","elementor")}}))))))};function Ul({children:e}){const t=(0,l.useContext)(xe)?.direction??"up";return l.createElement(s.Stack,{sx:{...jl,top:"up"===t?"37px":0}},e)}var Nl=()=>{const{element:e}=L();return l.createElement(l.Fragment,{key:e.id},l.createElement(Kl,null))},Kl=()=>{const e=Te(),t=(0,E.isExperimentActive)(Z.V_3_30)?e.defaultTab:"settings",[n,r]=ke("tab",t),{getTabProps:o,getTabPanelProps:a,getTabsProps:i}=(0,s.useTabs)(n);return l.createElement(Se,null,l.createElement(s.Stack,{direction:"column",sx:{width:"100%"}},l.createElement(s.Stack,{sx:{...jl,top:0}},l.createElement(s.Tabs,{variant:"fullWidth",size:"small",sx:{mt:.5},...i(),onChange:(e,t)=>{i().onChange(e,t),r(t)}},l.createElement(s.Tab,{label:(0,m.__)("General","elementor"),...o("settings")}),l.createElement(s.Tab,{label:(0,m.__)("Style","elementor"),...o("style")})),l.createElement(s.Divider,null)),l.createElement(s.TabPanel,{...a("settings"),disablePadding:!0},l.createElement(We,null)),l.createElement(s.TabPanel,{...a("style"),disablePadding:!0},l.createElement($l,null))))},{useMenuItems:Hl}=he,{panel:Jl,usePanelActions:Yl,usePanelStatus:Xl}=(0,f.__createPanel)({id:"editing-panel",component:()=>{const{element:e,elementType:t}=(0,r.useSelectedElement)(),o=I(),c=Hl().default;if(!e||!t)return null;const u=(0,m.__)("Edit %s","elementor").replace("%s",t.title);return l.createElement(s.ErrorBoundary,{fallback:l.createElement(ye,null)},l.createElement(g.SessionStorageProvider,{prefix:"elementor"},l.createElement(a.ThemeProvider,null,l.createElement(f.Panel,null,l.createElement(f.PanelHeader,null,l.createElement(f.PanelHeaderTitle,null,u),l.createElement(i.AtomIcon,{fontSize:"small",sx:{color:"text.tertiary"}})),l.createElement(f.PanelBody,null,l.createElement(n.ControlActionsProvider,{items:c},l.createElement(n.ControlReplacementsProvider,{replacements:o},l.createElement(G,{element:e,elementType:t},l.createElement(Nl,null)))))))))}}),ql=()=>{const e=(0,r.getSelectedElements)(),t=(0,r.getWidgetsCache)();return 1===e.length&&!!t?.[e[0].type]?.atomic_controls},Zl=()=>((()=>{const{open:e}=Yl();(0,l.useEffect)((()=>(0,E.__privateListenTo)((0,E.commandStartEvent)("panel/editor/open"),(()=>{ql()&&e()}))),[])})(),null),Ql=({alignItems:e,gap:t=1.5,p:n,children:r})=>l.createElement(s.Stack,{alignItems:e,gap:t,p:n},r),er=e=>{const{element:t}=L(),n=`dynamic/non-dynamic-values-history/${t.id}/${e}`;return(0,g.useSessionStorage)(n)},tr=()=>{const{atomicDynamicTags:e}=(()=>{const e=window;return e.elementor?.config??{}})();return e?{tags:e.tags,groups:e.groups}:null},nr="dynamic",lr=e=>{const t="union"===e.kind&&e.prop_types[nr];return t&&t.key===nr?t:null},rr=(0,p.createPropUtils)(nr,w.z.strictObject({name:w.z.string(),settings:w.z.any().optional()})),or=()=>{let e=[];const{propType:t}=(0,n.useBoundProp)();if(t){const n=lr(t);e=n?.settings.categories||[]}return(0,l.useMemo)((()=>ar(e)),[e.join()])},ar=e=>{const t=tr();if(!e.length||!t?.tags)return[];const n=new Set(e);return Object.values(t.tags).filter((e=>e.categories.some((e=>n.has(e)))))},ir=e=>{const t=or();return(0,l.useMemo)((()=>t.find((t=>t.name===e))??null),[t,e])},cr=({bind:e,children:t})=>{const{value:r,setValue:o}=(0,n.useBoundProp)(rr),{name:a="",settings:i}=r??{},c=ir(a);if(!c)throw new Error(`Dynamic tag ${a} not found`);const s=c.props_schema[e],m=s?.default,u=i?.[e]??m,d=Ve({schema:c.props_schema});return l.createElement(n.PropProvider,{propType:d,setValue:e=>{o({name:a,settings:{...i,...e}})},value:{[e]:u}},l.createElement(n.PropKeyProvider,{bind:e},t))},sr="tiny",mr=({onSelect:e})=>{const[t,r]=(0,l.useState)(""),{groups:o}=tr()||{},{value:a}=(0,n.useBoundProp)(),{bind:c,value:u,setValue:d}=(0,n.useBoundProp)(rr),[,p]=er(c),E=!!u,f=pr(t),g=!f.length&&!t.trim();return l.createElement(s.Stack,null,g?l.createElement(dr,null):l.createElement(l.Fragment,null,l.createElement(s.Box,{px:1.5,pb:1},l.createElement(s.TextField,{fullWidth:!0,size:sr,value:t,onChange:e=>{r(e.target.value)},placeholder:(0,m.__)("Search dynamic tags…","elementor"),InputProps:{startAdornment:l.createElement(s.InputAdornment,{position:"start"},l.createElement(i.SearchIcon,{fontSize:sr}))}})),l.createElement(s.Divider,null),l.createElement(s.Box,{sx:{overflowY:"auto",height:260,width:220}},f.length>0?l.createElement(s.MenuList,{role:"listbox",tabIndex:0},f.map((([t,n],r)=>l.createElement(l.Fragment,{key:r},l.createElement(s.MenuSubheader,{sx:{px:1.5,typography:"caption",color:"text.tertiary"}},o?.[t]?.title||t),n.map((({value:t,label:n})=>{const r=E&&t===u?.name;return l.createElement(s.MenuItem,{key:t,selected:r,autoFocus:r,sx:{px:3.5,typography:"caption"},onClick:()=>((t,n)=>{E||p(a),d({name:t,settings:{label:n}}),e?.()})(t,n)},n)})))))):l.createElement(ur,{searchValue:t,onClear:()=>r("")}))))},ur=({searchValue:e,onClear:t})=>l.createElement(s.Stack,{gap:1,alignItems:"center",justifyContent:"center",height:"100%",p:2.5,color:"text.secondary",sx:{pb:3.5}},l.createElement(i.DatabaseIcon,{fontSize:"large"}),l.createElement(s.Typography,{align:"center",variant:"subtitle2"},(0,m.__)("Sorry, nothing matched","elementor"),l.createElement("br",null),"“",e,"”."),l.createElement(s.Typography,{align:"center",variant:"caption"},(0,m.__)("Try something else.","elementor")," ",l.createElement(s.Link,{color:"text.secondary",variant:"caption",component:"button",onClick:t},(0,m.__)("Clear & try again","elementor")))),dr=()=>l.createElement(s.Box,{sx:{overflowY:"hidden",height:297,width:220}},l.createElement(s.Divider,null),l.createElement(s.Stack,{gap:1,alignItems:"center",justifyContent:"center",height:"100%",p:2.5,color:"text.secondary",sx:{pb:3.5}},l.createElement(i.DatabaseIcon,{fontSize:"large"}),l.createElement(s.Typography,{align:"center",variant:"subtitle2"},(0,m.__)("Streamline your workflow with dynamic tags","elementor")),l.createElement(s.Typography,{align:"center",variant:"caption"},(0,m.__)("You’ll need Elementor Pro to use this feature.","elementor")))),pr=e=>[...or().reduce(((t,{name:n,label:l,group:r})=>l.toLowerCase().includes(e.trim().toLowerCase())?(t.has(r)||t.set(r,[]),t.get(r)?.push({label:l,value:n}),t):t),new Map)],Er="tiny",fr=()=>{const{setValue:e}=(0,n.useBoundProp)(),{bind:t,value:r}=(0,n.useBoundProp)(rr),[o]=er(t),a=(0,s.usePopupState)({variant:"popover"}),{name:c=""}=r,u=ir(c);if(!u)throw new Error(`Dynamic tag ${c} not found`);return l.createElement(s.Box,null,l.createElement(s.UnstableTag,{fullWidth:!0,showActionsOnHover:!0,label:u.label,startIcon:l.createElement(i.DatabaseIcon,{fontSize:Er}),...(0,s.bindTrigger)(a),actions:l.createElement(l.Fragment,null,l.createElement(gr,{dynamicTag:u}),l.createElement(s.IconButton,{size:Er,onClick:()=>{e(o??null)},"aria-label":(0,m.__)("Remove dynamic value","elementor")},l.createElement(i.XIcon,{fontSize:Er})))}),l.createElement(s.Popover,{disablePortal:!0,disableScrollLock:!0,anchorOrigin:{vertical:"bottom",horizontal:"left"},...(0,s.bindPopover)(a)},l.createElement(s.Stack,null,l.createElement(s.Stack,{direction:"row",alignItems:"center",pl:1.5,pr:.5,py:1.5},l.createElement(i.DatabaseIcon,{fontSize:Er,sx:{mr:.5}}),l.createElement(s.Typography,{variant:"subtitle2"},(0,m.__)("Dynamic tags","elementor")),l.createElement(s.IconButton,{size:Er,sx:{ml:"auto"},onClick:a.close},l.createElement(i.XIcon,{fontSize:Er}))),l.createElement(mr,{onSelect:a.close}))))},gr=({dynamicTag:e})=>{const t=(0,s.usePopupState)({variant:"popover"});return e.atomic_controls.length?l.createElement(l.Fragment,null,l.createElement(s.IconButton,{size:Er,...(0,s.bindTrigger)(t),"aria-label":(0,m.__)("Settings","elementor")},l.createElement(i.SettingsIcon,{fontSize:Er})),l.createElement(s.Popover,{disablePortal:!0,disableScrollLock:!0,anchorOrigin:{vertical:"bottom",horizontal:"center"},...(0,s.bindPopover)(t)},l.createElement(s.Paper,{component:s.Stack,sx:{minHeight:"300px",width:"220px"}},l.createElement(s.Stack,{direction:"row",alignItems:"center",px:1.5,pt:2,pb:1},l.createElement(i.DatabaseIcon,{fontSize:Er,sx:{mr:.5}}),l.createElement(s.Typography,{variant:"subtitle2"},e.label),l.createElement(s.IconButton,{sx:{ml:"auto"},size:Er,onClick:t.close},l.createElement(i.XIcon,{fontSize:Er}))),l.createElement(br,{controls:e.atomic_controls})))):null},br=({controls:e})=>{const t=e.filter((({type:e})=>"section"===e)),{getTabsProps:n,getTabProps:r,getTabPanelProps:o}=(0,s.useTabs)(0);return t.length?l.createElement(l.Fragment,null,l.createElement(s.Tabs,{size:"small",variant:"fullWidth",...n()},t.map((({value:e},t)=>l.createElement(s.Tab,{key:t,label:e.label,sx:{px:1,py:.5},...r(t)})))),l.createElement(s.Divider,null),t.map((({value:e},t)=>l.createElement(s.TabPanel,{key:t,sx:{flexGrow:1,py:0},...o(t)},l.createElement(Ql,{p:2,gap:2},e.items.map((e=>"control"===e.type?l.createElement(vr,{key:e.value.bind,control:e.value}):null))))))):null},vr=({control:e})=>Pe(e.type)?l.createElement(cr,{bind:e.bind},l.createElement(s.Grid,{container:!0,gap:.75},e.label?l.createElement(s.Grid,{item:!0,xs:12},l.createElement(n.ControlFormLabel,null,e.label)):null,l.createElement(s.Grid,{item:!0,xs:12},l.createElement(Ge,{type:e.type,props:e.props})))):null,_r=(0,u.createError)({code:"dynamic_tags_manager_not_found",message:"Dynamic tags manager not found"}),hr=(0,h.createTransformer)((e=>e.name?function(e,t){const n=window,{dynamicTags:l}=n.elementor??{};if(!l)throw new _r;const r=()=>{const n=l.createTag("v4-dynamic-tag",e,t);return n?l.loadTagDataFromCache(n)??null:null},o=r();return null!==o?o:new Promise((e=>{l.refreshCacheFromServer((()=>{e(r())}))}))}(e.name,function(e){const t=Object.entries(e).map((([e,t])=>[e,(0,p.isTransformable)(t)?t.value:t]));return Object.fromEntries(t)}(e.settings??{})):null)),yr=()=>{const{propType:e}=(0,n.useBoundProp)(),t=!!e&&(e=>!!lr(e))(e);return{visible:t,icon:i.DatabaseIcon,title:(0,m.__)("Dynamic tags","elementor"),popoverContent:({closePopover:e})=>l.createElement(mr,{onSelect:e})}},{registerPopoverAction:xr}=he,wr=()=>{C({component:fr,condition:({value:e})=>{return t=e,(0,p.isTransformable)(t)&&t.$$type===nr;var t}}),xr({id:"dynamic-tags",useProps:yr}),h.styleTransformersRegistry.register("dynamic",hr),h.settingsTransformersRegistry.register("dynamic",hr)},{registerAction:Sr}=he,Cr=["order","flex-grow","flex-shrink","flex-basis"];function Ir(){const e=!!(0,l.useContext)(D),{value:t,setValue:r,path:o,bind:a}=(0,n.useBoundProp)();return{visible:e&&null!=t&&o.length<=2&&!Cr.includes(a),title:(0,m.__)("Clear","elementor"),icon:i.BrushBigIcon,onClick:()=>r(null)}}var Tr=(0,h.createTransformer)((e=>l.createElement(s.Stack,{direction:"row",gap:10},l.createElement(kr,{value:e}),l.createElement(zr,{value:e})))),kr=({value:e})=>{const{color:t}=e;return l.createElement(Pr,{size:"inherit",component:"span",value:t})},zr=({value:{color:e}})=>l.createElement("span",null,e),Pr=(0,s.styled)(s.UnstableColorIndicator)((({theme:e})=>({borderRadius:e.shape.borderRadius/2+"px"}))),Gr=(0,h.createTransformer)((e=>l.createElement(s.Stack,{direction:"row",gap:10},l.createElement(Lr,{value:e}),l.createElement(Rr,{value:e})))),Lr=({value:e})=>{const t=Br(e);return l.createElement(Pr,{size:"inherit",component:"span",value:t})},Rr=({value:e})=>"linear"===e.type?l.createElement("span",null,(0,m.__)("Linear Gradient","elementor")):l.createElement("span",null,(0,m.__)("Radial Gradient","elementor")),Br=e=>{const t=e.stops?.map((({color:e,offset:t})=>`${e} ${t??0}%`))?.join(",");return"linear"===e.type?`linear-gradient(${e.angle}deg, ${t})`:`radial-gradient(circle at ${e.positions}, ${t})`},Vr=(0,h.createTransformer)((e=>l.createElement(s.Stack,{direction:"row",gap:10},l.createElement(Ar,{value:e}),l.createElement(Dr,{value:e})))),Ar=({value:e})=>{const{imageUrl:t}=Mr(e);return l.createElement(s.CardMedia,{image:t,sx:e=>({height:"1em",width:"1em",borderRadius:e.shape.borderRadius/2+"px",outline:`1px solid ${e.palette.action.disabled}`})})},Dr=({value:e})=>{const{imageTitle:t}=Mr(e);return l.createElement(a.EllipsisWithTooltip,{title:t},l.createElement("span",null,t))},Mr=e=>{let t,n=null;const l=e?.image.src,{data:r}=(0,S.useWpMediaAttachment)(l.id||null);if(l.id){const e=Or(r?.filename);t=`${r?.title}${e}`||null,n=r?.url||null}else l.url&&(n=l.url,t=n?.substring(n.lastIndexOf("/")+1)||null);return{imageTitle:t,imageUrl:n}},Or=e=>e?`.${e.substring(e.lastIndexOf(".")+1)}`:"",Wr=(0,h.createTransformer)((e=>e&&0!==e.length?l.createElement(s.Stack,{direction:"column"},e.map(((e,t)=>l.createElement(s.Stack,{key:t},e)))):null));var jr=()=>{ot()&&function(){const e=h.styleTransformersRegistry.all();Object.entries(e).forEach((([e,t])=>{rt.has(e)||bt.register(e,t)})),bt.registerFallback((0,h.createTransformer)((e=>e))),bt.register("background-color-overlay",Tr),bt.register("background-gradient-overlay",Gr),bt.register("background-image-overlay",Vr),bt.register("background-overlay",Wr)}()};function Fr(){(0,f.__registerPanel)(Jl),$r(),(0,y.injectIntoLogic)({id:"editing-panel-hooks",component:Zl}),(0,y.injectIntoLogic)({id:"current-user-data",component:x.PrefetchUserData}),wr(),jr(),(0,E.isExperimentActive)(Z.V_3_30)&&Sr({id:"reset-style-value",useProps:Ir})}var $r=()=>{(0,E.blockCommand)({command:"panel/editor/open",condition:ql})};(window.elementorV2=window.elementorV2||{}).editorEditingPanel=t}(),window.elementorV2.editorEditingPanel?.init?.();