/*! For license information please see editor-editing-panel.js.LICENSE.txt */
!function(){"use strict";var e={react:function(e){e.exports=window.React},"@elementor/editor":function(e){e.exports=window.elementorV2.editor},"@elementor/editor-canvas":function(e){e.exports=window.elementorV2.editorCanvas},"@elementor/editor-controls":function(e){e.exports=window.elementorV2.editorControls},"@elementor/editor-current-user":function(e){e.exports=window.elementorV2.editorCurrentUser},"@elementor/editor-documents":function(e){e.exports=window.elementorV2.editorDocuments},"@elementor/editor-elements":function(e){e.exports=window.elementorV2.editorElements},"@elementor/editor-panels":function(e){e.exports=window.elementorV2.editorPanels},"@elementor/editor-props":function(e){e.exports=window.elementorV2.editorProps},"@elementor/editor-responsive":function(e){e.exports=window.elementorV2.editorResponsive},"@elementor/editor-styles":function(e){e.exports=window.elementorV2.editorStyles},"@elementor/editor-styles-repository":function(e){e.exports=window.elementorV2.editorStylesRepository},"@elementor/editor-ui":function(e){e.exports=window.elementorV2.editorUi},"@elementor/editor-v1-adapters":function(e){e.exports=window.elementorV2.editorV1Adapters},"@elementor/icons":function(e){e.exports=window.elementorV2.icons},"@elementor/locations":function(e){e.exports=window.elementorV2.locations},"@elementor/menus":function(e){e.exports=window.elementorV2.menus},"@elementor/schema":function(e){e.exports=window.elementorV2.schema},"@elementor/session":function(e){e.exports=window.elementorV2.session},"@elementor/ui":function(e){e.exports=window.elementorV2.ui},"@elementor/utils":function(e){e.exports=window.elementorV2.utils},"@elementor/wp-media":function(e){e.exports=window.elementorV2.wpMedia},"@wordpress/i18n":function(e){e.exports=window.wp.i18n}},t={};function n(l){var r=t[l];if(void 0!==r)return r.exports;var o=t[l]={exports:{}};return e[l](o,o.exports,n),o.exports}n.d=function(e,t){for(var l in t)n.o(t,l)&&!n.o(e,l)&&Object.defineProperty(e,l,{enumerable:!0,get:t[l]})},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var l={};!function(){n.r(l),n.d(l,{controlActionsMenu:function(){return he},init:function(){return Fr},injectIntoClassSelectorActions:function(){return Ee},registerControlReplacement:function(){return C},useBoundProp:function(){return e.useBoundProp},usePanelActions:function(){return Yl},usePanelStatus:function(){return Xl}});var e=n("@elementor/editor-controls"),t=n("react"),r=n("@elementor/editor-elements"),o=n("@elementor/editor-styles-repository"),a=n("@elementor/editor-ui"),i=n("@elementor/icons"),c=n("@elementor/locations"),s=n("@elementor/ui"),m=n("@wordpress/i18n"),u=n("@elementor/utils"),d=n("@elementor/editor-documents"),p=n("@elementor/editor-props"),E=n("@elementor/editor-v1-adapters"),f=n("@elementor/editor-panels"),g=n("@elementor/session"),b=n("@elementor/menus"),v=n("@elementor/editor-responsive"),_=n("@elementor/editor-styles"),h=n("@elementor/editor-canvas"),y=n("@elementor/editor"),x=n("@elementor/editor-current-user"),w=n("@elementor/schema"),S=n("@elementor/wp-media"),{registerControlReplacement:C,getControlReplacements:I}=(0,e.createControlReplacementsRegistry)(),T=(0,t.createContext)(null);function k({children:e,prop:n}){return t.createElement(T.Provider,{value:{prop:n}},e)}function z(){const e=(0,t.useContext)(T);if(!e)throw new Error("useClassesProp must be used within a ClassesPropProvider");return e.prop}var P=(0,t.createContext)(null);function G({children:e,element:n,elementType:l}){return t.createElement(P.Provider,{value:{element:n,elementType:l}},e)}function L(){const e=(0,t.useContext)(P);if(!e)throw new Error("useElement must be used within a ElementProvider");return e}var R=(0,u.createError)({code:"control_type_not_found",message:"Control type not found."}),B=(0,u.createError)({code:"provider_not_found",message:"Styles provider not found."}),V=(0,u.createError)({code:"provider_cannot_update_props",message:"Styles provider doesn't support updating props."}),A=(0,u.createError)({code:"style_not_found_under_provider",message:"Style not found under the provider."}),D=(0,t.createContext)(null);function M({children:e,...n}){const l=null===n.id?null:W(n.id),{userCan:r}=(0,o.useUserStylesCapability)();if(n.id&&!l)throw new B({context:{styleId:n.id}});const a=r(l?.getKey()??"").updateProps;return t.createElement(D.Provider,{value:{...n,provider:l,canEdit:a}},e)}function O(){const e=(0,t.useContext)(D);if(!e)throw new Error("useStyle must be used within a StyleProvider");return e}function W(e){return o.stylesRepository.getProviders().find((t=>t.actions.all().find((t=>t.id===e))))??null}function j(e){const{_group:t,_action:n,...l}=e;return l}var F=t.forwardRef((function({selected:e,options:n,entityName:l,onSelect:r,placeholder:o,onCreate:a,validate:i,renderEmptyState:c,...m},u){const{inputValue:d,setInputValue:p,error:E,setError:f,inputHandlers:g}=function(e){const[n,l]=(0,t.useState)(""),[r,o]=(0,t.useState)(null);return{inputValue:n,setInputValue:l,error:r,setError:o,inputHandlers:{onChange:t=>{const{value:n}=t.target;if(l(n),!e)return;if(!n)return void o(null);const{isValid:r,errorMessage:a}=e(n,"inputChange");o(r?null:a)},onBlur:()=>{l(""),o(null)}}}}(i),{open:b,openDropdown:v,closeDropdown:_}=function(e=!1){const[n,l]=(0,t.useState)(e);return{open:n,openDropdown:()=>l(!0),closeDropdown:()=>l(!1)}}(m.open),{createOption:h,loading:y}=function(e){const{onCreate:n,validate:l,setInputValue:r,setError:o,closeDropdown:a}=e,[i,c]=(0,t.useState)(!1);return n?{createOption:async e=>{if(c(!0),l){const{isValid:t,errorMessage:n}=l(e,"create");if(!t)return o(n),void c(!1)}try{r(""),a(),await n(e)}catch{}finally{c(!1)}},loading:i}:{createOption:null,loading:!1}}({onCreate:a,validate:i,setInputValue:p,setError:f,closeDropdown:_}),[x,w]=(0,t.useMemo)((()=>[n,e].map((e=>function(e,t){return e.map((e=>({...e,_group:`Existing ${t??"options"}`})))}(e,l?.plural)))),[n,e,l?.plural]),S=function(e){const{options:t,onSelect:n,createOption:l,setInputValue:r,closeDropdown:o}=e;if(n||l)return async(e,n,i,c)=>{const s=c?.option;if(!s||"object"==typeof s&&s.fixed)return;const m=n.filter((e=>"string"!=typeof e));switch(i){case"removeOption":a(m,"removeOption",s);break;case"selectOption":{const e=s;if("create"===e._action){const t=e.value;return l?.(t)}a(m,"selectOption",e);break}case"createOption":{const e=s,n=t.find((t=>t.label.toLocaleLowerCase()===e.toLocaleLowerCase()));if(!n)return l?.(e);m.push(n),a(m,"selectOption",n);break}}r(""),o()};function a(e,t,l){n?.(e.map((e=>j(e))),t,j(l))}}({options:x,onSelect:r,createOption:h,setInputValue:p,closeDropdown:_}),C=function(e){const{options:t,selected:n,onCreate:l,entityName:r}=e,o=(0,s.createFilterOptions)();return(e,a)=>{const i=n.map((e=>e.value)),c=o(e.filter((e=>!i.includes(e.value))),a),s=t.some((e=>a.inputValue===e.label));return Boolean(l)&&""!==a.inputValue&&!i.includes(a.inputValue)&&!s&&c.unshift({label:`Create "${a.inputValue}"`,value:a.inputValue,_group:`Create a new ${r?.singular??"option"}`,key:`create-${a.inputValue}`,_action:"create"}),c}}({options:n,selected:e,onCreate:a,entityName:l}),I=Boolean(a)||d.length<2||void 0;return t.createElement(s.Autocomplete,{renderTags:(e,n)=>e.map(((e,l)=>t.createElement(s.Chip,{size:"tiny",...n({index:l}),key:e.key??e.value??e.label,label:e.label}))),...m,ref:u,freeSolo:I,forcePopupIcon:!1,multiple:!0,clearOnBlur:!0,selectOnFocus:!0,disableClearable:!0,handleHomeEndKeys:!0,disabled:y,open:b,onOpen:v,onClose:_,disableCloseOnSelect:!0,value:w,options:x,ListboxComponent:E?t.forwardRef(((e,n)=>t.createElement(U,{ref:n,error:E}))):void 0,renderGroup:e=>t.createElement($,{...e}),inputValue:d,renderInput:e=>t.createElement(s.TextField,{...e,error:Boolean(E),placeholder:o,...g,sx:e=>({".MuiAutocomplete-inputRoot.MuiInputBase-adornedStart":{paddingLeft:e.spacing(.25),paddingRight:e.spacing(.25)}})}),onChange:S,getOptionLabel:e=>"string"==typeof e?e:e.label,getOptionKey:e=>"string"==typeof e?e:e.key??e.value??e.label,filterOptions:C,groupBy:e=>e._group??"",renderOption:(e,n)=>{const{_group:l,label:r}=n;return t.createElement("li",{...e,style:{display:"block",textOverflow:"ellipsis"},"data-group":l},r)},noOptionsText:c?.({searchValue:d,onClear:()=>{p(""),_()}}),isOptionEqualToValue:(e,t)=>"string"==typeof e?e===t:e.value===t.value})})),$=e=>{const n=`combobox-group-${(0,t.useId)().replace(/:/g,"_")}`;return t.createElement(N,{role:"group","aria-labelledby":n},t.createElement(K,{id:n}," ",e.group),t.createElement(H,{role:"listbox"},e.children))},U=t.forwardRef((({error:e="error"},n)=>t.createElement(s.Box,{ref:n,sx:e=>({padding:e.spacing(2)})},t.createElement(s.Typography,{variant:"caption",sx:{color:"error.main",display:"inline-block"}},e)))),N=(0,s.styled)("li")`
	&:not( :last-of-type ) {
		border-bottom: 1px solid ${({theme:e})=>e.palette.divider};
	}
`,K=(0,s.styled)(s.Box)((({theme:e})=>({position:"sticky",top:"-8px",padding:e.spacing(1,2),color:e.palette.text.tertiary,backgroundColor:e.palette.primary.contrastText}))),H=(0,s.styled)("ul")`
	padding: 0;
`,J=(0,t.createContext)(null),Y=()=>{const e=(0,t.useContext)(J);if(!e)throw new Error("useCssClass must be used within a CssClassProvider");return e};function X({children:e,...n}){return t.createElement(J.Provider,{value:n},e)}var q=(0,s.styled)("div",{shouldForwardProp:e=>"variant"!==e})`
	width: 5px;
	height: 5px;
	border-radius: 50%;
	background-color: ${({theme:e,variant:t})=>{switch(t){case"overridden":return e.palette.warning.light;case"global":return e.palette.global.dark;case"local":return e.palette.accent.main;default:return e.palette.text.disabled}}};
`,Z={V_3_30:"e_v_3_30"};function Q(){const{id:e,setId:n}=O(),{element:l}=L(),o=(0,E.isExperimentActive)(Z.V_3_30),a=ee(),i=te(),c=(0,t.useMemo)((()=>(0,E.undoable)({do:({classId:t})=>{const n=e;return i(t),n},undo:({classId:e},t)=>{a(e),n(t)}},{title:(0,r.getElementLabel)(l.id),subtitle:({classLabel:e})=>(0,m.__)("class %s removed","elementor").replace("%s",e)})),[e,a,l.id,i,n]),s=(0,t.useCallback)((({classId:e})=>{i(e)}),[i]);return o?c:s}function ee(){const{element:e}=L(),{setId:n}=O(),{setClasses:l,getAppliedClasses:r}=ne();return(0,t.useCallback)((t=>{const o=r();if(o.includes(t))throw new Error(`Class ${t} is already applied to element ${e.id}, cannot re-apply.`);const a=[...o,t];l(a),n(t)}),[e.id,r,n,l])}function te(){const{element:e}=L(),{id:n,setId:l}=O(),{setClasses:r,getAppliedClasses:o}=ne();return(0,t.useCallback)((t=>{const a=o();if(!a.includes(t))throw new Error(`Class ${t} is not applied to element ${e.id}, cannot unapply it.`);const i=a.filter((e=>e!==t));r(i),n===t&&l(i[0]??null)}),[n,e.id,o,l,r])}function ne(){const{element:e}=L(),n=z(),l=(0,E.isExperimentActive)(Z.V_3_30);return(0,t.useMemo)((()=>({setClasses:t=>{(0,r.updateElementSettings)({id:e.id,props:{[n]:p.classesPropTypeUtil.create(t)},withHistory:!l}),l&&(0,d.setDocumentModifiedStatus)(!0)},getAppliedClasses:()=>(0,r.getElementSetting)(e.id,n)?.value||[]})),[n,e.id,l])}var le=[{key:"normal",value:null},{key:"hover",value:"hover"},{key:"focus",value:"focus"},{key:"active",value:"active"}];function re({popupState:e,anchorEl:n,fixed:l}){const{provider:r}=Y();return t.createElement(s.Menu,{MenuListProps:{dense:!0,sx:{minWidth:"160px"}},...(0,s.bindMenu)(e),anchorEl:n,anchorOrigin:{vertical:"bottom",horizontal:"left"},transformOrigin:{horizontal:"left",vertical:-4},onKeyDown:e=>{e.stopPropagation()},disableAutoFocusItem:!0},function({provider:e,closeMenu:n,fixed:l}){if(!e)return[];const r=o.stylesRepository.getProviderByKey(e),a=r?.actions,i=a?.update,c=!l,m=[i&&t.createElement(ie,{key:"rename-class",closeMenu:n}),c&&t.createElement(ae,{key:"unapply-class",closeMenu:n})].filter(Boolean);return m.length&&(m.unshift(t.createElement(s.MenuSubheader,{key:"provider-label",sx:{typography:"caption",color:"text.secondary",pb:.5,pt:1,textTransform:"capitalize"}},r?.labels?.singular)),m.push(t.createElement(s.Divider,{key:"provider-actions-divider"}))),m}({provider:r,closeMenu:e.close,fixed:l}),t.createElement(s.MenuSubheader,{sx:{typography:"caption",color:"text.secondary",pb:.5,pt:1}},(0,m.__)("States","elementor")),le.map((n=>t.createElement(oe,{key:n.key,state:n.value,closeMenu:e.close}))))}function oe({state:e,closeMenu:n,...l}){const{id:r,provider:i}=Y(),{id:c,setId:u,setMetaState:d,meta:p}=O(),{state:E}=p,{userCan:f}=(0,o.useUserStylesCapability)(),g=function(e){const{meta:t}=O(),n=o.stylesRepository.all().find((t=>t.id===e));return Object.fromEntries(n?.variants.filter((e=>t.breakpoint===e.meta.breakpoint)).map((e=>[e.meta.state??"normal",!0]))??[])}(r),b=!e||f(i??"").updateProps,v=!i||(0,o.isElementsStylesProvider)(i)?"local":"global",_=g[e??"normal"]??!1,h=!b&&!_,y=r===c,x=e===E&&y;return t.createElement(a.MenuListItem,{...l,selected:x,disabled:h,sx:{textTransform:"capitalize"},onClick:()=>{y||u(r),d(e),n()}},t.createElement(a.MenuItemInfotip,{showInfoTip:h,content:(0,m.__)("With your current role, you can only use existing states.","elementor")},t.createElement(s.Stack,{gap:.75,direction:"row",alignItems:"center"},_&&t.createElement(q,{"aria-label":(0,m.__)("Has style","elementor"),variant:v}),e??"normal")))}function ae({closeMenu:e,...n}){const{id:l,label:r}=Y(),o=Q();return l?t.createElement(a.MenuListItem,{...n,onClick:()=>{o({classId:l,classLabel:r}),e()}},(0,m.__)("Remove","elementor")):null}function ie({closeMenu:e}){const{handleRename:n,provider:l}=Y(),{userCan:r}=(0,o.useUserStylesCapability)();if(!l)return null;const i=r(l).update;return t.createElement(a.MenuListItem,{disabled:!i,onClick:()=>{e(),n()}},t.createElement(a.MenuItemInfotip,{showInfoTip:!i,content:(0,m.__)("With your current role, you can use existing classes but can’t modify them.","elementor")},(0,m.__)("Rename","elementor")))}var ce="tiny";function se(e){const{chipProps:n,icon:l,color:r,fixed:c,...u}=e,{id:d,provider:p,label:E,isActive:f,onClickActive:g,renameLabel:b,setError:v}=u,{meta:_,setMetaState:h}=O(),y=(0,s.usePopupState)({variant:"popover"}),[x,w]=(0,t.useState)(null),{onDelete:S,...C}=n,{userCan:I}=(0,o.useUserStylesCapability)(),{ref:T,isEditing:k,openEditMode:z,error:P,getProps:G}=(0,a.useEditable)({value:E,onSubmit:b,validation:me,onError:v}),L=P?"error":r,R=p?o.stylesRepository.getProviderByKey(p)?.actions:null,B=Boolean(R?.update)&&I(p??"")?.update,V=f&&_.state;return t.createElement(t.Fragment,null,t.createElement(s.UnstableChipGroup,{ref:w,...C,"aria-label":`Edit ${E}`,role:"group",sx:e=>({"&.MuiChipGroup-root.MuiAutocomplete-tag":{margin:e.spacing(.125)}})},t.createElement(s.Chip,{size:ce,label:k?t.createElement(a.EditableField,{ref:T,...G()}):t.createElement(a.EllipsisWithTooltip,{maxWidth:"10ch",title:E,as:"div"}),variant:!f||_.state||k?"standard":"filled",shape:"rounded",icon:l,color:L,onClick:()=>{V?h(null):B&&f?z():g(d)},"aria-pressed":f,sx:e=>({lineHeight:1,cursor:f&&B&&!V?"text":"pointer",borderRadius:.75*e.shape.borderRadius+"px","&.Mui-focusVisible":{boxShadow:"none !important"}})}),!k&&t.createElement(s.Chip,{icon:V?void 0:t.createElement(i.DotsVerticalIcon,{fontSize:"tiny"}),size:ce,label:V?t.createElement(s.Stack,{direction:"row",gap:.5,alignItems:"center"},t.createElement(s.Typography,{variant:"inherit"},_.state),t.createElement(i.DotsVerticalIcon,{fontSize:"tiny"})):void 0,variant:"filled",shape:"rounded",color:L,...(0,s.bindTrigger)(y),"aria-label":(0,m.__)("Open CSS Class Menu","elementor"),sx:e=>({borderRadius:.75*e.shape.borderRadius+"px",paddingRight:0,...V?{}:{paddingLeft:0},".MuiChip-label":V?{paddingRight:0}:{padding:0}})})),t.createElement(X,{...u,handleRename:z},t.createElement(re,{popupState:y,anchorEl:x,fixed:c})))}var me=e=>{const t=(0,o.validateStyleLabel)(e,"rename");return t.isValid?null:t.errorMessage},ue="elementor-css-class-selector",de={label:(0,m.__)("local","elementor"),value:null,fixed:!0,color:"accent",icon:t.createElement(i.MapPinIcon,null),provider:null},{Slot:pe,inject:Ee}=(0,c.createLocation)();function fe(){const e=function(){const{element:e}=L();return(0,o.useProviders)().filter((e=>!!e.actions.updateProps)).flatMap((n=>{const l=(0,o.isElementsStylesProvider)(n.getKey()),r=n.actions.all({elementId:e.id});return l&&0===r.length?[de]:r.map((e=>({label:e.label,value:e.id,fixed:l,color:l?"accent":"global",icon:l?t.createElement(i.MapPinIcon,null):null,provider:n.getKey()})))}))}(),{id:n,setId:l}=O(),c=(0,t.useRef)(null),[u,d]=(0,t.useState)(null),p=function(){const e=function(){const{id:e,setId:n}=O(),{element:l}=L(),o=(0,E.isExperimentActive)(Z.V_3_30),a=ee(),i=te(),c=(0,t.useMemo)((()=>(0,E.undoable)({do:({classId:t})=>{const n=e;return a(t),n},undo:({classId:e},t)=>{i(e),n(t)}},{title:(0,r.getElementLabel)(l.id),subtitle:({classLabel:e})=>(0,m.__)("class %s applied","elementor").replace("%s",e)})),[e,a,l.id,i,n]),s=(0,t.useCallback)((({classId:e})=>{a(e)}),[a]);return o?c:s}(),n=Q();return(t,l,r)=>{if(r.value)switch(l){case"selectOption":e({classId:r.value,classLabel:r.label});break;case"removeOption":n({classId:r.value,classLabel:r.label})}}}(),{create:f,validate:g,entityName:b}=function(){const[e,n]=function(){const{id:e,setId:n}=O(),l=(0,E.isExperimentActive)(Z.V_3_30),[r,a]=(0,o.useGetStylesRepositoryCreateAction)()??[null,null],i=r?.actions.delete,c=ee(),s=te(),u=(0,t.useMemo)((()=>{if(r&&a)return(0,E.undoable)({do:({classLabel:t})=>{const n=e,l=a(t);return c(l),{prevActiveId:n,createdId:l}},undo:(e,{prevActiveId:t,createdId:l})=>{s(l),i?.(l),n(t)}},{title:(0,m.__)("Class","elementor"),subtitle:({classLabel:e})=>(0,m.__)("%s created","elementor").replace("%s",e)})}),[e,c,a,i,r,n,s]),d=(0,t.useCallback)((({classLabel:e})=>{if(!a)return;const t=a(e);c(t)}),[c,a]);return r&&u?l?[r,u]:[r,d]:[null,null]}();if(!e||!n)return{};return{create:e=>{n({classLabel:e})},validate:(t,n)=>function(e){return e.actions.all().length>=e.limit}(e)?{isValid:!1,errorMessage:(0,m.__)("You’ve reached the limit of 50 classes. Please remove an existing one to create a new class.","elementor")}:(0,o.validateStyleLabel)(t,n),entityName:e.labels.singular&&e.labels.plural?e.labels:void 0}}(),v=function(e){const{element:t}=L(),n=z(),l=(0,r.useElementSetting)(t.id,n)?.value||[],a=e.filter((e=>e.value&&l.includes(e.value)));return a.some((e=>e.provider&&(0,o.isElementsStylesProvider)(e.provider)))||a.unshift(de),a}(e),_=v.find((e=>e.value===n))??de,h=v.every((({fixed:e})=>e)),{userCan:y}=(0,o.useUserStylesCapability)(),x=!_.provider||y(_.provider).updateProps;return t.createElement(s.Stack,{p:2},t.createElement(s.Stack,{direction:"row",gap:1,alignItems:"center",justifyContent:"space-between"},t.createElement(s.FormLabel,{htmlFor:ue,size:"small"},(0,m.__)("Classes","elementor")),t.createElement(s.Stack,{direction:"row",gap:1},t.createElement(pe,null))),t.createElement(a.WarningInfotip,{open:Boolean(u),text:u??"",placement:"bottom",width:c.current?.getBoundingClientRect().width,offset:[0,-15]},t.createElement(F,{id:ue,ref:c,size:"tiny",placeholder:h?(0,m.__)("Type class name","elementor"):void 0,options:e,selected:v,entityName:b,onSelect:p,onCreate:f??void 0,validate:g??void 0,limitTags:50,renderEmptyState:ge,getLimitTagsText:e=>t.createElement(s.Chip,{size:"tiny",variant:"standard",label:`+${e}`,clickable:!0}),renderTags:(e,n)=>e.map(((e,r)=>{const o=n({index:r}),a=e.value===_?.value;return t.createElement(se,{key:o.key,fixed:e.fixed,label:e.label,provider:e.provider,id:e.value,isActive:a,color:a&&e.color?e.color:"default",icon:e.icon,chipProps:o,onClickActive:()=>l(e.value),renameLabel:t=>{if(!e.value)throw new Error("Cannot rename a class without style id");return be(e.provider,{label:t,id:e.value})},setError:d})}))})),!x&&t.createElement(a.InfoAlert,{content:(0,m.__)("With your current role, you can use existing classes but can’t modify them.","elementor"),sx:{mt:1}}))}var ge=({searchValue:e,onClear:n})=>t.createElement(s.Box,{sx:{py:4}},t.createElement(s.Stack,{gap:1,alignItems:"center",color:"text.secondary",justifyContent:"center",sx:{px:2,m:"auto",maxWidth:"236px"}},t.createElement(i.ColorSwatchIcon,{sx:{transform:"rotate(90deg)"},fontSize:"large"}),t.createElement(s.Typography,{align:"center",variant:"subtitle2"},(0,m.__)("Sorry, nothing matched","elementor"),t.createElement("br",null),"“",e,"”."),t.createElement(s.Typography,{align:"center",variant:"caption",sx:{mb:2}},(0,m.__)("With your current role,","elementor"),t.createElement("br",null),(0,m.__)("you can only use existing classes.","elementor")),t.createElement(s.Link,{color:"text.secondary",variant:"caption",component:"button",onClick:n},(0,m.__)("Clear & try again","elementor")))),be=(e,t)=>{if(!e)return;const n=o.stylesRepository.getProviderByKey(e);return n?n.actions.update?.(t):void 0},ve="tiny",_e="tiny",he=(0,b.createMenu)({components:{Action:function({title:e,visible:n=!0,icon:l,onClick:r}){return n?t.createElement(s.Tooltip,{placement:"bottom",title:e,arrow:!0},t.createElement(s.IconButton,{"aria-label":e,size:ve,onClick:r},t.createElement(l,{fontSize:ve}))):null},PopoverAction:function({title:e,visible:n=!0,icon:l,popoverContent:r}){const o=(0,t.useId)(),a=(0,s.usePopupState)({variant:"popover",popupId:`elementor-popover-action-${o}`});return n?t.createElement(t.Fragment,null,t.createElement(s.Tooltip,{placement:"top",title:e},t.createElement(s.IconButton,{"aria-label":e,key:o,size:_e,...(0,s.bindToggle)(a)},t.createElement(l,{fontSize:_e}))),t.createElement(s.Popover,{disablePortal:!0,disableScrollLock:!0,anchorOrigin:{vertical:"bottom",horizontal:"center"},...(0,s.bindPopover)(a)},t.createElement(s.Stack,{direction:"row",alignItems:"center",pl:1.5,pr:.5,py:1.5},t.createElement(l,{fontSize:_e,sx:{mr:.5}}),t.createElement(s.Typography,{variant:"subtitle2"},e),t.createElement(s.IconButton,{sx:{ml:"auto"},size:_e,onClick:a.close},t.createElement(i.XIcon,{fontSize:_e}))),t.createElement(r,{closePopover:a.close}))):null}}});function ye(){return t.createElement(s.Box,{role:"alert",sx:{minHeight:"100%",p:2}},t.createElement(s.Alert,{severity:"error",sx:{mb:2,maxWidth:400,textAlign:"center"}},t.createElement("strong",null,"Something went wrong")))}var xe=(0,t.createContext)(void 0),we=(0,s.styled)("div")`
	height: 100%;
	overflow-y: auto;
`;function Se({children:e}){const[n,l]=(0,t.useState)("up"),r=(0,t.useRef)(null),o=(0,t.useRef)(0);return(0,t.useEffect)((()=>{const e=r.current;if(!e)return;const t=()=>{const{scrollTop:t}=e;t>o.current?l("down"):t<o.current&&l("up"),o.current=t};return e.addEventListener("scroll",t),()=>{e.removeEventListener("scroll",t)}})),t.createElement(xe.Provider,{value:{direction:n}},t.createElement(we,{ref:r},e))}var Ce={defaultSectionsExpanded:{settings:["Content","Settings"],style:[]},defaultTab:"settings"},Ie=(0,t.createContext)({"e-div-block":{defaultSectionsExpanded:Ce.defaultSectionsExpanded,defaultTab:"style"},"e-flexbox":{defaultSectionsExpanded:Ce.defaultSectionsExpanded,defaultTab:"style"}}),Te=()=>{const{element:e}=L();return(0,t.useContext)(Ie)[e.type]||Ce},ke=(e,n)=>{const{element:l}=L(),r=(0,E.isExperimentActive)(Z.V_3_30),o=`elementor/editor-state/${l.id}/${e}`,a=r?(0,g.getSessionStorageItem)(o):n,[i,c]=(0,t.useState)(a??n);return[i,e=>{(0,g.setSessionStorageItem)(o,e),c(e)}]},ze={image:{component:e.ImageControl,layout:"full"},"svg-media":{component:e.SvgMediaControl,layout:"full"},text:{component:e.TextControl,layout:"full"},textarea:{component:e.TextAreaControl,layout:"full"},size:{component:e.SizeControl,layout:"two-columns"},select:{component:e.SelectControl,layout:"two-columns"},link:{component:e.LinkControl,layout:"full"},url:{component:e.UrlControl,layout:"full"},switch:{component:e.SwitchControl,layout:"two-columns"}},Pe=e=>ze[e]?.component,Ge=({props:e,type:n})=>{const l=Pe(n),{element:r}=L();if(!l)throw new R({context:{controlType:n}});return t.createElement(l,{...e,context:{elementId:r.id}})},Le=({children:e,layout:n})=>t.createElement(Re,{layout:n},e),Re=(0,s.styled)(s.Box,{shouldForwardProp:e=>!["layout"].includes(e)})((({layout:e,theme:t})=>({display:"grid",gridGap:t.spacing(1),...Be(e)}))),Be=e=>({justifyContent:"space-between",gridTemplateColumns:{full:"minmax(0, 1fr)","two-columns":"repeat(2, minmax(0, 1fr))"}[e]}),Ve=({schema:e})=>({key:"",kind:"object",meta:{},settings:{},default:null,shape:e}),Ae=({bind:n,children:l})=>{const{element:o,elementType:a}=L(),i=(0,r.useElementSetting)(o.id,n),c={[n]:i},s=Ve({schema:a.propsSchema});return t.createElement(e.PropProvider,{propType:s,value:c,setValue:e=>{(0,r.updateElementSettings)({id:o.id,props:{...e}})}},t.createElement(e.PropKeyProvider,{bind:n},l))},De=(0,s.styled)(i.ChevronDownIcon,{shouldForwardProp:e=>"open"!==e})((({theme:e,open:t})=>({transform:t?"rotate(180deg)":"rotate(0deg)",transition:e.transitions.create("transform",{duration:e.transitions.duration.standard})})));function Me({title:e,children:n,defaultExpanded:l=!1}){const[r,o]=ke(e,!!l),a=(0,t.useId)(),i=`label-${a}`,c=`content-${a}`;return t.createElement(t.Fragment,null,t.createElement(s.ListItemButton,{id:i,"aria-controls":c,onClick:()=>o(!r),sx:{"&:hover":{backgroundColor:"transparent"}}},t.createElement(s.ListItemText,{secondary:e,secondaryTypographyProps:{color:"text.primary",variant:"caption",fontWeight:"bold"}}),t.createElement(De,{open:r,color:"secondary",fontSize:"tiny"})),t.createElement(s.Collapse,{id:c,"aria-labelledby":i,in:r,timeout:"auto",unmountOnExit:!0},t.createElement(s.Stack,{gap:2.5,p:2},n)),t.createElement(s.Divider,null))}function Oe(e){return t.createElement(s.List,{disablePadding:!0,component:"div",...e})}var We=()=>{const{elementType:e,element:n}=L(),l=Te();return t.createElement(g.SessionStorageProvider,{prefix:n.id},t.createElement(Oe,null,e.controls.map((({type:e,value:n},r)=>{return"control"===e?t.createElement(je,{key:n.bind,control:n}):"section"===e?t.createElement(Me,{title:n.label,key:e+"."+r,defaultExpanded:(o=n.label,!(0,E.isExperimentActive)(Z.V_3_30)||l.defaultSectionsExpanded.settings?.includes(o))},n.items?.map((e=>"control"===e.type?t.createElement(je,{key:e.value.bind,control:e.value}):null))):null;var o}))))},je=({control:n})=>{if(!Pe(n.type))return null;const l=n.meta?.layout||(r=n.type,ze[r].layout);var r;return t.createElement(Ae,{bind:n.bind},n.meta?.topDivider&&t.createElement(s.Divider,null),t.createElement(Le,{layout:l},n.label?t.createElement(e.ControlFormLabel,null,n.label):null,t.createElement(Ge,{type:n.type,props:n.props})))},Fe=()=>{const{provider:e}=O(),[,n]=(0,t.useReducer)((e=>!e),!1);(0,t.useEffect)((()=>e?.subscribe(n)),[e])},$e="normal",Ue=e=>e??$e,Ne=e=>e??"desktop";function Ke(e,t){const n=function(e){const t={},n=(e,l)=>{const{id:r,children:o}=e;t[r]=l?[...l]:[],o?.forEach((e=>{n(e,[...t[r]??[],r])}))};return n(e),t}(t),l={};return t=>{const{breakpoint:r,state:o}=t,a=Ue(o),i=Ne(r);if(l[i]?.[a])return l[i][a].snapshot;const c=[...n[i],r];return c.forEach(((t,n)=>{const r=n>0?c[n-1]:null;((t,n,r)=>{const o=Ne(t),a=Ue(r);l[o]||(l[o]={[$e]:He(e({breakpoint:t,state:null}),n,{},null)}),r&&!l[o][a]&&(l[o][a]=He(e({breakpoint:t,state:r}),n,l[o],r))})(t,r?l[r]:void 0,o)})),l[i]?.[a]?.snapshot}}function He(e,t,n,l){const r=function(e){const t={};return e.forEach((e=>{const{variant:{props:n}}=e;Object.entries(n).forEach((([n,l])=>{const r=(0,p.filterEmptyValues)(l);if(null===r)return;t[n]||(t[n]=[]);const o={...e,value:r};t[n].push(o)}))})),{snapshot:t,stateSpecificSnapshot:t}}(e);return l?{snapshot:Je([r.snapshot,t?.[l]?.stateSpecificSnapshot,n[$e]?.snapshot]),stateSpecificSnapshot:Je([r.stateSpecificSnapshot,t?.[l]?.stateSpecificSnapshot])}:{snapshot:Je([r.snapshot,t?.[$e]?.snapshot]),stateSpecificSnapshot:void 0}}function Je(e){const t={};return e.filter(Boolean).forEach((e=>Object.entries(e).forEach((([e,n])=>{t[e]||(t[e]=[]),t[e]=t[e].concat(n)})))),t}function Ye(e,t,n){return e&&"object"==typeof e?function(e,t){return!!e&&(0,p.isTransformable)(t)&&e.key!==t.$$type}(n,e)?e:t.reduce(((e,t)=>e?(0,p.isTransformable)(e)?e.value?.[t]??null:"object"==typeof e?e[t]??null:null:null),e):null}var Xe=(e,t)=>e&&"union"===e.kind?Object.values(e.prop_types).find((e=>!!t.reduce(((e,t)=>{if("object"!==e?.kind)return null;const{shape:n}=e;return n[t]?n[t]:null}),e)))??null:null,qe=(0,t.createContext)(null);function Ze({children:e}){const n=et(),l=(0,v.getBreakpointsTree)(),{getSnapshot:r,getInheritanceChain:o}=function(e,t){const n=function(e){const t={};return e.forEach((e=>{const n=W(e.id)?.getKey()??null;e.variants.forEach((l=>{const{meta:r}=l,{state:o,breakpoint:a}=r,i=Ne(a),c=Ue(o);t[i]||(t[i]={});const s=t[i];s[c]||(s[c]=[]),s[c].push({style:e,variant:l,provider:n})}))})),t}(e);return{getSnapshot:Ke((({breakpoint:e,state:t})=>n?.[Ne(e)]?.[Ue(t)]??[]),t),getInheritanceChain:(e,t,n)=>{const[l,...r]=t;let o=e[l]??[];if(r.length>0){const e=Xe(n,r);o=o.map((({value:t,...n})=>({...n,value:Ye(t,r,e)}))).filter((({value:e})=>!(0,p.isEmpty)(e)))}return o}}}(n,l);return t.createElement(qe.Provider,{value:{getSnapshot:r,getInheritanceChain:o}},e)}function Qe(e){const n=(0,t.useContext)(qe);if(!n)throw new Error("useStylesInheritanceChain must be used within a StyleInheritanceProvider");const l=(0,_.getStylesSchema)(),r=l?.[e[0]],o=function(){const e=(0,t.useContext)(qe),{meta:n}=O();if(!e)throw new Error("useStylesInheritanceSnapshot must be used within a StyleInheritanceProvider");return n?e.getSnapshot(n)??null:null}();return o?n.getInheritanceChain(o,e,r):[]}var et=()=>{const{element:e}=L(),t=z(),n=tt();Fe();const l=(0,r.useElementSetting)(e.id,t),a=p.classesPropTypeUtil.extract(l)??[];return o.stylesRepository.all().filter((e=>[...n,...a].includes(e.id)))},tt=()=>{const{elementType:e}=L(),t=(0,r.getWidgetsCache)(),n=t?.[e.key];return Object.keys(n?.base_styles??{})};function nt(e){const{element:n}=L(),{id:l,meta:a,provider:i}=O(),c=z(),s=(0,t.useMemo)((()=>(0,E.undoable)({do:({elementId:e,styleId:t,provider:n,meta:l,props:r})=>{if(!n.actions.updateProps)throw new V({context:{providerKey:n.getKey()}});const o=function(e,t){if(!e)return{};const n=(0,_.getVariantByMeta)(e,t);return structuredClone(n?.props??{})}(n.actions.get(t,{elementId:e}),l);return n.actions.updateProps({id:t,meta:l,props:r},{elementId:e}),o},undo:({elementId:e,styleId:t,meta:n,provider:l},r)=>{l.actions.updateProps?.({id:t,meta:n,props:r},{elementId:e})}},{title:({elementId:e})=>(0,r.getElementLabel)(e),subtitle:(0,m.__)("Style edited","elementor")})),[]),u=(0,t.useMemo)((()=>(0,E.undoable)({do:e=>(0,r.createElementStyle)({...e,label:o.ELEMENTS_STYLES_RESERVED_LABEL}),undo:({elementId:e},t)=>{(0,r.deleteElementStyle)(e,t)},redo:(e,t)=>(0,r.createElementStyle)({...e,styleId:t,label:o.ELEMENTS_STYLES_RESERVED_LABEL})},{title:({elementId:e})=>(0,r.getElementLabel)(e),subtitle:(0,m.__)("Style edited","elementor")})),[]);Fe();const d=function({styleId:e,elementId:t,provider:n,meta:l,propNames:r}){if(!n||!e)return null;const o=n.actions.get(e,{elementId:t});if(!o)throw new A({context:{styleId:e,providerKey:n.getKey()}});const a=(0,_.getVariantByMeta)(o,l);return Object.fromEntries(r.map((e=>[e,a?.props[e]??null])))}({elementId:n.id,styleId:l,provider:i,meta:a,propNames:e});return[d,e=>{null!==l?s({elementId:n.id,styleId:l,provider:i,meta:a,props:e}):u({elementId:n.id,classesProp:c,meta:a,props:e})}]}function lt(e){const[t,n]=nt([e]);return[t?.[e]??null,t=>{n({[e]:t})}]}var rt=new Set(["background-color-overlay","background-image-overlay","background-gradient-overlay","gradient-color-stop","color-stop","background-image-position-offset","background-image-size-scale","image-src","image","background-overlay"]),ot=()=>(0,E.isExperimentActive)("e_v_3_30"),at=(0,t.createContext)(null),it=({gap:e=2,sx:n,children:l})=>{const r=(0,t.useRef)(null);return t.createElement(at.Provider,{value:r},t.createElement(s.Stack,{gap:e,sx:{...n},ref:r},l))};function ct(){const e="rtl"===(0,s.useTheme)().direction;return{isSiteRtl:!!(()=>{const e=window;return e.elementorFrontend?.config??{}})()?.is_rtl,isUiRtl:e}}var st={widescreen:i.WidescreenIcon,desktop:i.DesktopIcon,laptop:i.LaptopIcon,tablet_extra:i.TabletLandscapeIcon,tablet:i.TabletPortraitIcon,mobile_extra:i.MobileLandscapeIcon,mobile:i.MobilePortraitIcon},mt=({breakpoint:e})=>{const n=(0,v.useBreakpoints)(),l=e||"desktop",r=st[l];if(!r)return null;const o=n.find((e=>e.id===l))?.label;return t.createElement(s.Tooltip,{title:o,placement:"top"},t.createElement(r,{fontSize:"tiny",sx:{mt:"2px"}}))},ut="tiny",dt=({displayLabel:e,provider:n,chipColor:l})=>{const r=n===o.ELEMENTS_BASE_STYLES_PROVIDER_KEY?t.createElement(s.Tooltip,{title:(0,m.__)("Inherited from base styles","elementor"),placement:"top"},t.createElement(i.InfoCircleIcon,{fontSize:ut})):void 0;return t.createElement(s.Chip,{label:e,size:ut,color:l,variant:"standard",state:"enabled",icon:r,sx:e=>({lineHeight:1,flexWrap:"nowrap",alignItems:"center",borderRadius:.75*e.shape.borderRadius+"px",flexDirection:"row-reverse",".MuiChip-label":{overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"}})})},pt=({index:e,value:n})=>t.createElement(s.Typography,{variant:"caption",color:"text.tertiary",sx:{mt:"1px",textDecoration:0===e?"none":"line-through",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"}},n),Et=()=>t.createElement(s.Box,{display:"flex",gap:.5,alignItems:"center"}),ft=async(e,n,l)=>{try{const r=await l({props:{[n]:e.value}}),o=r?.[n]??r;return(0,t.isValidElement)(o)?o:"object"==typeof o?JSON.stringify(o):String(o)}catch{return""}},gt=e=>{const{provider:t="",style:n}=e;return t===o.ELEMENTS_BASE_STYLES_PROVIDER_KEY?"default":"local"===n?.label?"accent":"global"},bt=(0,h.createTransformersRegistry)(),vt=({inheritanceChain:e,propType:n,path:l,label:r,children:a})=>{const[i,c]=(0,t.useState)(!1),u=()=>c(!1),d=l.join("."),p=(0,t.useContext)(at),E=p?.current?.offsetWidth??320,f=(0,t.useMemo)((()=>(0,h.createPropsResolver)({transformers:bt,schema:{[d]:n}})),[d,n]),g=((e,n,l)=>{const[r,a]=(0,t.useState)([]);return(0,t.useEffect)((()=>{(async()=>{const t=(await Promise.all(e.filter((({style:e})=>e)).map(((e,t)=>(async(e,t,n,l)=>{const{variant:{meta:{state:r,breakpoint:o}},style:{label:a,id:i}}=e,c=`${a}${r?":"+r:""}`;return{id:i?i+(r??""):t,provider:e.provider||"",breakpoint:o??"desktop",displayLabel:c,value:await ft(e,n,l),chipColor:gt(e)}})(e,t,n,l))))).map((e=>({...e,displayLabel:o.ELEMENTS_BASE_STYLES_PROVIDER_KEY!==e.provider?e.displayLabel:(0,m.__)("Base","elementor")}))).filter((e=>!e.value||""!==e.displayLabel)).slice(0,2);a(t)})()}),[e,n,l]),r})(e,d,f),b=t.createElement(s.ClickAwayListener,{onClickAway:u},t.createElement(s.Card,{elevation:0,sx:{width:`${E}px`,maxWidth:500,overflowX:"hidden"}},t.createElement(s.CardContent,{sx:{display:"flex",gap:.5,flexDirection:"column",p:0,"&:last-child":{pb:0}}},t.createElement(s.Stack,{direction:"row",alignItems:"center",sx:{pl:1.5,pr:.5,minHeight:36,py:.5}},t.createElement(s.Typography,{variant:"subtitle2",color:"secondary",sx:{fontSize:12,fontWeight:"500"}},(0,m.__)("Style origin","elementor")),t.createElement(s.CloseButton,{slotProps:{icon:{fontSize:"tiny"}},sx:{ml:"auto"},onClick:u})),t.createElement(s.Stack,{gap:1.5,sx:{pl:2,pr:1,pb:2,overflowX:"hidden",overflowY:"auto"},role:"list"},g.map(((e,n)=>t.createElement(s.Box,{key:e.id,display:"flex",gap:.5,role:"listitem","aria-label":(0,m.__)("Inheritance item: %s","elementor").replace("%s",e.displayLabel)},t.createElement(s.Box,{display:"flex",gap:.5,sx:{flexWrap:"wrap",width:"100%"}},t.createElement(mt,{breakpoint:e.breakpoint}),t.createElement(dt,{displayLabel:e.displayLabel,provider:e.provider,chipColor:e.chipColor}),t.createElement(pt,{index:n,value:e.value})),t.createElement(Et,null))))))));return t.createElement(_t,{showInfotip:i,onClose:u,infotipContent:b},t.createElement(s.IconButton,{onClick:()=>c((e=>!e)),"aria-label":r,sx:{my:"-1px"}},a))};function _t({children:e,showInfotip:n,onClose:l,infotipContent:r}){const{isSiteRtl:o}=ct(),a=o?9999999:-9999999;return n?t.createElement(t.Fragment,null,t.createElement(s.Backdrop,{open:n,onClick:l,sx:{backgroundColor:"transparent",zIndex:e=>e.zIndex.modal-1}}),t.createElement(s.Infotip,{placement:"top",content:r,open:n,onClose:l,disableHoverListener:!0,componentsProps:{tooltip:{sx:{mx:2}}},slotProps:{popper:{modifiers:[{name:"offset",options:{offset:[a,0]}}]}}},e)):t.createElement(s.Tooltip,{title:(0,m.__)("Style origin","elementor"),placement:"top"},e)}var ht=()=>{const{path:n,propType:l}=(0,e.useBoundProp)(),{id:r,provider:a,meta:i}=O(),c=(0,E.isExperimentActive)(Z.V_3_30)?n:n.slice(0,1),u=Qe(c);if(!u.length)return null;const d=u.find((({style:e,variant:{meta:{breakpoint:t,state:n}}})=>e.id===r&&t===i.breakpoint&&n===i.state)),f=!(0,p.isEmpty)(d?.value),[g]=u;if(g.provider===o.ELEMENTS_BASE_STYLES_PROVIDER_KEY)return null;const b=d===g,v=yt({isFinalValue:b,hasValue:f}),_=xt({isFinalValue:b,hasValue:f,currentStyleProvider:a});return ot()?t.createElement(vt,{inheritanceChain:u,path:c,propType:l,label:v},t.createElement(q,{variant:_})):t.createElement(s.Tooltip,{title:(0,m.__)("Style origin","elementor"),placement:"top"},t.createElement(q,{variant:_,"aria-label":v}))},yt=({isFinalValue:e,hasValue:t})=>e?(0,m.__)("This is the final value","elementor"):t?(0,m.__)("This value is overridden by another style","elementor"):(0,m.__)("This has value from another style","elementor"),xt=({isFinalValue:e,hasValue:t,currentStyleProvider:n})=>e?(0,o.isElementsStylesProvider)(n?.getKey?.())?"local":"global":t?"overridden":void 0,wt=({bind:n,placeholder:l,children:r})=>{const[o,a]=lt(n),{canEdit:i}=O(),c=(0,_.getStylesSchema)(),s=Ve({schema:c}),m={[n]:o},u={[n]:l};return t.createElement(e.ControlAdornmentsProvider,{items:[{id:"styles-inheritance",Adornment:ht}]},t.createElement(e.PropProvider,{propType:s,value:m,setValue:e=>{a(e[n])},placeholder:u,disabled:!i},t.createElement(e.PropKeyProvider,{bind:n},r)))},St=()=>t.createElement(it,null,t.createElement(wt,{bind:"background"},t.createElement(e.BackgroundControl,null))),Ct=()=>t.createElement(s.Divider,{sx:{my:.5}}),It=({children:n})=>t.createElement(s.Stack,{direction:"row",alignItems:"center",justifyItems:"start",gap:1},t.createElement(e.ControlFormLabel,null,n),t.createElement(e.ControlAdornments,null)),Tt="tiny",kt=({isAdded:e,label:n,onAdd:l,onRemove:r,children:o,disabled:a})=>t.createElement(it,null,t.createElement(s.Stack,{direction:"row",sx:{justifyContent:"space-between",alignItems:"center",marginInlineEnd:-.75}},t.createElement(It,null,n),e?t.createElement(s.IconButton,{size:Tt,onClick:r,"aria-label":"Remove",disabled:a},t.createElement(i.MinusIcon,{fontSize:Tt})):t.createElement(s.IconButton,{size:Tt,onClick:l,"aria-label":"Add",disabled:a},t.createElement(i.PlusIcon,{fontSize:Tt}))),t.createElement(s.Collapse,{in:e,unmountOnExit:!0},t.createElement(it,null,o))),zt=()=>t.createElement(wt,{bind:"border-color"},t.createElement(s.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},t.createElement(s.Grid,{item:!0,xs:6},t.createElement(It,null,(0,m.__)("Border color","elementor"))),t.createElement(s.Grid,{item:!0,xs:6},t.createElement(e.ColorControl,null)))),Pt=[{value:"none",label:(0,m.__)("None","elementor")},{value:"solid",label:(0,m.__)("Solid","elementor")},{value:"dashed",label:(0,m.__)("Dashed","elementor")},{value:"dotted",label:(0,m.__)("Dotted","elementor")},{value:"double",label:(0,m.__)("Double","elementor")},{value:"groove",label:(0,m.__)("Groove","elementor")},{value:"ridge",label:(0,m.__)("Ridge","elementor")},{value:"inset",label:(0,m.__)("Inset","elementor")},{value:"outset",label:(0,m.__)("Outset","elementor")}],Gt=()=>t.createElement(wt,{bind:"border-style"},t.createElement(s.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},t.createElement(s.Grid,{item:!0,xs:6},t.createElement(It,null,(0,m.__)("Border type","elementor"))),t.createElement(s.Grid,{item:!0,xs:6,sx:{overflow:"hidden"}},t.createElement(e.SelectControl,{options:Pt})))),Lt=(0,s.withDirection)(i.SideRightIcon),Rt=(0,s.withDirection)(i.SideLeftIcon),Bt=e=>[{label:(0,m.__)("Top","elementor"),icon:t.createElement(i.SideTopIcon,{fontSize:"tiny"}),bind:"block-start"},{label:e?(0,m.__)("Left","elementor"):(0,m.__)("Right","elementor"),icon:t.createElement(Lt,{fontSize:"tiny"}),bind:"inline-end"},{label:(0,m.__)("Bottom","elementor"),icon:t.createElement(i.SideBottomIcon,{fontSize:"tiny"}),bind:"block-end"},{label:e?(0,m.__)("Right","elementor"):(0,m.__)("Left","elementor"),icon:t.createElement(Rt,{fontSize:"tiny"}),bind:"inline-start"}],Vt=()=>{const{isSiteRtl:n}=ct();return t.createElement(wt,{bind:"border-width"},t.createElement(e.EqualUnequalSizesControl,{items:Bt(n),label:(0,m.__)("Border width","elementor"),icon:t.createElement(i.SideAllIcon,{fontSize:"tiny"}),tooltipLabel:(0,m.__)("Adjust borders","elementor"),multiSizePropTypeUtil:p.borderWidthPropTypeUtil}))},At={"border-width":{$$type:"size",value:{size:1,unit:"px"}},"border-color":{$$type:"color",value:"#000000"},"border-style":{$$type:"string",value:"solid"}},Dt=()=>{const{canEdit:e}=O(),[n,l]=nt(Object.keys(At)),r=Object.values(n??{}).some(Boolean);return t.createElement(kt,{label:(0,m.__)("Border","elementor"),isAdded:r,onAdd:()=>{l(At)},onRemove:()=>{l({"border-width":null,"border-color":null,"border-style":null})},disabled:!e},t.createElement(Vt,null),t.createElement(zt,null),t.createElement(Gt,null))},Mt=(0,s.withDirection)(i.RadiusTopLeftIcon),Ot=(0,s.withDirection)(i.RadiusTopRightIcon),Wt=(0,s.withDirection)(i.RadiusBottomLeftIcon),jt=(0,s.withDirection)(i.RadiusBottomRightIcon),Ft=e=>e?(0,m.__)("Top right","elementor"):(0,m.__)("Top left","elementor"),$t=e=>e?(0,m.__)("Top left","elementor"):(0,m.__)("Top right","elementor"),Ut=e=>e?(0,m.__)("Bottom right","elementor"):(0,m.__)("Bottom left","elementor"),Nt=e=>e?(0,m.__)("Bottom left","elementor"):(0,m.__)("Bottom right","elementor"),Kt=e=>[{label:Ft(e),icon:t.createElement(Mt,{fontSize:"tiny"}),bind:"start-start"},{label:$t(e),icon:t.createElement(Ot,{fontSize:"tiny"}),bind:"start-end"},{label:Ut(e),icon:t.createElement(Wt,{fontSize:"tiny"}),bind:"end-start"},{label:Nt(e),icon:t.createElement(jt,{fontSize:"tiny"}),bind:"end-end"}],Ht=()=>{const{isSiteRtl:n}=ct();return t.createElement(wt,{bind:"border-radius"},t.createElement(e.EqualUnequalSizesControl,{items:Kt(n),label:(0,m.__)("Border radius","elementor"),icon:t.createElement(i.BorderCornersIcon,{fontSize:"tiny"}),tooltipLabel:(0,m.__)("Adjust corners","elementor"),multiSizePropTypeUtil:p.borderRadiusPropTypeUtil}))},Jt=()=>t.createElement(it,null,t.createElement(Ht,null),t.createElement(Ct,null),t.createElement(Dt,null)),Yt=()=>t.createElement(it,null,t.createElement(wt,{bind:"box-shadow"},t.createElement(e.BoxShadowRepeaterControl,null))),Xt={row:0,column:90,"row-reverse":180,"column-reverse":270},qt={row:0,column:-90,"row-reverse":-180,"column-reverse":-270},Zt=({icon:e,size:n,isClockwise:l=!0,offset:r=0,disableRotationForReversed:o=!1})=>{const a=(0,t.useRef)(Qt(l,r,o));return a.current=Qt(l,r,o,a),t.createElement(e,{fontSize:n,sx:{transition:".3s",rotate:`${a.current}deg`}})},Qt=(e,t,n,l)=>{const[r]=lt("flex-direction"),o="rtl"===(0,s.useTheme)().direction?-1:1,a=e?Xt:qt,i=r?.value||"row",c=l?l.current*o:a[i]+t,m=((a[i]+t-c+360)%360+180)%360-180;return n&&["row-reverse","column-reverse"].includes(i)?0:(c+m)*o},en=(0,s.withDirection)(i.JustifyTopIcon),tn=(0,s.withDirection)(i.JustifyBottomIcon),nn={isClockwise:!1,offset:0,disableRotationForReversed:!0},ln=[{value:"start",label:(0,m.__)("Start","elementor"),renderContent:({size:e})=>t.createElement(Zt,{icon:en,size:e,...nn}),showTooltip:!0},{value:"center",label:(0,m.__)("Center","elementor"),renderContent:({size:e})=>t.createElement(Zt,{icon:i.JustifyCenterIcon,size:e,...nn}),showTooltip:!0},{value:"end",label:(0,m.__)("End","elementor"),renderContent:({size:e})=>t.createElement(Zt,{icon:tn,size:e,...nn}),showTooltip:!0},{value:"space-between",label:(0,m.__)("Space between","elementor"),renderContent:({size:e})=>t.createElement(Zt,{icon:i.JustifySpaceBetweenVerticalIcon,size:e,...nn}),showTooltip:!0},{value:"space-around",label:(0,m.__)("Space around","elementor"),renderContent:({size:e})=>t.createElement(Zt,{icon:i.JustifySpaceAroundVerticalIcon,size:e,...nn}),showTooltip:!0},{value:"space-evenly",label:(0,m.__)("Space evenly","elementor"),renderContent:({size:e})=>t.createElement(Zt,{icon:i.JustifyDistributeVerticalIcon,size:e,...nn}),showTooltip:!0}],rn=()=>{const{isSiteRtl:n}=ct();return t.createElement(s.DirectionProvider,{rtl:n},t.createElement(s.ThemeProvider,null,t.createElement(wt,{bind:"align-content"},t.createElement(s.Stack,{gap:1},t.createElement(It,null,(0,m.__)("Align content","elementor")),t.createElement(e.ToggleControl,{options:ln,fullWidth:!0})))))},on=(0,s.withDirection)(i.LayoutAlignLeftIcon),an=(0,s.withDirection)(i.LayoutAlignRightIcon),cn={isClockwise:!1,offset:90},sn=[{value:"start",label:(0,m.__)("Start","elementor"),renderContent:({size:e})=>t.createElement(Zt,{icon:on,size:e,...cn}),showTooltip:!0},{value:"center",label:(0,m.__)("Center","elementor"),renderContent:({size:e})=>t.createElement(Zt,{icon:i.LayoutAlignCenterIcon,size:e,...cn}),showTooltip:!0},{value:"end",label:(0,m.__)("End","elementor"),renderContent:({size:e})=>t.createElement(Zt,{icon:an,size:e,...cn}),showTooltip:!0},{value:"stretch",label:(0,m.__)("Stretch","elementor"),renderContent:({size:e})=>t.createElement(Zt,{icon:i.LayoutDistributeVerticalIcon,size:e,...cn}),showTooltip:!0}],mn=()=>{const{isSiteRtl:n}=ct();return t.createElement(s.DirectionProvider,{rtl:n},t.createElement(s.ThemeProvider,null,t.createElement(wt,{bind:"align-items"},t.createElement(s.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},t.createElement(s.Grid,{item:!0,xs:6},t.createElement(It,null,(0,m.__)("Align items","elementor"))),t.createElement(s.Grid,{item:!0,xs:6,sx:{display:"flex",justifyContent:"end"}},t.createElement(e.ToggleControl,{options:sn}))))))},un={row:90,"row-reverse":90,column:0,"column-reverse":0},dn=(0,s.withDirection)(i.LayoutAlignLeftIcon),pn=(0,s.withDirection)(i.LayoutAlignRightIcon),En={isClockwise:!1},fn=e=>[{value:"start",label:(0,m.__)("Start","elementor"),renderContent:({size:n})=>t.createElement(Zt,{icon:dn,size:n,offset:un[e],...En}),showTooltip:!0},{value:"center",label:(0,m.__)("Center","elementor"),renderContent:({size:n})=>t.createElement(Zt,{icon:i.LayoutAlignCenterIcon,size:n,offset:un[e],...En}),showTooltip:!0},{value:"end",label:(0,m.__)("End","elementor"),renderContent:({size:n})=>t.createElement(Zt,{icon:pn,size:n,offset:un[e],...En}),showTooltip:!0},{value:"stretch",label:(0,m.__)("Stretch","elementor"),renderContent:({size:n})=>t.createElement(Zt,{icon:i.LayoutDistributeVerticalIcon,size:n,offset:un[e],...En}),showTooltip:!0}],gn=({parentStyleDirection:n})=>{const{isSiteRtl:l}=ct();return t.createElement(s.DirectionProvider,{rtl:l},t.createElement(s.ThemeProvider,null,t.createElement(wt,{bind:"align-self"},t.createElement(s.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},t.createElement(s.Grid,{item:!0,xs:6},t.createElement(It,null,(0,m.__)("Align self","elementor"))),t.createElement(s.Grid,{item:!0,xs:6,sx:{display:"flex",justifyContent:"flex-end"}},t.createElement(e.ToggleControl,{options:fn(n)}))))))},bn=[{value:"block",renderContent:()=>(0,m.__)("Block","elementor"),label:(0,m.__)("Block","elementor"),showTooltip:!0},{value:"flex",renderContent:()=>(0,m.__)("Flex","elementor"),label:(0,m.__)("Flex","elementor"),showTooltip:!0},{value:"inline-block",renderContent:()=>(0,m.__)("In-blk","elementor"),label:(0,m.__)("Inline-block","elementor"),showTooltip:!0}],vn=()=>{const n=(0,E.isExperimentActive)(Z.V_3_30),l=[...bn];n&&l.push({value:"none",renderContent:()=>(0,m.__)("None","elementor"),label:(0,m.__)("None","elementor"),showTooltip:!0}),l.push({value:"inline-flex",renderContent:()=>(0,m.__)("In-flx","elementor"),label:(0,m.__)("Inline-flex","elementor"),showTooltip:!0});const r=_n();return t.createElement(wt,{bind:"display",placeholder:r},t.createElement(s.Stack,{gap:.75},t.createElement(It,null,(0,m.__)("Display","elementor")),t.createElement(e.ToggleControl,{options:l,maxItems:4,fullWidth:!0})))},_n=()=>Qe(["display"])[0]?.value??void 0,hn=[{value:"row",label:(0,m.__)("Row","elementor"),renderContent:({size:e})=>{const n=(0,s.withDirection)(i.ArrowRightIcon);return t.createElement(n,{fontSize:e})},showTooltip:!0},{value:"column",label:(0,m.__)("Column","elementor"),renderContent:({size:e})=>t.createElement(i.ArrowDownSmallIcon,{fontSize:e}),showTooltip:!0},{value:"row-reverse",label:(0,m.__)("Reversed row","elementor"),renderContent:({size:e})=>{const n=(0,s.withDirection)(i.ArrowLeftIcon);return t.createElement(n,{fontSize:e})},showTooltip:!0},{value:"column-reverse",label:(0,m.__)("Reversed column","elementor"),renderContent:({size:e})=>t.createElement(i.ArrowUpSmallIcon,{fontSize:e}),showTooltip:!0}],yn=()=>{const{isSiteRtl:n}=ct();return t.createElement(s.DirectionProvider,{rtl:n},t.createElement(s.ThemeProvider,null,t.createElement(wt,{bind:"flex-direction"},t.createElement(s.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},t.createElement(s.Grid,{item:!0,xs:6},t.createElement(It,null,(0,m.__)("Direction","elementor"))),t.createElement(s.Grid,{item:!0,xs:6,sx:{display:"flex",justifyContent:"end"}},t.createElement(e.ToggleControl,{options:hn}))))))},xn=-99999,wn="first",Sn="last",Cn="custom",In={[wn]:xn,[Sn]:99999},Tn=[{value:wn,label:(0,m.__)("First","elementor"),renderContent:({size:e})=>t.createElement(i.ArrowUpSmallIcon,{fontSize:e}),showTooltip:!0},{value:Sn,label:(0,m.__)("Last","elementor"),renderContent:({size:e})=>t.createElement(i.ArrowDownSmallIcon,{fontSize:e}),showTooltip:!0},{value:Cn,label:(0,m.__)("Custom","elementor"),renderContent:({size:e})=>t.createElement(i.PencilIcon,{fontSize:e}),showTooltip:!0}],kn=()=>{const{isSiteRtl:n}=ct(),[l,r]=lt("order"),{canEdit:o}=O(),[a,i]=(0,t.useState)(zn(l?.value||null));return t.createElement(s.DirectionProvider,{rtl:n},t.createElement(s.ThemeProvider,null,t.createElement(wt,{bind:"order"},t.createElement(it,null,t.createElement(s.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},t.createElement(s.Grid,{item:!0,xs:6},t.createElement(It,null,(0,m.__)("Order","elementor"))),t.createElement(s.Grid,{item:!0,xs:6,sx:{display:"flex",justifyContent:"end"}},t.createElement(e.ControlToggleButtonGroup,{items:Tn,value:a,onChange:e=>{i(e),r(e&&e!==Cn?{$$type:"number",value:In[e]}:null)},exclusive:!0,disabled:!o}))),Cn===a&&t.createElement(s.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},t.createElement(s.Grid,{item:!0,xs:6},t.createElement(It,null,(0,m.__)("Custom order","elementor"))),t.createElement(s.Grid,{item:!0,xs:6,sx:{display:"flex",justifyContent:"end"}},t.createElement(e.NumberControl,{min:-99998,max:99998,shouldForceInt:!0})))))))},zn=e=>99999===e?Sn:xn===e?wn:0===e||e?Cn:null,Pn=[{value:"flex-grow",label:(0,m.__)("Grow","elementor"),renderContent:({size:e})=>t.createElement(i.ExpandIcon,{fontSize:e}),showTooltip:!0},{value:"flex-shrink",label:(0,m.__)("Shrink","elementor"),renderContent:({size:e})=>t.createElement(i.ShrinkIcon,{fontSize:e}),showTooltip:!0},{value:"custom",label:(0,m.__)("Custom","elementor"),renderContent:({size:e})=>t.createElement(i.PencilIcon,{fontSize:e}),showTooltip:!0}],Gn=()=>{const{isSiteRtl:n}=ct(),{canEdit:l}=O(),[r,o]=nt(["flex-grow","flex-shrink","flex-basis"]),a=r?.["flex-grow"]?.value||null,i=r?.["flex-shrink"]?.value||null,c=r?.["flex-basis"]?.value||null,u=(0,t.useMemo)((()=>Rn({grow:a,shrink:i,basis:c})),[a,i,c]),[d,E]=(0,t.useState)(u);return t.createElement(s.DirectionProvider,{rtl:n},t.createElement(s.ThemeProvider,null,t.createElement(it,null,t.createElement(s.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},t.createElement(wt,{bind:d??""},t.createElement(s.Grid,{item:!0,xs:6},t.createElement(It,null,(0,m.__)("Size","elementor"))),t.createElement(s.Grid,{item:!0,xs:6,sx:{display:"flex",justifyContent:"end"}},t.createElement(e.ControlToggleButtonGroup,{value:d,onChange:(e=null)=>{E(e),o(e&&"custom"!==e?"flex-grow"!==e?{"flex-basis":null,"flex-grow":null,"flex-shrink":p.numberPropTypeUtil.create(1)}:{"flex-basis":null,"flex-grow":p.numberPropTypeUtil.create(1),"flex-shrink":null}:{"flex-basis":null,"flex-grow":null,"flex-shrink":null})},disabled:!l,items:Pn,exclusive:!0})))),"custom"===d&&t.createElement(Ln,null))))},Ln=()=>t.createElement(t.Fragment,null,t.createElement(wt,{bind:"flex-grow"},t.createElement(s.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},t.createElement(s.Grid,{item:!0,xs:6},t.createElement(It,null,(0,m.__)("Grow","elementor"))),t.createElement(s.Grid,{item:!0,xs:6,sx:{display:"flex",justifyContent:"end"}},t.createElement(e.NumberControl,{min:0,shouldForceInt:!0})))),t.createElement(wt,{bind:"flex-shrink"},t.createElement(s.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},t.createElement(s.Grid,{item:!0,xs:6},t.createElement(It,null,(0,m.__)("Shrink","elementor"))),t.createElement(s.Grid,{item:!0,xs:6,sx:{display:"flex",justifyContent:"end"}},t.createElement(e.NumberControl,{min:0,shouldForceInt:!0})))),t.createElement(wt,{bind:"flex-basis"},t.createElement(s.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},t.createElement(s.Grid,{item:!0,xs:6},t.createElement(It,null,(0,m.__)("Basis","elementor"))),t.createElement(s.Grid,{item:!0,xs:6,sx:{display:"flex",justifyContent:"end"}},t.createElement(e.SizeControl,{extendedValues:["auto"]}))))),Rn=({grow:e,shrink:t,basis:n})=>null!==e||null!==t||n?t&&e||n?"custom":1===e?"flex-grow":1===t?"flex-shrink":"custom":null,Bn=()=>t.createElement(s.Stack,{gap:1},t.createElement(wt,{bind:"gap"},t.createElement(e.GapControl,{label:(0,m.__)("Gaps","elementor")}))),Vn=(0,s.withDirection)(i.JustifyTopIcon),An=(0,s.withDirection)(i.JustifyBottomIcon),Dn={isClockwise:!0,offset:-90},Mn=[{value:"flex-start",label:(0,m.__)("Start","elementor"),renderContent:({size:e})=>t.createElement(Zt,{icon:Vn,size:e,...Dn}),showTooltip:!0},{value:"center",label:(0,m.__)("Center","elementor"),renderContent:({size:e})=>t.createElement(Zt,{icon:i.JustifyCenterIcon,size:e,...Dn}),showTooltip:!0},{value:"flex-end",label:(0,m.__)("End","elementor"),renderContent:({size:e})=>t.createElement(Zt,{icon:An,size:e,...Dn}),showTooltip:!0},{value:"space-between",label:(0,m.__)("Space between","elementor"),renderContent:({size:e})=>t.createElement(Zt,{icon:i.JustifySpaceBetweenVerticalIcon,size:e,...Dn}),showTooltip:!0},{value:"space-around",label:(0,m.__)("Space around","elementor"),renderContent:({size:e})=>t.createElement(Zt,{icon:i.JustifySpaceAroundVerticalIcon,size:e,...Dn}),showTooltip:!0},{value:"space-evenly",label:(0,m.__)("Space evenly","elementor"),renderContent:({size:e})=>t.createElement(Zt,{icon:i.JustifyDistributeVerticalIcon,size:e,...Dn}),showTooltip:!0}],On=()=>{const{isSiteRtl:n}=ct();return t.createElement(s.DirectionProvider,{rtl:n},t.createElement(s.ThemeProvider,null,t.createElement(wt,{bind:"justify-content"},t.createElement(s.Stack,{gap:.75},t.createElement(It,null,(0,m.__)("Justify content","elementor")),t.createElement(e.ToggleControl,{options:Mn,fullWidth:!0})))))},Wn=[{value:"nowrap",label:(0,m.__)("No wrap","elementor"),renderContent:({size:e})=>t.createElement(i.ArrowRightIcon,{fontSize:e}),showTooltip:!0},{value:"wrap",label:(0,m.__)("Wrap","elementor"),renderContent:({size:e})=>t.createElement(i.ArrowBackIcon,{fontSize:e}),showTooltip:!0},{value:"wrap-reverse",label:(0,m.__)("Reversed wrap","elementor"),renderContent:({size:e})=>t.createElement(i.ArrowForwardIcon,{fontSize:e}),showTooltip:!0}],jn=()=>{const{isSiteRtl:n}=ct();return t.createElement(s.DirectionProvider,{rtl:n},t.createElement(s.ThemeProvider,null,t.createElement(wt,{bind:"flex-wrap"},t.createElement(s.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},t.createElement(s.Grid,{item:!0,xs:6},t.createElement(It,null,(0,m.__)("Wrap","elementor"))),t.createElement(s.Grid,{item:!0,xs:6,sx:{display:"flex",justifyContent:"flex-end"}},t.createElement(e.ToggleControl,{options:Wn}))))))},Fn=()=>{const[e]=lt("display"),n=_n(),l=Nn(e,n),{element:o}=L(),a=(0,r.useParentElement)(o.id),i=(c=a?.id||null,(0,E.__privateUseListenTo)([(0,E.windowEvent)("elementor/device-mode/change"),(0,E.commandEndEvent)("document/elements/reset-style"),(0,E.commandEndEvent)("document/elements/settings"),(0,E.commandEndEvent)("document/elements/paste-style")],(()=>{if(!c)return null;const e=window,t=e.elementor?.getContainer?.(c);return t?.view?.el?window.getComputedStyle(t.view.el):null})));var c;const s=i?.flexDirection??"row";return t.createElement(it,null,t.createElement(vn,null),l&&t.createElement($n,null),"flex"===i?.display&&t.createElement(Un,{parentStyleDirection:s}))},$n=()=>{const[e]=lt("flex-wrap");return t.createElement(t.Fragment,null,t.createElement(yn,null),t.createElement(On,null),t.createElement(mn,null),t.createElement(Ct,null),t.createElement(Bn,null),t.createElement(jn,null),["wrap","wrap-reverse"].includes(e?.value)&&t.createElement(rn,null))},Un=({parentStyleDirection:n})=>t.createElement(t.Fragment,null,t.createElement(Ct,null),t.createElement(e.ControlFormLabel,null,(0,m.__)("Flex child","elementor")),t.createElement(gn,{parentStyleDirection:n}),t.createElement(kn,null),t.createElement(Gn,null)),Nn=(e,t)=>{const n=e?.value??t?.value;return!!n&&("flex"===n||"inline-flex"===n)},Kn=(0,s.withDirection)(i.SideLeftIcon),Hn=(0,s.withDirection)(i.SideRightIcon),Jn={"inset-block-start":t.createElement(i.SideTopIcon,{fontSize:"tiny"}),"inset-block-end":t.createElement(i.SideBottomIcon,{fontSize:"tiny"}),"inset-inline-start":t.createElement(Zt,{icon:Kn,size:"tiny"}),"inset-inline-end":t.createElement(Zt,{icon:Hn,size:"tiny"})},Yn=e=>e?(0,m.__)("Right","elementor"):(0,m.__)("Left","elementor"),Xn=e=>e?(0,m.__)("Left","elementor"):(0,m.__)("Right","elementor"),qn=()=>{const{isSiteRtl:e}=ct();return t.createElement(t.Fragment,null,t.createElement(s.Stack,{direction:"row",gap:2,flexWrap:"nowrap"},t.createElement(Zn,{side:"inset-block-start",label:(0,m.__)("Top","elementor")}),t.createElement(Zn,{side:"inset-inline-end",label:Xn(e)})),t.createElement(s.Stack,{direction:"row",gap:2,flexWrap:"nowrap"},t.createElement(Zn,{side:"inset-block-end",label:(0,m.__)("Bottom","elementor")}),t.createElement(Zn,{side:"inset-inline-start",label:Yn(e)})))},Zn=({side:n,label:l})=>t.createElement(s.Grid,{container:!0,gap:.75,alignItems:"center"},t.createElement(s.Grid,{item:!0,xs:12},t.createElement(It,null,l)),t.createElement(s.Grid,{item:!0,xs:12},t.createElement(wt,{bind:n},t.createElement(e.SizeControl,{startIcon:Jn[n],extendedValues:["auto"]})))),Qn=()=>t.createElement(wt,{bind:"scroll-margin-top"},t.createElement(s.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},t.createElement(s.Grid,{item:!0,xs:6},t.createElement(It,null,(0,m.__)("Anchor offset","elementor"))),t.createElement(s.Grid,{item:!0,xs:6},t.createElement(e.SizeControl,{units:["px","em","rem","vw","vh"]})))),el=[{label:(0,m.__)("Static","elementor"),value:"static"},{label:(0,m.__)("Relative","elementor"),value:"relative"},{label:(0,m.__)("Absolute","elementor"),value:"absolute"},{label:(0,m.__)("Fixed","elementor"),value:"fixed"},{label:(0,m.__)("Sticky","elementor"),value:"sticky"}],tl=({onChange:n})=>t.createElement(wt,{bind:"position"},t.createElement(s.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},t.createElement(s.Grid,{item:!0,xs:6},t.createElement(It,null,(0,m.__)("Position","elementor"))),t.createElement(s.Grid,{item:!0,xs:6,sx:{overflow:"hidden"}},t.createElement(e.SelectControl,{options:el,onChange:n})))),nl=()=>t.createElement(wt,{bind:"z-index"},t.createElement(s.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},t.createElement(s.Grid,{item:!0,xs:6},t.createElement(It,null,(0,m.__)("Z-index","elementor"))),t.createElement(s.Grid,{item:!0,xs:6},t.createElement(e.NumberControl,null)))),ll=()=>{const[e]=lt("position"),[n,l]=nt(["inset-block-start","inset-block-end","inset-inline-start","inset-inline-end"]),[r,o,a]=rl(),i=(0,E.isExperimentActive)("e_v_3_30"),c=e&&"static"!==e?.value;return t.createElement(it,null,t.createElement(tl,{onChange:(e,t)=>{"static"===e?n&&(o(n),l({"inset-block-start":void 0,"inset-block-end":void 0,"inset-inline-start":void 0,"inset-inline-end":void 0})):"static"===t&&r&&(l(r),a())}}),c?t.createElement(t.Fragment,null,t.createElement(qn,null),t.createElement(nl,null)):null,i&&t.createElement(t.Fragment,null,t.createElement(Ct,null),t.createElement(Qn,null)))},rl=()=>{const{id:e,meta:t}=O(),n=`styles/${e}/${t.breakpoint||"desktop"}/${t.state||"null"}/dimensions`;return(0,g.useSessionStorage)(n)},ol=({children:e,defaultOpen:n=!1})=>{const[l,r]=(0,t.useState)(n);return t.createElement(s.Stack,null,t.createElement(s.Button,{fullWidth:!0,size:"small",color:"secondary",variant:"outlined",onClick:()=>{r((e=>!e))},endIcon:t.createElement(De,{open:l}),sx:{my:.5}},l?(0,m.__)("Show less","elementor"):(0,m.__)("Show more","elementor")),t.createElement(s.Collapse,{in:l,timeout:"auto",unmountOnExit:!0},e))},al=[{label:(0,m.__)("Fill","elementor"),value:"fill"},{label:(0,m.__)("Cover","elementor"),value:"cover"},{label:(0,m.__)("Contain","elementor"),value:"contain"},{label:(0,m.__)("None","elementor"),value:"none"},{label:(0,m.__)("Scale down","elementor"),value:"scale-down"}],il=()=>t.createElement(wt,{bind:"object-fit"},t.createElement(s.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},t.createElement(s.Grid,{item:!0,xs:6},t.createElement(It,null,(0,m.__)("Object fit","elementor"))),t.createElement(s.Grid,{item:!0,xs:6,sx:{overflow:"hidden"}},t.createElement(e.SelectControl,{options:al})))),cl=[{label:(0,m.__)("Center center","elementor"),value:"center center"},{label:(0,m.__)("Center left","elementor"),value:"center left"},{label:(0,m.__)("Center right","elementor"),value:"center right"},{label:(0,m.__)("Top center","elementor"),value:"top center"},{label:(0,m.__)("Top left","elementor"),value:"top left"},{label:(0,m.__)("Top right","elementor"),value:"top right"},{label:(0,m.__)("Bottom center","elementor"),value:"bottom center"},{label:(0,m.__)("Bottom left","elementor"),value:"bottom left"},{label:(0,m.__)("Bottom right","elementor"),value:"bottom right"}],sl=()=>t.createElement(wt,{bind:"object-position"},t.createElement(s.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},t.createElement(s.Grid,{item:!0,xs:6},t.createElement(It,null,(0,m.__)("Object position","elementor"))),t.createElement(s.Grid,{item:!0,xs:6,sx:{overflow:"hidden"}},t.createElement(e.SelectControl,{options:cl})))),ml=[{value:"visible",label:(0,m.__)("Visible","elementor"),renderContent:({size:e})=>t.createElement(i.EyeIcon,{fontSize:e}),showTooltip:!0},{value:"hidden",label:(0,m.__)("Hidden","elementor"),renderContent:({size:e})=>t.createElement(i.EyeOffIcon,{fontSize:e}),showTooltip:!0},{value:"auto",label:(0,m.__)("Auto","elementor"),renderContent:({size:e})=>t.createElement(i.LetterAIcon,{fontSize:e}),showTooltip:!0}],ul=()=>t.createElement(wt,{bind:"overflow"},t.createElement(s.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},t.createElement(s.Grid,{item:!0,xs:6},t.createElement(It,null,(0,m.__)("Overflow","elementor"))),t.createElement(s.Grid,{item:!0,xs:6,sx:{display:"flex",justifyContent:"flex-end"}},t.createElement(e.ToggleControl,{options:ml})))),dl=()=>{const[n]=lt("object-fit"),l=n&&"fill"!==n?.value,r=(0,E.isExperimentActive)("e_v_3_30");return t.createElement(it,null,t.createElement(s.Grid,{container:!0,gap:2,flexWrap:"nowrap"},t.createElement(s.Grid,{item:!0,xs:6},t.createElement(pl,{bind:"width",label:(0,m.__)("Width","elementor"),extendedValues:["auto"]})),t.createElement(s.Grid,{item:!0,xs:6},t.createElement(pl,{bind:"height",label:(0,m.__)("Height","elementor"),extendedValues:["auto"]}))),t.createElement(s.Grid,{container:!0,gap:2,flexWrap:"nowrap"},t.createElement(s.Grid,{item:!0,xs:6},t.createElement(pl,{bind:"min-width",label:(0,m.__)("Min width","elementor"),extendedValues:["auto"]})),t.createElement(s.Grid,{item:!0,xs:6},t.createElement(pl,{bind:"min-height",label:(0,m.__)("Min height","elementor"),extendedValues:["auto"]}))),t.createElement(s.Grid,{container:!0,gap:2,flexWrap:"nowrap"},t.createElement(s.Grid,{item:!0,xs:6},t.createElement(pl,{bind:"max-width",label:(0,m.__)("Max width","elementor")})),t.createElement(s.Grid,{item:!0,xs:6},t.createElement(pl,{bind:"max-height",label:(0,m.__)("Max height","elementor")}))),t.createElement(Ct,null),t.createElement(s.Stack,null,t.createElement(ul,null)),r&&t.createElement(ol,null,t.createElement(s.Stack,{gap:2},t.createElement(wt,{bind:"aspect-ratio"},t.createElement(e.AspectRatioControl,{label:(0,m.__)("Aspect Ratio","elementor")})),t.createElement(Ct,null),t.createElement(il,null),l&&t.createElement(s.Grid,{item:!0,xs:6},t.createElement(sl,null)))))},pl=({label:n,bind:l,extendedValues:r})=>t.createElement(wt,{bind:l},t.createElement(s.Grid,{container:!0,gap:.75,alignItems:"center"},t.createElement(s.Grid,{item:!0,xs:12},t.createElement(It,null,n)),t.createElement(s.Grid,{item:!0,xs:12},t.createElement(e.SizeControl,{extendedValues:r})))),El=()=>{const{isSiteRtl:n}=ct();return t.createElement(it,null,t.createElement(wt,{bind:"margin"},t.createElement(e.LinkedDimensionsControl,{label:(0,m.__)("Margin","elementor"),isSiteRtl:n,extendedValues:["auto"]})),t.createElement(Ct,null),t.createElement(wt,{bind:"padding"},t.createElement(e.LinkedDimensionsControl,{label:(0,m.__)("Padding","elementor"),isSiteRtl:n})))},fl=()=>t.createElement(wt,{bind:"column-count"},t.createElement(s.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},t.createElement(s.Grid,{item:!0,xs:6},t.createElement(It,null,(0,m.__)("Columns","elementor"))),t.createElement(s.Grid,{item:!0,xs:6},t.createElement(e.NumberControl,{shouldForceInt:!0,min:0,step:1})))),gl=()=>t.createElement(wt,{bind:"column-gap"},t.createElement(s.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},t.createElement(s.Grid,{item:!0,xs:6},t.createElement(It,null,(0,m.__)("Column gap","elementor"))),t.createElement(s.Grid,{item:!0,xs:6},t.createElement(e.SizeControl,null)))),bl={system:(0,m.__)("System","elementor"),custom:(0,m.__)("Custom Fonts","elementor"),googlefonts:(0,m.__)("Google Fonts","elementor")},vl=()=>{const n=(()=>{const e=(()=>{const{controls:e}=(()=>{const e=window;return e.elementor?.config??{}})(),t=e?.font?.options;return t||null})();return(0,t.useMemo)((()=>{const t=["system","custom","googlefonts"];return Object.entries(e||{}).reduce(((e,[n,l])=>{if(!bl[l])return e;const r=t.indexOf(l);return e[r]||(e[r]={label:bl[l],fonts:[]}),e[r].fonts.push(n),e}),[]).filter(Boolean)}),[e])})();return 0===n.length?null:t.createElement(wt,{bind:"font-family"},t.createElement(s.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},t.createElement(s.Grid,{item:!0,xs:6},t.createElement(It,null,(0,m.__)("Font family","elementor"))),t.createElement(s.Grid,{item:!0,xs:6,sx:{minWidth:0}},t.createElement(e.FontFamilyControl,{fontFamilies:n}))))},_l=()=>t.createElement(wt,{bind:"font-size"},t.createElement(s.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},t.createElement(s.Grid,{item:!0,xs:6},t.createElement(It,null,(0,m.__)("Font size","elementor"))),t.createElement(s.Grid,{item:!0,xs:6},t.createElement(e.SizeControl,null)))),hl=[{value:"normal",label:(0,m.__)("Normal","elementor"),renderContent:({size:e})=>t.createElement(i.MinusIcon,{fontSize:e}),showTooltip:!0},{value:"italic",label:(0,m.__)("Italic","elementor"),renderContent:({size:e})=>t.createElement(i.ItalicIcon,{fontSize:e}),showTooltip:!0}],yl=()=>t.createElement(wt,{bind:"font-style"},t.createElement(s.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},t.createElement(s.Grid,{item:!0,xs:6},t.createElement(e.ControlFormLabel,null,(0,m.__)("Font style","elementor"))),t.createElement(s.Grid,{item:!0,xs:6,display:"flex",justifyContent:"end"},t.createElement(e.ToggleControl,{options:hl})))),xl=[{value:"100",label:(0,m.__)("100 - Thin","elementor")},{value:"200",label:(0,m.__)("200 - Extra light","elementor")},{value:"300",label:(0,m.__)("300 - Light","elementor")},{value:"400",label:(0,m.__)("400 - Normal","elementor")},{value:"500",label:(0,m.__)("500 - Medium","elementor")},{value:"600",label:(0,m.__)("600 - Semi bold","elementor")},{value:"700",label:(0,m.__)("700 - Bold","elementor")},{value:"800",label:(0,m.__)("800 - Extra bold","elementor")},{value:"900",label:(0,m.__)("900 - Black","elementor")}],wl=()=>t.createElement(wt,{bind:"font-weight"},t.createElement(s.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},t.createElement(s.Grid,{item:!0,xs:6},t.createElement(It,null,(0,m.__)("Font weight","elementor"))),t.createElement(s.Grid,{item:!0,xs:6,sx:{overflow:"hidden"}},t.createElement(e.SelectControl,{options:xl})))),Sl=()=>t.createElement(wt,{bind:"letter-spacing"},t.createElement(s.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},t.createElement(s.Grid,{item:!0,xs:6},t.createElement(It,null,(0,m.__)("Letter spacing","elementor"))),t.createElement(s.Grid,{item:!0,xs:6},t.createElement(e.SizeControl,null)))),Cl=()=>t.createElement(wt,{bind:"line-height"},t.createElement(s.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},t.createElement(s.Grid,{item:!0,xs:6},t.createElement(It,null,(0,m.__)("Line height","elementor"))),t.createElement(s.Grid,{item:!0,xs:6},t.createElement(e.SizeControl,null)))),Il=(0,s.withDirection)(i.AlignLeftIcon),Tl=(0,s.withDirection)(i.AlignRightIcon),kl=[{value:"start",label:(0,m.__)("Start","elementor"),renderContent:({size:e})=>t.createElement(Il,{fontSize:e}),showTooltip:!0},{value:"center",label:(0,m.__)("Center","elementor"),renderContent:({size:e})=>t.createElement(i.AlignCenterIcon,{fontSize:e}),showTooltip:!0},{value:"end",label:(0,m.__)("End","elementor"),renderContent:({size:e})=>t.createElement(Tl,{fontSize:e}),showTooltip:!0},{value:"justify",label:(0,m.__)("Justify","elementor"),renderContent:({size:e})=>t.createElement(i.AlignJustifiedIcon,{fontSize:e}),showTooltip:!0}],zl=()=>t.createElement(wt,{bind:"text-align"},t.createElement(s.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},t.createElement(s.Grid,{item:!0,xs:6},t.createElement(It,null,(0,m.__)("Text align","elementor"))),t.createElement(s.Grid,{item:!0,xs:6,display:"flex",justifyContent:"end"},t.createElement(e.ToggleControl,{options:kl})))),Pl=()=>t.createElement(wt,{bind:"color"},t.createElement(s.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},t.createElement(s.Grid,{item:!0,xs:6},t.createElement(It,null,(0,m.__)("Text color","elementor"))),t.createElement(s.Grid,{item:!0,xs:6},t.createElement(e.ColorControl,null)))),Gl=[{value:"none",label:(0,m.__)("None","elementor"),renderContent:({size:e})=>t.createElement(i.MinusIcon,{fontSize:e}),showTooltip:!0,exclusive:!0},{value:"underline",label:(0,m.__)("Underline","elementor"),renderContent:({size:e})=>t.createElement(i.UnderlineIcon,{fontSize:e}),showTooltip:!0},{value:"line-through",label:(0,m.__)("Line-through","elementor"),renderContent:({size:e})=>t.createElement(i.StrikethroughIcon,{fontSize:e}),showTooltip:!0},{value:"overline",label:(0,m.__)("Overline","elementor"),renderContent:({size:e})=>t.createElement(i.OverlineIcon,{fontSize:e}),showTooltip:!0}],Ll=()=>t.createElement(wt,{bind:"text-decoration"},t.createElement(s.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},t.createElement(s.Grid,{item:!0,xs:6},t.createElement(It,null,(0,m.__)("Line decoration","elementor"))),t.createElement(s.Grid,{item:!0,xs:6,display:"flex",justifyContent:"end"},t.createElement(e.ToggleControl,{options:Gl,exclusive:!1})))),Rl=[{value:"ltr",label:(0,m.__)("Left to right","elementor"),renderContent:({size:e})=>t.createElement(i.TextDirectionLtrIcon,{fontSize:e}),showTooltip:!0},{value:"rtl",label:(0,m.__)("Right to left","elementor"),renderContent:({size:e})=>t.createElement(i.TextDirectionRtlIcon,{fontSize:e}),showTooltip:!0}],Bl=()=>t.createElement(wt,{bind:"direction"},t.createElement(s.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},t.createElement(s.Grid,{item:!0,xs:6},t.createElement(It,null,(0,m.__)("Direction","elementor"))),t.createElement(s.Grid,{item:!0,xs:6,display:"flex",justifyContent:"end"},t.createElement(e.ToggleControl,{options:Rl})))),Vl={$$type:"stroke",value:{color:{$$type:"color",value:"#000000"},width:{$$type:"size",value:{unit:"px",size:1}}}},Al=()=>{const{canEdit:n}=O(),[l,r]=lt("stroke"),o=Boolean(l);return t.createElement(wt,{bind:"stroke"},t.createElement(kt,{label:(0,m.__)("Text stroke","elementor"),isAdded:o,onAdd:()=>{r(Vl)},onRemove:()=>{r(null)},disabled:!n},t.createElement(e.StrokeControl,null)))},Dl=[{value:"none",label:(0,m.__)("None","elementor"),renderContent:({size:e})=>t.createElement(i.MinusIcon,{fontSize:e}),showTooltip:!0},{value:"capitalize",label:(0,m.__)("Capitalize","elementor"),renderContent:({size:e})=>t.createElement(i.LetterCaseIcon,{fontSize:e}),showTooltip:!0},{value:"uppercase",label:(0,m.__)("Uppercase","elementor"),renderContent:({size:e})=>t.createElement(i.LetterCaseUpperIcon,{fontSize:e}),showTooltip:!0},{value:"lowercase",label:(0,m.__)("Lowercase","elementor"),renderContent:({size:e})=>t.createElement(i.LetterCaseLowerIcon,{fontSize:e}),showTooltip:!0}],Ml=()=>t.createElement(wt,{bind:"text-transform"},t.createElement(s.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},t.createElement(s.Grid,{item:!0,xs:6},t.createElement(It,null,(0,m.__)("Text transform","elementor"))),t.createElement(s.Grid,{item:!0,xs:6,display:"flex",justifyContent:"end"},t.createElement(e.ToggleControl,{options:Dl})))),Ol=()=>t.createElement(wt,{bind:"word-spacing"},t.createElement(s.Grid,{container:!0,gap:2,alignItems:"center",flexWrap:"nowrap"},t.createElement(s.Grid,{item:!0,xs:6},t.createElement(It,null,(0,m.__)("Word spacing","elementor"))),t.createElement(s.Grid,{item:!0,xs:6},t.createElement(e.SizeControl,null)))),Wl=()=>{const[e]=lt("column-count"),n=(0,E.isExperimentActive)("e_v_3_30"),l=!!(e?.value&&e?.value>1);return t.createElement(it,null,t.createElement(vl,null),t.createElement(wl,null),t.createElement(_l,null),t.createElement(Ct,null),t.createElement(zl,null),t.createElement(Pl,null),t.createElement(ol,null,t.createElement(it,{sx:{pt:2}},t.createElement(Cl,null),t.createElement(Sl,null),t.createElement(Ol,null),n&&t.createElement(t.Fragment,null,t.createElement(fl,null),l&&t.createElement(gl,null)),t.createElement(Ct,null),t.createElement(Ll,null),t.createElement(Ml,null),t.createElement(Bl,null),t.createElement(yl,null),t.createElement(Al,null))))},jl={position:"sticky",zIndex:1,opacity:1,backgroundColor:"background.default",transition:"top 300ms ease"},Fl=({section:e})=>{const{component:n,name:l,title:r}=e,o=Te(),a=n,i=!!(0,E.isExperimentActive)(Z.V_3_30)&&o.defaultSectionsExpanded.style?.includes(l);return t.createElement(Me,{title:r,defaultExpanded:i},t.createElement(a,null))},$l=()=>{const e=function(){const{elementType:e}=L(),t=Object.entries(e.propsSchema).find((([,e])=>"plain"===e.kind&&e.key===p.CLASSES_PROP_KEY));if(!t)throw new Error("Element does not have a classes prop");return t[0]}(),[n,l]=function(e){const[t,n]=ke("active-style-id",null),l=function(e){const{element:t}=L();return(0,r.useElementSetting)(t.id,e)}(e)?.value||[],o=function(e){const{element:t}=L(),n=(0,r.getElementStyles)(t.id)??{};return Object.values(n).find((t=>e.includes(t.id)))}(l);return[function(e,t){return e&&t.includes(e)?e:null}(t,l)||o?.id||null,n]}(e),[o,a]=(0,t.useState)(null),i=(0,v.useActiveBreakpoint)();return t.createElement(k,{prop:e},t.createElement(M,{meta:{breakpoint:i,state:o},id:n,setId:e=>{l(e),a(null)},setMetaState:a},t.createElement(g.SessionStorageProvider,{prefix:n??""},t.createElement(Ze,null,t.createElement(Ul,null,t.createElement(fe,null),t.createElement(s.Divider,null)),t.createElement(Oe,null,t.createElement(Fl,{section:{component:Fn,name:"Layout",title:(0,m.__)("Layout","elementor")}}),t.createElement(Fl,{section:{component:El,name:"Spacing",title:(0,m.__)("Spacing","elementor")}}),t.createElement(Fl,{section:{component:dl,name:"Size",title:(0,m.__)("Size","elementor")}}),t.createElement(Fl,{section:{component:ll,name:"Position",title:(0,m.__)("Position","elementor")}}),t.createElement(Fl,{section:{component:Wl,name:"Typography",title:(0,m.__)("Typography","elementor")}}),t.createElement(Fl,{section:{component:St,name:"Background",title:(0,m.__)("Background","elementor")}}),t.createElement(Fl,{section:{component:Jt,name:"Border",title:(0,m.__)("Border","elementor")}}),t.createElement(Fl,{section:{component:Yt,name:"Effects",title:(0,m.__)("Effects","elementor")}}))))))};function Ul({children:e}){const n=(0,t.useContext)(xe)?.direction??"up";return t.createElement(s.Stack,{sx:{...jl,top:"up"===n?"37px":0}},e)}var Nl=()=>{const{element:e}=L();return t.createElement(t.Fragment,{key:e.id},t.createElement(Kl,null))},Kl=()=>{const e=Te(),n=(0,E.isExperimentActive)(Z.V_3_30)?e.defaultTab:"settings",[l,r]=ke("tab",n),{getTabProps:o,getTabPanelProps:a,getTabsProps:i}=(0,s.useTabs)(l);return t.createElement(Se,null,t.createElement(s.Stack,{direction:"column",sx:{width:"100%"}},t.createElement(s.Stack,{sx:{...jl,top:0}},t.createElement(s.Tabs,{variant:"fullWidth",size:"small",sx:{mt:.5},...i(),onChange:(e,t)=>{i().onChange(e,t),r(t)}},t.createElement(s.Tab,{label:(0,m.__)("General","elementor"),...o("settings")}),t.createElement(s.Tab,{label:(0,m.__)("Style","elementor"),...o("style")})),t.createElement(s.Divider,null)),t.createElement(s.TabPanel,{...a("settings"),disablePadding:!0},t.createElement(We,null)),t.createElement(s.TabPanel,{...a("style"),disablePadding:!0},t.createElement($l,null))))},{useMenuItems:Hl}=he,{panel:Jl,usePanelActions:Yl,usePanelStatus:Xl}=(0,f.__createPanel)({id:"editing-panel",component:()=>{const{element:n,elementType:l}=(0,r.useSelectedElement)(),o=I(),c=Hl().default;if(!n||!l)return null;const u=(0,m.__)("Edit %s","elementor").replace("%s",l.title);return t.createElement(s.ErrorBoundary,{fallback:t.createElement(ye,null)},t.createElement(g.SessionStorageProvider,{prefix:"elementor"},t.createElement(a.ThemeProvider,null,t.createElement(f.Panel,null,t.createElement(f.PanelHeader,null,t.createElement(f.PanelHeaderTitle,null,u),t.createElement(i.AtomIcon,{fontSize:"small",sx:{color:"text.tertiary"}})),t.createElement(f.PanelBody,null,t.createElement(e.ControlActionsProvider,{items:c},t.createElement(e.ControlReplacementsProvider,{replacements:o},t.createElement(G,{element:n,elementType:l},t.createElement(Nl,null)))))))))}}),ql=()=>{const e=(0,r.getSelectedElements)(),t=(0,r.getWidgetsCache)();return 1===e.length&&!!t?.[e[0].type]?.atomic_controls},Zl=()=>((()=>{const{open:e}=Yl();(0,t.useEffect)((()=>(0,E.__privateListenTo)((0,E.commandStartEvent)("panel/editor/open"),(()=>{ql()&&e()}))),[])})(),null),Ql=({alignItems:e,gap:n=1.5,p:l,children:r})=>t.createElement(s.Stack,{alignItems:e,gap:n,p:l},r),er=e=>{const{element:t}=L(),n=`dynamic/non-dynamic-values-history/${t.id}/${e}`;return(0,g.useSessionStorage)(n)},tr=()=>{const{atomicDynamicTags:e}=(()=>{const e=window;return e.elementor?.config??{}})();return e?{tags:e.tags,groups:e.groups}:null},nr="dynamic",lr=e=>{const t="union"===e.kind&&e.prop_types[nr];return t&&t.key===nr?t:null},rr=(0,p.createPropUtils)(nr,w.z.strictObject({name:w.z.string(),settings:w.z.any().optional()})),or=()=>{let n=[];const{propType:l}=(0,e.useBoundProp)();if(l){const e=lr(l);n=e?.settings.categories||[]}return(0,t.useMemo)((()=>ar(n)),[n.join()])},ar=e=>{const t=tr();if(!e.length||!t?.tags)return[];const n=new Set(e);return Object.values(t.tags).filter((e=>e.categories.some((e=>n.has(e)))))},ir=e=>{const n=or();return(0,t.useMemo)((()=>n.find((t=>t.name===e))??null),[n,e])},cr=({bind:n,children:l})=>{const{value:r,setValue:o}=(0,e.useBoundProp)(rr),{name:a="",settings:i}=r??{},c=ir(a);if(!c)throw new Error(`Dynamic tag ${a} not found`);const s=c.props_schema[n],m=s?.default,u=i?.[n]??m,d=Ve({schema:c.props_schema});return t.createElement(e.PropProvider,{propType:d,setValue:e=>{o({name:a,settings:{...i,...e}})},value:{[n]:u}},t.createElement(e.PropKeyProvider,{bind:n},l))},sr="tiny",mr=({onSelect:n})=>{const[l,r]=(0,t.useState)(""),{groups:o}=tr()||{},{value:a}=(0,e.useBoundProp)(),{bind:c,value:u,setValue:d}=(0,e.useBoundProp)(rr),[,p]=er(c),E=!!u,f=pr(l),g=!f.length&&!l.trim();return t.createElement(s.Stack,null,g?t.createElement(dr,null):t.createElement(t.Fragment,null,t.createElement(s.Box,{px:1.5,pb:1},t.createElement(s.TextField,{fullWidth:!0,size:sr,value:l,onChange:e=>{r(e.target.value)},placeholder:(0,m.__)("Search dynamic tags…","elementor"),InputProps:{startAdornment:t.createElement(s.InputAdornment,{position:"start"},t.createElement(i.SearchIcon,{fontSize:sr}))}})),t.createElement(s.Divider,null),t.createElement(s.Box,{sx:{overflowY:"auto",height:260,width:220}},f.length>0?t.createElement(s.MenuList,{role:"listbox",tabIndex:0},f.map((([e,l],r)=>t.createElement(t.Fragment,{key:r},t.createElement(s.MenuSubheader,{sx:{px:1.5,typography:"caption",color:"text.tertiary"}},o?.[e]?.title||e),l.map((({value:e,label:l})=>{const r=E&&e===u?.name;return t.createElement(s.MenuItem,{key:e,selected:r,autoFocus:r,sx:{px:3.5,typography:"caption"},onClick:()=>((e,t)=>{E||p(a),d({name:e,settings:{label:t}}),n?.()})(e,l)},l)})))))):t.createElement(ur,{searchValue:l,onClear:()=>r("")}))))},ur=({searchValue:e,onClear:n})=>t.createElement(s.Stack,{gap:1,alignItems:"center",justifyContent:"center",height:"100%",p:2.5,color:"text.secondary",sx:{pb:3.5}},t.createElement(i.DatabaseIcon,{fontSize:"large"}),t.createElement(s.Typography,{align:"center",variant:"subtitle2"},(0,m.__)("Sorry, nothing matched","elementor"),t.createElement("br",null),"“",e,"”."),t.createElement(s.Typography,{align:"center",variant:"caption"},(0,m.__)("Try something else.","elementor")," ",t.createElement(s.Link,{color:"text.secondary",variant:"caption",component:"button",onClick:n},(0,m.__)("Clear & try again","elementor")))),dr=()=>t.createElement(s.Box,{sx:{overflowY:"hidden",height:297,width:220}},t.createElement(s.Divider,null),t.createElement(s.Stack,{gap:1,alignItems:"center",justifyContent:"center",height:"100%",p:2.5,color:"text.secondary",sx:{pb:3.5}},t.createElement(i.DatabaseIcon,{fontSize:"large"}),t.createElement(s.Typography,{align:"center",variant:"subtitle2"},(0,m.__)("Streamline your workflow with dynamic tags","elementor")),t.createElement(s.Typography,{align:"center",variant:"caption"},(0,m.__)("You’ll need Elementor Pro to use this feature.","elementor")))),pr=e=>[...or().reduce(((t,{name:n,label:l,group:r})=>l.toLowerCase().includes(e.trim().toLowerCase())?(t.has(r)||t.set(r,[]),t.get(r)?.push({label:l,value:n}),t):t),new Map)],Er="tiny",fr=()=>{const{setValue:n}=(0,e.useBoundProp)(),{bind:l,value:r}=(0,e.useBoundProp)(rr),[o]=er(l),a=(0,s.usePopupState)({variant:"popover"}),{name:c=""}=r,u=ir(c);if(!u)throw new Error(`Dynamic tag ${c} not found`);return t.createElement(s.Box,null,t.createElement(s.UnstableTag,{fullWidth:!0,showActionsOnHover:!0,label:u.label,startIcon:t.createElement(i.DatabaseIcon,{fontSize:Er}),...(0,s.bindTrigger)(a),actions:t.createElement(t.Fragment,null,t.createElement(gr,{dynamicTag:u}),t.createElement(s.IconButton,{size:Er,onClick:()=>{n(o??null)},"aria-label":(0,m.__)("Remove dynamic value","elementor")},t.createElement(i.XIcon,{fontSize:Er})))}),t.createElement(s.Popover,{disablePortal:!0,disableScrollLock:!0,anchorOrigin:{vertical:"bottom",horizontal:"left"},...(0,s.bindPopover)(a)},t.createElement(s.Stack,null,t.createElement(s.Stack,{direction:"row",alignItems:"center",pl:1.5,pr:.5,py:1.5},t.createElement(i.DatabaseIcon,{fontSize:Er,sx:{mr:.5}}),t.createElement(s.Typography,{variant:"subtitle2"},(0,m.__)("Dynamic tags","elementor")),t.createElement(s.IconButton,{size:Er,sx:{ml:"auto"},onClick:a.close},t.createElement(i.XIcon,{fontSize:Er}))),t.createElement(mr,{onSelect:a.close}))))},gr=({dynamicTag:e})=>{const n=(0,s.usePopupState)({variant:"popover"});return e.atomic_controls.length?t.createElement(t.Fragment,null,t.createElement(s.IconButton,{size:Er,...(0,s.bindTrigger)(n),"aria-label":(0,m.__)("Settings","elementor")},t.createElement(i.SettingsIcon,{fontSize:Er})),t.createElement(s.Popover,{disablePortal:!0,disableScrollLock:!0,anchorOrigin:{vertical:"bottom",horizontal:"center"},...(0,s.bindPopover)(n)},t.createElement(s.Paper,{component:s.Stack,sx:{minHeight:"300px",width:"220px"}},t.createElement(s.Stack,{direction:"row",alignItems:"center",px:1.5,pt:2,pb:1},t.createElement(i.DatabaseIcon,{fontSize:Er,sx:{mr:.5}}),t.createElement(s.Typography,{variant:"subtitle2"},e.label),t.createElement(s.IconButton,{sx:{ml:"auto"},size:Er,onClick:n.close},t.createElement(i.XIcon,{fontSize:Er}))),t.createElement(br,{controls:e.atomic_controls})))):null},br=({controls:e})=>{const n=e.filter((({type:e})=>"section"===e)),{getTabsProps:l,getTabProps:r,getTabPanelProps:o}=(0,s.useTabs)(0);return n.length?t.createElement(t.Fragment,null,t.createElement(s.Tabs,{size:"small",variant:"fullWidth",...l()},n.map((({value:e},n)=>t.createElement(s.Tab,{key:n,label:e.label,sx:{px:1,py:.5},...r(n)})))),t.createElement(s.Divider,null),n.map((({value:e},n)=>t.createElement(s.TabPanel,{key:n,sx:{flexGrow:1,py:0},...o(n)},t.createElement(Ql,{p:2,gap:2},e.items.map((e=>"control"===e.type?t.createElement(vr,{key:e.value.bind,control:e.value}):null))))))):null},vr=({control:n})=>Pe(n.type)?t.createElement(cr,{bind:n.bind},t.createElement(s.Grid,{container:!0,gap:.75},n.label?t.createElement(s.Grid,{item:!0,xs:12},t.createElement(e.ControlFormLabel,null,n.label)):null,t.createElement(s.Grid,{item:!0,xs:12},t.createElement(Ge,{type:n.type,props:n.props})))):null,_r=(0,u.createError)({code:"dynamic_tags_manager_not_found",message:"Dynamic tags manager not found"}),hr=(0,h.createTransformer)((e=>e.name?function(e,t){const n=window,{dynamicTags:l}=n.elementor??{};if(!l)throw new _r;const r=()=>{const n=l.createTag("v4-dynamic-tag",e,t);return n?l.loadTagDataFromCache(n)??null:null},o=r();return null!==o?o:new Promise((e=>{l.refreshCacheFromServer((()=>{e(r())}))}))}(e.name,function(e){const t=Object.entries(e).map((([e,t])=>[e,(0,p.isTransformable)(t)?t.value:t]));return Object.fromEntries(t)}(e.settings??{})):null)),yr=()=>{const{propType:n}=(0,e.useBoundProp)(),l=!!n&&(e=>!!lr(e))(n);return{visible:l,icon:i.DatabaseIcon,title:(0,m.__)("Dynamic tags","elementor"),popoverContent:({closePopover:e})=>t.createElement(mr,{onSelect:e})}},{registerPopoverAction:xr}=he,wr=()=>{C({component:fr,condition:({value:e})=>{return t=e,(0,p.isTransformable)(t)&&t.$$type===nr;var t}}),xr({id:"dynamic-tags",useProps:yr}),h.styleTransformersRegistry.register("dynamic",hr),h.settingsTransformersRegistry.register("dynamic",hr)},{registerAction:Sr}=he,Cr=["order","flex-grow","flex-shrink","flex-basis"];function Ir(){const n=!!(0,t.useContext)(D),{value:l,setValue:r,path:o,bind:a}=(0,e.useBoundProp)();return{visible:n&&null!=l&&o.length<=2&&!Cr.includes(a),title:(0,m.__)("Clear","elementor"),icon:i.BrushBigIcon,onClick:()=>r(null)}}var Tr=(0,h.createTransformer)((e=>t.createElement(s.Stack,{direction:"row",gap:10},t.createElement(kr,{value:e}),t.createElement(zr,{value:e})))),kr=({value:e})=>{const{color:n}=e;return t.createElement(Pr,{size:"inherit",component:"span",value:n})},zr=({value:{color:e}})=>t.createElement("span",null,e),Pr=(0,s.styled)(s.UnstableColorIndicator)((({theme:e})=>({borderRadius:e.shape.borderRadius/2+"px"}))),Gr=(0,h.createTransformer)((e=>t.createElement(s.Stack,{direction:"row",gap:10},t.createElement(Lr,{value:e}),t.createElement(Rr,{value:e})))),Lr=({value:e})=>{const n=Br(e);return t.createElement(Pr,{size:"inherit",component:"span",value:n})},Rr=({value:e})=>"linear"===e.type?t.createElement("span",null,(0,m.__)("Linear Gradient","elementor")):t.createElement("span",null,(0,m.__)("Radial Gradient","elementor")),Br=e=>{const t=e.stops?.map((({color:e,offset:t})=>`${e} ${t??0}%`))?.join(",");return"linear"===e.type?`linear-gradient(${e.angle}deg, ${t})`:`radial-gradient(circle at ${e.positions}, ${t})`},Vr=(0,h.createTransformer)((e=>t.createElement(s.Stack,{direction:"row",gap:10},t.createElement(Ar,{value:e}),t.createElement(Dr,{value:e})))),Ar=({value:e})=>{const{imageUrl:n}=Mr(e);return t.createElement(s.CardMedia,{image:n,sx:e=>({height:"1em",width:"1em",borderRadius:e.shape.borderRadius/2+"px",outline:`1px solid ${e.palette.action.disabled}`})})},Dr=({value:e})=>{const{imageTitle:n}=Mr(e);return t.createElement(a.EllipsisWithTooltip,{title:n},t.createElement("span",null,n))},Mr=e=>{let t,n=null;const l=e?.image.src,{data:r}=(0,S.useWpMediaAttachment)(l.id||null);if(l.id){const e=Or(r?.filename);t=`${r?.title}${e}`||null,n=r?.url||null}else l.url&&(n=l.url,t=n?.substring(n.lastIndexOf("/")+1)||null);return{imageTitle:t,imageUrl:n}},Or=e=>e?`.${e.substring(e.lastIndexOf(".")+1)}`:"",Wr=(0,h.createTransformer)((e=>e&&0!==e.length?t.createElement(s.Stack,{direction:"column"},e.map(((e,n)=>t.createElement(s.Stack,{key:n},e)))):null));var jr=()=>{ot()&&function(){const e=h.styleTransformersRegistry.all();Object.entries(e).forEach((([e,t])=>{rt.has(e)||bt.register(e,t)})),bt.registerFallback((0,h.createTransformer)((e=>e))),bt.register("background-color-overlay",Tr),bt.register("background-gradient-overlay",Gr),bt.register("background-image-overlay",Vr),bt.register("background-overlay",Wr)}()};function Fr(){(0,f.__registerPanel)(Jl),$r(),(0,y.injectIntoLogic)({id:"editing-panel-hooks",component:Zl}),(0,y.injectIntoLogic)({id:"current-user-data",component:x.PrefetchUserData}),wr(),jr(),(0,E.isExperimentActive)(Z.V_3_30)&&Sr({id:"reset-style-value",useProps:Ir})}var $r=()=>{(0,E.blockCommand)({command:"panel/editor/open",condition:ql})}}(),(window.elementorV2=window.elementorV2||{}).editorEditingPanel=l}(),window.elementorV2.editorEditingPanel?.init?.();