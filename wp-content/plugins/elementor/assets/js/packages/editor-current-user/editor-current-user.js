/*! For license information please see editor-current-user.js.LICENSE.txt */
!function(){"use strict";var e={"@elementor/http-client":function(e){e.exports=window.elementorV2.httpClient},"@elementor/query":function(e){e.exports=window.elementorV2.query}},t={};function r(n){var s=t[n];if(void 0!==s)return s.exports;var u=t[n]={exports:{}};return e[n](u,u.exports,r),u.exports}r.d=function(e,t){for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var n={};!function(){r.r(n),r.d(n,{PrefetchUserData:function(){return d},getCurrentUser:function(){return c},useCurrentUserCapabilities:function(){return f},useSuppressedMessage:function(){return y}});var e=r("@elementor/query"),t=r("@elementor/http-client"),s="/users/me",u={params:{context:"edit"}},i={get:()=>(0,t.httpService)().get("wp/v2"+s,u).then((e=>o(e.data))),update:e=>(0,t.httpService)().patch("wp/v2"+s,a(e))},o=e=>({suppressedMessages:Object.entries(e.elementor_introduction).filter((([,e])=>e)).map((([e])=>e)),capabilities:Object.keys(e.capabilities)}),a=e=>({elementor_introduction:e.suppressedMessages?.reduce(((e,t)=>(e[t]=!0,e)),{})}),c=()=>i.get(),p="editor-current-user",l=()=>(0,e.useQuery)({queryKey:[p],queryFn:c});function d(){return(0,e.useQueryClient)().prefetchQuery({queryKey:[p],queryFn:i.get}),null}var y=t=>{const{data:r}=l(),{mutate:n}=(()=>{const t=(0,e.useQueryClient)();return(0,e.useMutation)({mutationFn:i.update,onSuccess:()=>t.invalidateQueries({queryKey:[p]})})})(),s=!!r?.suppressedMessages.includes(t);return[s,()=>{s||n({suppressedMessages:[...r?.suppressedMessages??[],t]})}]},f=()=>{const{data:e}=l();return{canUser:t=>Boolean(e?.capabilities.includes(t)),capabilities:e?.capabilities}}}(),(window.elementorV2=window.elementorV2||{}).editorCurrentUser=n}(),window.elementorV2.editorCurrentUser?.init?.();