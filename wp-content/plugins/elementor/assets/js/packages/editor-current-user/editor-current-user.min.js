!function(){"use strict";var e={d:function(t,r){for(var n in r)e.o(r,n)&&!e.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:r[n]})},o:function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r:function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{PrefetchUserData:function(){return l},getCurrentUser:function(){return c},useCurrentUserCapabilities:function(){return f},useSuppressedMessage:function(){return y}});var r=window.elementorV2.query,n=window.elementorV2.httpClient,s="/users/me",u={params:{context:"edit"}},i={get:()=>(0,n.httpService)().get("wp/v2"+s,u).then((e=>o(e.data))),update:e=>(0,n.httpService)().patch("wp/v2"+s,a(e))},o=e=>({suppressedMessages:Object.entries(e.elementor_introduction).filter((([,e])=>e)).map((([e])=>e)),capabilities:Object.keys(e.capabilities)}),a=e=>({elementor_introduction:e.suppressedMessages?.reduce(((e,t)=>(e[t]=!0,e)),{})}),c=()=>i.get(),p="editor-current-user",d=()=>(0,r.useQuery)({queryKey:[p],queryFn:c});function l(){return(0,r.useQueryClient)().prefetchQuery({queryKey:[p],queryFn:i.get}),null}var y=e=>{const{data:t}=d(),{mutate:n}=(()=>{const e=(0,r.useQueryClient)();return(0,r.useMutation)({mutationFn:i.update,onSuccess:()=>e.invalidateQueries({queryKey:[p]})})})(),s=!!t?.suppressedMessages.includes(e);return[s,()=>{s||n({suppressedMessages:[...t?.suppressedMessages??[],e]})}]},f=()=>{const{data:e}=d();return{canUser:t=>Boolean(e?.capabilities.includes(t)),capabilities:e?.capabilities}};(window.elementorV2=window.elementorV2||{}).editorCurrentUser=t}(),window.elementorV2.editorCurrentUser?.init?.();