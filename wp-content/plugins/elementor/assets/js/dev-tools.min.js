/*! elementor - v3.29.0 - 04-06-2025 */
(()=>{var e={40440:(e,r,t)=>{"use strict";var o=t(96784);Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var n=o(t(18821)),a=o(t(39805)),s=o(t(40989)),i=function deprecatedMessage(e,r,t,o){var n="`".concat(r,"` is ").concat(e," deprecated since ").concat(t);o&&(n+=" - Use `".concat(o,"` instead")),elementorDevTools.consoleWarn(n)};r.default=function(){return(0,s.default)((function Deprecation(){(0,a.default)(this,Deprecation)}),[{key:"deprecated",value:function deprecated(e,r,t){this.isHardDeprecated(r)?function hardDeprecated(e,r,t){i("hard",e,r,t)}(e,r,t):function softDeprecated(e,r,t){elementorDevToolsConfig.isDebug&&i("soft",e,r,t)}(e,r,t)}},{key:"parseVersion",value:function parseVersion(e){var r=e.split(".");if(r.length<3||r.length>4)throw new RangeError("Invalid Semantic Version string provided");var t=(0,n.default)(r,4),o=t[0],a=t[1],s=t[2],i=t[3],u=void 0===i?"":i;return{major1:parseInt(o),major2:parseInt(a),minor:parseInt(s),build:u}}},{key:"getTotalMajor",value:function getTotalMajor(e){var r=parseInt("".concat(e.major1).concat(e.major2,"0"));return r=Number((r/10).toFixed(0)),e.major2>9&&(r=e.major2-9),r}},{key:"compareVersion",value:function compareVersion(e,r){var t=this;return[this.parseVersion(e),this.parseVersion(r)].map((function(e){return t.getTotalMajor(e)})).reduce((function(e,r){return e-r}))}},{key:"isSoftDeprecated",value:function isSoftDeprecated(e){return this.compareVersion(e,elementorDevToolsConfig.deprecation.current_version)<=elementorDevToolsConfig.deprecation.soft_version_count}},{key:"isHardDeprecated",value:function isHardDeprecated(e){var r=this.compareVersion(e,elementorDevToolsConfig.deprecation.current_version);return r<0||r>=elementorDevToolsConfig.deprecation.hard_version_count}}])}()},54952:(e,r,t)=>{"use strict";var o=t(96784);Object.defineProperty(r,"__esModule",{value:!0}),r.default=void 0;var n=o(t(10906)),a=o(t(18821)),s=o(t(39805)),i=o(t(40989)),u=o(t(85707));o(t(40440)),r.default=function(){return(0,i.default)((function Module(e){(0,s.default)(this,Module),(0,u.default)(this,"deprecation",void 0),this.deprecation=e}),[{key:"notifyBackendDeprecations",value:function notifyBackendDeprecations(){var e=this,r=elementorDevToolsConfig.deprecation.soft_notices;Object.entries(r).forEach((function(r){var t,o=(0,a.default)(r,2),s=o[0],i=o[1];(t=e.deprecation).deprecated.apply(t,[s].concat((0,n.default)(i)))}))}},{key:"consoleWarn",value:function consoleWarn(){for(var e,r='font-size: 12px; background-image: url("'.concat(elementorDevToolsConfig.urls.assets,'images/logo-icon.png"); background-repeat: no-repeat; background-size: contain;'),t=arguments.length,o=new Array(t),n=0;n<t;n++)o[n]=arguments[n];o.unshift("%c  %c",r,""),(e=console).warn.apply(e,o)}}])}()},78113:e=>{e.exports=function _arrayLikeToArray(e,r){(null==r||r>e.length)&&(r=e.length);for(var t=0,o=Array(r);t<r;t++)o[t]=e[t];return o},e.exports.__esModule=!0,e.exports.default=e.exports},70569:e=>{e.exports=function _arrayWithHoles(e){if(Array.isArray(e))return e},e.exports.__esModule=!0,e.exports.default=e.exports},91819:(e,r,t)=>{var o=t(78113);e.exports=function _arrayWithoutHoles(e){if(Array.isArray(e))return o(e)},e.exports.__esModule=!0,e.exports.default=e.exports},39805:e=>{e.exports=function _classCallCheck(e,r){if(!(e instanceof r))throw new TypeError("Cannot call a class as a function")},e.exports.__esModule=!0,e.exports.default=e.exports},40989:(e,r,t)=>{var o=t(45498);function _defineProperties(e,r){for(var t=0;t<r.length;t++){var n=r[t];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,o(n.key),n)}}e.exports=function _createClass(e,r,t){return r&&_defineProperties(e.prototype,r),t&&_defineProperties(e,t),Object.defineProperty(e,"prototype",{writable:!1}),e},e.exports.__esModule=!0,e.exports.default=e.exports},85707:(e,r,t)=>{var o=t(45498);e.exports=function _defineProperty(e,r,t){return(r=o(r))in e?Object.defineProperty(e,r,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[r]=t,e},e.exports.__esModule=!0,e.exports.default=e.exports},96784:e=>{e.exports=function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}},e.exports.__esModule=!0,e.exports.default=e.exports},20365:e=>{e.exports=function _iterableToArray(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)},e.exports.__esModule=!0,e.exports.default=e.exports},65474:e=>{e.exports=function _iterableToArrayLimit(e,r){var t=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=t){var o,n,a,s,i=[],u=!0,l=!1;try{if(a=(t=t.call(e)).next,0===r){if(Object(t)!==t)return;u=!1}else for(;!(u=(o=a.call(t)).done)&&(i.push(o.value),i.length!==r);u=!0);}catch(e){l=!0,n=e}finally{try{if(!u&&null!=t.return&&(s=t.return(),Object(s)!==s))return}finally{if(l)throw n}}return i}},e.exports.__esModule=!0,e.exports.default=e.exports},11018:e=>{e.exports=function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},e.exports.__esModule=!0,e.exports.default=e.exports},78687:e=>{e.exports=function _nonIterableSpread(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},e.exports.__esModule=!0,e.exports.default=e.exports},18821:(e,r,t)=>{var o=t(70569),n=t(65474),a=t(37744),s=t(11018);e.exports=function _slicedToArray(e,r){return o(e)||n(e,r)||a(e,r)||s()},e.exports.__esModule=!0,e.exports.default=e.exports},10906:(e,r,t)=>{var o=t(91819),n=t(20365),a=t(37744),s=t(78687);e.exports=function _toConsumableArray(e){return o(e)||n(e)||a(e)||s()},e.exports.__esModule=!0,e.exports.default=e.exports},11327:(e,r,t)=>{var o=t(10564).default;e.exports=function toPrimitive(e,r){if("object"!=o(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,r||"default");if("object"!=o(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)},e.exports.__esModule=!0,e.exports.default=e.exports},45498:(e,r,t)=>{var o=t(10564).default,n=t(11327);e.exports=function toPropertyKey(e){var r=n(e,"string");return"symbol"==o(r)?r:r+""},e.exports.__esModule=!0,e.exports.default=e.exports},10564:e=>{function _typeof(r){return e.exports=_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports.default=e.exports,_typeof(r)}e.exports=_typeof,e.exports.__esModule=!0,e.exports.default=e.exports},37744:(e,r,t)=>{var o=t(78113);e.exports=function _unsupportedIterableToArray(e,r){if(e){if("string"==typeof e)return o(e,r);var t={}.toString.call(e).slice(8,-1);return"Object"===t&&e.constructor&&(t=e.constructor.name),"Map"===t||"Set"===t?Array.from(e):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?o(e,r):void 0}},e.exports.__esModule=!0,e.exports.default=e.exports}},r={};function __webpack_require__(t){var o=r[t];if(void 0!==o)return o.exports;var n=r[t]={exports:{}};return e[t](n,n.exports,__webpack_require__),n.exports}(()=>{"use strict";var e=__webpack_require__(96784),r=e(__webpack_require__(40440)),t=e(__webpack_require__(54952));window.elementorDevTools||(window.elementorDevTools=new t.default(new r.default),window.elementorDevTools.notifyBackendDeprecations())})()})();