/*! elementor - v3.29.0 - 04-06-2025 */
"use strict";
(self["webpackChunkelementorFrontend"] = self["webpackChunkelementorFrontend"] || []).push([["section-frontend-handlers"],{

/***/ "../assets/dev/js/frontend/handlers/section/stretched-section.js":
/*!***********************************************************************!*\
  !*** ../assets/dev/js/frontend/handlers/section/stretched-section.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, exports) => {



Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports["default"] = void 0;
class StretchedSection extends elementorModules.frontend.handlers.StretchedElement {
  getStretchedClass() {
    return 'elementor-section-stretched';
  }
  getStretchSettingName() {
    return 'stretch_section';
  }
  getStretchActiveValue() {
    return 'section-stretched';
  }
}
exports["default"] = StretchedSection;

/***/ })

}]);
//# sourceMappingURL=section-frontend-handlers.d0665d28b9f0b188fe4f.bundle.js.map