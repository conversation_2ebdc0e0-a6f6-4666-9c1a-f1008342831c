/*! elementor - v3.29.0 - 04-06-2025 */
"use strict";(self.webpackChunkelementorFrontend=self.webpackChunkelementorFrontend||[]).push([[396],{9956:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n(4846),n(6211);const s="e-handles-inside";class HandlesPosition extends elementorModules.frontend.handlers.Base{onInit(){this.$element.on("mouseenter",this.setHandlesPosition.bind(this))}isSectionScrollSnapEnabled(){return elementor.settings.page.model.attributes.scroll_snap}isFirstElement(){return this.$element[0]===document.querySelector(".elementor-section-wrap > .elementor-element:first-child")}isOverflowHidden(){return"hidden"===this.$element.css("overflow")}getOffset(){if("body"===elementor.config.document.container)return this.$element.offset().top;const e=jQuery(elementor.config.document.container);return this.$element.offset().top-e.offset().top}setHandlesPosition(){const e=elementor.documents.getCurrent();if(!e||!e.container.isEditable())return;if(this.isSectionScrollSnapEnabled())return void this.$element.addClass(s);if(!this.isOverflowHidden()&&!this.isFirstElement())return void this.$element.removeClass(s);const t=this.getOffset(),n=this.$element.find("> .elementor-element-overlay > .elementor-editor-section-settings");t<25?(this.$element.addClass(s),n.css("top",t<-5?-t:"")):this.$element.removeClass(s)}}t.default=HandlesPosition}}]);