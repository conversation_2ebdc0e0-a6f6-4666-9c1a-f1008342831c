/*! elementor - v3.29.0 - 04-06-2025 */
/*! For license information please see e-wc-product-editor.min.js.LICENSE.txt */
(()=>{var e={91003:(e,t,r)=>{"use strict";r.r(t),r.d(t,{Children:()=>n.Children,Component:()=>n.Component,Fragment:()=>n.Fragment,Platform:()=>d,PureComponent:()=>n.PureComponent,RawHTML:()=>RawHTML,StrictMode:()=>n.StrictMode,Suspense:()=>n.Suspense,cloneElement:()=>n.cloneElement,concatChildren:()=>concatChildren,createContext:()=>n.createContext,createElement:()=>n.createElement,createInterpolateElement:()=>create_interpolate_element,createPortal:()=>c.createPortal,createRef:()=>n.createRef,createRoot:()=>u.createRoot,findDOMNode:()=>c.findDOMNode,flushSync:()=>c.flushSync,forwardRef:()=>n.forwardRef,hydrate:()=>c.hydrate,hydrateRoot:()=>u.hydrateRoot,isEmptyElement:()=>isEmptyElement,isValidElement:()=>n.isValidElement,lazy:()=>n.lazy,memo:()=>n.memo,render:()=>c.render,renderToString:()=>E,startTransition:()=>n.startTransition,switchChildrenNodeName:()=>switchChildrenNodeName,unmountComponentAtNode:()=>c.unmountComponentAtNode,useCallback:()=>n.useCallback,useContext:()=>n.useContext,useDebugValue:()=>n.useDebugValue,useDeferredValue:()=>n.useDeferredValue,useEffect:()=>n.useEffect,useId:()=>n.useId,useImperativeHandle:()=>n.useImperativeHandle,useInsertionEffect:()=>n.useInsertionEffect,useLayoutEffect:()=>n.useLayoutEffect,useMemo:()=>n.useMemo,useReducer:()=>n.useReducer,useRef:()=>n.useRef,useState:()=>n.useState,useSyncExternalStore:()=>n.useSyncExternalStore,useTransition:()=>n.useTransition});var n=r(41594);let o,a,i,s;const l=/<(\/)?(\w+)\s*(\/)?>/g;function createFrame(e,t,r,n,o){return{element:e,tokenStart:t,tokenLength:r,prevOffset:n,leadingTextStart:o,children:[]}}const isValidConversionMap=e=>{const t="object"==typeof e,r=t&&Object.values(e);return t&&r.length&&r.every((e=>(0,n.isValidElement)(e)))};function proceed(e){const t=function nextToken(){const e=l.exec(o);if(null===e)return["no-more-tokens"];const t=e.index,[r,n,a,i]=e,s=r.length;if(i)return["self-closed",a,t,s];if(n)return["closer",a,t,s];return["opener",a,t,s]}(),[r,c,u,d]=t,p=s.length,f=u>a?a:null;if(!e[c])return addText(),!1;switch(r){case"no-more-tokens":if(0!==p){const{leadingTextStart:e,tokenStart:t}=s.pop();i.push(o.substr(e,t))}return addText(),!1;case"self-closed":return 0===p?(null!==f&&i.push(o.substr(f,u-f)),i.push(e[c]),a=u+d,!0):(addChild(createFrame(e[c],u,d)),a=u+d,!0);case"opener":return s.push(createFrame(e[c],u,d,u+d,f)),a=u+d,!0;case"closer":if(1===p)return function closeOuterElement(e){const{element:t,leadingTextStart:r,prevOffset:a,tokenStart:l,children:c}=s.pop(),u=e?o.substr(a,e-a):o.substr(a);u&&c.push(u);null!==r&&i.push(o.substr(r,l-r));i.push((0,n.cloneElement)(t,null,...c))}(u),a=u+d,!0;const t=s.pop(),r=o.substr(t.prevOffset,u-t.prevOffset);t.children.push(r),t.prevOffset=u+d;const l=createFrame(t.element,t.tokenStart,t.tokenLength,u+d);return l.children=t.children,addChild(l),a=u+d,!0;default:return addText(),!1}}function addText(){const e=o.length-a;0!==e&&i.push(o.substr(a,e))}function addChild(e){const{element:t,tokenStart:r,tokenLength:a,prevOffset:i,children:l}=e,c=s[s.length-1],u=o.substr(c.prevOffset,r-c.prevOffset);u&&c.children.push(u),c.children.push((0,n.cloneElement)(t,null,...l)),c.prevOffset=i||r+a}const create_interpolate_element=(e,t)=>{if(o=e,a=0,i=[],s=[],l.lastIndex=0,!isValidConversionMap(t))throw new TypeError("The conversionMap provided is not valid. It must be an object with values that are React Elements");do{}while(proceed(t));return(0,n.createElement)(n.Fragment,null,...i)};function concatChildren(...e){return e.reduce(((e,t,r)=>(n.Children.forEach(t,((t,o)=>{t&&"string"!=typeof t&&(t=(0,n.cloneElement)(t,{key:[r,o].join()})),e.push(t)})),e)),[])}function switchChildrenNodeName(e,t){return e&&n.Children.map(e,((e,r)=>{if("string"==typeof e?.valueOf())return(0,n.createElement)(t,{key:r},e);const{children:o,...a}=e.props;return(0,n.createElement)(t,{key:r,...a},o)}))}var c=r(75206),u=r(7470);const isEmptyElement=e=>"number"!=typeof e&&("string"==typeof e?.valueOf()||Array.isArray(e)?!e.length:!e),d={OS:"web",select:e=>"web"in e?e.web:e.default,isWeb:!0};function isObject(e){return"[object Object]"===Object.prototype.toString.call(e)}var __assign=function(){return __assign=Object.assign||function __assign(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},__assign.apply(this,arguments)};Object.create;Object.create;"function"==typeof SuppressedError&&SuppressedError;function lowerCase(e){return e.toLowerCase()}var p=[/([a-z0-9])([A-Z])/g,/([A-Z])([A-Z][a-z])/g],f=/[^A-Z0-9]+/gi;function replace(e,t,r){return t instanceof RegExp?e.replace(t,r):t.reduce((function(e,t){return e.replace(t,r)}),e)}function dotCase(e,t){return void 0===t&&(t={}),function noCase(e,t){void 0===t&&(t={});for(var r=t.splitRegexp,n=void 0===r?p:r,o=t.stripRegexp,a=void 0===o?f:o,i=t.transform,s=void 0===i?lowerCase:i,l=t.delimiter,c=void 0===l?" ":l,u=replace(replace(e,n,"$1\0$2"),a,"\0"),d=0,m=u.length;"\0"===u.charAt(d);)d++;for(;"\0"===u.charAt(m-1);)m--;return u.slice(d,m).split("\0").map(s).join(c)}(e,__assign({delimiter:"."},t))}function paramCase(e,t){return void 0===t&&(t={}),dotCase(e,__assign({delimiter:"-"},t))}const m=/[\u007F-\u009F "'>/="\uFDD0-\uFDEF]/;function escapeAmpersand(e){return e.replace(/&(?!([a-z0-9]+|#[0-9]+|#x[a-f0-9]+);)/gi,"&amp;")}function escapeLessThan(e){return e.replace(/</g,"&lt;")}function escapeAttribute(e){return function __unstableEscapeGreaterThan(e){return e.replace(/>/g,"&gt;")}(function escapeQuotationMark(e){return e.replace(/"/g,"&quot;")}(escapeAmpersand(e)))}function isValidAttributeName(e){return!m.test(e)}function RawHTML({children:e,...t}){let r="";return n.Children.toArray(e).forEach((e=>{"string"==typeof e&&""!==e.trim()&&(r+=e)})),(0,n.createElement)("div",{dangerouslySetInnerHTML:{__html:r},...t})}const{Provider:h,Consumer:y}=(0,n.createContext)(void 0),g=(0,n.forwardRef)((()=>null)),_=new Set(["string","boolean","number"]),b=new Set(["area","base","br","col","command","embed","hr","img","input","keygen","link","meta","param","source","track","wbr"]),x=new Set(["allowfullscreen","allowpaymentrequest","allowusermedia","async","autofocus","autoplay","checked","controls","default","defer","disabled","download","formnovalidate","hidden","ismap","itemscope","loop","multiple","muted","nomodule","novalidate","open","playsinline","readonly","required","reversed","selected","typemustmatch"]),v=new Set(["autocapitalize","autocomplete","charset","contenteditable","crossorigin","decoding","dir","draggable","enctype","formenctype","formmethod","http-equiv","inputmode","kind","method","preload","scope","shape","spellcheck","translate","type","wrap"]),w=new Set(["animation","animationIterationCount","baselineShift","borderImageOutset","borderImageSlice","borderImageWidth","columnCount","cx","cy","fillOpacity","flexGrow","flexShrink","floodOpacity","fontWeight","gridColumnEnd","gridColumnStart","gridRowEnd","gridRowStart","lineHeight","opacity","order","orphans","r","rx","ry","shapeImageThreshold","stopOpacity","strokeDasharray","strokeDashoffset","strokeMiterlimit","strokeOpacity","strokeWidth","tabSize","widows","x","y","zIndex","zoom"]);function hasPrefix(e,t){return t.some((t=>0===e.indexOf(t)))}function isInternalAttribute(e){return"key"===e||"children"===e}function getNormalAttributeValue(e,t){return"style"===e?function renderStyle(e){if(!function isPlainObject(e){var t,r;return!1!==isObject(e)&&(void 0===(t=e.constructor)||!1!==isObject(r=t.prototype)&&!1!==r.hasOwnProperty("isPrototypeOf"))}(e))return e;let t;for(const r in e){const n=e[r];if(null==n)continue;t?t+=";":t="";t+=getNormalStylePropertyName(r)+":"+getNormalStylePropertyValue(r,n)}return t}(t):t}const k=["accentHeight","alignmentBaseline","arabicForm","baselineShift","capHeight","clipPath","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","dominantBaseline","enableBackground","fillOpacity","fillRule","floodColor","floodOpacity","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","horizAdvX","horizOriginX","imageRendering","letterSpacing","lightingColor","markerEnd","markerMid","markerStart","overlinePosition","overlineThickness","paintOrder","panose1","pointerEvents","renderingIntent","shapeRendering","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","textAnchor","textDecoration","textRendering","underlinePosition","underlineThickness","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","vHanging","vIdeographic","vMathematical","vectorEffect","vertAdvY","vertOriginX","vertOriginY","wordSpacing","writingMode","xmlnsXlink","xHeight"].reduce(((e,t)=>(e[t.toLowerCase()]=t,e)),{}),S=["allowReorder","attributeName","attributeType","autoReverse","baseFrequency","baseProfile","calcMode","clipPathUnits","contentScriptType","contentStyleType","diffuseConstant","edgeMode","externalResourcesRequired","filterRes","filterUnits","glyphRef","gradientTransform","gradientUnits","kernelMatrix","kernelUnitLength","keyPoints","keySplines","keyTimes","lengthAdjust","limitingConeAngle","markerHeight","markerUnits","markerWidth","maskContentUnits","maskUnits","numOctaves","pathLength","patternContentUnits","patternTransform","patternUnits","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","refX","refY","repeatCount","repeatDur","requiredExtensions","requiredFeatures","specularConstant","specularExponent","spreadMethod","startOffset","stdDeviation","stitchTiles","suppressContentEditableWarning","suppressHydrationWarning","surfaceScale","systemLanguage","tableValues","targetX","targetY","textLength","viewBox","viewTarget","xChannelSelector","yChannelSelector"].reduce(((e,t)=>(e[t.toLowerCase()]=t,e)),{}),C=["xlink:actuate","xlink:arcrole","xlink:href","xlink:role","xlink:show","xlink:title","xlink:type","xml:base","xml:lang","xml:space","xmlns:xlink"].reduce(((e,t)=>(e[t.replace(":","").toLowerCase()]=t,e)),{});function getNormalAttributeName(e){switch(e){case"htmlFor":return"for";case"className":return"class"}const t=e.toLowerCase();return S[t]?S[t]:k[t]?paramCase(k[t]):C[t]?C[t]:t}function getNormalStylePropertyName(e){return e.startsWith("--")?e:hasPrefix(e,["ms","O","Moz","Webkit"])?"-"+paramCase(e):paramCase(e)}function getNormalStylePropertyValue(e,t){return"number"!=typeof t||0===t||w.has(e)?t:t+"px"}function renderElement(e,t,r={}){if(null==e||!1===e)return"";if(Array.isArray(e))return renderChildren(e,t,r);switch(typeof e){case"string":return function escapeHTML(e){return escapeLessThan(escapeAmpersand(e))}(e);case"number":return e.toString()}const{type:o,props:a}=e;switch(o){case n.StrictMode:case n.Fragment:return renderChildren(a.children,t,r);case RawHTML:const{children:e,...o}=a;return renderNativeComponent(Object.keys(o).length?"div":null,{...o,dangerouslySetInnerHTML:{__html:e}},t,r)}switch(typeof o){case"string":return renderNativeComponent(o,a,t,r);case"function":return o.prototype&&"function"==typeof o.prototype.render?function renderComponent(e,t,r,n={}){const o=new e(t,n);"function"==typeof o.getChildContext&&Object.assign(n,o.getChildContext());const a=renderElement(o.render(),r,n);return a}(o,a,t,r):renderElement(o(a,r),t,r)}switch(o&&o.$$typeof){case h.$$typeof:return renderChildren(a.children,a.value,r);case y.$$typeof:return renderElement(a.children(t||o._currentValue),t,r);case g.$$typeof:return renderElement(o.render(a),t,r)}return""}function renderNativeComponent(e,t,r,n={}){let o="";if("textarea"===e&&t.hasOwnProperty("value")){o=renderChildren(t.value,r,n);const{value:e,...a}=t;t=a}else t.dangerouslySetInnerHTML&&"string"==typeof t.dangerouslySetInnerHTML.__html?o=t.dangerouslySetInnerHTML.__html:void 0!==t.children&&(o=renderChildren(t.children,r,n));if(!e)return o;const a=function renderAttributes(e){let t="";for(const r in e){const n=getNormalAttributeName(r);if(!isValidAttributeName(n))continue;let o=getNormalAttributeValue(r,e[r]);if(!_.has(typeof o))continue;if(isInternalAttribute(r))continue;const a=x.has(n);if(a&&!1===o)continue;const i=a||hasPrefix(r,["data-","aria-"])||v.has(n);("boolean"!=typeof o||i)&&(t+=" "+n,a||("string"==typeof o&&(o=escapeAttribute(o)),t+='="'+o+'"'))}return t}(t);return b.has(e)?"<"+e+a+"/>":"<"+e+a+">"+o+"</"+e+">"}function renderChildren(e,t,r={}){let n="";e=Array.isArray(e)?e:[e];for(let o=0;o<e.length;o++){n+=renderElement(e[o],t,r)}return n}const E=renderElement},7470:(e,t,r)=>{"use strict";var n=r(75206);t.createRoot=n.createRoot,t.hydrateRoot=n.hydrateRoot},41594:e=>{"use strict";e.exports=React},75206:e=>{"use strict";e.exports=ReactDOM},67818:e=>{"use strict";e.exports=wc.adminLayout},2214:e=>{"use strict";e.exports=wp.components},59165:e=>{"use strict";e.exports=wp.coreData},37562:e=>{"use strict";e.exports=wp.data},12470:e=>{"use strict";e.exports=wp.i18n},59986:e=>{"use strict";e.exports=wp.plugins},78113:e=>{e.exports=function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n},e.exports.__esModule=!0,e.exports.default=e.exports},70569:e=>{e.exports=function _arrayWithHoles(e){if(Array.isArray(e))return e},e.exports.__esModule=!0,e.exports.default=e.exports},96784:e=>{e.exports=function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}},e.exports.__esModule=!0,e.exports.default=e.exports},65474:e=>{e.exports=function _iterableToArrayLimit(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,a,i,s=[],l=!0,c=!1;try{if(a=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=a.call(r)).done)&&(s.push(n.value),s.length!==t);l=!0);}catch(e){c=!0,o=e}finally{try{if(!l&&null!=r.return&&(i=r.return(),Object(i)!==i))return}finally{if(c)throw o}}return s}},e.exports.__esModule=!0,e.exports.default=e.exports},11018:e=>{e.exports=function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},e.exports.__esModule=!0,e.exports.default=e.exports},18821:(e,t,r)=>{var n=r(70569),o=r(65474),a=r(37744),i=r(11018);e.exports=function _slicedToArray(e,t){return n(e)||o(e,t)||a(e,t)||i()},e.exports.__esModule=!0,e.exports.default=e.exports},37744:(e,t,r)=>{var n=r(78113);e.exports=function _unsupportedIterableToArray(e,t){if(e){if("string"==typeof e)return n(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?n(e,t):void 0}},e.exports.__esModule=!0,e.exports.default=e.exports}},t={};function __webpack_require__(r){var n=t[r];if(void 0!==n)return n.exports;var o=t[r]={exports:{}};return e[r](o,o.exports,__webpack_require__),o.exports}__webpack_require__.d=(e,t)=>{for(var r in t)__webpack_require__.o(t,r)&&!__webpack_require__.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},__webpack_require__.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),__webpack_require__.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},(()=>{"use strict";var e=__webpack_require__(96784),t=e(__webpack_require__(41594)),r=e(__webpack_require__(18821)),n=__webpack_require__(91003),o=__webpack_require__(12470),a=__webpack_require__(37562),i=__webpack_require__(59165),s=__webpack_require__(2214),l=__webpack_require__(59986),c=__webpack_require__(67818);(0,l.registerPlugin)("elementor-header-item",{render:function EditWithElementorButton(){var e=(0,n.useState)(!1),l=(0,r.default)(e,2),u=l[0],d=l[1],p=(0,i.useEntityId)("postType","product"),f=(0,a.useDispatch)("core").saveEntityRecord,m=(0,a.useSelect)((function(e){var t;return null===(t=e("core").getEditedEntityRecord("postType","product",p))||void 0===t?void 0:t.status}),[p]),h=wp.data.select("core/editor").isSavingPost();(0,n.useEffect)((function(){u&&!h&&y()}),[u,h]);var y=function redirectToElementor(){window.location.href=g()},g=function getEditUrl(){var e=new URL(ElementorWCProductEditorSettings.editLink);return e.searchParams.set("post",p),e.searchParams.set("action","elementor"),e.toString()};return t.default.createElement(c.WooHeaderItem,{name:"product"},t.default.createElement(s.Button,{variant:"primary",onClick:function handleClick(){"auto-draft"===m?f("postType","product",{id:p,name:"Elementor #".concat(p),status:"draft"}).then((function(){d(!0)})).catch((function(){})):d(!0)},style:{display:"flex",alignItems:"center"}},t.default.createElement("i",{className:"eicon-elementor-square","aria-hidden":"true",style:{paddingInlineEnd:"8px"}}),(0,o.__)("Edit with Elementor","elementor")))},scope:"woocommerce-product-block-editor"})})()})();