/*! elementor - v3.29.0 - 04-06-2025 */
/*! For license information please see admin.min.js.LICENSE.txt */
(()=>{var e={1977:e=>{"use strict";e.exports=elementorModules.ViewModule.extend({getDefaultSettings:function getDefaultSettings(){return{selectors:{modeSelect:".elementor_maintenance_mode_mode select",maintenanceModeTable:"#tab-maintenance_mode table",maintenanceModeDescriptions:".elementor-maintenance-mode-description",excludeModeSelect:".elementor_maintenance_mode_exclude_mode select",excludeRolesArea:".elementor_maintenance_mode_exclude_roles",templateSelect:".elementor_maintenance_mode_template_id select",editTemplateButton:".elementor-edit-template",maintenanceModeError:".elementor-maintenance-mode-error"},classes:{isEnabled:"elementor-maintenance-mode-is-enabled"}}},getDefaultElements:function getDefaultElements(){var e={},t=this.getSettings("selectors");return e.$modeSelect=jQuery(t.modeSelect),e.$maintenanceModeTable=e.$modeSelect.parents(t.maintenanceModeTable),e.$excludeModeSelect=e.$maintenanceModeTable.find(t.excludeModeSelect),e.$excludeRolesArea=e.$maintenanceModeTable.find(t.excludeRolesArea),e.$templateSelect=e.$maintenanceModeTable.find(t.templateSelect),e.$editTemplateButton=e.$maintenanceModeTable.find(t.editTemplateButton),e.$maintenanceModeDescriptions=e.$maintenanceModeTable.find(t.maintenanceModeDescriptions),e.$maintenanceModeError=e.$maintenanceModeTable.find(t.maintenanceModeError),e},handleModeSelectChange:function handleModeSelectChange(){var e=this.getSettings(),t=this.elements;t.$maintenanceModeTable.toggleClass(e.classes.isEnabled,!!t.$modeSelect.val()),t.$maintenanceModeDescriptions.hide(),t.$maintenanceModeDescriptions.filter('[data-value="'+t.$modeSelect.val()+'"]').show()},handleExcludeModeSelectChange:function handleExcludeModeSelectChange(){var e=this.elements;e.$excludeRolesArea.toggle("custom"===e.$excludeModeSelect.val())},handleTemplateSelectChange:function handleTemplateSelectChange(){var e=this.elements,t=e.$templateSelect.val();if(!t)return e.$editTemplateButton.hide(),void e.$maintenanceModeError.show();var n=elementorAdmin.config.home_url+"?p="+t+"&elementor";e.$editTemplateButton.prop("href",n).show(),e.$maintenanceModeError.hide()},bindEvents:function bindEvents(){var e=this.elements;e.$modeSelect.on("change",this.handleModeSelectChange.bind(this)),e.$excludeModeSelect.on("change",this.handleExcludeModeSelectChange.bind(this)),e.$templateSelect.on("change",this.handleTemplateSelectChange.bind(this))},onAdminInit:function onAdminInit(){this.handleModeSelectChange(),this.handleExcludeModeSelectChange(),this.handleTemplateSelectChange()},onInit:function onInit(){elementorModules.ViewModule.prototype.onInit.apply(this,arguments),elementorCommon.elements.$window.on("elementor/admin/init",this.onAdminInit)}})},26098:(e,t,n)=>{"use strict";var o=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=o(n(39805)),a=o(n(40989)),i=o(n(15118)),l=o(n(29402)),s=o(n(41621)),u=o(n(87861));function _isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!e})()}t.default=function(e){function MenuHandler(){return(0,r.default)(this,MenuHandler),function _callSuper(e,t,n){return t=(0,l.default)(t),(0,i.default)(e,_isNativeReflectConstruct()?Reflect.construct(t,n||[],(0,l.default)(e).constructor):t.apply(e,n))}(this,MenuHandler,arguments)}return(0,u.default)(MenuHandler,e),(0,a.default)(MenuHandler,[{key:"getDefaultSettings",value:function getDefaultSettings(){return{selectors:{currentSubmenuItems:"#adminmenu .current"}}}},{key:"getDefaultElements",value:function getDefaultElements(){var e=this.getSettings();return{$currentSubmenuItems:jQuery(e.selectors.currentSubmenuItems),$adminPageMenuLink:jQuery('a[href="'.concat(e.path,'"]'))}}},{key:"highlightSubMenuItem",value:function highlightSubMenuItem(){var e=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:null)||this.elements.$adminPageMenuLink;this.elements.$currentSubmenuItems.length&&this.elements.$currentSubmenuItems.removeClass("current"),e.addClass("current"),e.parent().addClass("current")}},{key:"highlightTopLevelMenuItem",value:function highlightTopLevelMenuItem(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n="wp-has-current-submenu wp-menu-open current";e.parent().addClass(n).removeClass("wp-not-current-submenu"),t&&t.removeClass(n)}},{key:"onInit",value:function onInit(){!function _superPropGet(e,t,n,o){var r=(0,s.default)((0,l.default)(1&o?e.prototype:e),t,n);return 2&o&&"function"==typeof r?function(e){return r.apply(n,e)}:r}(MenuHandler,"onInit",this,3)([]);var e=this.getSettings();window.location.href.includes(e.path)&&this.highlightSubMenuItem()}}])}(elementorModules.ViewModule)},95067:(e,t,n)=>{"use strict";var o=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=o(n(18821)),a=o(n(39805)),i=o(n(40989));t.default=function(){return(0,i.default)((function TemplateControls(){(0,a.default)(this,TemplateControls)}),[{key:"setDynamicControlsVisibility",value:function setDynamicControlsVisibility(e,t){if(void 0!==t)for(var n=0,o=Object.entries(t);n<o.length;n++){var a=(0,r.default)(o[n],2),i=a[0],l=a[1];this.setVisibilityForControl(e,l,i)}}},{key:"setVisibilityForControl",value:function setVisibilityForControl(e,t,n){var o,r=this;Object.entries(null!==(o=t.conditions)&&void 0!==o?o:{}).forEach((function(t){r.changeVisibilityBasedOnCondition(e,t,n)}))}},{key:"changeVisibilityBasedOnCondition",value:function changeVisibilityBasedOnCondition(e,t,n){var o=(0,r.default)(t,2),a=o[0],i=o[1],l=document.getElementById(e+n+"__wrapper"),s=document.getElementById(e+a);l.classList.toggle("elementor-hidden",!s||i!==s.value)}}])}()},14100:(e,t,n)=>{"use strict";var o=n(12470).__,r=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=r(n(39805)),i=r(n(40989));t.default=function(){return(0,i.default)((function FilesUploadHandler(){(0,a.default)(this,FilesUploadHandler)}),null,[{key:"isUploadEnabled",value:function isUploadEnabled(e){return!["svg","application/json"].includes(e)||elementorCommon.config.filesUpload.unfilteredFiles}},{key:"setUploadTypeCaller",value:function setUploadTypeCaller(e){e.uploader.uploader.param("uploadTypeCaller","elementor-wp-media-upload")}},{key:"getUnfilteredFilesNonAdminDialog",value:function getUnfilteredFilesNonAdminDialog(){return elementorCommon.dialogsManager.createWidget("alert",{id:"e-unfiltered-files-disabled-dialog",headerMessage:o("Sorry, you can't upload that file yet","elementor"),message:o("This is because JSON files may pose a security risk.","elementor")+"<br><br>"+o("To upload them anyway, ask the site administrator to enable unfiltered file uploads.","elementor"),strings:{confirm:o("Got it","elementor")}})}},{key:"getUnfilteredFilesNotEnabledDialog",value:function getUnfilteredFilesNotEnabledDialog(e){var t=window.elementorAdmin||window.elementor;if(!t.config.user.is_administrator)return this.getUnfilteredFilesNonAdminDialog();return t.helpers.getSimpleDialog("e-enable-unfiltered-files-dialog",o("Enable Unfiltered File Uploads","elementor"),o("Before you enable unfiltered files upload, note that such files include a security risk. Elementor does run a process to remove possible malicious code, but there is still risk involved when using such files.","elementor"),o("Enable","elementor"),(function onConfirm(){elementorCommon.ajax.addRequest("enable_unfiltered_files_upload",{},!0),elementorCommon.config.filesUpload.unfilteredFiles=!0,e()}))}},{key:"getUnfilteredFilesNotEnabledImportTemplateDialog",value:function getUnfilteredFilesNotEnabledImportTemplateDialog(e){return(window.elementorAdmin||window.elementor).config.user.is_administrator?elementorCommon.dialogsManager.createWidget("confirm",{id:"e-enable-unfiltered-files-dialog-import-template",headerMessage:o("Enable Unfiltered File Uploads","elementor"),message:o("Before you enable unfiltered files upload, note that such files include a security risk. Elementor does run a process to remove possible malicious code, but there is still risk involved when using such files.","elementor")+"<br /><br />"+o("If you do not enable uploading unfiltered files, any SVG or JSON (including lottie) files used in the uploaded template will not be imported.","elementor"),position:{my:"center center",at:"center center"},strings:{confirm:o("Enable and Import","elementor"),cancel:o("Import Without Enabling","elementor")},onConfirm:function onConfirm(){elementorCommon.ajax.addRequest("enable_unfiltered_files_upload",{success:function success(){elementorCommon.config.filesUpload.unfilteredFiles=!0,e()}},!0)},onCancel:function onCancel(){return e()}}):this.getUnfilteredFilesNonAdminDialog()}}])}()},54799:(e,t,n)=>{"use strict";var o=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=t.Events=void 0;var r=o(n(39805)),a=o(n(40989)),i=t.Events=function(){return(0,a.default)((function Events(){(0,r.default)(this,Events)}),null,[{key:"dispatch",value:function dispatch(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null;e=e instanceof jQuery?e[0]:e,o&&e.dispatchEvent(new CustomEvent(o,{detail:n})),e.dispatchEvent(new CustomEvent(t,{detail:n}))}}])}();t.default=i},67631:(e,t,n)=>{"use strict";var o=n(12470).__,r=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.showJsonUploadWarningMessageIfNeeded=function showJsonUploadWarningMessageIfNeeded(e){var t=e.introductionMap,n=e.IntroductionClass,r=e.waitForSetViewed,u=void 0!==r&&r;s||(s=function createGenericWarningModal(e){var t,n,r="e-generic-warning-modal-for-json-upload",a=new e({introductionKey:l,dialogType:"confirm",dialogOptions:{id:r,headerMessage:o("Warning: JSON files may be unsafe","elementor"),message:o("Uploading JSON files from unknown sources can be harmful and put your site at risk. For maximum safety, only install JSON files from trusted sources.","elementor"),effects:{show:"fadeIn",hide:"fadeOut"},hide:{onBackgroundClick:!0,onButtonClick:!1},strings:{confirm:o("Continue","elementor"),cancel:o("Cancel","elementor")}}}),i=function createCheckboxAndLabel(e){var t="".concat(e,"-dont-show-again"),n=document.createElement("input");n.type="checkbox",n.name=t,n.id=t;var r=document.createElement("label");return r.htmlFor=t,r.textContent=o("Do not show this message again","elementor"),r.style.display="block",r.style.marginTop="20px",r.style.marginBottom="20px",r.prepend(n),{checkbox:n,label:r}}(r),s=i.checkbox,u=i.label;return a.getDialog().addElement("checkbox-dont-show-again",s),null===(t=a.getDialog().getElements("message"))||void 0===t||null===(n=t.append)||void 0===n||n.call(t,u),a}(n));if(s.setIntroductionMap(t),s.introductionViewed)return Promise.resolve();var c=s.getDialog();return new Promise((function(e,t){c.onHide=function(){t()},c.onConfirm=(0,i.default)(a.default.mark((function _callee(){return a.default.wrap((function _callee$(t){for(;;)switch(t.prev=t.next){case 0:if(!c.getElements("checkbox-dont-show-again").prop("checked")){t.next=7;break}if(!u){t.next=6;break}return t.next=4,s.setViewed();case 4:t.next=7;break;case 6:s.setViewed();case 7:e(),c.hide();case 9:case"end":return t.stop()}}),_callee)}))),c.onCancel=function(){c.hide()},s.show()}))};var a=r(n(61790)),i=r(n(58155)),l="upload_json_warning_generic_message",s=null},75115:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=function matchUserAgent(e){return o.indexOf(e)>=0},o=navigator.userAgent,r=!!window.opr&&!!opr.addons||!!window.opera||n(" OPR/"),a=n("Firefox"),i=/^((?!chrome|android).)*safari/i.test(o)||/constructor/i.test(window.HTMLElement)||"[object SafariRemoteNotification]"===(!window.safari||"undefined"!=typeof safari&&safari.pushNotification).toString(),l=/Trident|MSIE/.test(o)&&!!document.documentMode,s=!l&&!!window.StyleMedia||n("Edg"),u=!!window.chrome&&n("Chrome")&&!(s||r),c=n("Chrome")&&!!window.CSS,d=n("AppleWebKit")&&!c,f={isTouchDevice:"ontouchstart"in window||navigator.maxTouchPoints>0||navigator.msMaxTouchPoints>0,appleWebkit:d,blink:c,chrome:u,edge:s,firefox:a,ie:l,mac:n("Macintosh"),opera:r,safari:i,webkit:n("AppleWebKit")};t.default=f},6280:(e,t,n)=>{"use strict";var o=n(12470).__,r=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=r(n(18821)),i=r(n(39805)),l=r(n(40989)),s=r(n(85707)),u="active",c="inactive",d="default";t.default=function(){return(0,l.default)((function ExperimentsMessages(e){var t=e.selects,n=e.submit;(0,i.default)(this,ExperimentsMessages),(0,s.default)(this,"elements",{}),this.elements={selects:t,submit:n}}),[{key:"bindEvents",value:function bindEvents(){var e=this;this.elements.selects.forEach((function(t){t.addEventListener("change",(function(t){return e.onExperimentStateChange(t)}))}))}},{key:"onExperimentStateChange",value:function onExperimentStateChange(e){var t=e.currentTarget.dataset.experimentId;switch(this.getExperimentActualState(t)){case u:this.shouldShowDependenciesDialog(t)&&this.showDependenciesDialog(t);break;case c:this.shouldShowDeactivationDialog(t)?this.showDeactivationDialog(t):this.deactivateDependantExperiments(t)}}},{key:"getExperimentData",value:function getExperimentData(e){return elementorAdminConfig.experiments[e]}},{key:"getExperimentDependencies",value:function getExperimentDependencies(e){var t=this;return this.getExperimentData(e).dependencies.map((function(e){return t.getExperimentData(e)}))}},{key:"getExperimentSelect",value:function getExperimentSelect(e){return this.elements.selects.find((function(t){return t.matches('[data-experiment-id="'.concat(e,'"]'))}))}},{key:"setExperimentState",value:function setExperimentState(e,t){this.getExperimentSelect(e).value=t}},{key:"getExperimentActualState",value:function getExperimentActualState(e){var t,n=null===(t=this.getExperimentSelect(e))||void 0===t?void 0:t.value;return n?n!==d?n:this.isExperimentActiveByDefault(e)?u:c:this.getExperimentData(e).state}},{key:"isExperimentActive",value:function isExperimentActive(e){return this.getExperimentActualState(e)===u}},{key:"isExperimentActiveByDefault",value:function isExperimentActiveByDefault(e){return this.getExperimentData(e).default===u}},{key:"areAllDependenciesActive",value:function areAllDependenciesActive(e){var t=this;return e.every((function(e){return t.isExperimentActive(e.name)}))}},{key:"deactivateDependantExperiments",value:function deactivateDependantExperiments(e){var t=this;Object.entries(elementorAdminConfig.experiments).forEach((function(n){var o=(0,a.default)(n,2),r=o[0],i=o[1].dependencies.includes(e),l=t.getExperimentActualState(r)===u;i&&l&&t.setExperimentState(r,c)}))}},{key:"shouldShowDependenciesDialog",value:function shouldShowDependenciesDialog(e){var t=this.getExperimentDependencies(e);return!this.areAllDependenciesActive(t)}},{key:"shouldShowDeactivationDialog",value:function shouldShowDeactivationDialog(e){var t=this.getExperimentData(e),n=t.state===u||t.state===d&&t.default===u;return!!this.getMessage(e,"on_deactivate")&&n}},{key:"showDialog",value:function showDialog(e){return elementorCommon.dialogsManager.createWidget("confirm",{id:"e-experiments-messages-dialog",headerMessage:e.headerMessage,message:e.message,position:{my:"center center",at:"center center"},strings:{confirm:e.strings.confirm,cancel:e.strings.cancel},hide:{onOutsideClick:!1,onBackgroundClick:!1,onEscKeyPress:!1},onConfirm:e.onConfirm,onCancel:e.onCancel}).show()}},{key:"getSiteLanguageCode",value:function getSiteLanguageCode(){var e=document.querySelector("html").getAttribute("lang");return null!=e?e:"en"}},{key:"formatDependenciesList",value:function formatDependenciesList(e){var t=e.map((function(e){return e.title})),n=this.getSiteLanguageCode();return new Intl.ListFormat(n).format(t)}},{key:"showDependenciesDialog",value:function showDependenciesDialog(e){var t=this,n=this.getExperimentData(e).title,r=this.formatDependenciesList(this.getExperimentDependencies(e)),a=o("In order to use %1$s, first you need to activate %2$s.","elementor").replace("%1$s","<strong>".concat(n,"</strong>")).replace("%2$s",r);this.showDialog({message:a,headerMessage:o("First, activate another experiment.","elementor"),strings:{confirm:o("Activate","elementor"),cancel:o("Cancel","elementor")},onConfirm:function onConfirm(){t.getExperimentDependencies(e).forEach((function(e){t.setExperimentState(e.name,u)})),t.elements.submit.click()},onCancel:function onCancel(){t.setExperimentState(e,c)}})}},{key:"showDeactivationDialog",value:function showDeactivationDialog(e){var t=this;this.showDialog({message:this.getMessage(e,"on_deactivate"),headerMessage:o("Are you sure?","elementor"),strings:{confirm:o("Deactivate","elementor"),cancel:o("Cancel","elementor")},onConfirm:function onConfirm(){t.setExperimentState(e,c),t.deactivateDependantExperiments(e),t.elements.submit.click()},onCancel:function onCancel(){t.setExperimentState(e,u)}})}},{key:"getMessage",value:function getMessage(e,t){var n;return null===(n=this.getExperimentData(e))||void 0===n?void 0:n.messages[t]}}])}()},41095:(e,t,n)=>{"use strict";var o=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=o(n(39805)),a=o(n(40989)),i=o(n(15118)),l=o(n(29402)),s=o(n(41621)),u=o(n(87861)),c=o(n(6280));function _isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!e})()}t.default=function(e){function ExperimentsModule(){return(0,r.default)(this,ExperimentsModule),function _callSuper(e,t,n){return t=(0,l.default)(t),(0,i.default)(e,_isNativeReflectConstruct()?Reflect.construct(t,n||[],(0,l.default)(e).constructor):t.apply(e,n))}(this,ExperimentsModule,arguments)}return(0,u.default)(ExperimentsModule,e),(0,a.default)(ExperimentsModule,[{key:"getDefaultSettings",value:function getDefaultSettings(){return{selectors:{experimentIndicators:".e-experiment__title__indicator",experimentForm:"#elementor-settings-form",experimentSelects:".e-experiment__select",experimentsButtons:".e-experiment__button"}}}},{key:"getDefaultElements",value:function getDefaultElements(){var e=this.getSettings().selectors;return{$experimentIndicators:jQuery(e.experimentIndicators),$experimentForm:jQuery(e.experimentForm),$experimentSelects:jQuery(e.experimentSelects),$experimentsButtons:jQuery(e.experimentsButtons)}}},{key:"bindEvents",value:function bindEvents(){var e=this;this.elements.$experimentsButtons.on("click",(function(t){return e.onExperimentsButtonsClick(t)}))}},{key:"onExperimentsButtonsClick",value:function onExperimentsButtonsClick(e){var t=jQuery(e.currentTarget);this.elements.$experimentSelects.val(t.val()),this.elements.$experimentForm.find("#submit").trigger("click")}},{key:"addTipsy",value:function addTipsy(e){e.tipsy({gravity:"s",offset:8,title:function title(){return this.getAttribute("data-tooltip")}})}},{key:"addIndicatorsTooltips",value:function addIndicatorsTooltips(){var e=this;this.elements.$experimentIndicators.each((function(t,n){return e.addTipsy(jQuery(n))}))}},{key:"onInit",value:function onInit(){var e=this;!function _superPropGet(e,t,n,o){var r=(0,s.default)((0,l.default)(1&o?e.prototype:e),t,n);return 2&o&&"function"==typeof r?function(e){return r.apply(n,e)}:r}(ExperimentsModule,"onInit",this,3)([]),this.experimentsDependency=new c.default({selects:this.elements.$experimentSelects.toArray(),submit:this.elements.$experimentForm.find("#submit").get(0)}),this.experimentsDependency.bindEvents(),this.elements.$experimentIndicators.length&&import("".concat(elementorCommon.config.urls.assets,"lib/tipsy/tipsy.min.js?ver=1.0.0")).then((function(){return e.addIndicatorsTooltips()}))}}])}(elementorModules.ViewModule)},45046:(e,t,n)=>{"use strict";var o=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=o(n(39805)),a=o(n(40989)),i=o(n(15118)),l=o(n(29402)),s=o(n(41621)),u=o(n(87861)),c=o(n(26098));function _isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!e})()}function _superPropGet(e,t,n,o){var r=(0,s.default)((0,l.default)(1&o?e.prototype:e),t,n);return 2&o&&"function"==typeof r?function(e){return r.apply(n,e)}:r}t.default=function(e){function FloatingButtonsHandler(){return(0,r.default)(this,FloatingButtonsHandler),function _callSuper(e,t,n){return t=(0,l.default)(t),(0,i.default)(e,_isNativeReflectConstruct()?Reflect.construct(t,n||[],(0,l.default)(e).constructor):t.apply(e,n))}(this,FloatingButtonsHandler,arguments)}return(0,u.default)(FloatingButtonsHandler,e),(0,a.default)(FloatingButtonsHandler,[{key:"getDefaultSettings",value:function getDefaultSettings(){var e="e-floating-buttons",t={contactPagesTablePage:'a[href="edit.php?post_type='+e+'"]',contactPagesAddNewPage:'a[href="edit.php?post_type=elementor_library&page='+e+'"]'};return{selectors:{addButton:".page-title-action:first",templatesMenuItem:".menu-icon-elementor_library",contactPagesMenuItem:"".concat(t.contactPagesTablePage,", ").concat(t.contactPagesAddNewPage)}}}},{key:"getDefaultElements",value:function getDefaultElements(){var e=this.getSettings("selectors"),t=_superPropGet(FloatingButtonsHandler,"getDefaultElements",this,3)([]);return t.$templatesMenuItem=jQuery(e.templatesMenuItem),t.$contactPagesMenuItem=jQuery(e.contactPagesMenuItem),t}},{key:"onInit",value:function onInit(){var e;_superPropGet(FloatingButtonsHandler,"onInit",this,3)([]);var t=this.getSettings(),n=!!window.location.href.includes(t.paths.contactPagesTablePage),o=!!window.location.href.includes(t.paths.contactPagesTrashPage),r=!!window.location.href.includes(t.paths.contactPagesAddNewPage);null!==(e=elementorAdminConfig.urls)&&void 0!==e&&e.viewContactPageUrl&&this.elements.$templatesMenuItem.find("li.submenu-e-contact a").attr("href",elementorAdminConfig.urls.viewContactPageUrl),(n||o||r)&&(this.highlightTopLevelMenuItem(this.elements.$templatesMenuItem,this.elements.$pagesMenuItemAndLink),this.highlightSubMenuItem(this.elements.$contactPagesMenuItem),jQuery(t.selectors.addButton).attr("href",elementorAdminConfig.urls.addNewLinkUrlContact))}}])}(c.default)},68400:(e,t,n)=>{"use strict";var o=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=o(n(39805)),a=o(n(40989)),i=o(n(15118)),l=o(n(29402)),s=o(n(87861)),u=o(n(45046));function _isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!e})()}t.default=function(e){function _default(){var e;return(0,r.default)(this,_default),e=function _callSuper(e,t,n){return t=(0,l.default)(t),(0,i.default)(e,_isNativeReflectConstruct()?Reflect.construct(t,n||[],(0,l.default)(e).constructor):t.apply(e,n))}(this,_default),elementorCommon.elements.$window.on("elementor/admin/init",(function(){e.runHandler()})),e}return(0,s.default)(_default,e),(0,a.default)(_default,[{key:"runHandler",value:function runHandler(){var e="e-floating-buttons",t={paths:{contactPagesTablePage:"edit.php?post_type="+e,contactPagesAddNewPage:"edit.php?post_type=elementor_library&page="+e,contactPagesTrashPage:"edit.php?post_status=trash&post_type="+e}};new u.default(t)}}])}(elementorModules.Module)},47427:(e,t,n)=>{"use strict";var o=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=o(n(39805)),a=o(n(40989)),i=o(n(15118)),l=o(n(29402)),s=o(n(41621)),u=o(n(87861)),c=o(n(26098));function _isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!e})()}function _superPropGet(e,t,n,o){var r=(0,s.default)((0,l.default)(1&o?e.prototype:e),t,n);return 2&o&&"function"==typeof r?function(e){return r.apply(n,e)}:r}t.default=function(e){function LandingPagesHandler(){return(0,r.default)(this,LandingPagesHandler),function _callSuper(e,t,n){return t=(0,l.default)(t),(0,i.default)(e,_isNativeReflectConstruct()?Reflect.construct(t,n||[],(0,l.default)(e).constructor):t.apply(e,n))}(this,LandingPagesHandler,arguments)}return(0,u.default)(LandingPagesHandler,e),(0,a.default)(LandingPagesHandler,[{key:"getDefaultSettings",value:function getDefaultSettings(){var e="e-landing-page",t={landingPagesTablePage:'a[href="edit.php?post_type='+e+'"]',landingPagesAddNewPage:'a[href="edit.php?post_type=elementor_library&page='+e+'"]'};return{selectors:{addButton:".page-title-action:first",pagesMenuItemAndLink:"#menu-pages, #menu-pages > a",landingPagesMenuItem:"".concat(t.landingPagesTablePage,", ").concat(t.landingPagesAddNewPage),templatesMenuItem:".menu-icon-elementor_library"}}}},{key:"getDefaultElements",value:function getDefaultElements(){var e=this.getSettings("selectors"),t=_superPropGet(LandingPagesHandler,"getDefaultElements",this,3)([]);return t.$landingPagesMenuItem=jQuery(e.landingPagesMenuItem),t.$templatesMenuItem=jQuery(e.templatesMenuItem),t.$pagesMenuItemAndLink=jQuery(e.pagesMenuItemAndLink),t}},{key:"onInit",value:function onInit(){_superPropGet(LandingPagesHandler,"onInit",this,3)([]);var e=this.getSettings(),t=!!window.location.href.includes(e.paths.landingPagesTablePage),n=!!window.location.href.includes(e.paths.landingPagesTrashPage),o=!!window.location.href.includes(e.paths.landingPagesAddNewPage);(t||n||o||e.isLandingPageAdminEdit)&&(this.highlightTopLevelMenuItem(this.elements.$templatesMenuItem,this.elements.$pagesMenuItemAndLink),this.highlightSubMenuItem(this.elements.$landingPagesMenuItem),jQuery(e.selectors.addButton).attr("href",elementorAdminConfig.urls.addNewLandingPageUrl))}}])}(c.default)},72405:(e,t,n)=>{"use strict";var o=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=o(n(39805)),a=o(n(40989)),i=o(n(15118)),l=o(n(29402)),s=o(n(87861)),u=o(n(47427));function _isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!e})()}t.default=function(e){function _default(){var e;return(0,r.default)(this,_default),e=function _callSuper(e,t,n){return t=(0,l.default)(t),(0,i.default)(e,_isNativeReflectConstruct()?Reflect.construct(t,n||[],(0,l.default)(e).constructor):t.apply(e,n))}(this,_default),elementorCommon.elements.$window.on("elementor/admin/init",(function(){e.runHandler()})),e}return(0,s.default)(_default,e),(0,a.default)(_default,[{key:"runHandler",value:function runHandler(){var e,t,n="e-landing-page",o={landingPagesTablePage:"edit.php?post_type="+n,landingPagesAddNewPage:"edit.php?post_type=elementor_library&page="+n,landingPagesTrashPage:"edit.php?post_status=trash&post_type="+n},r={path:null!==(e=elementorAdmin.config.landingPages)&&void 0!==e&&e.landingPagesHasPages?o.landingPagesTablePage:o.landingPagesAddNewPage,isLandingPageAdminEdit:null===(t=elementorAdmin.config.landingPages)||void 0===t?void 0:t.isLandingPageAdminEdit,paths:o};new u.default(r)}}])}(elementorModules.Module)},12470:e=>{"use strict";e.exports=wp.i18n},78113:e=>{e.exports=function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=Array(t);n<t;n++)o[n]=e[n];return o},e.exports.__esModule=!0,e.exports.default=e.exports},70569:e=>{e.exports=function _arrayWithHoles(e){if(Array.isArray(e))return e},e.exports.__esModule=!0,e.exports.default=e.exports},36417:e=>{e.exports=function _assertThisInitialized(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e},e.exports.__esModule=!0,e.exports.default=e.exports},58155:e=>{function asyncGeneratorStep(e,t,n,o,r,a,i){try{var l=e[a](i),s=l.value}catch(e){return void n(e)}l.done?t(s):Promise.resolve(s).then(o,r)}e.exports=function _asyncToGenerator(e){return function(){var t=this,n=arguments;return new Promise((function(o,r){var a=e.apply(t,n);function _next(e){asyncGeneratorStep(a,o,r,_next,_throw,"next",e)}function _throw(e){asyncGeneratorStep(a,o,r,_next,_throw,"throw",e)}_next(void 0)}))}},e.exports.__esModule=!0,e.exports.default=e.exports},39805:e=>{e.exports=function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")},e.exports.__esModule=!0,e.exports.default=e.exports},40989:(e,t,n)=>{var o=n(45498);function _defineProperties(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,o(r.key),r)}}e.exports=function _createClass(e,t,n){return t&&_defineProperties(e.prototype,t),n&&_defineProperties(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e},e.exports.__esModule=!0,e.exports.default=e.exports},85707:(e,t,n)=>{var o=n(45498);e.exports=function _defineProperty(e,t,n){return(t=o(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e},e.exports.__esModule=!0,e.exports.default=e.exports},41621:(e,t,n)=>{var o=n(14718);function _get(){return e.exports=_get="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,n){var r=o(e,t);if(r){var a=Object.getOwnPropertyDescriptor(r,t);return a.get?a.get.call(arguments.length<3?e:n):a.value}},e.exports.__esModule=!0,e.exports.default=e.exports,_get.apply(null,arguments)}e.exports=_get,e.exports.__esModule=!0,e.exports.default=e.exports},29402:e=>{function _getPrototypeOf(t){return e.exports=_getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},e.exports.__esModule=!0,e.exports.default=e.exports,_getPrototypeOf(t)}e.exports=_getPrototypeOf,e.exports.__esModule=!0,e.exports.default=e.exports},87861:(e,t,n)=>{var o=n(91270);e.exports=function _inherits(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&o(e,t)},e.exports.__esModule=!0,e.exports.default=e.exports},96784:e=>{e.exports=function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}},e.exports.__esModule=!0,e.exports.default=e.exports},65474:e=>{e.exports=function _iterableToArrayLimit(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,r,a,i,l=[],s=!0,u=!1;try{if(a=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;s=!1}else for(;!(s=(o=a.call(n)).done)&&(l.push(o.value),l.length!==t);s=!0);}catch(e){u=!0,r=e}finally{try{if(!s&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(u)throw r}}return l}},e.exports.__esModule=!0,e.exports.default=e.exports},11018:e=>{e.exports=function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},e.exports.__esModule=!0,e.exports.default=e.exports},15118:(e,t,n)=>{var o=n(10564).default,r=n(36417);e.exports=function _possibleConstructorReturn(e,t){if(t&&("object"==o(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return r(e)},e.exports.__esModule=!0,e.exports.default=e.exports},53051:(e,t,n)=>{var o=n(10564).default;function _regeneratorRuntime(){"use strict";e.exports=_regeneratorRuntime=function _regeneratorRuntime(){return n},e.exports.__esModule=!0,e.exports.default=e.exports;var t,n={},r=Object.prototype,a=r.hasOwnProperty,i=Object.defineProperty||function(e,t,n){e[t]=n.value},l="function"==typeof Symbol?Symbol:{},s=l.iterator||"@@iterator",u=l.asyncIterator||"@@asyncIterator",c=l.toStringTag||"@@toStringTag";function define(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{define({},"")}catch(t){define=function define(e,t,n){return e[t]=n}}function wrap(e,t,n,o){var r=t&&t.prototype instanceof Generator?t:Generator,a=Object.create(r.prototype),l=new Context(o||[]);return i(a,"_invoke",{value:makeInvokeMethod(e,n,l)}),a}function tryCatch(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}n.wrap=wrap;var d="suspendedStart",f="suspendedYield",m="executing",p="completed",g={};function Generator(){}function GeneratorFunction(){}function GeneratorFunctionPrototype(){}var h={};define(h,s,(function(){return this}));var v=Object.getPrototypeOf,y=v&&v(v(values([])));y&&y!==r&&a.call(y,s)&&(h=y);var _=GeneratorFunctionPrototype.prototype=Generator.prototype=Object.create(h);function defineIteratorMethods(e){["next","throw","return"].forEach((function(t){define(e,t,(function(e){return this._invoke(t,e)}))}))}function AsyncIterator(e,t){function invoke(n,r,i,l){var s=tryCatch(e[n],e,r);if("throw"!==s.type){var u=s.arg,c=u.value;return c&&"object"==o(c)&&a.call(c,"__await")?t.resolve(c.__await).then((function(e){invoke("next",e,i,l)}),(function(e){invoke("throw",e,i,l)})):t.resolve(c).then((function(e){u.value=e,i(u)}),(function(e){return invoke("throw",e,i,l)}))}l(s.arg)}var n;i(this,"_invoke",{value:function value(e,o){function callInvokeWithMethodAndArg(){return new t((function(t,n){invoke(e,o,t,n)}))}return n=n?n.then(callInvokeWithMethodAndArg,callInvokeWithMethodAndArg):callInvokeWithMethodAndArg()}})}function makeInvokeMethod(e,n,o){var r=d;return function(a,i){if(r===m)throw Error("Generator is already running");if(r===p){if("throw"===a)throw i;return{value:t,done:!0}}for(o.method=a,o.arg=i;;){var l=o.delegate;if(l){var s=maybeInvokeDelegate(l,o);if(s){if(s===g)continue;return s}}if("next"===o.method)o.sent=o._sent=o.arg;else if("throw"===o.method){if(r===d)throw r=p,o.arg;o.dispatchException(o.arg)}else"return"===o.method&&o.abrupt("return",o.arg);r=m;var u=tryCatch(e,n,o);if("normal"===u.type){if(r=o.done?p:f,u.arg===g)continue;return{value:u.arg,done:o.done}}"throw"===u.type&&(r=p,o.method="throw",o.arg=u.arg)}}}function maybeInvokeDelegate(e,n){var o=n.method,r=e.iterator[o];if(r===t)return n.delegate=null,"throw"===o&&e.iterator.return&&(n.method="return",n.arg=t,maybeInvokeDelegate(e,n),"throw"===n.method)||"return"!==o&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+o+"' method")),g;var a=tryCatch(r,e.iterator,n.arg);if("throw"===a.type)return n.method="throw",n.arg=a.arg,n.delegate=null,g;var i=a.arg;return i?i.done?(n[e.resultName]=i.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,g):i:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,g)}function pushTryEntry(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function resetTryEntry(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function Context(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(pushTryEntry,this),this.reset(!0)}function values(e){if(e||""===e){var n=e[s];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var r=-1,i=function next(){for(;++r<e.length;)if(a.call(e,r))return next.value=e[r],next.done=!1,next;return next.value=t,next.done=!0,next};return i.next=i}}throw new TypeError(o(e)+" is not iterable")}return GeneratorFunction.prototype=GeneratorFunctionPrototype,i(_,"constructor",{value:GeneratorFunctionPrototype,configurable:!0}),i(GeneratorFunctionPrototype,"constructor",{value:GeneratorFunction,configurable:!0}),GeneratorFunction.displayName=define(GeneratorFunctionPrototype,c,"GeneratorFunction"),n.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===GeneratorFunction||"GeneratorFunction"===(t.displayName||t.name))},n.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,GeneratorFunctionPrototype):(e.__proto__=GeneratorFunctionPrototype,define(e,c,"GeneratorFunction")),e.prototype=Object.create(_),e},n.awrap=function(e){return{__await:e}},defineIteratorMethods(AsyncIterator.prototype),define(AsyncIterator.prototype,u,(function(){return this})),n.AsyncIterator=AsyncIterator,n.async=function(e,t,o,r,a){void 0===a&&(a=Promise);var i=new AsyncIterator(wrap(e,t,o,r),a);return n.isGeneratorFunction(t)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},defineIteratorMethods(_),define(_,c,"Generator"),define(_,s,(function(){return this})),define(_,"toString",(function(){return"[object Generator]"})),n.keys=function(e){var t=Object(e),n=[];for(var o in t)n.push(o);return n.reverse(),function next(){for(;n.length;){var e=n.pop();if(e in t)return next.value=e,next.done=!1,next}return next.done=!0,next}},n.values=values,Context.prototype={constructor:Context,reset:function reset(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(resetTryEntry),!e)for(var n in this)"t"===n.charAt(0)&&a.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function stop(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function dispatchException(e){if(this.done)throw e;var n=this;function handle(o,r){return i.type="throw",i.arg=e,n.next=o,r&&(n.method="next",n.arg=t),!!r}for(var o=this.tryEntries.length-1;o>=0;--o){var r=this.tryEntries[o],i=r.completion;if("root"===r.tryLoc)return handle("end");if(r.tryLoc<=this.prev){var l=a.call(r,"catchLoc"),s=a.call(r,"finallyLoc");if(l&&s){if(this.prev<r.catchLoc)return handle(r.catchLoc,!0);if(this.prev<r.finallyLoc)return handle(r.finallyLoc)}else if(l){if(this.prev<r.catchLoc)return handle(r.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<r.finallyLoc)return handle(r.finallyLoc)}}}},abrupt:function abrupt(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&a.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var r=o;break}}r&&("break"===e||"continue"===e)&&r.tryLoc<=t&&t<=r.finallyLoc&&(r=null);var i=r?r.completion:{};return i.type=e,i.arg=t,r?(this.method="next",this.next=r.finallyLoc,g):this.complete(i)},complete:function complete(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),g},finish:function finish(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),resetTryEntry(n),g}},catch:function _catch(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var o=n.completion;if("throw"===o.type){var r=o.arg;resetTryEntry(n)}return r}}throw Error("illegal catch attempt")},delegateYield:function delegateYield(e,n,o){return this.delegate={iterator:values(e),resultName:n,nextLoc:o},"next"===this.method&&(this.arg=t),g}},n}e.exports=_regeneratorRuntime,e.exports.__esModule=!0,e.exports.default=e.exports},91270:e=>{function _setPrototypeOf(t,n){return e.exports=_setPrototypeOf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},e.exports.__esModule=!0,e.exports.default=e.exports,_setPrototypeOf(t,n)}e.exports=_setPrototypeOf,e.exports.__esModule=!0,e.exports.default=e.exports},18821:(e,t,n)=>{var o=n(70569),r=n(65474),a=n(37744),i=n(11018);e.exports=function _slicedToArray(e,t){return o(e)||r(e,t)||a(e,t)||i()},e.exports.__esModule=!0,e.exports.default=e.exports},14718:(e,t,n)=>{var o=n(29402);e.exports=function _superPropBase(e,t){for(;!{}.hasOwnProperty.call(e,t)&&null!==(e=o(e)););return e},e.exports.__esModule=!0,e.exports.default=e.exports},11327:(e,t,n)=>{var o=n(10564).default;e.exports=function toPrimitive(e,t){if("object"!=o(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=o(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)},e.exports.__esModule=!0,e.exports.default=e.exports},45498:(e,t,n)=>{var o=n(10564).default,r=n(11327);e.exports=function toPropertyKey(e){var t=r(e,"string");return"symbol"==o(t)?t:t+""},e.exports.__esModule=!0,e.exports.default=e.exports},10564:e=>{function _typeof(t){return e.exports=_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports.default=e.exports,_typeof(t)}e.exports=_typeof,e.exports.__esModule=!0,e.exports.default=e.exports},37744:(e,t,n)=>{var o=n(78113);e.exports=function _unsupportedIterableToArray(e,t){if(e){if("string"==typeof e)return o(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?o(e,t):void 0}},e.exports.__esModule=!0,e.exports.default=e.exports},61790:(e,t,n)=>{var o=n(53051)();e.exports=o;try{regeneratorRuntime=o}catch(e){"object"==typeof globalThis?globalThis.regeneratorRuntime=o:Function("r","regeneratorRuntime = r")(o)}}},t={};function __webpack_require__(n){var o=t[n];if(void 0!==o)return o.exports;var r=t[n]={exports:{}};return e[n](r,r.exports,__webpack_require__),r.exports}(()=>{"use strict";var e,t,n=__webpack_require__(12470).__,o=__webpack_require__(96784),r=o(__webpack_require__(61790)),a=o(__webpack_require__(58155)),i=o(__webpack_require__(18821)),l=o(__webpack_require__(72405)),s=o(__webpack_require__(41095)),u=o(__webpack_require__(75115)),c=o(__webpack_require__(54799)),d=o(__webpack_require__(14100)),f=o(__webpack_require__(95067)),m=__webpack_require__(67631),p=o(__webpack_require__(68400));e=jQuery,t=elementorModules.ViewModule.extend({maintenanceMode:null,config:elementorAdminConfig,getDefaultElements:function getDefaultElements(){var t={$switchMode:e("#elementor-switch-mode"),$goToEditLink:e("#elementor-go-to-edit-page-link"),$switchModeInput:e("#elementor-switch-mode-input"),$switchModeButton:e("#elementor-switch-mode-button"),$elementorLoader:e(".elementor-loader"),$builderEditor:e("#elementor-editor"),$importButton:e("#elementor-import-template-trigger"),$importNowButton:e("#e-import-template-action"),$importArea:e("#elementor-import-template-area"),$importForm:e("#elementor-import-template-form"),$importFormFileInput:e('#elementor-import-template-form input[type="file"]'),$settingsForm:e("#elementor-settings-form"),$settingsTabsWrapper:e("#elementor-settings-tabs-wrapper"),$menuGetHelpLink:e('a[href="admin.php?page=go_knowledge_base_site"]'),$menuGoProLink:e('a[href="admin.php?page=go_elementor_pro"]'),$reMigrateGlobalsButton:e(".elementor-re-migrate-globals-button")};return t.$settingsFormPages=t.$settingsForm.find(".elementor-settings-form-page"),t.$activeSettingsPage=t.$settingsFormPages.filter(".elementor-active"),t.$settingsTabs=t.$settingsTabsWrapper.children(),t.$activeSettingsTab=t.$settingsTabs.filter(".nav-tab-active"),t},toggleStatus:function toggleStatus(){var e=this.isElementorMode();elementorCommon.elements.$body.toggleClass("elementor-editor-active",e).toggleClass("elementor-editor-inactive",!e)},bindEvents:function bindEvents(){var t=this;t.elements.$switchModeButton.on("click",(function(o){if(o.preventDefault(),t.isElementorMode())elementorCommon.dialogsManager.createWidget("confirm",{message:n("Please note that you are switching to WordPress default editor. Your current layout, design and content might break.","elementor"),headerMessage:n("Back to WordPress Editor","elementor"),strings:{confirm:n("Continue","elementor"),cancel:n("Cancel","elementor")},defaultOption:"confirm",onConfirm:function onConfirm(){t.elements.$switchModeInput.val(""),t.toggleStatus()}}).show();else{t.elements.$switchModeInput.val(!0);var r=e("#title");r.val()||r.val("Elementor #"+e("#post_ID").val()),wp.autosave&&wp.autosave.server.triggerSave(),t.animateLoader(),e(document).on("heartbeat-tick.autosave",(function(){elementorCommon.elements.$window.off("beforeunload.edit-post"),location.href=t.elements.$goToEditLink.attr("href")})),t.toggleStatus()}})),t.elements.$goToEditLink.on("click",(function(){t.animateLoader()})),e(".e-notice--dismissible").on("click",".e-notice__dismiss, .e-notice-dismiss",(function(t){t.preventDefault();var n=e(this).closest(".e-notice--dismissible");e.post(ajaxurl,{action:"elementor_set_admin_notice_viewed",notice_id:n.data("notice_id"),_wpnonce:n.data("nonce")}),n.fadeTo(100,0,(function(){n.slideUp(100,(function(){n.remove()}))}))})),e('.e-notice--cta.e-notice--dismissible[data-notice_id="plugin_image_optimization"] a.e-button--cta').on("click",(function(){elementorCommon.ajax.addRequest("elementor_image_optimization_campaign",{data:{source:"io-wp-media-library-install"}})})),e('.e-a-apps .e-a-item[data-plugin="image-optimization/image-optimization.php"] a.e-btn').on("click",(function(){elementorCommon.ajax.addRequest("elementor_image_optimization_campaign",{data:{source:"io-esetting-addons-install"}})})),e('.e-notice--cta.e-notice--dismissible[data-notice_id="site_mailer_promotion"] a.e-button--cta').on("click",(function(){var t=e(this).closest(".e-notice").hasClass("sm-notice-wc");elementorCommon.ajax.addRequest("elementor_core_site_mailer_campaign",{data:{source:t?"sm-core-woo-install":"sm-core-form-install"}})})),e("#elementor-clear-cache-button").on("click",(function(t){t.preventDefault();var n=e(this);n.removeClass("success").addClass("loading"),e.post(ajaxurl,{action:"elementor_clear_cache",_nonce:n.data("nonce")}).done((function(){n.removeClass("loading").addClass("success")}))})),e("#elementor-library-sync-button").on("click",(function(t){t.preventDefault();var n=e(this);n.removeClass("success").addClass("loading"),e.post(ajaxurl,{action:"elementor_reset_library",_nonce:n.data("nonce")}).done((function(){n.removeClass("loading").addClass("success")}))})),e("#elementor-recreate-kit-button").on("click",(function(t){t.preventDefault();var n=e(this);n.removeClass("success error").addClass("loading").next(".e-recreate-kit-error-message").remove(),e.post(ajaxurl,{action:"elementor_recreate_kit",_nonce:n.data("nonce")}).done((function(){n.removeClass("loading").addClass("success")})).fail((function(e){var t,o=e.responseJSON;n.removeClass("loading").addClass("error"),null!==(t=o.data)&&void 0!==t&&t.message&&n.after('<div class="e-recreate-kit-error-message">'.concat(o.data.message,"</div>"))}))})),e("#elementor-replace-url-button").on("click",(function(t){t.preventDefault();var n=e(this),o=n.parents("tr"),r=o.find('[name="from"]'),a=o.find('[name="to"]');n.removeClass("success").addClass("loading"),e.post(ajaxurl,{action:"elementor_replace_url",from:r.val(),to:a.val(),_nonce:n.data("nonce")}).done((function(e){n.removeClass("loading"),e.success&&n.addClass("success"),elementorCommon.dialogsManager.createWidget("alert",{message:e.data}).show()}))})),e("#elementor_upgrade_fa_button").on("click",(function(t){t.preventDefault();var o=e(this);o.addClass("loading"),elementorCommon.dialogsManager.createWidget("confirm",{id:"confirm_fa_migration_admin_modal",message:n("I understand that by upgrading to Font Awesome 5,","elementor")+"<br>"+n("I acknowledge that some changes may affect my website and that this action cannot be undone.","elementor"),headerMessage:n("Font Awesome 5 Migration","elementor"),strings:{confirm:n("Continue","elementor"),cancel:n("Cancel","elementor")},defaultOption:"confirm",onConfirm:function onConfirm(){o.removeClass("error").addClass("loading");var t=o.data(),n=t._nonce,r=t.action,a=t.redirectUrl;e.post(ajaxurl,{action:r,_nonce:n}).done((function(t){o.removeClass("loading").addClass("success");var n=document.createElement("p");n.appendChild(document.createTextNode(t.data.message)),e("#elementor_upgrade_fa_button").parent().append(n),a?location.href=decodeURIComponent(a):history.go(-1)})).fail((function(){o.removeClass("loading").addClass("error")}))},onCancel:function onCancel(){o.removeClass("loading").addClass("error")}}).show()})),t.elements.$settingsTabs.on({click:function click(e){e.preventDefault(),e.currentTarget.focus()},focus:function focus(){var e=location.href.replace(/#.*/,"");history.pushState({},"",e+this.hash),t.goToSettingsTabFromHash()}}),e("select.elementor-rollback-select").on("change",(function(){var t=e(this),n=t.next(".elementor-rollback-button"),o=n.data("placeholder-text"),r=n.data("placeholder-url");n.html(o.replace("{VERSION}",t.val())),n.attr("href",r.replace("VERSION",t.val()))})).trigger("change"),e(".elementor-rollback-button").on("click",(function(t){t.preventDefault();var o=e(this);elementorCommon.dialogsManager.createWidget("confirm",{headerMessage:n("Rollback to Previous Version","elementor"),message:n("Are you sure you want to reinstall previous version?","elementor"),strings:{confirm:n("Continue","elementor"),cancel:n("Cancel","elementor")},onConfirm:function onConfirm(){o.addClass("loading"),location.href=o.attr("href")}}).show()})),t.elements.$reMigrateGlobalsButton.on("click",(function(t){t.preventDefault();var o=e(t.currentTarget);elementorCommon.dialogsManager.createWidget("confirm",{headerMessage:n("Migrate to v3.0","elementor"),message:n("Please note that this process will revert all changes made to Global Colors and Fonts since upgrading to v3.x.","elementor"),strings:{confirm:n("Continue","elementor"),cancel:n("Cancel","elementor")},onConfirm:function onConfirm(){o.removeClass("success").addClass("loading"),elementorCommon.ajax.addRequest("re_migrate_globals",{success:function success(){return o.removeClass("loading").addClass("success")}})}}).show()})),e(".elementor_google_font select").on("change",(function(){e(".elementor_font_display").toggle("1"===e(this).val())})).trigger("change")},onInit:function onInit(){elementorModules.ViewModule.prototype.onInit.apply(this,arguments),this.initTemplatesImport(),this.initMaintenanceMode(),this.goToSettingsTabFromHash(),this.openLinksInNewTab(),this.addUserAgentClasses(),this.roleManager.init(),elementorCommon.config.experimentalFeatures["landing-pages"]&&new l.default,elementorCommon.config.experimentalFeatures.container&&new p.default,this.templateControls=new f.default,new s.default},addUserAgentClasses:function addUserAgentClasses(){var e=document.querySelector("body");Object.entries(u.default).forEach((function(t){var n=(0,i.default)(t,2),o=n[0];n[1]&&e.classList.add("e--ua-"+o)}))},openLinksInNewTab:function openLinksInNewTab(){[this.elements.$menuGetHelpLink,this.elements.$menuGoProLink].forEach((function(e){e.length&&e.attr("target","_blank")}))},initTemplatesImport:function initTemplatesImport(){var t,o;if((elementorAdminConfig.user.is_administrator||null!==(t=null===(o=elementorAdminConfig.user.restrictions)||void 0===o?void 0:o.includes("json-upload"))&&void 0!==t&&t)&&elementorCommon.elements.$body.hasClass("post-type-elementor_library")){var i=this,l=i.elements.$importForm,s=i.elements.$importButton,u=i.elements.$importArea,c=i.elements.$importNowButton,f=i.elements.$importFormFileInput;i.elements.$formAnchor=e(".wp-header-end"),e("#wpbody-content").find(".page-title-action").last().after(s),i.elements.$formAnchor.after(u),s.on("click",(function(){e("#elementor-import-template-area").toggle()}));var p={jsonUploadWarning:{shown:!1},enableUnfilteredFiles:{shown:!1}},g=c[0].value;l.on("submit",function(){var e=(0,a.default)(r.default.mark((function _callee(e){var t,o;return r.default.wrap((function _callee$(r){for(;;)switch(r.prev=r.next){case 0:if(c[0].disabled=!0,c[0].value=n("Importing...","elementor"),p.jsonUploadWarning.shown){r.next=16;break}return e.preventDefault(),r.prev=4,r.next=7,(0,m.showJsonUploadWarningMessageIfNeeded)({IntroductionClass:window.elementorModules.admin.utils.Introduction,introductionMap:window.elementorAdmin.config.user.introduction,waitForSetViewed:!0});case 7:p.jsonUploadWarning.shown=!0,l.trigger("submit"),r.next=15;break;case 11:r.prev=11,r.t0=r.catch(4),c[0].disabled=!1,c[0].value=g;case 15:return r.abrupt("return");case 16:if(t=f[0].files.length,o=elementorCommon.config.filesUpload.unfilteredFiles,!t||o||p.enableUnfilteredFiles.shown){r.next=23;break}return e.preventDefault(),d.default.getUnfilteredFilesNotEnabledImportTemplateDialog((function(){p.enableUnfilteredFiles.shown=!0,l.trigger("submit")})).show(),r.abrupt("return");case 23:p.jsonUploadWarning.shown=!1,p.enableUnfilteredFiles.shown=!1;case 25:case"end":return r.stop()}}),_callee,null,[[4,11]])})));return function(t){return e.apply(this,arguments)}}())}},initMaintenanceMode:function initMaintenanceMode(){var e=__webpack_require__(1977);this.maintenanceMode=new e},isElementorMode:function isElementorMode(){return!!this.elements.$switchModeInput.val()},animateLoader:function animateLoader(){this.elements.$goToEditLink.addClass("elementor-animate")},goToSettingsTabFromHash:function goToSettingsTabFromHash(){var e=location.hash.slice(1);e&&this.goToSettingsTab(e)},goToSettingsTab:function goToSettingsTab(e){var t=this.elements.$settingsFormPages;if(t.length){var n=t.filter("#"+e);this.elements.$activeSettingsPage.removeClass("elementor-active"),this.elements.$activeSettingsTab.removeClass("nav-tab-active");var o=this.elements.$settingsTabs.filter("#elementor-settings-"+e);n.addClass("elementor-active"),o.addClass("nav-tab-active"),this.elements.$settingsForm.attr("action","options.php#"+e),this.elements.$activeSettingsPage=n,this.elements.$activeSettingsTab=o}},translate:function translate(e,t){return elementorCommon.translate(e,null,t,this.config.i18n)},roleManager:{selectors:{body:"elementor-role-manager",row:".elementor-role-row",label:".elementor-role-label",excludedIndicator:".elementor-role-excluded-indicator",excludedField:'input[name="elementor_exclude_user_roles[]"]',controlsContainer:".elementor-role-controls",toggleHandle:".elementor-role-toggle",arrowUp:"dashicons-arrow-up",arrowDown:"dashicons-arrow-down"},toggle:function toggle(e){var t=this,n=e.closest(t.selectors.row),o=n.find(t.selectors.toggleHandle).find(".dashicons"),r=n.find(t.selectors.controlsContainer);r.toggleClass("hidden"),r.hasClass("hidden")?o.removeClass(t.selectors.arrowUp).addClass(t.selectors.arrowDown):o.removeClass(t.selectors.arrowDown).addClass(t.selectors.arrowUp),t.updateLabel(n)},updateLabel:function updateLabel(e){var t=this,n=e.find(t.selectors.excludedIndicator),o=e.find(t.selectors.excludedField).is(":checked");o?n.html(n.data("excluded-label")):n.html(""),t.setAdvancedState(e,o)},setAdvancedState:function setAdvancedState(t,n){t.find('input[type="checkbox"]').not(this.selectors.excludedField).each((function(t,o){e(o).prop("disabled",n)}))},bind:function bind(){var t=this;e(document).on("click",t.selectors.label+","+t.selectors.toggleHandle,(function(n){n.stopPropagation(),n.preventDefault(),t.toggle(e(this))})).on("change",t.selectors.excludedField,(function(){t.updateLabel(e(this).closest(t.selectors.row))}))},init:function init(){var t=this;e('body[class*="'+t.selectors.body+'"]').length&&(t.bind(),e(t.selectors.row).each((function(n,o){t.updateLabel(e(o))})))}}}),e((function(){window.elementorAdmin=new t,c.default.dispatch(elementorCommon.elements.$window,"elementor/admin/init")}))})()})();