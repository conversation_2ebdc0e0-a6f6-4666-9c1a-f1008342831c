/*! elementor - v3.29.0 - 04-06-2025 */
/*! For license information please see ai-layout.min.js.LICENSE.txt */
(()=>{var e={18791:(e,t,n)=>{"use strict";var o=n(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;_interopRequireWildcard(n(41594));var a=_interopRequireWildcard(n(75206)),i=n(7470);function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?n:t})(e)}function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=o(e)&&"function"!=typeof e)return{default:e};var n=_getRequireWildcardCache(t);if(n&&n.has(e))return n.get(e);var a={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var l in e)if("default"!==l&&{}.hasOwnProperty.call(e,l)){var u=i?Object.getOwnPropertyDescriptor(e,l):null;u&&(u.get||u.set)?Object.defineProperty(a,l,u):a[l]=e[l]}return a.default=e,n&&n.set(e,a),a}t.default={render:function render(e,t){var n;try{var o=(0,i.createRoot)(t);o.render(e),n=function unmountFunction(){o.unmount()}}catch(o){a.render(e,t),n=function unmountFunction(){a.unmountComponentAtNode(t)}}return{unmount:n}}}},47389:(e,t,n)=>{"use strict";var o=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=o(n(39805)),i=o(n(40989)),l=o(n(15118)),u=o(n(29402)),c=o(n(87861)),s=o(n(85707)),d=n(12470),f=n(36833),p=n(91258);function _isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!e})()}t.default=function(e){function AiLayoutBehavior(){var e;(0,a.default)(this,AiLayoutBehavior);for(var t=arguments.length,n=new Array(t),o=0;o<t;o++)n[o]=arguments[o];return e=function _callSuper(e,t,n){return t=(0,u.default)(t),(0,l.default)(e,_isNativeReflectConstruct()?Reflect.construct(t,n||[],(0,u.default)(e).constructor):t.apply(e,n))}(this,AiLayoutBehavior,[].concat(n)),(0,s.default)(e,"previewContainer",null),e}return(0,c.default)(AiLayoutBehavior,e),(0,i.default)(AiLayoutBehavior,[{key:"ui",value:function ui(){return{aiButton:".e-ai-layout-button",addTemplateButton:".elementor-add-template-button"}}},{key:"events",value:function events(){return{"click @ui.aiButton":"onAiButtonClick"}}},{key:"onAiButtonClick",value:function onAiButtonClick(e){e.stopPropagation(),window.elementorAiCurrentContext=this.getOption("context"),(0,f.renderLayoutApp)({parentContainer:elementor.getPreviewContainer(),mode:p.MODE_LAYOUT,at:this.view.getOption("at"),onInsert:this.onInsert.bind(this),onRenderApp:function onRenderApp(e){e.previewContainer.init()},onGenerate:function onGenerate(e){e.previewContainer.reset()}})}},{key:"hideDropArea",value:function hideDropArea(){this.view.onCloseButtonClick()}},{key:"onInsert",value:function onInsert(e){this.hideDropArea(),(0,f.importToEditor)({parentContainer:elementor.getPreviewContainer(),at:this.view.getOption("at"),template:e,historyTitle:(0,d.__)("AI Layout","elementor")})}},{key:"onRender",value:function onRender(){var e=jQuery("<button>",{type:"button",class:"e-ai-layout-button elementor-add-section-area-button e-button-primary",title:(0,d.__)("Build with AI","elementor"),"aria-label":(0,d.__)("Build with AI","elementor")});e.html('\n\t\t\t<div class="e-ai-layout-button--sparkle"></div>\n\t\t\t<div class="e-ai-layout-button--sparkle"></div>\n\t\t\t<div class="e-ai-layout-button--sparkle"></div>\n\t\t\t<div class="e-ai-layout-button--sparkle"></div>\n\t\t\t<div class="e-ai-layout-button--sparkle"></div>\n\t\t\t<div class="e-ai-layout-button--sparkle"></div>\n\t\t\t<div class="e-ai-layout-button--sparkle"></div>\n\t\t\t<i class="eicon-ai" aria-hidden="true"></i>\n\t\t'),this.ui.addTemplateButton.after(e)}}])}(Marionette.Behavior)},95034:(e,t,n)=>{"use strict";var o=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.uploadImage=t.toggleFavoriteHistoryItem=t.setStatusFeedback=t.setGetStarted=t.getUserInformation=t.getTextToImageGeneration=t.getRemoteFrontendConfig=t.getRemoteConfig=t.getProductImageUnification=t.getLayoutPromptEnhanced=t.getImageToImageUpscale=t.getImageToImageReplaceBackground=t.getImageToImageRemoveText=t.getImageToImageRemoveBackground=t.getImageToImageOutPainting=t.getImageToImageMaskGeneration=t.getImageToImageMaskCleanup=t.getImageToImageIsolateObjects=t.getImageToImageGeneration=t.getImagePromptEnhanced=t.getHistory=t.getFeaturedImage=t.getExcerpt=t.getEditText=t.getCustomCode=t.getCustomCSS=t.getCompletionText=t.getAnimation=t.generateLayout=t.deleteHistoryItem=void 0;var a=o(n(85707));function ownKeys(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(n),!0).forEach((function(t){(0,a.default)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ownKeys(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var i=function request(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],o=arguments.length>3?arguments[3]:void 0;return Object.keys(t).length&&(window.elementorAiCurrentContext?t.context=window.elementorAiCurrentContext:t.context=window.elementorWpAiCurrentContext),new Promise((function(a,i){var l=elementorCommon.ajax.addRequest(e,{success:a,error:i,data:t,unique_id:t.unique_id},n);o&&l.jqXhr&&o.addEventListener("abort",l.jqXhr.abort)}))};t.getUserInformation=function getUserInformation(e){return i("ai_get_user_information",void 0,e)},t.getRemoteConfig=function getRemoteConfig(){return i("ai_get_remote_config")},t.getRemoteFrontendConfig=function getRemoteFrontendConfig(e,t){return i("ai_get_remote_frontend_config",{payload:e},t)},t.getCompletionText=function getCompletionText(e){return i("ai_get_completion_text",{payload:e})},t.getExcerpt=function getExcerpt(e){return i("ai_get_excerpt",{payload:e})},t.getFeaturedImage=function getFeaturedImage(e){return i("ai_get_featured_image",{payload:e})},t.getEditText=function getEditText(e){return i("ai_get_edit_text",{payload:e})},t.getCustomCode=function getCustomCode(e){return i("ai_get_custom_code",{payload:e})},t.getCustomCSS=function getCustomCSS(e){return i("ai_get_custom_css",{payload:e})},t.setGetStarted=function setGetStarted(){return i("ai_set_get_started")},t.setStatusFeedback=function setStatusFeedback(e){return i("ai_set_status_feedback",{response_id:e},!0)},t.getTextToImageGeneration=function getTextToImageGeneration(e){return i("ai_get_text_to_image",{payload:e})},t.getImageToImageGeneration=function getImageToImageGeneration(e){return i("ai_get_image_to_image",{payload:e})},t.getImageToImageMaskCleanup=function getImageToImageMaskCleanup(e){return i("ai_get_image_to_image_mask_cleanup",{payload:e})},t.getImageToImageMaskGeneration=function getImageToImageMaskGeneration(e){return i("ai_get_image_to_image_mask",{payload:e})},t.getImageToImageOutPainting=function getImageToImageOutPainting(e){return i("ai_get_image_to_image_outpainting",{payload:e})},t.getImageToImageUpscale=function getImageToImageUpscale(e){return i("ai_get_image_to_image_upscale",{payload:e})},t.getImageToImageRemoveBackground=function getImageToImageRemoveBackground(e){return i("ai_get_image_to_image_remove_background",{payload:e})},t.getImageToImageIsolateObjects=function getImageToImageIsolateObjects(e){return i("ai_get_image_to_image_isolate_objects",{payload:e})},t.getImageToImageReplaceBackground=function getImageToImageReplaceBackground(e){return i("ai_get_image_to_image_replace_background",{payload:e})},t.getImageToImageRemoveText=function getImageToImageRemoveText(e){return i("ai_get_image_to_image_remove_text",{image:e})},t.getImagePromptEnhanced=function getImagePromptEnhanced(e){return i("ai_get_image_prompt_enhancer",{prompt:e})},t.getProductImageUnification=function getProductImageUnification(e,t){return i("ai_get_product_image_unification",{payload:e},t)},t.getAnimation=function getAnimation(e){return i("ai_get_animation",{payload:e})},t.uploadImage=function uploadImage(e){return i("ai_upload_image",_objectSpread(_objectSpread({},e),{},{editor_post_id:e.image.editor_post_id,unique_id:e.image.unique_id}))},t.generateLayout=function generateLayout(e,t){return i("ai_generate_layout",e,!0,t)},t.getLayoutPromptEnhanced=function getLayoutPromptEnhanced(e,t){return i("ai_get_layout_prompt_enhancer",{prompt:e,enhance_type:t})},t.getHistory=function getHistory(e,t,n){return i("ai_get_history",{type:e,page:t,limit:n})},t.deleteHistoryItem=function deleteHistoryItem(e){return i("ai_delete_history_item",{id:e})},t.toggleFavoriteHistoryItem=function toggleFavoriteHistoryItem(e){return i("ai_toggle_favorite_history_item",{id:e})}},86353:(e,t,n)=>{"use strict";var o=n(96784),a=n(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.AlertDialog=void 0;var i=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=a(e)&&"function"!=typeof e)return{default:e};var n=_getRequireWildcardCache(t);if(n&&n.has(e))return n.get(e);var o={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var l in e)if("default"!==l&&{}.hasOwnProperty.call(e,l)){var u=i?Object.getOwnPropertyDescriptor(e,l):null;u&&(u.get||u.set)?Object.defineProperty(o,l,u):o[l]=e[l]}return o.default=e,n&&n.set(e,o),o}(n(41594)),l=o(n(18821)),u=n(86956),c=n(12470),s=o(n(62688));function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?n:t})(e)}(t.AlertDialog=function AlertDialog(e){var t=(0,i.useState)(!0),n=(0,l.default)(t,2),o=n[0],a=n[1];return o?i.default.createElement(u.Dialog,{open:!0,maxWidth:"lg"},i.default.createElement(u.DialogContent,{sx:{padding:0}},i.default.createElement(u.Typography,{sx:{textAlign:"center",padding:3}},e.message),i.default.createElement(u.Stack,{alignItems:"center",spacing:2,marginBottom:2},i.default.createElement(u.Button,{variant:"contained",type:"button",color:"primary",onClick:function onClick(){var t;a(!1),null===(t=e.onClose)||void 0===t||t.call(e)}},(0,c.__)("Close","elementor"))))):null}).propTypes={message:s.default.string.isRequired,onClose:s.default.func}},99476:(e,t,n)=>{"use strict";var o=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=o(n(41594)),i=o(n(78304)),l=n(86956),u=n(12470),c=o(n(62688)),s=n(44048),d=(0,l.styled)((function ElementorLogo(e){return a.default.createElement(l.SvgIcon,(0,i.default)({viewBox:"0 0 32 32"},e),a.default.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M2.69648 24.8891C0.938383 22.2579 0 19.1645 0 16C0 11.7566 1.68571 7.68687 4.68629 4.68629C7.68687 1.68571 11.7566 0 16 0C19.1645 0 22.2579 0.938383 24.8891 2.69648C27.5203 4.45459 29.5711 6.95344 30.7821 9.87706C31.9931 12.8007 32.3099 16.0177 31.6926 19.1214C31.0752 22.2251 29.5514 25.0761 27.3137 27.3137C25.0761 29.5514 22.2251 31.0752 19.1214 31.6926C16.0177 32.3099 12.8007 31.9931 9.87706 30.7821C6.95344 29.5711 4.45459 27.5203 2.69648 24.8891ZM12.0006 9.33281H9.33437V22.6665H12.0006V9.33281ZM22.6657 9.33281H14.6669V11.9991H22.6657V9.33281ZM22.6657 14.6654H14.6669V17.3316H22.6657V14.6654ZM22.6657 20.0003H14.6669V22.6665H22.6657V20.0003Z"}))}))((function(e){var t=e.theme;return{width:t.spacing(3),height:t.spacing(3),"& path":{fill:t.palette.text.primary}}})),f=function DialogHeader(e){return a.default.createElement(l.AppBar,{sx:{fontWeight:"normal"},color:"transparent",position:"relative"},a.default.createElement(l.Toolbar,{variant:"dense"},a.default.createElement(d,{sx:{mr:1}}),a.default.createElement(l.Typography,{component:"span",variant:"subtitle2",sx:{fontWeight:"bold",textTransform:"uppercase"}},(0,u.__)("AI","elementor")),a.default.createElement(l.Chip,{label:(0,u.__)("Beta","elementor"),color:"default",size:"small",sx:{ml:1}}),a.default.createElement(l.Stack,{direction:"row",spacing:1,alignItems:"center",sx:{ml:"auto"}},e.children,a.default.createElement(l.IconButton,{size:"small","aria-label":"close",onClick:e.onClose,sx:{"&.MuiButtonBase-root":{mr:-1}}},a.default.createElement(s.XIcon,null)))))};f.propTypes={onClose:c.default.func.isRequired,children:c.default.oneOfType([c.default.arrayOf(c.default.node),c.default.node])};t.default=f},73319:(e,t,n)=>{"use strict";var o=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=o(n(41594)),i=o(n(78304)),l=o(n(85707)),u=o(n(40453)),c=n(86956),s=o(n(62688)),d=["sx","BoxProps"];function ownKeys(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(n),!0).forEach((function(t){(0,l.default)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ownKeys(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var f=function Loader(e){var t=e.sx,n=void 0===t?{}:t,o=e.BoxProps,l=void 0===o?{}:o,s=(0,u.default)(e,d);return a.default.createElement(c.Box,(0,i.default)({width:"100%",display:"flex",alignItems:"center"},l,{sx:_objectSpread({px:1.5,minHeight:function minHeight(e){return e.spacing(5)}},l.sx||{})}),a.default.createElement(c.LinearProgress,(0,i.default)({color:"secondary"},s,{sx:_objectSpread({width:"100%"},n)})))};f.propTypes={sx:s.default.object,BoxProps:s.default.object};t.default=f},48968:(e,t,n)=>{"use strict";var o=n(96784),a=n(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=a(e)&&"function"!=typeof e)return{default:e};var n=_getRequireWildcardCache(t);if(n&&n.has(e))return n.get(e);var o={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var l in e)if("default"!==l&&{}.hasOwnProperty.call(e,l)){var u=i?Object.getOwnPropertyDescriptor(e,l):null;u&&(u.get||u.set)?Object.defineProperty(o,l,u):o[l]=e[l]}return o.default=e,n&&n.set(e,o),o}(n(41594)),l=o(n(78304)),u=o(n(85707)),c=o(n(18821)),s=n(86956),d=o(n(62688)),f=o(n(38230)),p=o(n(99476));function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?n:t})(e)}function ownKeys(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(n),!0).forEach((function(t){(0,u.default)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ownKeys(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var m=function DraggablePaper(e){var t=(0,i.useState)({x:0,y:0}),n=(0,c.default)(t,2),o=n[0],a=n[1],u=(0,i.useRef)(null),d=(0,i.useRef)(null),p=function handlePositionBoundaries(){clearTimeout(d.current),d.current=setTimeout((function(){var e,t=null===(e=u.current)||void 0===e?void 0:e.getBoundingClientRect().top;t<0&&a((function(e){return _objectSpread(_objectSpread({},e),{},{y:e.y-t})}))}),50)};return(0,i.useEffect)((function(){var e=new ResizeObserver(p);return e.observe(u.current),function(){e.disconnect()}}),[]),i.default.createElement(f.default,{position:o,onDrag:function onDrag(e,t){var n=t.x,o=t.y;return a({x:n,y:o})},handle:".MuiAppBar-root",cancel:'[class*="MuiDialogContent-root"]',bounds:"parent"},i.default.createElement(s.Paper,(0,l.default)({},e,{ref:u})))},g=function PromptDialog(e){return i.default.createElement(s.Dialog,(0,l.default)({scroll:"paper",open:!0,fullWidth:!0,hideBackdrop:!0,PaperComponent:m,disableScrollLock:!0,sx:{"& .MuiDialog-container":{alignItems:"flex-start",mt:"18vh"}},PaperProps:{sx:{m:0,maxHeight:"76vh"}}},e),e.children)};g.propTypes={onClose:d.default.func.isRequired,children:d.default.node,maxWidth:d.default.oneOf(["xs","sm","md","lg","xl",!1])},g.Header=p.default,g.Content=s.DialogContent;t.default=g},43091:(e,t,n)=>{"use strict";var o=n(12470).sprintf,a=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=a(n(41594)),l=a(n(78304)),u=a(n(40453)),c=n(86956),s=n(12470),d=a(n(62688)),f=["error","onRetry","actionPosition"],p=function PromptErrorMessage(e){var t=e.error,n=e.onRetry,a=void 0===n?function(){}:n,d=e.actionPosition,p=void 0===d?"default":d,m=(0,u.default)(e,f);function getQuotaReachedTrailMessage(e){return e?{text:i.default.createElement(c.AlertTitle,null,o((0,s.__)("You've used all AI credits for %s.","elementor"),e.toLowerCase())),description:(0,s.__)("Upgrade now to keep using this feature. You still have credits for other AI features (Text, Code, Images, Containers, etc.)","elementor"),buttonText:(0,s.__)("Upgrade now","elementor"),buttonAction:function buttonAction(){return window.open("https://go.elementor.com/ai-popup-purchase-limit-reached/","_blank")}}:{text:i.default.createElement(c.AlertTitle,null,(0,s.__)("It's time to upgrade.","elementor")),description:(0,s.__)("Enjoy the free trial? Upgrade now for unlimited access to built-in image, text and custom code generators.","elementor"),buttonText:(0,s.__)("Upgrade","elementor"),buttonAction:function buttonAction(){return window.open("https://go.elementor.com/ai-popup-purchase-limit-reached/","_blank")}}}var g=function getErrorMessage(){var e,n=t.message||t,o=null===(e=t.extra_data)||void 0===e?void 0:e.featureName,l={default:{text:i.default.createElement(c.AlertTitle,null,(0,s.__)("There was a glitch.","elementor")),description:(0,s.__)("Wait a moment and give it another go, or try tweaking the prompt.","elementor"),buttonText:(0,s.__)("Try again","elementor"),buttonAction:a},service_outage_internal:{text:i.default.createElement(c.AlertTitle,null,(0,s.__)("There was a glitch.","elementor")),description:(0,s.__)("Wait a moment and give it another go.","elementor"),buttonText:(0,s.__)("Try again","elementor"),buttonAction:a},invalid_connect_data:{text:i.default.createElement(c.AlertTitle,null,(0,s.__)("There was a glitch.","elementor")),description:i.default.createElement(i.default.Fragment,null,(0,s.__)("Try exiting Elementor and sign in again.","elementor")," ",i.default.createElement("a",{href:"https://elementor.com/help/disconnecting-reconnecting-your-elementor-account/",target:"_blank",rel:"noreferrer"},(0,s.__)("Show me how","elementor"))),buttonText:(0,s.__)("Reconnect","elementor"),buttonAction:function buttonAction(){return window.open(window.ElementorAiConfig.connect_url)}},not_connected:{text:i.default.createElement(c.AlertTitle,null,(0,s.__)("You aren't connected to Elementor AI.","elementor")),description:(0,s.__)("Elementor AI is just a few clicks away. Connect your account to instantly create texts and custom code.","elementor"),buttonText:(0,s.__)("Connect","elementor"),buttonAction:function buttonAction(){return window.open(window.ElementorAiConfig.connect_url)}},quota_reached_trail:getQuotaReachedTrailMessage(o),quota_reached_subscription:{text:i.default.createElement(c.AlertTitle,null,(0,s.__)("Looks like you're out of credits.","elementor")),description:(0,s.__)("Ready to take it to the next level?","elementor"),buttonText:(0,s.__)("Upgrade now","elementor"),buttonAction:function buttonAction(){return window.open("https://go.elementor.com/ai-popup-purchase-limit-reached/","_blank")}},rate_limit_network:{text:i.default.createElement(c.AlertTitle,null,(0,s.__)("Whoa! Slow down there.","elementor")),description:(0,s.__)("We can’t process that many requests so fast. Try again in 15 minutes.","elementor")},invalid_prompts:{text:i.default.createElement(c.AlertTitle,null,(0,s.__)("We were unable to generate that prompt.","elementor")),description:(0,s.__)("Seems like the prompt contains words that could generate harmful content. Write a different prompt to continue.","elementor")},service_unavailable:{text:i.default.createElement(c.AlertTitle,null,(0,s.__)("There was a glitch.","elementor")),description:(0,s.__)("Wait a moment and give it another go, or try tweaking the prompt.","elementor"),buttonText:(0,s.__)("Try again","elementor"),buttonAction:a},request_timeout_error:{text:i.default.createElement(c.AlertTitle,null,(0,s.__)("There was a glitch.","elementor")),description:(0,s.__)("Wait a moment and give it another go, or try tweaking the prompt.","elementor"),buttonText:(0,s.__)("Try again","elementor"),buttonAction:a},invalid_token:{text:i.default.createElement(c.AlertTitle,null,(0,s.__)("Try again","elementor")),description:(0,s.__)("Try exiting Elementor and sign in again.","elementor"),buttonText:(0,s.__)("Reconnect","elementor"),buttonAction:a},file_too_large:{text:i.default.createElement(c.AlertTitle,null,(0,s.__)("The file is too large.","elementor")),description:(0,s.__)("Please upload a file that is less than 4MB.","elementor")},image_resolution_maximum_exceeded:{text:i.default.createElement(c.AlertTitle,null,(0,s.__)("The image resolution exceeds the maximum allowed size.","elementor")),description:(0,s.__)("Please upload a file with dimensions less than 2048x2048 pixels.","elementor")},external_service_unavailable:{text:i.default.createElement(c.AlertTitle,null,(0,s.__)("Temporary external service issue","elementor")),description:(0,s.__)("It seems that one of our partner services is temporarily unavailable. Please try again in a few minutes.","elementor"),buttonText:(0,s.__)("Try Again","elementor"),buttonAction:a}};return l[n]||l.default}(),h=(null==g?void 0:g.buttonText)&&i.default.createElement(c.Button,{color:"inherit",size:"small",variant:"outlined",onClick:g.buttonAction},g.buttonText);return i.default.createElement(c.Alert,(0,l.default)({severity:g.severity||"error",action:"default"===p&&h},m),g.text,g.description,"bottom"===p&&i.default.createElement(c.Box,{sx:{mt:1}},h))};p.propTypes={error:d.default.oneOfType([d.default.object,d.default.string]),onRetry:d.default.func,actionPosition:d.default.oneOf(["default","bottom"])};t.default=p},53497:(e,t,n)=>{"use strict";var o=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=o(n(41594)),i=n(86956),l=n(12470),u=o(n(62688)),c=function PromptLibraryLink(e){return a.default.createElement(i.Typography,{variant:"body2",color:"text.secondary"},(0,l.__)("For more suggestions, explore our")," ",a.default.createElement(i.Link,{href:e.libraryLink,className:"elementor-clickable",target:"_blank"},(0,l.__)("prompt library")))};c.propTypes={libraryLink:u.default.string};t.default=c},35121:(e,t,n)=>{"use strict";var o=n(96784),a=n(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=a(e)&&"function"!=typeof e)return{default:e};var n=_getRequireWildcardCache(t);if(n&&n.has(e))return n.get(e);var o={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var l in e)if("default"!==l&&{}.hasOwnProperty.call(e,l)){var u=i?Object.getOwnPropertyDescriptor(e,l):null;u&&(u.get||u.set)?Object.defineProperty(o,l,u):o[l]=e[l]}return o.default=e,n&&n.set(e,o),o}(n(41594)),l=o(n(18821)),u=n(86956),c=n(12470),s=o(n(62688)),d=n(44048);function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?n:t})(e)}var f=(0,u.styled)(u.Paper)((function(e){var t=e.theme;return{position:"relative",'[data-popper-placement="top"] &':{marginBottom:t.spacing(2.5)},'[data-popper-placement="bottom"] &':{marginTop:t.spacing(2.5)},padding:t.spacing(3),boxShadow:t.shadows[4],zIndex:"9999"}})),p=(0,u.styled)(u.Box)((function(e){var t=e.theme;return{width:t.spacing(5),height:t.spacing(2.5),position:"absolute",overflow:"hidden",left:"50% !important",transform:"translateX(-50%) rotate(var(--rotate, 0deg)) !important",'[data-popper-placement="top"] &':{top:"100%"},'[data-popper-placement="bottom"] &':{"--rotate":"180deg",top:"calc(".concat(t.spacing(2.5)," * -1)")},"&::after":{backgroundColor:t.palette.background.paper,content:'""',display:"block",position:"absolute",width:t.spacing(2.5),height:t.spacing(2.5),top:0,left:"50%",transform:"translateX(-50%) translateY(-50%) rotate(45deg)",boxShadow:"1px 1px 5px 0px rgba(0, 0, 0, 0.2)",backgroundImage:"linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05))"}}})),m=[(0,c.__)("Get spot-on suggestions from AI Copilot and AI Context with appropriate designs, layouts, and content for your business.","elementor"),(0,c.__)("Generate professional texts about any topic, in any tone.","elementor"),(0,c.__)("Effortlessly create or enhance stunning images and bring your ideas to life.","elementor"),(0,c.__)("Unleash infinite possibilities with the custom code generator.","elementor"),(0,c.__)("Access 30-days of AI History with the AI Starter plan and 90-days with the Power plan.","elementor")],g=(0,u.styled)(u.Chip)((function(){return{"& .MuiChip-label":{lineHeight:1.5},"& .MuiSvgIcon-root.MuiChip-icon":{fontSize:"1.25rem"}}})),h=function UpgradeChip(e){var t=e.hasSubscription,n=void 0!==t&&t,o=e.usagePercentage,a=void 0===o?0:o,s=(0,i.useState)(!1),h=(0,l.default)(s,2),y=h[0],v=h[1],_=(0,i.useRef)(null),b=(0,i.useRef)(null),w="https://go.elementor.com/ai-popup-purchase-dropdown/";n&&(w=a>=100?"https://go.elementor.com/ai-popup-upgrade-limit-reached/":"https://go.elementor.com/ai-popup-upgrade-limit-reached-80-percent/");var C=n?(0,c.__)("Upgrade Elementor AI","elementor"):(0,c.__)("Get Elementor AI","elementor");return i.default.createElement(u.Box,{component:"span","aria-owns":y?"e-ai-upgrade-popover":void 0,"aria-haspopup":"true",onMouseEnter:function showPopover(){return v(!0)},onMouseLeave:function hidePopover(){return v(!1)},ref:_,display:"flex",alignItems:"center"},i.default.createElement(g,{color:"promotion",label:(0,c.__)("Upgrade","elementor"),icon:i.default.createElement(d.AIIcon,null),size:"small"}),i.default.createElement(u.Popper,{open:y,anchorEl:_.current,sx:{zIndex:"170001",maxWidth:300},modifiers:[{name:"arrow",enabled:!0,options:{element:b.current}}]},i.default.createElement(f,null,i.default.createElement(p,{ref:b}),i.default.createElement(u.Typography,{variant:"h5",color:"text.primary"},(0,c.__)("Unlimited access to Elementor AI","elementor")),i.default.createElement(u.List,{sx:{mb:1}},m.map((function(e,t){return i.default.createElement(u.ListItem,{key:t,disableGutters:!0,sx:{alignItems:"flex-start"}},i.default.createElement(u.ListItemIcon,null,i.default.createElement(d.CheckedCircleIcon,null)),i.default.createElement(u.ListItemText,{sx:{m:0}},i.default.createElement(u.Typography,{variant:"body2"},e)))}))),i.default.createElement(u.Button,{variant:"contained",color:"promotion",size:"small",href:w,target:"_blank",startIcon:i.default.createElement(d.AIIcon,null),sx:{"&:hover":{color:"promotion.contrastText"}}},C))))};t.default=h;h.propTypes={hasSubscription:s.default.bool,usagePercentage:s.default.number}},90291:(e,t,n)=>{"use strict";var o=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=t.VoicePromotionAlert=void 0;var a=o(n(85707)),i=o(n(41594)),l=n(86956),u=o(n(50923)),c=o(n(80366)),s=o(n(62688)),d=n(12470);function ownKeys(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(n),!0).forEach((function(t){(0,a.default)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ownKeys(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var f=t.VoicePromotionAlert=function VoicePromotionAlert(e){var t=(0,c.default)(e.introductionKey),n=t.isViewed,o=t.markAsViewed;return n?null:i.default.createElement(l.Box,{sx:_objectSpread({mt:2},e.sx),alignItems:"top"},i.default.createElement(l.Alert,{severity:"info",variant:"standard",icon:i.default.createElement(u.default,{sx:{alignSelf:"flex-start"}}),onClose:o},(0,d.__)("Get improved results from AI by adding personal context.","elementor"),i.default.createElement(l.Link,{onClick:function onClick(){return $e.route("panel/global/menu")},className:"elementor-clickable",style:{textDecoration:"none"},color:"info.main",href:"#"},(0,d.__)("Let’s do it","elementor"))))};f.propTypes={sx:s.default.object,introductionKey:s.default.string};t.default=f},4508:(e,t,n)=>{"use strict";var o=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=o(n(41594)),i=o(n(78304)),l=o(n(85707)),u=o(n(40453)),c=n(86956),s=o(n(62688)),d=o(n(99476)),f=["sx"];function ownKeys(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(n),!0).forEach((function(t){(0,l.default)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ownKeys(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var p=function WizardDialog(e){return a.default.createElement(c.Dialog,{open:!0,onClose:e.onClose,fullWidth:!0,hideBackdrop:!0,maxWidth:"lg",PaperProps:{sx:{height:"88vh"}},sx:{zIndex:9999}},e.children)};p.propTypes={onClose:s.default.func.isRequired,children:s.default.node.isRequired};var m=function WizardDialogContent(e){var t=e.sx,n=void 0===t?{}:t,o=(0,u.default)(e,f);return a.default.createElement(c.DialogContent,(0,i.default)({},o,{sx:_objectSpread({display:"flex",flexDirection:"column",justifyContent:"center"},n)}))};m.propTypes={sx:s.default.object},p.Header=d.default,p.Content=m;t.default=p},40128:(e,t,n)=>{"use strict";var o=n(96784),a=n(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.default=t.RequestIdsProvider=void 0,t.generateIds=function generateIds(e){var t;e.id=s().toString(),null!==(t=e.elements)&&void 0!==t&&t.length&&e.elements.map((function(e){return generateIds(e)}));return e},t.useRequestIds=t.getUniqueId=void 0;var i=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=a(e)&&"function"!=typeof e)return{default:e};var n=_getRequireWildcardCache(t);if(n&&n.has(e))return n.get(e);var o={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var l in e)if("default"!==l&&{}.hasOwnProperty.call(e,l)){var u=i?Object.getOwnPropertyDescriptor(e,l):null;u&&(u.get||u.set)?Object.defineProperty(o,l,u):o[l]=e[l]}return o.default=e,n&&n.set(e,o),o}(n(41594)),l=o(n(18821)),u=o(n(62688));function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?n:t})(e)}var c=(0,i.createContext)({}),s=(t.useRequestIds=function useRequestIds(){var e=(0,i.useContext)(c);if(!e)throw new Error("useRequestIds must be used within a RequestIdsProvider");return e},t.getUniqueId=function getUniqueId(e){return e+"-"+Math.random().toString(16).substr(2,7)});window.EDITOR_SESSION_ID=window.EDITOR_SESSION_ID||s("editor-session"),(t.RequestIdsProvider=function RequestIdsProvider(e){var t=(0,i.useRef)(window.EDITOR_SESSION_ID),n=(0,i.useRef)(""),o=(0,i.useRef)(""),a=(0,i.useRef)(""),u=(0,i.useRef)("");n.current=s("session");var d=(0,i.useState)(0),f=(0,l.default)(d,2),p=f[0],m=f[1];return i.default.createElement(c.Provider,{value:{editorSessionId:t,sessionId:n,generateId:o,batchId:a,requestId:u,setGenerate:function setGenerate(){return o.current=s("generate"),o},setBatch:function setBatch(){return a.current=s("batch"),a},setRequest:function setRequest(){return u.current=s("request"),u},usagePercentage:p,updateUsagePercentage:function updateUsagePercentage(e){m(e)}}},e.children)}).propTypes={children:u.default.node.isRequired};t.default=c},80366:(e,t,n)=>{"use strict";var o=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=function useIntroduction(e){var t,n,o,l=window.elementor?null===(t=window.elementor.config)||void 0===t?void 0:t.user:null===(n=window.elementorAdmin)||void 0===n||null===(n=n.config)||void 0===n?void 0:n.user,u=(0,i.useState)(!(null==l||null===(o=l.introduction)||void 0===o||!o[e])),c=(0,a.default)(u,2),s=c[0],d=c[1];return{isViewed:s,markAsViewed:function markAsViewed(){return e?new Promise((function(t,n){s&&n(),d(!0),elementorCommon.ajax.addRequest("introduction_viewed",{data:{introductionKey:e},error:function error(){d(!1),n()},success:function success(){d(!0),null!=l&&l.introduction&&(l.introduction[e]=!0),t()}})})):Promise.reject()}}};var a=o(n(18821)),i=n(41594)},34161:(e,t,n)=>{"use strict";var o=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=n(95034),i=o(n(48812)),l=n(91258),u=new Map([["media",a.getImagePromptEnhanced],["layout",a.getLayoutPromptEnhanced]]);t.default=function usePromptEnhancer(e,t){var n=(0,l.useConfig)().mode,o=(0,i.default)((function(){return function getResult(e,t,n){if(!u.has(t))throw new Error("Invalid prompt type: ".concat(t));return u.get(t)(e,n)}(e,t,n)}),e),a=o.data,c=o.isLoading;return{enhance:o.send,isEnhancing:c,enhancedPrompt:null==a?void 0:a.result}}},48812:(e,t,n)=>{"use strict";var o=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=o(n(61790)),i=o(n(85707)),l=o(n(58155)),u=o(n(18821)),c=o(n(40453)),s=n(41594),d=n(95034),f=n(40128),p=["text","response_id","usage","images"];function ownKeys(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(n),!0).forEach((function(t){(0,i.default)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ownKeys(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var m=function normalizeResponse(e){var t=e.text,n=e.response_id,o=e.usage,a=e.images,i=(0,c.default)(e,p),l=o?o.quota-o.usedQuota:0,u={result:t||a,responseId:n,credits:Math.max(l,0),usagePercentage:null==o?void 0:o.usagePercentage};return i.base_template_id&&(u.baseTemplateId=i.base_template_id),u.type=i.template_type,u};t.default=function usePrompt(e,t){var n=(0,s.useState)(!1),o=(0,u.default)(n,2),i=o[0],c=o[1],p=(0,s.useState)(""),g=(0,u.default)(p,2),h=g[0],y=g[1],v=(0,s.useState)(t),_=(0,u.default)(v,2),b=_[0],w=_[1],C=(0,f.useRequestIds)(),x=C.updateUsagePercentage,P=C.usagePercentage,E=(0,s.useRef)(function(){var e=(0,l.default)(a.default.mark((function _callee(e){return a.default.wrap((function _callee$(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt("return",e);case 1:case"end":return t.stop()}}),_callee)})));return function(t){return e.apply(this,arguments)}}()),O=(0,s.useRef)((function(){}));(0,s.useEffect)((function(){var e=null==b?void 0:b.usagePercentage;e&&e!==P&&x(e)}),[b,P,x]);var S=(0,f.useRequestIds)(),T=S.setRequest,R=S.editorSessionId,j=S.sessionId,I=S.generateId,M=S.batchId;E.current=(0,s.useCallback)(function(){var t=(0,l.default)(a.default.mark((function _callee2(t){return a.default.wrap((function _callee2$(n){for(;;)switch(n.prev=n.next){case 0:return n.abrupt("return",new Promise((function(n,o){y(""),c(!0);var a=T(),i={editorSessionId:R.current,sessionId:j.current,generateId:I.current,batchId:M.current,requestId:a.current};t=_objectSpread(_objectSpread({},t),{},{requestIds:i}),e(t).then((function(e){var t=m(e);w(t),n(t)})).catch((function(e){var t=(null==e?void 0:e.responseText)||e;y(t),o(t)})).finally((function(){return c(!1)}))})));case 1:case"end":return n.stop()}}),_callee2)})));return function(e){return t.apply(this,arguments)}}(),[M,R,e,I,j,T]),O.current=(0,s.useCallback)((function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:b;return e.responseId&&(0,d.setStatusFeedback)(e.responseId)}),[b]);return{isLoading:i,error:h,data:b,setResult:function setResult(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=_objectSpread({},b);n.result=e,t&&(n.responseId=t),w(n)},reset:function reset(){w((function(e){return{credits:e.credits,result:"",responseId:""}})),y(""),c(!1)},send:E.current,sendUsageData:O.current}}},77043:(e,t,n)=>{"use strict";var o=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.useTimeout=void 0;var a=o(n(18821)),i=n(41594);t.useTimeout=function useTimeout(e){var t=(0,i.useState)(!1),n=(0,a.default)(t,2),o=n[0],l=n[1],u=(0,i.useRef)(null);return(0,i.useEffect)((function(){return u.current=setTimeout((function(){l(!0)}),e),function(){clearTimeout(u.current)}}),[e]),[o,function turnOffTimeout(){clearTimeout(u.current),l(!1)}]}},24954:(e,t,n)=>{"use strict";var o=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=o(n(61790)),i=o(n(85707)),l=o(n(58155)),u=o(n(18821)),c=n(41594),s=n(95034),d=o(n(62688));function ownKeys(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(n),!0).forEach((function(t){(0,i.default)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ownKeys(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var f=function useUserInfo(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=(0,c.useState)(!1),n=(0,u.default)(t,2),o=n[0],i=n[1],d=(0,c.useState)(!1),f=(0,u.default)(d,2),p=f[0],m=f[1],g=(0,c.useState)({is_connected:!1,is_get_started:!1,connect_url:"",usage:{hasAiSubscription:!1,quota:0,usedQuota:0}}),h=(0,u.default)(g,2),y=h[0],v=h[1],_=y.usage.quota-y.usage.usedQuota,b=y.usage.quota?y.usage.usedQuota/y.usage.quota*100:0,w=function(){var t=(0,l.default)(a.default.mark((function _callee(){var t;return a.default.wrap((function _callee$(n){for(;;)switch(n.prev=n.next){case 0:return m(!0),n.next=3,(0,s.getUserInformation)(e);case 3:t=n.sent,v((function(e){return _objectSpread(_objectSpread({},e),t)})),i(!0),m(!1);case 7:case"end":return n.stop()}}),_callee)})));return function fetchData(){return t.apply(this,arguments)}}();return o||p||w(),{isLoading:p,isLoaded:o,isConnected:y.is_connected,isGetStarted:y.is_get_started,connectUrl:y.connect_url,builderUrl:y.usage.builderUrl,hasSubscription:y.usage.hasAiSubscription,credits:_<0?0:_,usagePercentage:Math.round(b),fetchData:w}};f.propTypes={immediately:d.default.bool};t.default=f},31593:(e,t,n)=>{"use strict";var o=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=o(n(78304)),i=o(n(41594)),l=n(86956),u=i.default.forwardRef((function(e,t){return i.default.createElement(l.SvgIcon,(0,a.default)({viewBox:"0 0 24 24"},e,{ref:t}),i.default.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M9.53033 7.46967C9.82322 7.76256 9.82322 8.23744 9.53033 8.53033L6.81066 11.25H19C19.4142 11.25 19.75 11.5858 19.75 12C19.75 12.4142 19.4142 12.75 19 12.75H6.81066L9.53033 15.4697C9.82322 15.7626 9.82322 16.2374 9.53033 16.5303C9.23744 16.8232 8.76256 16.8232 8.46967 16.5303L4.46967 12.5303C4.17678 12.2374 4.17678 11.7626 4.46967 11.4697L8.46967 7.46967C8.76256 7.17678 9.23744 7.17678 9.53033 7.46967Z"}))}));t.default=u},50923:(e,t,n)=>{"use strict";var o=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=o(n(78304)),i=o(n(41594)),l=n(86956),u=i.default.forwardRef((function(e,t){return i.default.createElement(l.SvgIcon,(0,a.default)({},e,{ref:t}),i.default.createElement("svg",{width:"22",height:"22",viewBox:"0 0 22 22",fill:"none",xmlns:"http://www.w3.org/2000/svg"},i.default.createElement("g",{clipPath:"url(#clip0_10743_8902)"},i.default.createElement("path",{d:"M2.75 10.0833H3.66667M11 2.75V3.66667M18.3333 10.0833H19.25M5.13333 5.13333L5.775 5.775M16.8667 5.13333L16.225 5.775",stroke:"#2563EB",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),i.default.createElement("path",{d:"M9.16675 16.041C8.70841 15.1243 6.91205 13.2842 6.62523 12.366C6.3384 11.4477 6.34775 10.4626 6.65195 9.54997C6.95615 8.63738 7.53978 7.84362 8.32016 7.28116C9.10054 6.71869 10.0381 6.41602 11.0001 6.41602C11.962 6.41602 12.8996 6.71869 13.68 7.28116C14.4604 7.84362 15.044 8.63738 15.3482 9.54997C15.6524 10.4626 15.6618 11.4477 15.3749 12.366C15.0881 13.2842 13.2917 15.1243 12.8334 16.041C12.8334 16.041 12.7597 17.3762 12.8334 17.8743C12.8334 18.3606 12.6403 18.8269 12.2964 19.1707C11.9526 19.5145 11.4863 19.7077 11.0001 19.7077C10.5139 19.7077 10.0475 19.5145 9.70372 19.1707C9.3599 18.8269 9.16675 18.3606 9.16675 17.8743C9.2405 17.3762 9.16675 16.041 9.16675 16.041Z",stroke:"#2563EB",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),i.default.createElement("path",{d:"M10.0833 16.5H11.9166",stroke:"#2563EB",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})),i.default.createElement("defs",null,i.default.createElement("clipPath",{id:"clip0_10743_8902"},i.default.createElement("rect",{width:"22",height:"22",fill:"white"})))))}));t.default=u},65141:(e,t,n)=>{"use strict";var o=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=o(n(78304)),i=o(n(41594)),l=n(86956),u=i.default.forwardRef((function(e,t){return i.default.createElement(l.SvgIcon,(0,a.default)({viewBox:"0 0 24 24"},e,{ref:t}),i.default.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M16.6667 0.208496C17.0534 0.208496 17.4244 0.362142 17.6979 0.635632C17.9714 0.909123 18.125 1.28006 18.125 1.66683V11.6668C18.125 12.0536 17.9714 12.4245 17.6979 12.698C17.4244 12.9715 17.0534 13.1252 16.6667 13.1252H14.7917V16.6668C14.7917 17.0536 14.638 17.4245 14.3645 17.698C14.091 17.9715 13.7201 18.1252 13.3333 18.1252H3.33333C2.94656 18.1252 2.57563 17.9715 2.30214 17.698C2.02865 17.4245 1.875 17.0536 1.875 16.6668V6.66683C1.875 6.28005 2.02865 5.90912 2.30214 5.63563C2.57563 5.36214 2.94656 5.2085 3.33333 5.2085H5.20833V1.66683C5.20833 1.28005 5.36198 0.909122 5.63547 0.635632C5.90896 0.362142 6.27989 0.208496 6.66667 0.208496H16.6667ZM6.66667 1.4585C6.61141 1.4585 6.55842 1.48045 6.51935 1.51952C6.48028 1.55859 6.45833 1.61158 6.45833 1.66683V3.54183H8.54167V1.4585H6.66667ZM3.125 9.79183V16.6668C3.125 16.7221 3.14695 16.7751 3.18602 16.8141C3.22509 16.8532 3.27808 16.8752 3.33333 16.8752H13.3333C13.3886 16.8752 13.4416 16.8532 13.4806 16.8141C13.5197 16.7751 13.5417 16.7221 13.5417 16.6668V13.1252H6.66667C6.27989 13.1252 5.90896 12.9715 5.63547 12.698C5.36198 12.4245 5.20833 12.0536 5.20833 11.6668V9.79183H3.125ZM5.20833 8.54183H3.125V6.66683C3.125 6.61158 3.14695 6.55859 3.18602 6.51952C3.22509 6.48045 3.27808 6.4585 3.33333 6.4585H5.20833V8.54183ZM6.45833 11.6668C6.45833 11.7221 6.48028 11.7751 6.51935 11.8141C6.55842 11.8532 6.61141 11.8752 6.66667 11.8752H16.6667C16.7219 11.8752 16.7749 11.8532 16.814 11.8141C16.853 11.7751 16.875 11.7221 16.875 11.6668V4.79183H6.45833V11.6668ZM9.79167 1.4585V3.54183H16.875V1.66683C16.875 1.61157 16.853 1.55858 16.814 1.51952C16.7749 1.48045 16.7219 1.4585 16.6667 1.4585H9.79167Z"}))}));t.default=u},51066:(e,t,n)=>{"use strict";var o=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=o(n(78304)),i=o(n(41594)),l=n(86956),u=i.default.forwardRef((function(e,t){return i.default.createElement(l.SvgIcon,(0,a.default)({viewBox:"0 0 24 24"},e,{ref:t}),i.default.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M13.9697 4.96967C14.6408 4.29858 15.5509 3.92157 16.5 3.92157C17.4491 3.92157 18.3592 4.29858 19.0303 4.96967C19.7014 5.64075 20.0784 6.55094 20.0784 7.5C20.0784 8.44905 19.7014 9.35924 19.0303 10.0303L8.53033 20.5303C8.38968 20.671 8.19891 20.75 8 20.75H4C3.58579 20.75 3.25 20.4142 3.25 20V16C3.25 15.8011 3.32902 15.6103 3.46967 15.4697L13.9697 4.96967ZM16.5 5.42157C15.9488 5.42157 15.4201 5.64055 15.0303 6.03033L4.75 16.3107V19.25H7.68934L17.9697 8.96967C18.3595 8.57989 18.5784 8.05123 18.5784 7.5C18.5784 6.94876 18.3595 6.42011 17.9697 6.03033C17.5799 5.64055 17.0512 5.42157 16.5 5.42157Z"}),i.default.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12.9697 5.96967C13.2626 5.67677 13.7374 5.67677 14.0303 5.96967L18.0303 9.96967C18.3232 10.2626 18.3232 10.7374 18.0303 11.0303C17.7374 11.3232 17.2626 11.3232 16.9697 11.0303L12.9697 7.03033C12.6768 6.73743 12.6768 6.26256 12.9697 5.96967Z"}))}));t.default=u},53952:(e,t,n)=>{"use strict";var o=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=o(n(78304)),i=o(n(41594)),l=n(86956),u=i.default.forwardRef((function(e,t){return i.default.createElement(l.SvgIcon,(0,a.default)({viewBox:"0 0 24 24"},e,{ref:t}),i.default.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M4 3.25H8C8.41421 3.25 8.75 3.58579 8.75 4C8.75 4.41421 8.41421 4.75 8 4.75H5.81066L10.5303 9.46967C10.8232 9.76256 10.8232 10.2374 10.5303 10.5303C10.2374 10.8232 9.76256 10.8232 9.46967 10.5303L4.75 5.81066V8C4.75 8.41421 4.41421 8.75 4 8.75C3.58579 8.75 3.25 8.41421 3.25 8V4C3.25 3.58579 3.58579 3.25 4 3.25ZM13.4697 13.4697C13.7626 13.1768 14.2374 13.1768 14.5303 13.4697L19.25 18.1893V16C19.25 15.5858 19.5858 15.25 20 15.25C20.4142 15.25 20.75 15.5858 20.75 16V20C20.75 20.4142 20.4142 20.75 20 20.75H16C15.5858 20.75 15.25 20.4142 15.25 20C15.25 19.5858 15.5858 19.25 16 19.25H18.1893L13.4697 14.5303C13.1768 14.2374 13.1768 13.7626 13.4697 13.4697Z"}))}));t.default=u},21995:(e,t,n)=>{"use strict";var o=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=o(n(78304)),i=o(n(41594)),l=n(86956),u=i.default.forwardRef((function(e,t){return i.default.createElement(l.SvgIcon,(0,a.default)({viewBox:"0 0 24 24"},e,{ref:t}),i.default.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M7.8125 11.9996C7.29473 11.9996 6.875 12.4473 6.875 12.9996V18.9996C6.875 19.5519 7.29473 19.9996 7.8125 19.9996H17.1875C17.7053 19.9996 18.125 19.5519 18.125 18.9996V12.9996C18.125 12.4473 17.7053 11.9996 17.1875 11.9996H7.8125ZM5 12.9996C5 11.3428 6.2592 9.99963 7.8125 9.99963H17.1875C18.7408 9.99963 20 11.3428 20 12.9996V18.9996C20 20.6565 18.7408 21.9996 17.1875 21.9996H7.8125C6.2592 21.9996 5 20.6565 5 18.9996V12.9996Z"}),i.default.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12.5 3.90527C11.7044 3.90527 10.9413 4.22134 10.3787 4.78395C9.81607 5.34656 9.5 6.10962 9.5 6.90527V10.9053C9.5 11.4576 9.05228 11.9053 8.5 11.9053C7.94772 11.9053 7.5 11.4576 7.5 10.9053V6.90527C7.5 5.57919 8.02678 4.30742 8.96447 3.36974C9.90215 2.43206 11.1739 1.90527 12.5 1.90527C13.8261 1.90527 15.0979 2.43206 16.0355 3.36974C16.9732 4.30742 17.5 5.57919 17.5 6.90527V10.9053C17.5 11.4576 17.0523 11.9053 16.5 11.9053C15.9477 11.9053 15.5 11.4576 15.5 10.9053V6.90527C15.5 6.10962 15.1839 5.34656 14.6213 4.78395C14.0587 4.22134 13.2956 3.90527 12.5 3.90527Z"}),i.default.createElement("path",{d:"M6 12H19V20H6V12Z"}))}));t.default=u},33724:(e,t,n)=>{"use strict";var o=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=o(n(78304)),i=o(n(41594)),l=n(86956),u=i.default.forwardRef((function(e,t){return i.default.createElement(l.SvgIcon,(0,a.default)({viewBox:"0 0 24 24"},e,{ref:t}),i.default.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M3.46967 3.46967C3.76256 3.17678 4.23744 3.17678 4.53033 3.46967L9.25 8.18934V6C9.25 5.58579 9.58579 5.25 10 5.25C10.4142 5.25 10.75 5.58579 10.75 6V10C10.75 10.4142 10.4142 10.75 10 10.75H6C5.58579 10.75 5.25 10.4142 5.25 10C5.25 9.58579 5.58579 9.25 6 9.25H8.18934L3.46967 4.53033C3.17678 4.23744 3.17678 3.76256 3.46967 3.46967ZM14 13.25H18C18.4142 13.25 18.75 13.5858 18.75 14C18.75 14.4142 18.4142 14.75 18 14.75H15.8107L20.5303 19.4697C20.8232 19.7626 20.8232 20.2374 20.5303 20.5303C20.2374 20.8232 19.7626 20.8232 19.4697 20.5303L14.75 15.8107V18C14.75 18.4142 14.4142 18.75 14 18.75C13.5858 18.75 13.25 18.4142 13.25 18V14C13.25 13.5858 13.5858 13.25 14 13.25Z"}))}));t.default=u},96793:(e,t,n)=>{"use strict";var o=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=o(n(78304)),i=o(n(41594)),l=n(86956),u=i.default.forwardRef((function(e,t){return i.default.createElement(l.SvgIcon,(0,a.default)({viewBox:"0 0 24 24"},e,{ref:t}),i.default.createElement("path",{d:"M12 2.69231C6.8595 2.69231 2.69231 6.8595 2.69231 12C2.69231 17.1405 6.8595 21.3077 12 21.3077C17.1405 21.3077 21.3077 17.1405 21.3077 12C21.3077 6.8595 17.1405 2.69231 12 2.69231ZM1 12C1 5.92487 5.92487 1 12 1C18.0751 1 23 5.92487 23 12C23 18.0751 18.0751 23 12 23C5.92487 23 1 18.0751 1 12ZM12 7.76923C12.4673 7.76923 12.8462 8.14807 12.8462 8.61538V11.1538H15.3846C15.8519 11.1538 16.2308 11.5327 16.2308 12C16.2308 12.4673 15.8519 12.8462 15.3846 12.8462H12.8462V15.3846C12.8462 15.8519 12.4673 16.2308 12 16.2308C11.5327 16.2308 11.1538 15.8519 11.1538 15.3846V12.8462H8.61538C8.14807 12.8462 7.76923 12.4673 7.76923 12C7.76923 11.5327 8.14807 11.1538 8.61538 11.1538H11.1538V8.61538C11.1538 8.14807 11.5327 7.76923 12 7.76923Z"}))}));t.default=u},33057:(e,t,n)=>{"use strict";var o=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=o(n(78304)),i=o(n(41594)),l=n(86956),u=i.default.forwardRef((function(e,t){return i.default.createElement(l.SvgIcon,(0,a.default)({viewBox:"0 0 24 24"},e,{ref:t}),i.default.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M7.55012 4.45178C9.23098 3.48072 11.1845 3.08925 13.1097 3.33767C15.035 3.58609 16.8251 4.46061 18.2045 5.82653C19.5838 7.19245 20.4757 8.97399 20.743 10.8967C20.8 11.307 20.5136 11.6858 20.1033 11.7428C19.6931 11.7998 19.3142 11.5135 19.2572 11.1032C19.0353 9.50635 18.2945 8.02677 17.149 6.89236C16.0035 5.75795 14.5167 5.03165 12.9178 4.82534C11.3189 4.61902 9.69644 4.94414 8.30047 5.75061C7.24361 6.36117 6.36093 7.22198 5.72541 8.24995H8.00009C8.41431 8.24995 8.75009 8.58574 8.75009 8.99995C8.75009 9.41417 8.41431 9.74995 8.00009 9.74995H4.51686C4.5055 9.75021 4.49412 9.75021 4.48272 9.74995H4.00009C3.58588 9.74995 3.25009 9.41417 3.25009 8.99995V4.99995C3.25009 4.58574 3.58588 4.24995 4.00009 4.24995C4.41431 4.24995 4.75009 4.58574 4.75009 4.99995V7.00691C5.48358 5.96916 6.43655 5.0951 7.55012 4.45178Z"}),i.default.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M3.89686 12.2571C4.30713 12.2001 4.68594 12.4864 4.74295 12.8967C4.96487 14.4936 5.70565 15.9731 6.85119 17.1075C7.99673 18.242 9.48347 18.9683 11.0824 19.1746C12.6813 19.3809 14.3037 19.0558 15.6997 18.2493C16.7566 17.6387 17.6393 16.7779 18.2748 15.75H16.0001C15.5859 15.75 15.2501 15.4142 15.2501 15C15.2501 14.5857 15.5859 14.25 16.0001 14.25H19.4833C19.4947 14.2497 19.5061 14.2497 19.5175 14.25H20.0001C20.4143 14.25 20.7501 14.5857 20.7501 15V19C20.7501 19.4142 20.4143 19.75 20.0001 19.75C19.5859 19.75 19.2501 19.4142 19.2501 19V16.993C18.5166 18.0307 17.5636 18.9048 16.4501 19.5481C14.7692 20.5192 12.8157 20.9107 10.8904 20.6622C8.9652 20.4138 7.17504 19.5393 5.79572 18.1734C4.4164 16.8074 3.52443 15.0259 3.25723 13.1032C3.20022 12.6929 3.48658 12.3141 3.89686 12.2571Z"}))}));t.default=u},53532:(e,t,n)=>{"use strict";var o=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=o(n(78304)),i=o(n(41594)),l=n(86956),u=i.default.forwardRef((function(e,t){return i.default.createElement(l.SvgIcon,(0,a.default)({viewBox:"0 0 24 24"},e,{ref:t}),i.default.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M9 2.25C9.41421 2.25 9.75 2.58579 9.75 3C9.75 3.33152 9.8817 3.64946 10.1161 3.88388C10.3505 4.1183 10.6685 4.25 11 4.25C11.4142 4.25 11.75 4.58579 11.75 5C11.75 5.41421 11.4142 5.75 11 5.75C10.6685 5.75 10.3505 5.8817 10.1161 6.11612C9.8817 6.35054 9.75 6.66848 9.75 7C9.75 7.41421 9.41421 7.75 9 7.75C8.58579 7.75 8.25 7.41421 8.25 7C8.25 6.66848 8.1183 6.35054 7.88388 6.11612C7.64946 5.8817 7.33152 5.75 7 5.75C6.58579 5.75 6.25 5.41421 6.25 5C6.25 4.58579 6.58579 4.25 7 4.25C7.33152 4.25 7.64946 4.1183 7.88388 3.88388C8.1183 3.64946 8.25 3.33152 8.25 3C8.25 2.58579 8.58579 2.25 9 2.25ZM9 4.88746C8.98182 4.90673 8.96333 4.92576 8.94454 4.94454C8.92576 4.96333 8.90673 4.98182 8.88746 5C8.90673 5.01818 8.92576 5.03667 8.94454 5.05546C8.96333 5.07424 8.98182 5.09327 9 5.11254C9.01818 5.09327 9.03667 5.07424 9.05546 5.05546C9.07424 5.03667 9.09327 5.01818 9.11254 5C9.09327 4.98182 9.07424 4.96333 9.05546 4.94454C9.03667 4.92576 9.01818 4.90673 9 4.88746Z"}),i.default.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M18.5303 2.46967C18.2374 2.17678 17.7626 2.17678 17.4697 2.46967L2.46967 17.4697C2.17678 17.7626 2.17678 18.2374 2.46967 18.5303L5.46967 21.5303C5.76256 21.8232 6.23744 21.8232 6.53033 21.5303L21.5303 6.53033C21.8232 6.23744 21.8232 5.76256 21.5303 5.46967L18.5303 2.46967ZM18 7.93934L19.9393 6L18 4.06066L16.0607 6L18 7.93934ZM15 7.06066L16.9393 9L6 19.9393L4.06066 18L15 7.06066Z"}),i.default.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M19.75 13C19.75 12.5858 19.4142 12.25 19 12.25C18.5858 12.25 18.25 12.5858 18.25 13C18.25 13.3315 18.1183 13.6495 17.8839 13.8839C17.6495 14.1183 17.3315 14.25 17 14.25C16.5858 14.25 16.25 14.5858 16.25 15C16.25 15.4142 16.5858 15.75 17 15.75C17.3315 15.75 17.6495 15.8817 17.8839 16.1161C18.1183 16.3505 18.25 16.6685 18.25 17C18.25 17.4142 18.5858 17.75 19 17.75C19.4142 17.75 19.75 17.4142 19.75 17C19.75 16.6685 19.8817 16.3505 20.1161 16.1161C20.3505 15.8817 20.6685 15.75 21 15.75C21.4142 15.75 21.75 15.4142 21.75 15C21.75 14.5858 21.4142 14.25 21 14.25C20.6685 14.25 20.3505 14.1183 20.1161 13.8839C19.8817 13.6495 19.75 13.3315 19.75 13ZM18.9445 14.9445C18.9633 14.9258 18.9818 14.9067 19 14.8875C19.0182 14.9067 19.0367 14.9258 19.0555 14.9445C19.0742 14.9633 19.0933 14.9818 19.1125 15C19.0933 15.0182 19.0742 15.0367 19.0555 15.0555C19.0367 15.0742 19.0182 15.0933 19 15.1125C18.9818 15.0933 18.9633 15.0742 18.9445 15.0555C18.9258 15.0367 18.9067 15.0182 18.8875 15C18.9067 14.9818 18.9258 14.9633 18.9445 14.9445Z"}))}));t.default=u},68627:(e,t,n)=>{"use strict";var o=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=o(n(78304)),i=o(n(41594)),l=n(86956),u=i.default.forwardRef((function(e,t){return i.default.createElement(l.SvgIcon,(0,a.default)({viewBox:"0 0 24 24"},e,{ref:t}),i.default.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M4.16707 3.95837C4.11182 3.95837 4.05883 3.98032 4.01976 4.01939C3.98069 4.05846 3.95874 4.11145 3.95874 4.16671V6.04171H6.04207V3.95837H4.16707ZM4.16707 2.70837C3.7803 2.70837 3.40937 2.86202 3.13588 3.13551C2.86239 3.409 2.70874 3.77993 2.70874 4.16671V15.8334C2.70874 16.2201 2.86239 16.5911 3.13588 16.8646C3.40937 17.1381 3.7803 17.2917 4.16707 17.2917H15.8337C16.2205 17.2917 16.5914 17.1381 16.8649 16.8646C17.1384 16.5911 17.2921 16.2201 17.2921 15.8334V4.16671C17.2921 3.77993 17.1384 3.409 16.8649 3.13551C16.5914 2.86202 16.2205 2.70837 15.8337 2.70837H4.16707ZM7.29207 3.95837V6.04171H16.0421V4.16671C16.0421 4.11145 16.0201 4.05846 15.9811 4.01939C15.942 3.98032 15.889 3.95837 15.8337 3.95837H7.29207ZM16.0421 7.29171H3.95874V15.8334C3.95874 15.8886 3.98069 15.9416 4.01976 15.9807C4.05883 16.0198 4.11182 16.0417 4.16707 16.0417H15.8337C15.889 16.0417 15.942 16.0198 15.9811 15.9807C16.0201 15.9416 16.0421 15.8886 16.0421 15.8334V7.29171Z"}))}));t.default=u},65815:(e,t,n)=>{"use strict";var o=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=o(n(78304)),i=o(n(41594)),l=n(86956),u=i.default.forwardRef((function(e,t){return i.default.createElement(l.SvgIcon,(0,a.default)({viewBox:"0 0 24 24"},e,{ref:t}),i.default.createElement("path",{d:"M12 2.69231C6.8595 2.69231 2.69231 6.8595 2.69231 12C2.69231 17.1405 6.8595 21.3077 12 21.3077C17.1405 21.3077 21.3077 17.1405 21.3077 12C21.3077 6.8595 17.1405 2.69231 12 2.69231ZM1 12C1 5.92487 5.92487 1 12 1C18.0751 1 23 5.92487 23 12C23 18.0751 18.0751 23 12 23C5.92487 23 1 18.0751 1 12ZM9.14527 9.14527C9.47571 8.81483 10.0115 8.81483 10.3419 9.14527L12 10.8034L13.6581 9.14527C13.9885 8.81483 14.5243 8.81483 14.8547 9.14527C15.1852 9.47571 15.1852 10.0115 14.8547 10.3419L13.1966 12L14.8547 13.6581C15.1852 13.9885 15.1852 14.5243 14.8547 14.8547C14.5243 15.1852 13.9885 15.1852 13.6581 14.8547L12 13.1966L10.3419 14.8547C10.0115 15.1852 9.47571 15.1852 9.14527 14.8547C8.81483 14.5243 8.81483 13.9885 9.14527 13.6581L10.8034 12L9.14527 10.3419C8.81483 10.0115 8.81483 9.47571 9.14527 9.14527Z"}))}));t.default=u},92263:(e,t,n)=>{"use strict";var o,a=n(36833),i=a.renderLayoutApp,l=a.importToEditor,u=n(91258).MODE_VARIATION,c=n(12470).__,s=n(4353),d=s.ATTACHMENT_TYPE_JSON,f=s.ELEMENTOR_LIBRARY_SOURCE;o=Marionette.Behavior.extend({ui:{applyButton:".elementor-template-library-template-apply-ai",generateVariation:".elementor-template-library-template-generate-variation"},events:{"click @ui.applyButton":"onApplyButtonClick","click @ui.generateVariation":"onGenerateVariationClick"},onGenerateVariationClick:function onGenerateVariationClick(){var e,t={model:this.view.model},n=$e.components.get("library"),o=null===(e=n.manager.modalConfig)||void 0===e||null===(e=e.importOptions)||void 0===e?void 0:e.at;n.downloadTemplate(t,(function(e){var n=t.model,a={type:d,previewHTML:'<img src="'.concat(n.get("thumbnail"),'" />'),content:e.content[0],label:"".concat(n.get("template_id")," - ").concat(n.get("title")),source:f};i({parentContainer:elementor.getPreviewContainer(),mode:u,at:o,attachments:[a],onInsert:function onInsert(e){l({parentContainer:elementor.getPreviewContainer(),at:o,template:e,historyTitle:c("AI Variation from library","elementor")})}}),$e.run("library/close")}))},onApplyButtonClick:function onApplyButtonClick(){var e={model:this.view.model};this.ui.applyButton.addClass("elementor-disabled");var t=e.model.get("source");!elementor.hooks.applyFilters("templates/source/is-remote","remote"===t,t)||elementor.config.library_connect.is_connected?$e.run("library/generate-ai-variation",e):$e.route("library/connect",e)}}),e.exports=o},47407:(e,t,n)=>{"use strict";var o=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=o(n(41594)),i=n(86956),l=o(n(62688)),u=function LayoutAppWrapper(e){return a.default.createElement(i.DirectionProvider,{rtl:e.isRTL},a.default.createElement(i.ThemeProvider,{colorScheme:e.colorScheme},e.children))};u.propTypes={children:l.default.node,isRTL:l.default.bool,colorScheme:l.default.oneOf(["auto","light","dark"])};t.default=u},93569:(e,t,n)=>{"use strict";var o=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=o(n(41594)),i=o(n(62688)),l=o(n(94459)),u=n(38298),c=n(91258),s=n(35589),d=n(40128),f=function LayoutApp(e){return a.default.createElement(s.RemoteConfigProvider,{onError:e.onClose},a.default.createElement(d.RequestIdsProvider,null,a.default.createElement(c.ConfigProvider,{mode:e.mode,attachmentsTypes:e.attachmentsTypes,onClose:e.onClose,onConnect:e.onConnect,onData:e.onData,onInsert:e.onInsert,onSelect:e.onSelect,onGenerate:e.onGenerate,currentContext:e.currentContext,hasPro:e.hasPro},a.default.createElement(l.default,{attachments:e.attachments}))))};f.propTypes={mode:i.default.oneOf(c.LAYOUT_APP_MODES).isRequired,attachmentsTypes:u.AttachmentsTypesPropType,attachments:i.default.arrayOf(u.AttachmentPropType),onClose:i.default.func.isRequired,onConnect:i.default.func.isRequired,onData:i.default.func.isRequired,onInsert:i.default.func.isRequired,onSelect:i.default.func.isRequired,onGenerate:i.default.func.isRequired,currentContext:i.default.object,hasPro:i.default.bool};t.default=f},94459:(e,t,n)=>{"use strict";var o=n(96784),a=n(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=a(e)&&"function"!=typeof e)return{default:e};var n=_getRequireWildcardCache(t);if(n&&n.has(e))return n.get(e);var o={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var l in e)if("default"!==l&&{}.hasOwnProperty.call(e,l)){var u=i?Object.getOwnPropertyDescriptor(e,l):null;u&&(u.get||u.set)?Object.defineProperty(o,l,u):o[l]=e[l]}return o.default=e,n&&n.set(e,o),o}(n(41594)),l=o(n(18821)),u=o(n(8299)),c=o(n(51550)),s=o(n(62805)),d=o(n(73319)),f=o(n(35121)),p=o(n(24954)),m=o(n(4508)),g=o(n(75690)),h=o(n(62688)),y=n(38298),v=n(91258),_=n(40128);function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?n:t})(e)}var b=function LayoutContent(e){var t=(0,p.default)(),n=t.isLoading,o=t.isConnected,a=t.isGetStarted,h=t.connectUrl,y=t.fetchData,b=t.hasSubscription,w=t.usagePercentage,C=(0,v.useConfig)(),x=C.onClose,P=C.onConnect,E=(0,_.useRequestIds)(),O=E.updateUsagePercentage,S=E.usagePercentage,T=(0,i.useState)(!1),R=(0,l.default)(T,2),j=R[0],I=R[1];if((0,i.useEffect)((function(){j||n||!w&&0!==w||(O(w),I(!0))}),[n,w,j,O]),n||!j)return i.default.createElement(g.default,{onClose:x},i.default.createElement(g.default.Header,{onClose:x}),i.default.createElement(g.default.Content,{dividers:!0},i.default.createElement(d.default,{BoxProps:{sx:{px:3}}})));if(!o)return i.default.createElement(m.default,{onClose:x},i.default.createElement(g.default,{onClose:x}),i.default.createElement(m.default.Content,{dividers:!0},i.default.createElement(u.default,{connectUrl:h,onSuccess:function onSuccess(e){P(e),y()}})));if(!a)return i.default.createElement(m.default,{onClose:x},i.default.createElement(g.default,{onClose:x}),i.default.createElement(m.default.Content,{dividers:!0},i.default.createElement(s.default,{onSuccess:y})));var M=!b||80<=S;return i.default.createElement(c.default,{attachments:e.attachments,DialogHeaderProps:{children:M&&i.default.createElement(f.default,{hasSubscription:b,usagePercentage:S})}})};b.propTypes={attachments:h.default.arrayOf(y.AttachmentPropType)};t.default=b},8299:(e,t,n)=>{"use strict";var o=n(96784),a=n(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=a(e)&&"function"!=typeof e)return{default:e};var n=_getRequireWildcardCache(t);if(n&&n.has(e))return n.get(e);var o={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var l in e)if("default"!==l&&{}.hasOwnProperty.call(e,l)){var u=i?Object.getOwnPropertyDescriptor(e,l):null;u&&(u.get||u.set)?Object.defineProperty(o,l,u):o[l]=e[l]}return o.default=e,n&&n.set(e,o),o}(n(41594)),l=n(86956),u=n(12470),c=o(n(62688)),s=n(44048);function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?n:t})(e)}var d=function Connect(e){var t=e.connectUrl,n=e.onSuccess,o=(0,i.useRef)();return(0,i.useEffect)((function(){jQuery.fn.elementorConnect&&jQuery(o.current).elementorConnect({success:function success(e,t){return n(t)},error:function error(){throw new Error("Elementor AI: Failed to connect.")}})}),[]),i.default.createElement(l.Stack,{alignItems:"center",gap:2},i.default.createElement(s.AIIcon,{sx:{color:"text.primary",fontSize:"60px",mb:1}}),i.default.createElement(l.Typography,{variant:"h4",sx:{color:"text.primary"}},(0,u.__)("Step into the future with Elementor AI","elementor")),i.default.createElement(l.Typography,{variant:"body2"},(0,u.__)("Create smarter with AI text and code generators built right into the editor.","elementor")),i.default.createElement(l.Typography,{variant:"caption",sx:{maxWidth:520,textAlign:"center"}},(0,u.__)('By clicking "Connect", I approve the ',"elementor"),i.default.createElement(l.Link,{href:"https://go.elementor.com/ai-terms/",target:"_blank",color:"info.main"},(0,u.__)("Terms of Service","elementor"))," & ",i.default.createElement(l.Link,{href:"https://go.elementor.com/ai-privacy-policy/",target:"_blank",color:"info.main"},(0,u.__)("Privacy Policy","elementor")),(0,u.__)(" of the Elementor AI service.","elementor")),i.default.createElement(l.Button,{ref:o,href:t,variant:"contained",sx:{mt:1,"&:hover":{color:"primary.contrastText"}}},(0,u.__)("Connect","elementor")))};d.propTypes={connectUrl:c.default.string.isRequired,onSuccess:c.default.func.isRequired};t.default=d},4353:(e,t,n)=>{"use strict";var o=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=t.USER_VARIATION_SOURCE=t.USER_URL_SOURCE=t.MENU_TYPE_LIBRARY=t.ELEMENTOR_LIBRARY_SOURCE=t.ATTACHMENT_TYPE_URL=t.ATTACHMENT_TYPE_JSON=void 0;var a=o(n(41594)),i=o(n(78304)),l=n(60992),u=o(n(25553)),c=o(n(45286)),s=o(n(68627)),d=o(n(65141)),f=n(12470),p=o(n(62688)),m=n(38298),g=n(86956),h=t.ATTACHMENT_TYPE_JSON="json",y=t.ATTACHMENT_TYPE_URL="url",v=t.MENU_TYPE_LIBRARY="library",_=(t.USER_VARIATION_SOURCE="user-variation",t.ELEMENTOR_LIBRARY_SOURCE="elementor-library",t.USER_URL_SOURCE="user-url",function Attachments(e){return e.attachments.length?a.default.createElement(g.Stack,{direction:"row",spacing:1},e.attachments.map((function(t,n){switch(t.type){case h:return a.default.createElement(u.default,(0,i.default)({key:n},e));case y:return a.default.createElement(c.default,(0,i.default)({key:n},e));default:return null}}))):a.default.createElement(l.Menu,{disabled:e.disabled,onAttach:e.onAttach,items:[{title:(0,f.__)("Reference a website","elementor"),icon:s.default,type:y},{title:(0,f.__)("Create variations from Template Library","elementor"),icon:d.default,type:v}]})});_.propTypes={attachments:p.default.arrayOf(m.AttachmentPropType).isRequired,onAttach:p.default.func.isRequired,onDetach:p.default.func,disabled:p.default.bool};t.default=_},89958:(e,t,n)=>{"use strict";var o=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=t.AttachDialog=void 0;var a=o(n(41594)),i=n(43220),l=o(n(62688)),u=n(66942),c=n(4353),s=t.AttachDialog=function AttachDialog(e){var t=e.type,n=e.url;switch(t){case c.ATTACHMENT_TYPE_URL:return a.default.createElement(i.UrlDialog,{url:n,onAttach:e.onAttach,onClose:e.onClose});case c.MENU_TYPE_LIBRARY:return a.default.createElement(u.LibraryDialog,{onAttach:e.onAttach,onClose:e.onClose})}return null};s.propTypes={type:l.default.string,onAttach:l.default.func,onClose:l.default.func,url:l.default.string};t.default=s},66942:(e,t,n)=>{"use strict";var o=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.LibraryDialog=void 0;var a=o(n(62688)),i=n(41594),l=n(4353);(t.LibraryDialog=function LibraryDialog(e){var t=(0,i.useRef)(!1);return(0,i.useEffect)((function(){var n=function onLibraryHide(){t.current||e.onClose()};return $e.components.get("library").layout.getModal().on("hide",n),function(){$e.components.get("library").layout.getModal().off("hide",n)}}),[e]),(0,i.useEffect)((function(){var n=function onMessage(n){var o=n.data,a=o.type,i=o.json,u=o.html,c=o.label,s=o.source;switch(a){case"library/attach:start":t.current=!0;break;case"library/attach":e.onAttach([{type:l.ATTACHMENT_TYPE_JSON,previewHTML:u,content:i,label:c,source:s}]),t.current=!1,e.onClose()}};return window.addEventListener("message",n),function(){window.removeEventListener("message",n)}})),$e.run("library/open",{toDefault:!0,mode:"ai-attachment"}),t.current=!1,null}).propTypes={onAttach:a.default.func.isRequired,onClose:a.default.func.isRequired}},60992:(e,t,n)=>{"use strict";var o=n(96784),a=n(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.Menu=void 0;var i=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=a(e)&&"function"!=typeof e)return{default:e};var n=_getRequireWildcardCache(t);if(n&&n.has(e))return n.get(e);var o={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var l in e)if("default"!==l&&{}.hasOwnProperty.call(e,l)){var u=i?Object.getOwnPropertyDescriptor(e,l):null;u&&(u.get||u.set)?Object.defineProperty(o,l,u):o[l]=e[l]}return o.default=e,n&&n.set(e,o),o}(n(41594)),l=o(n(18821)),u=n(86956),c=o(n(65815)),s=o(n(96793)),d=o(n(62688)),f=n(89958),p=o(n(80366));function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?n:t})(e)}(t.Menu=function Menu(e){var t=(0,i.useState)(!1),n=(0,l.default)(t,2),o=n[0],a=n[1],d=(0,i.useState)(null),m=(0,l.default)(d,2),g=m[0],h=m[1],y=(0,u.useTheme)().direction,v=(0,i.useRef)(null),_=(0,p.default)("e-ai-attachment-badge"),b=_.isViewed,w=_.markAsViewed;return i.default.createElement(i.default.Fragment,null,i.default.createElement(u.IconButton,{size:"small",ref:v,disabled:e.disabled,onClick:function onClick(){a(!0),b||w()},color:"secondary"},o?i.default.createElement(c.default,{fontSize:"small"}):b?i.default.createElement(s.default,{fontSize:"small"}):i.default.createElement(u.Badge,{color:"primary",badgeContent:" ",variant:"dot"},i.default.createElement(s.default,{fontSize:"small"}))),i.default.createElement(u.Popover,{open:o,anchorEl:v.current,onClose:function onClose(){return a(!1)},anchorOrigin:{vertical:"bottom",horizontal:"rtl"===y?"right":"left"},transformOrigin:{vertical:"top",horizontal:"rtl"===y?"right":"left"}},i.default.createElement(u.Stack,{sx:{width:440}},e.items.map((function(e){var t=e.icon;return i.default.createElement(u.MenuItem,{key:e.type,onClick:function onClick(){h(e.type),a(!1)}},i.default.createElement(u.ListItemIcon,null,i.default.createElement(t,null)),e.title)})))),i.default.createElement(f.AttachDialog,{type:g,onAttach:e.onAttach,onClose:function onClose(){a(!1),h(null)}}))}).propTypes={items:d.default.arrayOf(d.default.shape({title:d.default.string.isRequired,type:d.default.string.isRequired,icon:d.default.elementType})).isRequired,onAttach:d.default.func.isRequired,disabled:d.default.bool}},54403:(e,t,n)=>{"use strict";var o=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.PromptPowerNotice=void 0;var a=o(n(41594)),i=n(86956),l=n(12470),u=o(n(80366));t.PromptPowerNotice=function PromptPowerNotice(){var e=(0,u.default)("e-ai-builder-attachments-power"),t=e.isViewed,n=e.markAsViewed;return t?null:a.default.createElement(i.Box,{sx:{pt:2,px:2,pb:0}},a.default.createElement(i.Alert,{severity:"info",onClose:function onClose(){return n()}},a.default.createElement(i.Typography,{variant:"body2",display:"inline-block",sx:{paddingInlineEnd:1}},(0,l.__)("You’ve got the power.","elementor")),a.default.createElement(i.Typography,{variant:"body2",display:"inline-block"},(0,l.__)("Craft your prompt to affect content, images and/or colors - whichever you decide.","elementor"))))}},25553:(e,t,n)=>{"use strict";var o=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=t.ThumbnailJson=void 0;var a=o(n(41594)),i=n(71338),l=o(n(62688)),u=n(86956),c=n(38298),s=t.ThumbnailJson=function ThumbnailJson(e){var t,n=null===(t=e.attachments)||void 0===t?void 0:t.find((function(e){return"json"===e.type}));return n?n.previewHTML?a.default.createElement(i.Thumbnail,{html:n.previewHTML,disabled:e.disabled}):a.default.createElement(u.Skeleton,{animation:"wave",variant:"rounded",width:60,height:60}):null};s.propTypes={attachments:l.default.arrayOf(c.AttachmentPropType).isRequired,disabled:l.default.bool};t.default=s},45286:(e,t,n)=>{"use strict";var o=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=t.ThumbnailUrl=void 0;var a=o(n(41594)),i=n(71338),l=n(12470),u=o(n(62688)),c=n(86956),s=n(44048),d=n(38298),f=t.ThumbnailUrl=function ThumbnailUrl(e){var t,n=null===(t=e.attachments)||void 0===t?void 0:t.find((function(e){return"url"===e.type}));return n?a.default.createElement(c.Box,{sx:{position:"relative","&:hover::before":{content:'""',position:"absolute",userSelect:"none",inset:0,backgroundColor:"rgba(0,0,0,0.6)",borderRadius:1,zIndex:1},"&:hover .remove-attachment":{display:"flex"}}},a.default.createElement(c.IconButton,{className:"remove-attachment",size:"small","aria-label":(0,l.__)("Remove","elementor"),disabled:e.disabled,onClick:function onClick(t){t.stopPropagation(),e.onDetach()},sx:{display:"none",position:"absolute",insetInlineEnd:4,insetBlockStart:4,backgroundColor:"secondary.main",zIndex:1,borderRadius:1,p:"3px","&:hover":{backgroundColor:"secondary.dark"}}},a.default.createElement(s.TrashIcon,{sx:{fontSize:"1.125rem",color:"common.white"}})),a.default.createElement(i.Thumbnail,{disabled:e.disabled,html:n.previewHTML})):null};f.propTypes={attachments:u.default.arrayOf(d.AttachmentPropType).isRequired,disabled:u.default.bool,onDetach:u.default.func};t.default=f},71338:(e,t,n)=>{"use strict";var o=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.Thumbnail=t.THUMBNAIL_SIZE=void 0;var a,i=o(n(41594)),l=o(n(98832)),u=n(86956),c=n(12470),s=o(n(62688)),d=t.THUMBNAIL_SIZE=64,f=(0,u.styled)("body")(a||(a=(0,l.default)(["\n\thtml, body {\n\t\tmargin: 0;\n\t\tpadding: 0;\n\t\toverflow: hidden;\n\t}\n\n\tbody > * {\n\t\twidth: 100% !important;\n\t}\n\n\tbody > img {\n\t\theight: 100%;\n\t\tobject-fit: cover;\n\t}\n\n\tbody:has(> img) {\n\t\theight: ","px\n\t}\n"])),d);(t.Thumbnail=function Thumbnail(e){var t,n,o=null===(t=e.html.match('data-width="(?<width>\\d+)"'))||void 0===t||null===(t=t.groups)||void 0===t?void 0:t.width,a=null===(n=e.html.match('data-height="(?<height>\\d+)"'))||void 0===n||null===(n=n.groups)||void 0===n?void 0:n.height,l=o?parseInt(o):d,s=a?parseInt(a):d,p=Math.min(s,l),m=d/p,g=s>l?(d-d*(s/l))/2:0,h=l>s?(d-d*(l/s))/2:0;return i.default.createElement(u.Box,{dir:"ltr",sx:{position:"relative",cursor:"default",overflow:"hidden",border:"1px solid",borderColor:"grey.300",borderRadius:1,boxSizing:"border-box",width:d,height:d,opacity:e.disabled?.5:1}},i.default.createElement("iframe",{title:(0,c.__)("Preview","elementor"),sandbox:"",srcDoc:"<style>"+f.componentStyle.rules.join("")+"</style>"+e.html,style:{border:"none",overflow:"hidden",width:l,height:s,transform:"scale(".concat(m,")"),transformOrigin:"".concat(h,"px ").concat(g,"px")}}))}).propTypes={html:s.default.string.isRequired,disabled:s.default.bool}},43220:(e,t,n)=>{"use strict";var o=n(96784),a=n(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.UrlDialog=void 0;var i=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=a(e)&&"function"!=typeof e)return{default:e};var n=_getRequireWildcardCache(t);if(n&&n.has(e))return n.get(e);var o={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var l in e)if("default"!==l&&{}.hasOwnProperty.call(e,l)){var u=i?Object.getOwnPropertyDescriptor(e,l):null;u&&(u.get||u.set)?Object.defineProperty(o,l,u):o[l]=e[l]}return o.default=e,n&&n.set(e,o),o}(n(41594)),l=o(n(18821)),u=n(86956),c=o(n(62688)),s=n(12470),d=n(86353),f=n(77043),p=n(4353),m=n(35589),g=o(n(24954)),h=n(40128);function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?n:t})(e)}(t.UrlDialog=function UrlDialog(e){var t=(0,f.useTimeout)(1e4),n=(0,l.default)(t,2),o=n[0],a=n[1],c=(0,g.default)(),y=c.isLoading,v=c.usagePercentage,_=(0,h.useRequestIds)().updateUsagePercentage,b=(0,i.useState)(!1),w=(0,l.default)(b,2),C=w[0],x=w[1],P=(0,m.useRemoteConfig)().remoteConfig[m.CONFIG_KEYS.WEB_BASED_BUILDER_URL],E=(P?new URL(P):{}).origin,O=(0,i.useRef)(!1);return(0,i.useEffect)((function(){C||y||!v&&0!==v||(_(v),x(!0))}),[y,v,C,_]),(0,i.useEffect)((function(){if(!O.current)try{window.$e.run("ai-integration/open-choose-element",{url:e.url}),O.current=!0}catch(e){console.error(e)}}),[O.current]),(0,i.useEffect)((function(){var t=function onMessage(t){if(t.origin===E){var n=t.data,o=n.type,i=n.html,l=n.url;switch(o){case"element-selector/close":O.current=!1,e.onClose();break;case"element-selector/loaded":a(),O.current=!0;break;case"element-selector/attach":e.onAttach([{type:"url",previewHTML:i,content:i,label:l?new URL(l).href:"",source:p.USER_URL_SOURCE}])}}};return window.addEventListener("message",t),function(){window.removeEventListener("message",t)}}),[E,e,a]),i.default.createElement(i.default.Fragment,null,!O.current&&!o&&i.default.createElement(u.Dialog,{open:!0,maxWidth:"lg"},i.default.createElement(u.Typography,{sx:{textAlign:"center",padding:3}},(0,s.__)("Loading...","elementor"))),o&&i.default.createElement(d.AlertDialog,{message:(0,s.__)("The app is not responding. Please try again later. (#408)","elementor"),onClose:e.onClose}))}).propTypes={onAttach:c.default.func.isRequired,onClose:c.default.func.isRequired,url:c.default.string}},75690:(e,t,n)=>{"use strict";var o=n(96784),a=n(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=a(e)&&"function"!=typeof e)return{default:e};var n=_getRequireWildcardCache(t);if(n&&n.has(e))return n.get(e);var o={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var l in e)if("default"!==l&&{}.hasOwnProperty.call(e,l)){var u=i?Object.getOwnPropertyDescriptor(e,l):null;u&&(u.get||u.set)?Object.defineProperty(o,l,u):o[l]=e[l]}return o.default=e,n&&n.set(e,o),o}(n(41594)),l=o(n(78304)),u=o(n(85707)),c=o(n(18821)),s=o(n(40453)),d=n(86956),f=n(12470),p=o(n(62688)),m=o(n(48968)),g=n(44048),h=["sx","PaperProps"];function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?n:t})(e)}function ownKeys(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(n),!0).forEach((function(t){(0,u.default)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ownKeys(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var y=(0,d.styled)(m.default)((function(){return{"& .MuiDialog-container":{marginTop:0,alignItems:"flex-end",paddingBottom:"16vh"},"& .MuiPaper-root":{margin:0,maxHeight:"80vh"}}})),v=function DialogHeader(e){var t=e.onClose,n=e.children;return i.default.createElement(d.AppBar,{sx:{fontWeight:"normal"},color:"transparent",position:"relative"},i.default.createElement(d.Toolbar,{variant:"dense"},i.default.createElement(g.AIIcon,{sx:{mr:1}}),i.default.createElement(d.Typography,{component:"span",variant:"subtitle2",sx:{fontWeight:"bold",textTransform:"uppercase"}},(0,f.__)("AI","elementor")),i.default.createElement(d.Chip,{label:(0,f.__)("Beta","elementor"),color:"default",size:"small",sx:{ml:1}}),i.default.createElement(d.Stack,{direction:"row",spacing:1,alignItems:"center",sx:{ml:"auto"}},n,i.default.createElement(d.IconButton,{size:"small","aria-label":"close",onClick:t,sx:{"&.MuiButtonBase-root":{mr:-1}}},i.default.createElement(g.XIcon,null)))))};v.propTypes={children:p.default.node,onClose:p.default.func.isRequired};var _=(0,d.styled)(m.default.Content)((function(){return{"&.MuiDialogContent-root":{padding:0}}})),b=function LayoutDialog(e){var t=e.sx,n=void 0===t?{}:t,o=e.PaperProps,a=void 0===o?{}:o,u=(0,s.default)(e,h),d=(0,i.useState)({pointerEvents:"none"}),f=(0,c.default)(d,2),p=f[0],m=f[1],g=(0,i.useRef)(null);return i.default.createElement(y,(0,l.default)({maxWidth:"md",PaperProps:_objectSpread({sx:{pointerEvents:"auto"},onMouseEnter:function onMouseEnter(){clearTimeout(g.current),m({pointerEvents:"all"})},onMouseLeave:function onMouseLeave(){clearTimeout(g.current),g.current=setTimeout((function(){m({pointerEvents:"none"})}),200)}},a)},u,{sx:_objectSpread(_objectSpread({},p),n)}))};b.propTypes={sx:p.default.object,PaperProps:p.default.object},b.Header=v,b.Content=_;t.default=b},45395:(e,t,n)=>{"use strict";var o=n(96784),a=n(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.ProTemplateIndicator=void 0;var i=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=a(e)&&"function"!=typeof e)return{default:e};var n=_getRequireWildcardCache(t);if(n&&n.has(e))return n.get(e);var o={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var l in e)if("default"!==l&&{}.hasOwnProperty.call(e,l)){var u=i?Object.getOwnPropertyDescriptor(e,l):null;u&&(u.get||u.set)?Object.defineProperty(o,l,u):o[l]=e[l]}return o.default=e,n&&n.set(e,o),o}(n(41594)),l=o(n(18821)),u=n(12470),c=n(86956),s=o(n(21995));function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?n:t})(e)}var d=(0,c.styled)(c.Paper)((function(e){var t=e.theme;return{position:"relative",padding:t.spacing(3),boxShadow:t.shadows[4],zIndex:"9999"}})),f=(0,c.styled)(c.Box)((function(e){var t=e.theme;return{position:"absolute",width:t.spacing(5),height:t.spacing(5),overflow:"hidden",left:"100% !important",transform:"translateX(-50%) translateY(-50%) rotate(var(--rotate, 0deg)) !important","&::after":{backgroundColor:t.palette.background.paper,content:'""',display:"block",position:"absolute",width:t.spacing(2.5),height:t.spacing(2.5),top:"50%",left:"50%",transform:"translateX(-50%) translateY(-50%) rotate(45deg)",boxShadow:"5px -5px 5px 0px rgba(0, 0, 0, 0.2)",backgroundImage:"linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05))"}}}));t.ProTemplateIndicator=function ProTemplateIndicator(){var e=(0,u.__)("Go Pro","elementor"),t=(0,i.useState)(!1),n=(0,l.default)(t,2),o=n[0],a=n[1],p=(0,i.useRef)(null),m=(0,i.useRef)(null);return i.default.createElement(c.Box,{flexDirection:"row-reverse",component:"span",display:"flex",onMouseLeave:function hidePopover(){return a(!1)},alignItems:"center"},i.default.createElement(c.IconButton,{ref:p,onMouseEnter:function showPopover(){return a(!0)},onClick:function onClick(e){return e.stopPropagation()},"aria-owns":o?"e-pro-upgrade-popover":void 0,"aria-haspopup":"true",sx:{m:1,"&:hover":{backgroundColor:"action.selected"}}},i.default.createElement(s.default,{sx:{color:"text.primary"}})),i.default.createElement(c.Popper,{open:o,popperOptions:{placement:"left-start",modifiers:[{name:"arrow",enabled:!0,options:{element:m.current,padding:5}},{name:"offset",options:{offset:[0,10]}}]},anchorEl:p.current,sx:{zIndex:"9999",maxWidth:300}},i.default.createElement(d,null,i.default.createElement(f,{ref:m}),i.default.createElement(c.Stack,{alignItems:"start",spacing:2},i.default.createElement(c.Chip,{color:"promotion",variant:"outlined",size:"small",label:(0,u.__)("Pro","elementor"),icon:i.default.createElement(s.default,null)}),i.default.createElement(c.Typography,{variant:"body2"},(0,u.__)("This result includes an Elementor Pro widget that's not available with your current plan. Upgrade to use all the widgets in this result.","elementor")),i.default.createElement(c.Button,{variant:"contained",color:"promotion",size:"small",href:"https://go.elementor.com/go-pro-ai/",target:"_blank",sx:{alignSelf:"flex-end"}},e)))))}},4974:(e,t,n)=>{"use strict";var o=n(96784),a=n(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=a(e)&&"function"!=typeof e)return{default:e};var n=_getRequireWildcardCache(t);if(n&&n.has(e))return n.get(e);var o={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var l in e)if("default"!==l&&{}.hasOwnProperty.call(e,l)){var u=i?Object.getOwnPropertyDescriptor(e,l):null;u&&(u.get||u.set)?Object.defineProperty(o,l,u):o[l]=e[l]}return o.default=e,n&&n.set(e,o),o}(n(41594)),l=o(n(18821)),u=o(n(40453)),c=o(n(78304)),s=o(n(85707)),d=n(86956),f=o(n(62688)),p=n(12470),m=o(n(53497)),g=n(91258),h=["onSubmit"];function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?n:t})(e)}function ownKeys(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(n),!0).forEach((function(t){(0,s.default)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ownKeys(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var y=(0,i.forwardRef)((function(e,t){return i.default.createElement(d.TextField,(0,c.default)({autoFocus:!0,multiline:!0,size:"small",maxRows:3,color:"secondary",variant:"standard"},e,{inputRef:t,InputProps:_objectSpread(_objectSpread({},e.InputProps),{},{type:"search",sx:{pt:0}})}))}));y.propTypes={InputProps:f.default.object};var v=function PaperComponent(e){var t=(0,g.useConfig)().mode,n=g.MODE_VARIATION===t?"https://go.elementor.com/ai-prompt-library-variations/":"https://go.elementor.com/ai-prompt-library-containers/";return i.default.createElement(d.Paper,(0,c.default)({},e,{elevation:8,sx:{borderRadius:2}}),i.default.createElement(d.Typography,{component:d.Box,color:function color(e){return e.palette.text.tertiary},variant:"caption",paddingX:2,paddingY:1},(0,p.__)("Suggested Prompts","elementor")),i.default.createElement(d.Divider,null),e.children,i.default.createElement(d.Stack,{sx:{m:2}},i.default.createElement(m.default,{libraryLink:n})))};v.propTypes={children:f.default.node};var _=function PromptAutocomplete(e){var t=e.onSubmit,n=(0,u.default)(e,h),o=(0,i.useState)(!1),a=(0,l.default)(o,2),s=a[0],f=a[1],p=(0,d.useTheme)(),m=parseInt(p.spacing(4));return i.default.createElement(d.Autocomplete,(0,c.default)({PaperComponent:v,ListboxProps:{sx:{maxHeight:5*m}},renderOption:function renderOption(e,t){return i.default.createElement(d.Typography,(0,c.default)({},e,{title:t.text,noWrap:!0,variant:"body2",component:d.Box,sx:{"&.MuiAutocomplete-option":{display:"block",minHeight:m}}}),t.text)},freeSolo:!0,fullWidth:!0,disableClearable:!0,open:s,onClose:function onClose(e){var t;return f("A"===(null===(t=e.relatedTarget)||void 0===t?void 0:t.tagName))},onKeyDown:function onKeyDown(e){"Enter"!==e.key||e.shiftKey||s?"/"===e.key&&""===e.target.value&&(e.preventDefault(),f(!0)):t(e)}},n))};_.propTypes={onSubmit:f.default.func.isRequired},_.TextInput=y;t.default=_},85076:(e,t,n)=>{"use strict";var o=n(96784),a=n(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=a(e)&&"function"!=typeof e)return{default:e};var n=_getRequireWildcardCache(t);if(n&&n.has(e))return n.get(e);var o={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var l in e)if("default"!==l&&{}.hasOwnProperty.call(e,l)){var u=i?Object.getOwnPropertyDescriptor(e,l):null;u&&(u.get||u.set)?Object.defineProperty(o,l,u):o[l]=e[l]}return o.default=e,n&&n.set(e,o),o}(n(41594)),l=o(n(18821)),u=o(n(78304)),c=o(n(40453)),s=n(86956),d=n(12470),f=o(n(62688)),p=o(n(4974)),m=o(n(48584)),g=o(n(63223)),h=o(n(31593)),y=o(n(51066)),v=o(n(34161)),_=o(n(4353)),b=n(91258),w=n(38298),C=["tooltip"];function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?n:t})(e)}var x=Object.freeze([{text:(0,d.__)("Hero section on [topic] with heading, text, buttons on the right, and an image on the left","elementor.com")},{text:(0,d.__)("About Us section on [topic] with heading, text, and big image below","elementor.com")},{text:(0,d.__)("Team section with four image boxes showcasing team members","elementor.com")},{text:(0,d.__)("FAQ section with a toggle widget showcasing FAQs about [topic]","elementor.com")},{text:(0,d.__)("Gallery section with a carousel displaying three images at once","elementor.com")},{text:(0,d.__)("Contact section with a form for [topic]","elementor.com")},{text:(0,d.__)("Client section featuring companies' logos","elementor.com")},{text:(0,d.__)("Testimonial section with testimonials, each featuring a star rating and an image","elementor.com")},{text:(0,d.__)("Service section about [topic], showcasing four services with buttons","elementor.com")},{text:(0,d.__)("Stats section with counters displaying data about [topic]","elementor.com")},{text:(0,d.__)("Quote section with colored background, featuring a centered quote","elementor.com")},{text:(0,d.__)("Pricing section for [topic] with a pricing list","elementor.com")},{text:(0,d.__)("Subscribe section featuring a simple email form, inviting users to stay informed on [topic]","elementor.com")}]),P=function IconButtonWithTooltip(e){var t=e.tooltip,n=(0,c.default)(e,C);return i.default.createElement(s.Tooltip,{title:t},i.default.createElement(s.Box,{component:"span",sx:{cursor:n.disabled?"default":"pointer"}},i.default.createElement(s.IconButton,n)))};P.propTypes={tooltip:f.default.string,disabled:f.default.bool};var E=function BackButton(e){return i.default.createElement(P,(0,u.default)({size:"small",color:"secondary",tooltip:(0,d.__)("Back to results","elementor")},e),i.default.createElement(h.default,null))},O=function EditButton(e){return i.default.createElement(P,(0,u.default)({size:"small",color:"primary",tooltip:(0,d.__)("Edit prompt","elementor")},e),i.default.createElement(y.default,null))},S=function GenerateButton(e){return i.default.createElement(g.default,(0,u.default)({size:"small",fullWidth:!1},e),(0,d.__)("Generate","elementor"))},T=(0,i.forwardRef)((function(e,t){var n,o=e.attachments,a=e.isActive,c=e.isLoading,f=e.showActions,g=void 0!==f&&f,h=e.onAttach,y=e.onDetach,w=e.onSubmit,C=e.onBack,P=e.onEdit,T=e.shouldResetPrompt,R=void 0!==T&&T,j=(0,i.useState)(""),I=(0,l.default)(j,2),M=I[0],k=I[1];(0,i.useEffect)((function(){R&&k("")}),[R]);var A=(0,v.default)(M,"layout"),D=A.isEnhancing,L=A.enhance,q=(0,i.useRef)(""),W=(0,b.useConfig)().attachmentsTypes,B=c||D||!a,N=""===M&&!o.length,H=B||N,U=W[(null===(n=o[0])||void 0===n?void 0:n.type)||""],V=(null==U?void 0:U.promptSuggestions)||x,F=(null==U?void 0:U.promptPlaceholder)||(0,d.__)("Press '/' for suggested prompts or describe the layout you want to create","elementor");return i.default.createElement(s.Stack,{component:"form",onSubmit:function onSubmit(e){return w(e,M)},direction:"row",sx:{p:3},alignItems:"start",gap:1},i.default.createElement(s.Stack,{direction:"row",alignItems:"start",flexGrow:1,spacing:2},g&&(a?i.default.createElement(E,{disabled:c||D,onClick:function handleBack(){k(q.current),C()}}):i.default.createElement(O,{disabled:c,onClick:function handleEdit(){q.current=M,P()}})),i.default.createElement(_.default,{attachments:o,onAttach:h,onDetach:y,disabled:B}),i.default.createElement(p.default,{value:M,disabled:B,onSubmit:function onSubmit(e){return w(e,M)},options:V,onChange:function onChange(e,t){return k(t.text+" ")},renderInput:function renderInput(e){return i.default.createElement(p.default.TextInput,(0,u.default)({},e,{ref:t,onChange:function onChange(e){return k(e.target.value)},placeholder:F}))}})),i.default.createElement(m.default,{size:"small",disabled:H||""===M,isLoading:D,onClick:function onClick(){return L().then((function(e){var t=e.result;return k(t)}))}}),i.default.createElement(S,{disabled:H}))}));T.propTypes={isActive:f.default.bool,onAttach:f.default.func,onDetach:f.default.func,isLoading:f.default.bool,showActions:f.default.bool,onSubmit:f.default.func.isRequired,onBack:f.default.func.isRequired,onEdit:f.default.func.isRequired,attachments:f.default.arrayOf(w.AttachmentPropType),shouldResetPrompt:f.default.bool};t.default=T},25893:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=n(86956),a=(0,o.styled)(o.Box,{shouldForwardProp:function shouldForwardProp(e){return"outlineOffset"!==e}})((function(e){var t=e.theme,n=e.selected,o=e.height,a=e.disabled,i=e.outlineOffset,l=void 0===i?"0px":i,u=n?t.palette.text.primary:t.palette.text.disabled,c="2px solid ".concat(u);return{height:o,cursor:a?"default":"pointer",overflow:"hidden",boxSizing:"border-box",backgroundPosition:"top center",backgroundSize:"100% auto",backgroundRepeat:"no-repeat",backgroundColor:t.palette.common.white,borderRadius:.5*t.shape.borderRadius,outlineOffset:l,outline:c,opacity:a?"0.4":"1",transition:"all 50ms linear","&:hover":a?{}:{outlineColor:t.palette.text.primary}}}));t.default=a},93264:(e,t,n)=>{"use strict";var o=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=ScreenshotUnavailable;var a=o(n(41594)),i=o(n(78304)),l=o(n(85707)),u=o(n(62688)),c=n(12470),s=o(n(25893));function ownKeys(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(n),!0).forEach((function(t){(0,l.default)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ownKeys(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function ScreenshotUnavailable(e){return a.default.createElement(s.default,(0,i.default)({},e,{sx:_objectSpread(_objectSpread({},e.sx||{}),{},{display:"flex",alignItems:"center",justifyContent:"center",backgroundColor:"background.paper",color:"text.tertiary",fontStyle:"italic",fontSize:"12px",paddingInline:12,textAlign:"center",lineHeight:1.5})}),(0,c.__)("Preview unavailable","elementor"))}ScreenshotUnavailable.propTypes={sx:u.default.object}},79919:(e,t,n)=>{"use strict";var o=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=o(n(41594)),i=o(n(85707)),l=n(86956),u=o(n(62688)),c=o(n(25893)),s=o(n(93264)),d=o(n(51563));function ownKeys(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(n),!0).forEach((function(t){(0,i.default)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ownKeys(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var f="138px",p=function Screenshot(e){var t=e.url,n=e.type,o=e.isLoading,i=void 0!==o&&o,u=e.isSelected,p=void 0!==u&&u,m=e.isPlaceholder,g=e.disabled,h=e.onClick,y=e.sx,v=void 0===y?{}:y,_=e.outlineOffset;return m?a.default.createElement(l.Box,{sx:_objectSpread({height:f},v)}):i?a.default.createElement(l.Skeleton,{width:"100%",animation:"wave",variant:"rounded",height:f,sx:v}):t?a.default.createElement(c.default,{selected:p,disabled:g,sx:_objectSpread({backgroundImage:"url('".concat(t,"')")},v),onClick:h,height:f,outlineOffset:_},a.default.createElement(d.default,{type:n})):a.default.createElement(s.default,{selected:p,disabled:g,sx:v,onClick:h,height:f,outlineOffset:_})};p.propTypes={isSelected:u.default.bool,isLoading:u.default.bool,isPlaceholder:u.default.bool,disabled:u.default.bool,onClick:u.default.func.isRequired,url:u.default.string,type:u.default.string,sx:u.default.object,outlineOffset:u.default.string};t.default=p},51563:(e,t,n)=>{"use strict";var o=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=o(n(41594)),i=o(n(62688)),l=n(91258),u=n(45395),c=function TemplateBadge(e){var t=(0,l.useConfig)().hasPro;return"Pro"!==e.type||t?null:a.default.createElement(u.ProTemplateIndicator,null)};t.default=c;c.propTypes={type:i.default.string}},64162:(e,t,n)=>{"use strict";var o=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=o(n(41594)),i=o(n(78304)),l=o(n(40453)),u=n(86956),c=n(12470),s=o(n(62688)),d=["onClose","onCancel","title","text"],f=function UnsavedChangesAlert(e){var t=e.onClose,n=e.onCancel,o=e.title,s=e.text,f=(0,l.default)(e,d);return a.default.createElement(u.Dialog,(0,i.default)({"aria-labelledby":"unsaved-changes-alert-title","aria-describedby":"unsaved-changes-alert-description"},f),a.default.createElement(u.DialogTitle,{id:"unsaved-changes-alert-title"},o),a.default.createElement(u.DialogContent,null,a.default.createElement(u.DialogContentText,{id:"unsaved-changes-alert-description"},s)),a.default.createElement(u.DialogActions,null,a.default.createElement(u.Button,{onClick:n,color:"secondary"},(0,c.__)("Cancel","elementor")),a.default.createElement(u.Button,{onClick:t,color:"error",variant:"contained"},(0,c.__)("Yes, leave","elementor"))))};f.propTypes={title:s.default.string,text:s.default.string,onCancel:s.default.func,onClose:s.default.func};t.default=f},91258:(e,t,n)=>{"use strict";var o=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.useConfig=t.default=t.MODE_VARIATION=t.MODE_LAYOUT=t.LAYOUT_APP_MODES=t.ConfigProvider=void 0;var a=o(n(41594)),i=o(n(62688)),l=t.MODE_LAYOUT="layout",u=t.MODE_VARIATION="variation",c=t.LAYOUT_APP_MODES=[l,u],s=a.default.createContext({});t.useConfig=function useConfig(){return a.default.useContext(s)};(t.ConfigProvider=function ConfigProvider(e){return a.default.createElement(s.Provider,{value:{mode:e.mode,attachmentsTypes:e.attachmentsTypes,onClose:e.onClose,onConnect:e.onConnect,onData:e.onData,onInsert:e.onInsert,onSelect:e.onSelect,onGenerate:e.onGenerate,currentContext:e.currentContext,hasPro:e.hasPro}},e.children)}).propTypes={mode:i.default.oneOf(c).isRequired,children:i.default.node.isRequired,attachmentsTypes:i.default.object.isRequired,onClose:i.default.func.isRequired,onConnect:i.default.func.isRequired,onData:i.default.func.isRequired,onInsert:i.default.func.isRequired,onSelect:i.default.func.isRequired,onGenerate:i.default.func.isRequired,currentContext:i.default.object,hasPro:i.default.bool};t.default=s},35589:(e,t,n)=>{"use strict";var o=n(96784),a=n(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.useRemoteConfig=t.RemoteConfigProvider=t.CONFIG_KEYS=void 0;var i=o(n(61790)),l=o(n(58155)),u=o(n(18821)),c=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=a(e)&&"function"!=typeof e)return{default:e};var n=_getRequireWildcardCache(t);if(n&&n.has(e))return n.get(e);var o={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var l in e)if("default"!==l&&{}.hasOwnProperty.call(e,l)){var u=i?Object.getOwnPropertyDescriptor(e,l):null;u&&(u.get||u.set)?Object.defineProperty(o,l,u):o[l]=e[l]}return o.default=e,n&&n.set(e,o),o}(n(41594)),s=o(n(62688)),d=n(95034);function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?n:t})(e)}var f=c.default.createContext({});t.useRemoteConfig=function useRemoteConfig(){return c.default.useContext(f)},t.CONFIG_KEYS={WEB_BASED_BUILDER_URL:"webBasedBuilderUrl",AUTH_TOKEN:"jwt"};(t.RemoteConfigProvider=function RemoteConfigProvider(e){var t=(0,c.useState)(!1),n=(0,u.default)(t,2),o=n[0],a=n[1],s=(0,c.useState)(!1),p=(0,u.default)(s,2),m=p[0],g=p[1],h=(0,c.useState)(!1),y=(0,u.default)(h,2),v=y[0],_=y[1],b=(0,c.useState)({}),w=(0,u.default)(b,2),C=w[0],x=w[1],P=function(){var e=(0,l.default)(i.default.mark((function _callee(){var e;return i.default.wrap((function _callee$(t){for(;;)switch(t.prev=t.next){case 0:return a(!0),_(!1),t.prev=2,t.next=5,(0,d.getRemoteConfig)().finally((function(){g(!0),a(!1)}));case 5:if((e=t.sent).config){t.next=8;break}throw new Error("Invalid remote config");case 8:x(e.config),t.next=16;break;case 11:t.prev=11,t.t0=t.catch(2),_(!0),g(!0),a(!1);case 16:case"end":return t.stop()}}),_callee,null,[[2,11]])})));return function fetchData(){return e.apply(this,arguments)}}();return(0,c.useEffect)((function(){return window.addEventListener("elementor/connect/success",P),function(){window.removeEventListener("elementor/connect/success",P)}}),[]),m||o||P(),c.default.createElement(f.Provider,{value:{isLoading:o,isLoaded:m,isError:v,remoteConfig:C}},e.children)}).propTypes={children:s.default.node.isRequired,onError:s.default.func.isRequired}},3468:(e,t,n)=>{"use strict";var o=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=n(95034),i=o(n(48812));t.default=function useLayoutPrompt(e,t){return(0,i.default)((function(t,n){return t.variationType=e,(0,a.generateLayout)(t,n)}),t)}},69371:(e,t,n)=>{"use strict";var o=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=o(n(61790)),i=o(n(58155)),l=o(n(18821)),u=n(41594),c=o(n(3468));t.default=function useScreenshot(e,t){var n=(0,u.useState)(""),o=(0,l.default)(n,2),s=o[0],d=o[1],f=(0,u.useState)(!1),p=(0,l.default)(f,2),m=p[0],g=p[1],h=(0,c.default)(e,null);return{generate:function generate(e,n){return g(!0),d(""),h.send(e,n).then(function(){var e=(0,i.default)(a.default.mark((function _callee(e){var n;return a.default.wrap((function _callee$(o){for(;;)switch(o.prev=o.next){case 0:return o.next=2,t(e.result);case 2:return(n=o.sent).sendUsageData=function(){return h.sendUsageData(e)},n.baseTemplateId=e.baseTemplateId,n.type=e.type,o.abrupt("return",n);case 7:case"end":return o.stop()}}),_callee)})));return function(t){return e.apply(this,arguments)}}()).catch((function(e){throw d(e.extra_data?e:e.message||e),e})).finally((function(){return g(!1)}))},error:s,isLoading:m}}},85614:(e,t,n)=>{"use strict";var o=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=o(n(61790)),i=o(n(10906)),l=o(n(58155)),u=o(n(18821)),c=n(41594),s=o(n(69371)),d=n(91258),f=n(40128),p={isPending:!0};t.default=function useScreenshots(e){var t=e.onData,n=(0,c.useState)([]),o=(0,u.default)(n,2),m=o[0],g=o[1],h=(0,d.useConfig)().currentContext,y=(0,f.useRequestIds)(),v=y.editorSessionId,_=y.sessionId,b=y.setRequest,w=y.setBatch,C=y.setGenerate,x=(0,c.useRef)(""),P=w(),E=[(0,s.default)(0,t),(0,s.default)(1,t),(0,s.default)(2,t)],O=E.length,S=E.every((function(e){return null==e?void 0:e.error}))?E[0].error:"",T=E.some((function(e){return null==e?void 0:e.isLoading})),R=(0,c.useRef)(null),j=function(){var e=(0,l.default)(a.default.mark((function _callee(e,t){var n,o,l,u;return a.default.wrap((function _callee$(a){for(;;)switch(a.prev=a.next){case 0:return R.current=new AbortController,n=function onGenerate(e){return g((function(t){var n=(0,i.default)(t),o=n.indexOf(p);return n[o]=e,n})),!0},o=function onError(){return g((function(e){var t=(0,i.default)(e),n=t.lastIndexOf(p);return t[n]={isError:!0},t})),!1},l=E.map((function(a){var i=a.generate,l=m.map((function(e){return e.baseTemplateId||""}));return i({prompt:e,prevGeneratedIds:l,currentContext:h,ids:{editorSessionId:v.current,sessionId:_.current,generateId:x.current,batchId:P.current,requestId:b().current},attachments:t.map((function(e){return{type:e.type,content:e.content,label:e.label,source:e.source}}))},R.current.signal).then(n).catch(o)})),a.next=6,Promise.all(l);case 6:u=a.sent,u.every((function(e){return!1===e}))&&g((function(e){var t=(0,i.default)(e);return t.splice(-1*O),t}));case 9:case"end":return a.stop()}}),_callee)})));return function createScreenshots(t,n){return e.apply(this,arguments)}}();return{generate:function generate(e,t){var n=Array(O).fill(p);x.current=C().current,g(n),j(e,t)},regenerate:function regenerate(e,t){var n=Array(O).fill(p);g((function(e){return[].concat((0,i.default)(e),(0,i.default)(n))})),j(e,t)},screenshots:m,isLoading:T,error:S,abort:function abort(){var e;return null===(e=R.current)||void 0===e?void 0:e.abort()}}}},68022:(e,t,n)=>{"use strict";var o=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=t.SCREENSHOTS_PER_PAGE=t.MAX_PAGES=void 0;var a=o(n(18821)),i=n(41594),l=t.SCREENSHOTS_PER_PAGE=3;t.MAX_PAGES=5,t.default=function useSlider(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.slidesCount,n=void 0===t?0:t,o=e.slidesPerPage,u=void 0===o?l:o,c=e.gapPercentage,s=void 0===c?2:c,d=(0,i.useState)(1),f=(0,a.default)(d,2),p=f[0],m=f[1],g=(100-s*(u-1))/u,h=(g+s)*u*(p-1)*-1,y=Math.ceil(n/u);return(0,i.useEffect)((function(){p>1&&p>y&&m(y)}),[y]),{currentPage:p,setCurrentPage:m,pagesCount:y,slidesPerPage:u,gapPercentage:s,offsetXPercentage:h,slideWidthPercentage:g}}},51550:(e,t,n)=>{"use strict";var o=n(96784),a=n(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=_interopRequireWildcard(n(41594)),l=o(n(10906)),u=o(n(40453)),c=o(n(18821)),s=o(n(78304)),d=o(n(62688)),f=n(12470),p=n(86956),m=o(n(43091)),g=o(n(64162)),h=o(n(75690)),y=o(n(85076)),v=o(n(33057)),_=o(n(79919)),b=o(n(85614)),w=_interopRequireWildcard(n(68022)),C=o(n(33724)),x=o(n(53952)),P=n(91258),E=n(38298),O=n(54403),S=n(4353),T=o(n(89958)),R=o(n(94760)),j=n(90291),I=["children"];function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?n:t})(e)}function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=a(e)&&"function"!=typeof e)return{default:e};var n=_getRequireWildcardCache(t);if(n&&n.has(e))return n.get(e);var o={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var l in e)if("default"!==l&&{}.hasOwnProperty.call(e,l)){var u=i?Object.getOwnPropertyDescriptor(e,l):null;u&&(u.get||u.set)?Object.defineProperty(o,l,u):o[l]=e[l]}return o.default=e,n&&n.set(e,o),o}var M=(0,p.withDirection)(C.default),k=(0,p.withDirection)(x.default),A=function RegenerateButton(e){return i.default.createElement(p.Button,(0,s.default)({size:"small",color:"secondary",startIcon:i.default.createElement(v.default,null)},e),(0,f.__)("Regenerate","elementor"))},D=function UseLayoutButton(e){return i.default.createElement(p.Button,(0,s.default)({size:"small",variant:"contained"},e),(0,f.__)("Use Layout","elementor"))};D.propTypes={sx:d.default.object};var L=function isRegenerateButtonDisabled(e,t,n){return!(!t&&!n)||e.length>=w.SCREENSHOTS_PER_PAGE*w.MAX_PAGES},q=function FormLayout(e){var t,n,o=e.DialogHeaderProps,a=void 0===o?{}:o,d=e.DialogContentProps,v=void 0===d?{}:d,C=e.attachments,x=(0,P.useConfig)(),E=x.attachmentsTypes,q=x.onData,W=x.onInsert,B=x.onSelect,N=x.onClose,H=x.onGenerate,U=(0,b.default)({onData:q}),V=U.screenshots,F=U.generate,G=U.regenerate,$=U.isLoading,Y=U.error,z=U.abort,K=(0,w.default)({slidesCount:V.length}),X=K.currentPage,Z=K.setCurrentPage,Q=K.pagesCount,J=K.gapPercentage,ee=K.slidesPerPage,te=K.offsetXPercentage,re=K.slideWidthPercentage,ne=(0,i.useState)(-1),oe=(0,c.default)(ne,2),ae=oe[0],ie=oe[1],le=(0,i.useState)(!1),ue=(0,c.default)(le,2),ce=ue[0],se=ue[1],de=(0,i.useState)(!0),fe=(0,c.default)(de,2),pe=fe[0],me=fe[1],ge=(0,i.useState)([]),he=(0,c.default)(ge,2),ye=he[0],ve=he[1],_e=(0,i.useState)(!1),be=(0,c.default)(_e,2),we=be[0],Ce=be[1],xe=(0,i.useState)(!1),Pe=(0,c.default)(xe,2),Ee=Pe[0],Oe=Pe[1],Se=(0,i.useRef)((function(){})),Te=(0,i.useRef)(null),Re=null===(t=V[ae])||void 0===t?void 0:t.template,je=v.children,Ie=(0,u.default)(v,I),Me=!(!Y||0!==V.length),ke=pe||Me,Ae=function abortAndClose(){z(),N()},De=function onCloseIntent(){if(""!==Te.current.value.trim()||V.length>0)return se(!0);Ae()},Le=function handleScreenshotClick(e,t){return function(){ke||(ie(e),B(t))}},qe=function onAttach(e){e.forEach((function(e){if(!E[e.type])throw new Error("Invalid attachment type: ".concat(e.type));var t=E[e.type];!e.previewHTML&&t.previewGenerator&&t.previewGenerator(e.content).then((function(t){e.previewHTML=t,ve((function(t){return t.map((function(t){return t.content===e.content?e:t}))}))}))})),ve(e),Ce(!1),me(!0)};return(0,i.useEffect)((function(){var e;(null===(e=V[0])||void 0===e?void 0:e.template)&&(B(V[0].template),ie(0))}),[null===(n=V[0])||void 0===n?void 0:n.template]),(0,i.useEffect)((function(){null!=C&&C.length&&qe(C)}),[]),i.default.createElement(h.default,{onClose:De},i.default.createElement(h.default.Header,(0,s.default)({onClose:De},a),a.children,i.default.createElement(p.Tooltip,{title:Ee?(0,f.__)("Expand","elementor"):(0,f.__)("Minimize","elementor")},i.default.createElement(p.IconButton,{size:"small","aria-label":"minimize",onClick:function onClick(){return Oe((function(e){return!e}))}},Ee?i.default.createElement(k,null):i.default.createElement(M,null)))),i.default.createElement(h.default.Content,(0,s.default)({dividers:!0},Ie),i.default.createElement(p.Collapse,{in:!Ee},je&&i.default.createElement(p.Box,{sx:{pt:2,px:2,pb:0}},je),ye.length>0&&i.default.createElement(O.PromptPowerNotice,null),Y&&i.default.createElement(p.Box,{sx:{pt:2,px:2,pb:0}},i.default.createElement(m.default,{error:Y,onRetry:Se.current})),ce&&i.default.createElement(g.default,{open:ce,title:(0,f.__)("Leave Elementor AI?","elementor"),text:(0,f.__)("Your progress will be deleted, and can't be recovered.","elementor"),onClose:Ae,onCancel:function onCancel(){return se(!1)}}),we&&i.default.createElement(T.default,{type:S.ATTACHMENT_TYPE_URL,url:Te.current.value,onAttach:qe,onClose:function onClose(){Ce(!1)}}),i.default.createElement(y.default,{shouldResetPrompt:we,ref:Te,isActive:ke,isLoading:$,showActions:V.length>0||$,attachmentsTypes:E,attachments:ye,onAttach:qe,onDetach:function onDetach(e){ve((function(t){var n=(0,l.default)(t);return n.splice(e,1),n})),me(!0)},onSubmit:function handleGenerate(e,t){e.preventDefault(),""===t.trim()&&0===ye.length||((0,R.default)(t)?Ce(!0):(H(),Se.current=function(){ie(-1),F(t,ye)},Se.current(),me(!1),Z(1)))},onBack:function onBack(){return me(!1)},onEdit:function onEdit(){return me(!0)}}),(V.length>0||$)&&i.default.createElement(i.default.Fragment,null,i.default.createElement(p.Divider,null),i.default.createElement(p.Box,{sx:{p:1.5}},i.default.createElement(p.Box,{sx:{overflow:"hidden",p:.5}},i.default.createElement(p.Box,{sx:{display:"flex",transition:"all 0.4s ease",gap:"".concat(J,"%"),transform:"translateX(".concat(te,"%)")}},V.map((function(e,t){var n=e.screenshot,o=e.type,a=e.template,l=e.isError,u=e.isPending;return i.default.createElement(_.default,{key:t,url:n,type:o,disabled:ke,isPlaceholder:l,isLoading:u,isSelected:ae===t,onClick:Le(t,a),outlineOffset:"2px",sx:{flex:"0 0 ".concat(re,"%")}})})))),i.default.createElement(j.VoicePromotionAlert,{introductionKey:"ai-context-layout-promotion"})),V.length>0&&i.default.createElement(p.Box,{sx:{pt:0,px:2,pb:2},display:"grid",gridTemplateColumns:"repeat(3, 1fr)",justifyItems:"center"},i.default.createElement(A,{onClick:function handleRegenerate(){Se.current=function(){G(Te.current.value,ye),Z(Q+1)},Se.current()},disabled:L(V,$,ke),sx:{justifySelf:"start"}}),V.length>ee&&i.default.createElement(p.Pagination,{page:X,count:Q,disabled:ke,onChange:function onChange(e,t){return Z(t)}}),i.default.createElement(D,{onClick:function applyTemplate(){W(Re),V[ae].sendUsageData(),Ae()},disabled:ke||-1===ae,sx:{justifySelf:"end",gridColumn:3}}))))))};q.propTypes={DialogHeaderProps:d.default.object,DialogContentProps:d.default.object,attachments:d.default.arrayOf(E.AttachmentPropType)};t.default=q},48584:(e,t,n)=>{"use strict";var o=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=o(n(41594)),i=o(n(78304)),l=o(n(40453)),u=n(86956),c=n(12470),s=o(n(62688)),d=o(n(53532)),f=["isLoading"],p=(0,u.withDirection)(d.default),m=function EnhanceButton(e){var t=e.isLoading,n=(0,l.default)(e,f);return a.default.createElement(u.Tooltip,{title:(0,c.__)("Enhance prompt","elementor")},a.default.createElement(u.Box,{component:"span",sx:{cursor:n.disabled?"default":"pointer"}},a.default.createElement(u.IconButton,(0,i.default)({size:"small",color:"secondary"},n),t?a.default.createElement(u.CircularProgress,{color:"secondary",size:20}):a.default.createElement(p,{fontSize:"small"}))))};m.propTypes={disabled:s.default.bool,isLoading:s.default.bool};t.default=m},63223:(e,t,n)=>{"use strict";var o=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=o(n(41594)),i=o(n(78304)),l=n(86956),u=n(12470),c=o(n(62688)),s=function GenerateSubmit(e){return a.default.createElement(l.Button,(0,i.default)({fullWidth:!0,size:"medium",type:"submit",variant:"contained"},e),e.children||(0,u.__)("Generate","elementor"))};s.propTypes={children:c.default.node};t.default=s},62805:(e,t,n)=>{"use strict";var o=n(96784),a=n(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=a(e)&&"function"!=typeof e)return{default:e};var n=_getRequireWildcardCache(t);if(n&&n.has(e))return n.get(e);var o={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var l in e)if("default"!==l&&{}.hasOwnProperty.call(e,l)){var u=i?Object.getOwnPropertyDescriptor(e,l):null;u&&(u.get||u.set)?Object.defineProperty(o,l,u):o[l]=e[l]}return o.default=e,n&&n.set(e,o),o}(n(41594)),l=o(n(61790)),u=o(n(58155)),c=o(n(18821)),s=n(86956),d=n(12470),f=o(n(62688)),p=n(95034),m=n(44048);function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?n:t})(e)}var g=function GetStarted(e){var t=e.onSuccess,n=(0,i.useState)(!1),o=(0,c.default)(n,2),a=o[0],f=o[1],g=function(){var e=(0,u.default)(l.default.mark((function _callee(){return l.default.wrap((function _callee$(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,(0,p.setGetStarted)();case 2:t();case 3:case"end":return e.stop()}}),_callee)})));return function onGetStartedClick(){return e.apply(this,arguments)}}();return i.default.createElement(s.Stack,{alignItems:"center",gap:1.5},i.default.createElement(m.AIIcon,{sx:{color:"text.primary",fontSize:"60px",mb:1}}),i.default.createElement(s.Typography,{variant:"h4",sx:{color:"text.primary"}},(0,d.__)("Step into the future with Elementor AI","elementor")),i.default.createElement(s.Typography,{variant:"body2"},(0,d.__)("Create smarter with AI text and code generators built right into the editor.","elementor")),i.default.createElement(s.Stack,{direction:"row",gap:1.5,alignItems:"flex-start"},i.default.createElement(s.Checkbox,{id:"e-ai-terms-approval",color:"secondary",checked:a,onClick:function onClick(){return f((function(e){return!e}))}}),i.default.createElement(s.Stack,null,i.default.createElement(s.Typography,{variant:"caption",sx:{maxWidth:520},component:"label",htmlFor:"e-ai-terms-approval"},(0,d.__)("I approve the ","elementor"),i.default.createElement(s.Link,{href:"https://go.elementor.com/ai-terms/",target:"_blank",color:"info.main"},(0,d.__)("Terms of Service","elementor"))," & ",i.default.createElement(s.Link,{href:"https://go.elementor.com/ai-privacy-policy/",target:"_blank",color:"info.main"},(0,d.__)("Privacy Policy","elementor")),(0,d.__)(" of the Elementor AI service.","elementor"),i.default.createElement("br",null),(0,d.__)("This includes consenting to the collection and use of data to improve user experience.","elementor")))),i.default.createElement(s.Button,{disabled:!a,variant:"contained",onClick:g,sx:{mt:1,"&:hover":{color:"primary.contrastText"}}},(0,d.__)("Get Started","elementor")))};g.propTypes={onSuccess:f.default.func.isRequired};t.default=g},38298:(e,t,n)=>{"use strict";var o=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.AttachmentsTypesPropType=t.AttachmentPropType=void 0;var a=o(n(62688));t.AttachmentPropType=a.default.shape({type:a.default.string,previewHTML:a.default.string,content:a.default.string,label:a.default.string,source:a.default.string}),t.AttachmentsTypesPropType=a.default.shape({type:a.default.shape({promptPlaceholder:a.default.string,promptSuggestions:a.default.arrayOf(a.default.shape({text:a.default.string.isRequired})),previewGenerator:a.default.func})})},36833:(e,t,n)=>{"use strict";var o=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.renderLayoutApp=t.openPanel=t.onConnect=t.importToEditor=t.getUiConfig=t.closePanel=t.WEB_BASED_PROMPTS=t.VARIATIONS_PROMPTS=void 0;var a=o(n(41594)),i=o(n(61790)),l=o(n(58155)),u=o(n(18791)),c=n(74561),s=o(n(93569)),d=n(47547),f=n(40327),p=n(12470),m=o(n(47407)),g=n(40128),h=t.closePanel=function closePanel(){$e.run("panel/close"),$e.components.get("panel").blockUserInteractions()},y=t.openPanel=function openPanel(){$e.run("panel/open"),$e.components.get("panel").unblockUserInteractions()},v=t.onConnect=function onConnect(e){elementorCommon.config.library_connect.is_connected=!0,elementorCommon.config.library_connect.current_access_level=e.kits_access_level||e.access_level||0,elementorCommon.config.library_connect.current_access_tier=e.access_tier},_=t.getUiConfig=function getUiConfig(){var e,t;return{colorScheme:(null===(e=elementor)||void 0===e||null===(t=e.getPreferences)||void 0===t?void 0:t.call(e,"ui_theme"))||"auto",isRTL:elementorCommon.config.isRTL}},b=t.VARIATIONS_PROMPTS=[{text:(0,p.__)("Minimalist design with bold typography about","elementor")},{text:(0,p.__)("Elegant style with serif fonts discussing","elementor")},{text:(0,p.__)("Retro vibe with muted colors and classic fonts about","elementor")},{text:(0,p.__)("Futuristic design with neon accents about","elementor")},{text:(0,p.__)("Professional look with clean lines for","elementor")},{text:(0,p.__)("Earthy tones and organic shapes featuring","elementor")},{text:(0,p.__)("Luxurious theme with rich colors discussing","elementor")},{text:(0,p.__)("Tech-inspired style with modern fonts about","elementor")},{text:(0,p.__)("Warm hues with comforting visuals about","elementor")}],w=t.WEB_BASED_PROMPTS=[{text:(0,p.__)("Change the content to be about [topic]","elementor")},{text:(0,p.__)("Generate lorem ipsum placeholder text for all paragraphs","elementor")},{text:(0,p.__)("Revise the content to focus on [topic] and then translate it into Spanish","elementor")},{text:(0,p.__)("Shift the focus of the content to [topic] in order to showcase our company's mission and values","elementor")},{text:(0,p.__)("Alter the content to provide helpful tips related to [topic]","elementor")},{text:(0,p.__)("Adjust the content to include FAQs and answers for common inquiries about [topic]","elementor")}],C=(0,p.__)("Press '/' for suggestions or describe the changes you want to apply (optional)...","elementor");t.renderLayoutApp=function renderLayoutApp(){var e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{parentContainer:null,mode:"",at:null,onClose:null,onGenerate:null,onInsert:null,onRenderApp:null,onSelect:null,attachments:[]};h();var n=(0,c.createPreviewContainer)(t.parentContainer,{at:t.at}),o=_(),f=o.colorScheme,p=o.isRTL,g=document.createElement("div");document.body.append(g);var x,P=window.elementorFrontend.elements.$window[0].getComputedStyle(window.elementorFrontend.elements.$body[0]),E=u.default.render(a.default.createElement(m.default,{isRTL:p,colorScheme:f},a.default.createElement(s.default,{mode:t.mode,currentContext:{body:{backgroundColor:P.backgroundColor,backgroundImage:P.backgroundImage}},attachmentsTypes:{json:{promptSuggestions:b,promptPlaceholder:C,previewGenerator:(x=(0,l.default)(i.default.mark((function _callee(e){var t;return i.default.wrap((function _callee$(n){for(;;)switch(n.prev=n.next){case 0:return n.next=2,(0,d.takeScreenshot)(e);case 2:return t=n.sent,n.abrupt("return",'<img src="'.concat(t,'" />'));case 4:case"end":return n.stop()}}),_callee)}))),function previewGenerator(e){return x.apply(this,arguments)})},url:{promptPlaceholder:C,promptSuggestions:w}},attachments:t.attachments||[],onClose:function onClose(){var e;n.destroy(),null===(e=t.onClose)||void 0===e||e.call(t),O(),g.remove(),y()},onConnect:v,onGenerate:function onGenerate(){var e;null===(e=t.onGenerate)||void 0===e||e.call(t,{previewContainer:n})},onData:function(){var e=(0,l.default)(i.default.mark((function _callee2(e){var t;return i.default.wrap((function _callee2$(n){for(;;)switch(n.prev=n.next){case 0:return n.next=2,(0,d.takeScreenshot)(e);case 2:return t=n.sent,n.abrupt("return",{screenshot:t,template:e});case 4:case"end":return n.stop()}}),_callee2)})));return function(t){return e.apply(this,arguments)}}(),onSelect:function onSelect(e){var o;null===(o=t.onSelect)||void 0===o||o.call(t),n.setContent(e)},onInsert:t.onInsert,hasPro:elementor.helpers.hasPro()})),g),O=E.unmount;null===(e=t.onRenderApp)||void 0===e||e.call(t,{previewContainer:n})},t.importToEditor=function importToEditor(e){var t=e.parentContainer,n=e.at,o=e.template,a=e.historyTitle,i=e.replace,l=void 0!==i&&i,u=(0,f.startHistoryLog)({type:"import",title:a});l&&$e.run("document/elements/delete",{container:t.children.at(n)}),$e.run("document/elements/create",{container:t,model:(0,g.generateIds)(o),options:{at:n,edit:!0}}),u()}},40327:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.startHistoryLog=function startHistoryLog(e){var t=e.type,n=e.title,o=$e.internal("document/history/start-log",{type:t,title:n});return function(){return $e.internal("document/history/end-log",{id:o})}},t.toggleHistory=function toggleHistory(e){elementor.documents.getCurrent().history.setActive(e)}},74561:(e,t,n)=>{"use strict";var o=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.createPreviewContainer=function createPreviewContainer(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=new Map,o=function createIdleContainer(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=createContainer(e,{elType:"container"},t);return n.view.$el.addClass(s),n}(e,t);function getAllContainers(){return[].concat((0,i.default)(n.values()),[o])}return{init:function init(){showContainer(o)},reset:function reset(){deleteContainers((0,i.default)(n.values())),n.clear(),showContainer(o)},setContent:function setContent(o){if(o){if(function hideContainers(e){e.forEach((function(e){e.view.$el.addClass(c)}))}(getAllContainers()),!n.has(o)){var a=createContainer(e,o,t);n.set(o,a)}showContainer(n.get(o))}},destroy:function destroy(){deleteContainers(getAllContainers()),n.clear()}}};var a=o(n(85707)),i=o(n(10906)),l=n(40327);function ownKeys(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(n),!0).forEach((function(t){(0,a.default)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ownKeys(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var u="e-ai-preview-container",c=u+"--hidden",s=u+"--idle";function createContainer(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};(0,l.toggleHistory)(!1);var o=$e.run("document/elements/create",{container:e,model:_objectSpread(_objectSpread({},t),{},{id:"".concat(u,"-").concat(elementorCommon.helpers.getUniqueId().toString())}),options:_objectSpread(_objectSpread({},n),{},{edit:!1})});return(0,l.toggleHistory)(!0),o.view.$el.addClass(c),o}function showContainer(e){e.view.$el.removeClass(c),setTimeout((function(){e.view.$el[0].scrollIntoView({behavior:"smooth",block:"start"})}))}function deleteContainers(e){(0,l.toggleHistory)(!1),$e.run("document/elements/delete",{containers:e}),(0,l.toggleHistory)(!0)}},47547:(e,t,n)=>{"use strict";var o=n(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.takeScreenshot=void 0;var a=o(n(61790)),i=o(n(10906)),l=o(n(58155)),u=n(45549),c=n(40327),s=n(40128);t.takeScreenshot=function(){var e=(0,l.default)(a.default.mark((function _callee(e){var t,n,o;return a.default.wrap((function _callee$(a){for(;;)switch(a.prev=a.next){case 0:if(e){a.next=2;break}return a.abrupt("return","");case 2:return(0,c.toggleHistory)(!1),t=createHiddenWrapper(),wrapContainer(n=createContainer(e),t),elementor.getPreviewView().$childViewContainer[0].appendChild(t),a.next=9,waitForContainer(n.id);case 9:if(!e.elements.length){a.next=12;break}return a.next=12,Promise.all(e.elements.map((function(e){return waitForContainer(e.id)})));case 12:return a.prev=12,a.next=15,function toWebp(e){return _toWebp.apply(this,arguments)}(n.view.$el[0],{quality:.01,imagePlaceholder:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII="});case 15:o=a.sent,a.next=21;break;case 18:a.prev=18,a.t0=a.catch(12),o="";case 21:return deleteContainer(n),t.remove(),(0,c.toggleHistory)(!0),a.abrupt("return",o);case 25:case"end":return a.stop()}}),_callee,null,[[12,18]])})));return function takeScreenshot(t){return e.apply(this,arguments)}}();function _toWebp(){return _toWebp=(0,l.default)(a.default.mark((function _callee3(e){var t,n,o,i=arguments;return a.default.wrap((function _callee3$(a){for(;;)switch(a.prev=a.next){case 0:return n=i.length>1&&void 0!==i[1]?i[1]:{},a.next=3,(0,u.toCanvas)(e,n);case 3:return o=a.sent,a.abrupt("return",o.toDataURL("image/webp",null!==(t=n.quality)&&void 0!==t?t:1));case 5:case"end":return a.stop()}}),_callee3)}))),_toWebp.apply(this,arguments)}function createHiddenWrapper(){var e=document.createElement("div");return e.style.position="fixed",e.style.opacity="0",e.style.inset="0",e}function createContainer(e){var t=(0,s.generateIds)(e);return t.id="e-ai-screenshot-container-".concat(t.id),$e.run("document/elements/create",{container:elementor.getPreviewContainer(),model:t,options:{edit:!1}})}function deleteContainer(e){return $e.run("document/elements/delete",{container:e})}function waitForContainer(e){var t=function sleep(e){return new Promise((function(t){return setTimeout(t,e)}))}(arguments.length>1&&void 0!==arguments[1]?arguments[1]:5e3),n=new Promise((function(t){elementorFrontend.hooks.addAction("frontend/element_ready/global",function(){var n=(0,l.default)(a.default.mark((function _callee2(n){var o;return a.default.wrap((function _callee2$(a){for(;;)switch(a.prev=a.next){case 0:if(n.data("id")!==e){a.next=5;break}return o=(0,i.default)(n[0].querySelectorAll("img")),a.next=4,Promise.all(o.map(waitForImage));case 4:t();case 5:case"end":return a.stop()}}),_callee2)})));return function(e){return n.apply(this,arguments)}}())}));return Promise.any([t,n])}function waitForImage(e){return e.complete?Promise.resolve():new Promise((function(t){e.addEventListener("load",t),e.addEventListener("error",(function(){e.remove(),t()}))}))}function wrapContainer(e,t){var n=e.view.$el[0];n.parentNode.insertBefore(t,n),t.appendChild(n)}},45549:(e,t,n)=>{"use strict";n.r(t),n.d(t,{getFontEmbedCSS:()=>getFontEmbedCSS,toBlob:()=>toBlob,toCanvas:()=>toCanvas,toJpeg:()=>toJpeg,toPixelData:()=>toPixelData,toPng:()=>toPng,toSvg:()=>toSvg});const o=(()=>{let e=0;return()=>(e+=1,`u${`0000${(Math.random()*36**4|0).toString(36)}`.slice(-4)}${e}`)})();function toArray(e){const t=[];for(let n=0,o=e.length;n<o;n++)t.push(e[n]);return t}function px(e,t){const n=(e.ownerDocument.defaultView||window).getComputedStyle(e).getPropertyValue(t);return n?parseFloat(n.replace("px","")):0}function getImageSize(e,t={}){return{width:t.width||function getNodeWidth(e){const t=px(e,"border-left-width"),n=px(e,"border-right-width");return e.clientWidth+t+n}(e),height:t.height||function getNodeHeight(e){const t=px(e,"border-top-width"),n=px(e,"border-bottom-width");return e.clientHeight+t+n}(e)}}const a=16384;function createImage(e){return new Promise(((t,n)=>{const o=new Image;o.decode=()=>t(o),o.onload=()=>t(o),o.onerror=n,o.crossOrigin="anonymous",o.decoding="async",o.src=e}))}async function nodeToDataURL(e,t,n){const o="http://www.w3.org/2000/svg",a=document.createElementNS(o,"svg"),i=document.createElementNS(o,"foreignObject");return a.setAttribute("width",`${t}`),a.setAttribute("height",`${n}`),a.setAttribute("viewBox",`0 0 ${t} ${n}`),i.setAttribute("width","100%"),i.setAttribute("height","100%"),i.setAttribute("x","0"),i.setAttribute("y","0"),i.setAttribute("externalResourcesRequired","true"),a.appendChild(i),i.appendChild(e),async function svgToDataURL(e){return Promise.resolve().then((()=>(new XMLSerializer).serializeToString(e))).then(encodeURIComponent).then((e=>`data:image/svg+xml;charset=utf-8,${e}`))}(a)}const isInstanceOfElement=(e,t)=>{if(e instanceof t)return!0;const n=Object.getPrototypeOf(e);return null!==n&&(n.constructor.name===t.name||isInstanceOfElement(n,t))};function getPseudoElementStyle(e,t,n){const o=`.${e}:${t}`,a=n.cssText?function formatCSSText(e){const t=e.getPropertyValue("content");return`${e.cssText} content: '${t.replace(/'|"/g,"")}';`}(n):function formatCSSProperties(e){return toArray(e).map((t=>`${t}: ${e.getPropertyValue(t)}${e.getPropertyPriority(t)?" !important":""};`)).join(" ")}(n);return document.createTextNode(`${o}{${a}}`)}function clonePseudoElement(e,t,n){const a=window.getComputedStyle(e,n),i=a.getPropertyValue("content");if(""===i||"none"===i)return;const l=o();try{t.className=`${t.className} ${l}`}catch(e){return}const u=document.createElement("style");u.appendChild(getPseudoElementStyle(l,n,a)),t.appendChild(u)}const i="application/font-woff",l="image/jpeg",u={woff:i,woff2:i,ttf:"application/font-truetype",eot:"application/vnd.ms-fontobject",png:"image/png",jpg:l,jpeg:l,gif:"image/gif",tiff:"image/tiff",svg:"image/svg+xml",webp:"image/webp"};function getMimeType(e){const t=function getExtension(e){const t=/\.([^./]*?)$/g.exec(e);return t?t[1]:""}(e).toLowerCase();return u[t]||""}function isDataUrl(e){return-1!==e.search(/^(data:)/)}function makeDataUrl(e,t){return`data:${t};base64,${e}`}async function fetchAsDataURL(e,t,n){const o=await fetch(e,t);if(404===o.status)throw new Error(`Resource "${o.url}" not found`);const a=await o.blob();return new Promise(((e,t)=>{const i=new FileReader;i.onerror=t,i.onloadend=()=>{try{e(n({res:o,result:i.result}))}catch(e){t(e)}},i.readAsDataURL(a)}))}const c={};async function resourceToDataURL(e,t,n){const o=function getCacheKey(e,t,n){let o=e.replace(/\?.*/,"");return n&&(o=e),/ttf|otf|eot|woff2?/i.test(o)&&(o=o.replace(/.*\//,"")),t?`[${t}]${o}`:o}(e,t,n.includeQueryParams);if(null!=c[o])return c[o];let a;n.cacheBust&&(e+=(/\?/.test(e)?"&":"?")+(new Date).getTime());try{const o=await fetchAsDataURL(e,n.fetchRequestInit,(({res:e,result:n})=>(t||(t=e.headers.get("Content-Type")||""),function getContentFromDataUrl(e){return e.split(/,/)[1]}(n))));a=makeDataUrl(o,t)}catch(t){a=n.imagePlaceholder||"";let o=`Failed to fetch resource: ${e}`;t&&(o="string"==typeof t?t:t.message),o&&console.warn(o)}return c[o]=a,a}async function cloneSingleNode(e,t){return isInstanceOfElement(e,HTMLCanvasElement)?async function cloneCanvasElement(e){const t=e.toDataURL();return"data:,"===t?e.cloneNode(!1):createImage(t)}(e):isInstanceOfElement(e,HTMLVideoElement)?async function cloneVideoElement(e,t){if(e.currentSrc){const t=document.createElement("canvas"),n=t.getContext("2d");return t.width=e.clientWidth,t.height=e.clientHeight,null==n||n.drawImage(e,0,0,t.width,t.height),createImage(t.toDataURL())}const n=e.poster,o=getMimeType(n);return createImage(await resourceToDataURL(n,o,t))}(e,t):isInstanceOfElement(e,HTMLIFrameElement)?async function cloneIFrameElement(e){var t;try{if(null===(t=null==e?void 0:e.contentDocument)||void 0===t?void 0:t.body)return await cloneNode(e.contentDocument.body,{},!0)}catch(e){}return e.cloneNode(!1)}(e):e.cloneNode(!1)}const isSlotElement=e=>null!=e.tagName&&"SLOT"===e.tagName.toUpperCase();function decorate(e,t){return isInstanceOfElement(t,Element)&&(!function cloneCSSStyle(e,t){const n=t.style;if(!n)return;const o=window.getComputedStyle(e);o.cssText?(n.cssText=o.cssText,n.transformOrigin=o.transformOrigin):toArray(o).forEach((a=>{let i=o.getPropertyValue(a);if("font-size"===a&&i.endsWith("px")){const e=Math.floor(parseFloat(i.substring(0,i.length-2)))-.1;i=`${e}px`}isInstanceOfElement(e,HTMLIFrameElement)&&"display"===a&&"inline"===i&&(i="block"),"d"===a&&t.getAttribute("d")&&(i=`path(${t.getAttribute("d")})`),n.setProperty(a,i,o.getPropertyPriority(a))}))}(e,t),function clonePseudoElements(e,t){clonePseudoElement(e,t,":before"),clonePseudoElement(e,t,":after")}(e,t),function cloneInputValue(e,t){isInstanceOfElement(e,HTMLTextAreaElement)&&(t.innerHTML=e.value),isInstanceOfElement(e,HTMLInputElement)&&t.setAttribute("value",e.value)}(e,t),function cloneSelectValue(e,t){if(isInstanceOfElement(e,HTMLSelectElement)){const n=t,o=Array.from(n.children).find((t=>e.value===t.getAttribute("value")));o&&o.setAttribute("selected","")}}(e,t)),t}async function cloneNode(e,t,n){return n||!t.filter||t.filter(e)?Promise.resolve(e).then((e=>cloneSingleNode(e,t))).then((n=>async function cloneChildren(e,t,n){var o,a;let i=[];return i=isSlotElement(e)&&e.assignedNodes?toArray(e.assignedNodes()):isInstanceOfElement(e,HTMLIFrameElement)&&(null===(o=e.contentDocument)||void 0===o?void 0:o.body)?toArray(e.contentDocument.body.childNodes):toArray((null!==(a=e.shadowRoot)&&void 0!==a?a:e).childNodes),0===i.length||isInstanceOfElement(e,HTMLVideoElement)||await i.reduce(((e,o)=>e.then((()=>cloneNode(o,n))).then((e=>{e&&t.appendChild(e)}))),Promise.resolve()),t}(e,n,t))).then((t=>decorate(e,t))).then((e=>async function ensureSVGSymbols(e,t){const n=e.querySelectorAll?e.querySelectorAll("use"):[];if(0===n.length)return e;const o={};for(let a=0;a<n.length;a++){const i=n[a].getAttribute("xlink:href");if(i){const n=e.querySelector(i),a=document.querySelector(i);n||!a||o[i]||(o[i]=await cloneNode(a,t,!0))}}const a=Object.values(o);if(a.length){const t="http://www.w3.org/1999/xhtml",n=document.createElementNS(t,"svg");n.setAttribute("xmlns",t),n.style.position="absolute",n.style.width="0",n.style.height="0",n.style.overflow="hidden",n.style.display="none";const o=document.createElementNS(t,"defs");n.appendChild(o);for(let e=0;e<a.length;e++)o.appendChild(a[e]);e.appendChild(n)}return e}(e,t))):null}const s=/url\((['"]?)([^'"]+?)\1\)/g,d=/url\([^)]+\)\s*format\((["']?)([^"']+)\1\)/g,f=/src:\s*(?:url\([^)]+\)\s*format\([^)]+\)[,;]\s*)+/g;async function embed_resources_embed(e,t,n,o,a){try{const i=n?function resolveUrl(e,t){if(e.match(/^[a-z]+:\/\//i))return e;if(e.match(/^\/\//))return window.location.protocol+e;if(e.match(/^[a-z]+:/i))return e;const n=document.implementation.createHTMLDocument(),o=n.createElement("base"),a=n.createElement("a");return n.head.appendChild(o),n.body.appendChild(a),t&&(o.href=t),a.href=e,a.href}(t,n):t,l=getMimeType(t);let u;if(a){u=makeDataUrl(await a(i),l)}else u=await resourceToDataURL(i,l,o);return e.replace(function toRegex(e){const t=e.replace(/([.*+?^${}()|\[\]\/\\])/g,"\\$1");return new RegExp(`(url\\(['"]?)(${t})(['"]?\\))`,"g")}(t),`$1${u}$3`)}catch(e){}return e}function shouldEmbed(e){return-1!==e.search(s)}async function embedResources(e,t,n){if(!shouldEmbed(e))return e;const o=function filterPreferredFontFormat(e,{preferredFontFormat:t}){return t?e.replace(f,(e=>{for(;;){const[n,,o]=d.exec(e)||[];if(!o)return"";if(o===t)return`src: ${n};`}})):e}(e,n),a=function parseURLs(e){const t=[];return e.replace(s,((e,n,o)=>(t.push(o),e))),t.filter((e=>!isDataUrl(e)))}(o);return a.reduce(((e,o)=>e.then((e=>embed_resources_embed(e,o,t,n)))),Promise.resolve(o))}async function embedProp(e,t,n){var o;const a=null===(o=t.style)||void 0===o?void 0:o.getPropertyValue(e);if(a){const o=await embedResources(a,null,n);return t.style.setProperty(e,o,t.style.getPropertyPriority(e)),!0}return!1}async function embedImages(e,t){isInstanceOfElement(e,Element)&&(await async function embedBackground(e,t){await embedProp("background",e,t)||await embedProp("background-image",e,t),await embedProp("mask",e,t)||await embedProp("mask-image",e,t)}(e,t),await async function embedImageNode(e,t){const n=isInstanceOfElement(e,HTMLImageElement);if((!n||isDataUrl(e.src))&&(!isInstanceOfElement(e,SVGImageElement)||isDataUrl(e.href.baseVal)))return;const o=n?e.src:e.href.baseVal,a=await resourceToDataURL(o,getMimeType(o),t);await new Promise(((t,o)=>{e.onload=t,e.onerror=o;const i=e;i.decode&&(i.decode=t),"lazy"===i.loading&&(i.loading="eager"),n?(e.srcset="",e.src=a):e.href.baseVal=a}))}(e,t),await async function embedChildren(e,t){const n=toArray(e.childNodes).map((e=>embedImages(e,t)));await Promise.all(n).then((()=>e))}(e,t))}const p={};async function fetchCSS(e){let t=p[e];if(null!=t)return t;const n=await fetch(e);return t={url:e,cssText:await n.text()},p[e]=t,t}async function embedFonts(e,t){let n=e.cssText;const o=/url\(["']?([^"')]+)["']?\)/g,a=(n.match(/url\([^)]+\)/g)||[]).map((async a=>{let i=a.replace(o,"$1");return i.startsWith("https://")||(i=new URL(i,e.url).href),fetchAsDataURL(i,t.fetchRequestInit,(({result:e})=>(n=n.replace(a,`url(${e})`),[a,e])))}));return Promise.all(a).then((()=>n))}function parseCSS(e){if(null==e)return[];const t=[];let n=e.replace(/(\/\*[\s\S]*?\*\/)/gi,"");const o=new RegExp("((@.*?keyframes [\\s\\S]*?){([\\s\\S]*?}\\s*?)})","gi");for(;;){const e=o.exec(n);if(null===e)break;t.push(e[0])}n=n.replace(o,"");const a=/@import[\s\S]*?url\([^)]*\)[\s\S]*?;/gi,i=new RegExp("((\\s*?(?:\\/\\*[\\s\\S]*?\\*\\/)?\\s*?@media[\\s\\S]*?){([\\s\\S]*?)}\\s*?})|(([\\s\\S]*?){([\\s\\S]*?)})","gi");for(;;){let e=a.exec(n);if(null===e){if(e=i.exec(n),null===e)break;a.lastIndex=i.lastIndex}else i.lastIndex=a.lastIndex;t.push(e[0])}return t}async function parseWebFontRules(e,t){if(null==e.ownerDocument)throw new Error("Provided element is not within a Document");const n=toArray(e.ownerDocument.styleSheets),o=await async function getCSSRules(e,t){const n=[],o=[];return e.forEach((n=>{if("cssRules"in n)try{toArray(n.cssRules||[]).forEach(((e,a)=>{if(e.type===CSSRule.IMPORT_RULE){let i=a+1;const l=fetchCSS(e.href).then((e=>embedFonts(e,t))).then((e=>parseCSS(e).forEach((e=>{try{n.insertRule(e,e.startsWith("@import")?i+=1:n.cssRules.length)}catch(t){console.error("Error inserting rule from remote css",{rule:e,error:t})}})))).catch((e=>{console.error("Error loading remote css",e.toString())}));o.push(l)}}))}catch(a){const i=e.find((e=>null==e.href))||document.styleSheets[0];null!=n.href&&o.push(fetchCSS(n.href).then((e=>embedFonts(e,t))).then((e=>parseCSS(e).forEach((e=>{i.insertRule(e,n.cssRules.length)})))).catch((e=>{console.error("Error loading remote stylesheet",e)}))),console.error("Error inlining remote css file",a)}})),Promise.all(o).then((()=>(e.forEach((e=>{if("cssRules"in e)try{toArray(e.cssRules||[]).forEach((e=>{n.push(e)}))}catch(t){console.error(`Error while reading CSS rules from ${e.href}`,t)}})),n)))}(n,t);return function getWebFontRules(e){return e.filter((e=>e.type===CSSRule.FONT_FACE_RULE)).filter((e=>shouldEmbed(e.style.getPropertyValue("src"))))}(o)}async function getWebFontCSS(e,t){const n=await parseWebFontRules(e,t);return(await Promise.all(n.map((e=>{const n=e.parentStyleSheet?e.parentStyleSheet.href:null;return embedResources(e.cssText,n,t)})))).join("\n")}async function toSvg(e,t={}){const{width:n,height:o}=getImageSize(e,t),a=await cloneNode(e,t,!0);await async function embedWebFonts(e,t){const n=null!=t.fontEmbedCSS?t.fontEmbedCSS:t.skipFonts?null:await getWebFontCSS(e,t);if(n){const t=document.createElement("style"),o=document.createTextNode(n);t.appendChild(o),e.firstChild?e.insertBefore(t,e.firstChild):e.appendChild(t)}}(a,t),await embedImages(a,t),function applyStyle(e,t){const{style:n}=e;t.backgroundColor&&(n.backgroundColor=t.backgroundColor),t.width&&(n.width=`${t.width}px`),t.height&&(n.height=`${t.height}px`);const o=t.style;return null!=o&&Object.keys(o).forEach((e=>{n[e]=o[e]})),e}(a,t);return await nodeToDataURL(a,n,o)}async function toCanvas(e,t={}){const{width:n,height:o}=getImageSize(e,t),i=await toSvg(e,t),l=await createImage(i),u=document.createElement("canvas"),c=u.getContext("2d"),s=t.pixelRatio||function getPixelRatio(){let e,t;try{t=process}catch(e){}const n=t&&t.env?t.env.devicePixelRatio:null;return n&&(e=parseInt(n,10),Number.isNaN(e)&&(e=1)),e||window.devicePixelRatio||1}(),d=t.canvasWidth||n,f=t.canvasHeight||o;return u.width=d*s,u.height=f*s,t.skipAutoScale||function checkCanvasDimensions(e){(e.width>a||e.height>a)&&(e.width>a&&e.height>a?e.width>e.height?(e.height*=a/e.width,e.width=a):(e.width*=a/e.height,e.height=a):e.width>a?(e.height*=a/e.width,e.width=a):(e.width*=a/e.height,e.height=a))}(u),u.style.width=`${d}`,u.style.height=`${f}`,t.backgroundColor&&(c.fillStyle=t.backgroundColor,c.fillRect(0,0,u.width,u.height)),c.drawImage(l,0,0,u.width,u.height),u}async function toPixelData(e,t={}){const{width:n,height:o}=getImageSize(e,t);return(await toCanvas(e,t)).getContext("2d").getImageData(0,0,n,o).data}async function toPng(e,t={}){return(await toCanvas(e,t)).toDataURL()}async function toJpeg(e,t={}){return(await toCanvas(e,t)).toDataURL("image/jpeg",t.quality||1)}async function toBlob(e,t={}){const n=await toCanvas(e,t),o=await function canvasToBlob(e,t={}){return e.toBlob?new Promise((n=>{e.toBlob(n,t.type?t.type:"image/png",t.quality?t.quality:1)})):new Promise((n=>{const o=window.atob(e.toDataURL(t.type?t.type:void 0,t.quality?t.quality:void 0).split(",")[1]),a=o.length,i=new Uint8Array(a);for(let e=0;e<a;e+=1)i[e]=o.charCodeAt(e);n(new Blob([i],{type:t.type?t.type:"image/png"}))}))}(n);return o}async function getFontEmbedCSS(e,t={}){return getWebFontCSS(e,t)}},40362:(e,t,n)=>{"use strict";var o=n(56441);function emptyFunction(){}function emptyFunctionWithReset(){}emptyFunctionWithReset.resetWarningCache=emptyFunction,e.exports=function(){function shim(e,t,n,a,i,l){if(l!==o){var u=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw u.name="Invariant Violation",u}}function getShim(){return shim}shim.isRequired=shim;var e={array:shim,bigint:shim,bool:shim,func:shim,number:shim,object:shim,string:shim,symbol:shim,any:shim,arrayOf:getShim,element:shim,elementType:shim,instanceOf:getShim,node:shim,objectOf:getShim,oneOf:getShim,oneOfType:getShim,shape:getShim,exact:getShim,checkPropTypes:emptyFunctionWithReset,resetWarningCache:emptyFunction};return e.PropTypes=e,e}},62688:(e,t,n)=>{e.exports=n(40362)()},56441:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},7470:(e,t,n)=>{"use strict";var o=n(75206);t.createRoot=o.createRoot,t.hydrateRoot=o.hydrateRoot},9111:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"DraggableCore",{enumerable:!0,get:function(){return d.default}}),t.default=void 0;var o=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var n=_getRequireWildcardCache(t);if(n&&n.has(e))return n.get(e);var o={},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&Object.prototype.hasOwnProperty.call(e,i)){var l=a?Object.getOwnPropertyDescriptor(e,i):null;l&&(l.get||l.set)?Object.defineProperty(o,i,l):o[i]=e[i]}o.default=e,n&&n.set(e,o);return o}(n(41594)),a=_interopRequireDefault(n(62688)),i=_interopRequireDefault(n(75206)),l=_interopRequireDefault(n(38262)),u=n(32837),c=n(10402),s=n(26732),d=_interopRequireDefault(n(11060)),f=_interopRequireDefault(n(57988));function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(_getRequireWildcardCache=function(e){return e?n:t})(e)}function _extends(){return _extends=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},_extends.apply(this,arguments)}function _defineProperty(e,t,n){return(t=function _toPropertyKey(e){var t=function _toPrimitive(e,t){if("object"!=typeof e||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=typeof o)return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class Draggable extends o.Component{static getDerivedStateFromProps(e,t){let{position:n}=e,{prevPropsPosition:o}=t;return!n||o&&n.x===o.x&&n.y===o.y?null:((0,f.default)("Draggable: getDerivedStateFromProps %j",{position:n,prevPropsPosition:o}),{x:n.x,y:n.y,prevPropsPosition:{...n}})}constructor(e){super(e),_defineProperty(this,"onDragStart",((e,t)=>{(0,f.default)("Draggable: onDragStart: %j",t);if(!1===this.props.onStart(e,(0,c.createDraggableData)(this,t)))return!1;this.setState({dragging:!0,dragged:!0})})),_defineProperty(this,"onDrag",((e,t)=>{if(!this.state.dragging)return!1;(0,f.default)("Draggable: onDrag: %j",t);const n=(0,c.createDraggableData)(this,t),o={x:n.x,y:n.y,slackX:0,slackY:0};if(this.props.bounds){const{x:e,y:t}=o;o.x+=this.state.slackX,o.y+=this.state.slackY;const[a,i]=(0,c.getBoundPosition)(this,o.x,o.y);o.x=a,o.y=i,o.slackX=this.state.slackX+(e-o.x),o.slackY=this.state.slackY+(t-o.y),n.x=o.x,n.y=o.y,n.deltaX=o.x-this.state.x,n.deltaY=o.y-this.state.y}if(!1===this.props.onDrag(e,n))return!1;this.setState(o)})),_defineProperty(this,"onDragStop",((e,t)=>{if(!this.state.dragging)return!1;if(!1===this.props.onStop(e,(0,c.createDraggableData)(this,t)))return!1;(0,f.default)("Draggable: onDragStop: %j",t);const n={dragging:!1,slackX:0,slackY:0};if(Boolean(this.props.position)){const{x:e,y:t}=this.props.position;n.x=e,n.y=t}this.setState(n)})),this.state={dragging:!1,dragged:!1,x:e.position?e.position.x:e.defaultPosition.x,y:e.position?e.position.y:e.defaultPosition.y,prevPropsPosition:{...e.position},slackX:0,slackY:0,isElementSVG:!1},!e.position||e.onDrag||e.onStop||console.warn("A `position` was applied to this <Draggable>, without drag handlers. This will make this component effectively undraggable. Please attach `onDrag` or `onStop` handlers so you can adjust the `position` of this element.")}componentDidMount(){void 0!==window.SVGElement&&this.findDOMNode()instanceof window.SVGElement&&this.setState({isElementSVG:!0})}componentWillUnmount(){this.setState({dragging:!1})}findDOMNode(){var e,t;return null!==(e=null===(t=this.props)||void 0===t||null===(t=t.nodeRef)||void 0===t?void 0:t.current)&&void 0!==e?e:i.default.findDOMNode(this)}render(){const{axis:e,bounds:t,children:n,defaultPosition:a,defaultClassName:i,defaultClassNameDragging:s,defaultClassNameDragged:f,position:p,positionOffset:m,scale:g,...h}=this.props;let y={},v=null;const _=!Boolean(p)||this.state.dragging,b=p||a,w={x:(0,c.canDragX)(this)&&_?this.state.x:b.x,y:(0,c.canDragY)(this)&&_?this.state.y:b.y};this.state.isElementSVG?v=(0,u.createSVGTransform)(w,m):y=(0,u.createCSSTransform)(w,m);const C=(0,l.default)(n.props.className||"",i,{[s]:this.state.dragging,[f]:this.state.dragged});return o.createElement(d.default,_extends({},h,{onStart:this.onDragStart,onDrag:this.onDrag,onStop:this.onDragStop}),o.cloneElement(o.Children.only(n),{className:C,style:{...n.props.style,...y},transform:v}))}}t.default=Draggable,_defineProperty(Draggable,"displayName","Draggable"),_defineProperty(Draggable,"propTypes",{...d.default.propTypes,axis:a.default.oneOf(["both","x","y","none"]),bounds:a.default.oneOfType([a.default.shape({left:a.default.number,right:a.default.number,top:a.default.number,bottom:a.default.number}),a.default.string,a.default.oneOf([!1])]),defaultClassName:a.default.string,defaultClassNameDragging:a.default.string,defaultClassNameDragged:a.default.string,defaultPosition:a.default.shape({x:a.default.number,y:a.default.number}),positionOffset:a.default.shape({x:a.default.oneOfType([a.default.number,a.default.string]),y:a.default.oneOfType([a.default.number,a.default.string])}),position:a.default.shape({x:a.default.number,y:a.default.number}),className:s.dontSetMe,style:s.dontSetMe,transform:s.dontSetMe}),_defineProperty(Draggable,"defaultProps",{...d.default.defaultProps,axis:"both",bounds:!1,defaultClassName:"react-draggable",defaultClassNameDragging:"react-draggable-dragging",defaultClassNameDragged:"react-draggable-dragged",defaultPosition:{x:0,y:0},scale:1})},11060:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var n=_getRequireWildcardCache(t);if(n&&n.has(e))return n.get(e);var o={},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&Object.prototype.hasOwnProperty.call(e,i)){var l=a?Object.getOwnPropertyDescriptor(e,i):null;l&&(l.get||l.set)?Object.defineProperty(o,i,l):o[i]=e[i]}o.default=e,n&&n.set(e,o);return o}(n(41594)),a=_interopRequireDefault(n(62688)),i=_interopRequireDefault(n(75206)),l=n(32837),u=n(10402),c=n(26732),s=_interopRequireDefault(n(57988));function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(_getRequireWildcardCache=function(e){return e?n:t})(e)}function _defineProperty(e,t,n){return(t=function _toPropertyKey(e){var t=function _toPrimitive(e,t){if("object"!=typeof e||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=typeof o)return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}const d={start:"touchstart",move:"touchmove",stop:"touchend"},f={start:"mousedown",move:"mousemove",stop:"mouseup"};let p=f;class DraggableCore extends o.Component{constructor(){super(...arguments),_defineProperty(this,"dragging",!1),_defineProperty(this,"lastX",NaN),_defineProperty(this,"lastY",NaN),_defineProperty(this,"touchIdentifier",null),_defineProperty(this,"mounted",!1),_defineProperty(this,"handleDragStart",(e=>{if(this.props.onMouseDown(e),!this.props.allowAnyClick&&"number"==typeof e.button&&0!==e.button)return!1;const t=this.findDOMNode();if(!t||!t.ownerDocument||!t.ownerDocument.body)throw new Error("<DraggableCore> not mounted on DragStart!");const{ownerDocument:n}=t;if(this.props.disabled||!(e.target instanceof n.defaultView.Node)||this.props.handle&&!(0,l.matchesSelectorAndParentsTo)(e.target,this.props.handle,t)||this.props.cancel&&(0,l.matchesSelectorAndParentsTo)(e.target,this.props.cancel,t))return;"touchstart"===e.type&&e.preventDefault();const o=(0,l.getTouchIdentifier)(e);this.touchIdentifier=o;const a=(0,u.getControlPosition)(e,o,this);if(null==a)return;const{x:i,y:c}=a,d=(0,u.createCoreData)(this,i,c);(0,s.default)("DraggableCore: handleDragStart: %j",d),(0,s.default)("calling",this.props.onStart);!1!==this.props.onStart(e,d)&&!1!==this.mounted&&(this.props.enableUserSelectHack&&(0,l.addUserSelectStyles)(n),this.dragging=!0,this.lastX=i,this.lastY=c,(0,l.addEvent)(n,p.move,this.handleDrag),(0,l.addEvent)(n,p.stop,this.handleDragStop))})),_defineProperty(this,"handleDrag",(e=>{const t=(0,u.getControlPosition)(e,this.touchIdentifier,this);if(null==t)return;let{x:n,y:o}=t;if(Array.isArray(this.props.grid)){let e=n-this.lastX,t=o-this.lastY;if([e,t]=(0,u.snapToGrid)(this.props.grid,e,t),!e&&!t)return;n=this.lastX+e,o=this.lastY+t}const a=(0,u.createCoreData)(this,n,o);(0,s.default)("DraggableCore: handleDrag: %j",a);if(!1!==this.props.onDrag(e,a)&&!1!==this.mounted)this.lastX=n,this.lastY=o;else try{this.handleDragStop(new MouseEvent("mouseup"))}catch(e){const t=document.createEvent("MouseEvents");t.initMouseEvent("mouseup",!0,!0,window,0,0,0,0,0,!1,!1,!1,!1,0,null),this.handleDragStop(t)}})),_defineProperty(this,"handleDragStop",(e=>{if(!this.dragging)return;const t=(0,u.getControlPosition)(e,this.touchIdentifier,this);if(null==t)return;let{x:n,y:o}=t;if(Array.isArray(this.props.grid)){let e=n-this.lastX||0,t=o-this.lastY||0;[e,t]=(0,u.snapToGrid)(this.props.grid,e,t),n=this.lastX+e,o=this.lastY+t}const a=(0,u.createCoreData)(this,n,o);if(!1===this.props.onStop(e,a)||!1===this.mounted)return!1;const i=this.findDOMNode();i&&this.props.enableUserSelectHack&&(0,l.removeUserSelectStyles)(i.ownerDocument),(0,s.default)("DraggableCore: handleDragStop: %j",a),this.dragging=!1,this.lastX=NaN,this.lastY=NaN,i&&((0,s.default)("DraggableCore: Removing handlers"),(0,l.removeEvent)(i.ownerDocument,p.move,this.handleDrag),(0,l.removeEvent)(i.ownerDocument,p.stop,this.handleDragStop))})),_defineProperty(this,"onMouseDown",(e=>(p=f,this.handleDragStart(e)))),_defineProperty(this,"onMouseUp",(e=>(p=f,this.handleDragStop(e)))),_defineProperty(this,"onTouchStart",(e=>(p=d,this.handleDragStart(e)))),_defineProperty(this,"onTouchEnd",(e=>(p=d,this.handleDragStop(e))))}componentDidMount(){this.mounted=!0;const e=this.findDOMNode();e&&(0,l.addEvent)(e,d.start,this.onTouchStart,{passive:!1})}componentWillUnmount(){this.mounted=!1;const e=this.findDOMNode();if(e){const{ownerDocument:t}=e;(0,l.removeEvent)(t,f.move,this.handleDrag),(0,l.removeEvent)(t,d.move,this.handleDrag),(0,l.removeEvent)(t,f.stop,this.handleDragStop),(0,l.removeEvent)(t,d.stop,this.handleDragStop),(0,l.removeEvent)(e,d.start,this.onTouchStart,{passive:!1}),this.props.enableUserSelectHack&&(0,l.removeUserSelectStyles)(t)}}findDOMNode(){var e,t;return null!==(e=this.props)&&void 0!==e&&e.nodeRef?null===(t=this.props)||void 0===t||null===(t=t.nodeRef)||void 0===t?void 0:t.current:i.default.findDOMNode(this)}render(){return o.cloneElement(o.Children.only(this.props.children),{onMouseDown:this.onMouseDown,onMouseUp:this.onMouseUp,onTouchEnd:this.onTouchEnd})}}t.default=DraggableCore,_defineProperty(DraggableCore,"displayName","DraggableCore"),_defineProperty(DraggableCore,"propTypes",{allowAnyClick:a.default.bool,children:a.default.node.isRequired,disabled:a.default.bool,enableUserSelectHack:a.default.bool,offsetParent:function(e,t){if(e[t]&&1!==e[t].nodeType)throw new Error("Draggable's offsetParent must be a DOM Node.")},grid:a.default.arrayOf(a.default.number),handle:a.default.string,cancel:a.default.string,nodeRef:a.default.object,onStart:a.default.func,onDrag:a.default.func,onStop:a.default.func,onMouseDown:a.default.func,scale:a.default.number,className:c.dontSetMe,style:c.dontSetMe,transform:c.dontSetMe}),_defineProperty(DraggableCore,"defaultProps",{allowAnyClick:!1,disabled:!1,enableUserSelectHack:!0,onStart:function(){},onDrag:function(){},onStop:function(){},onMouseDown:function(){},scale:1})},38230:(e,t,n)=>{"use strict";const{default:o,DraggableCore:a}=n(9111);e.exports=o,e.exports.default=o,e.exports.DraggableCore=a},32837:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.addClassName=addClassName,t.addEvent=function addEvent(e,t,n,o){if(!e)return;const a={capture:!0,...o};e.addEventListener?e.addEventListener(t,n,a):e.attachEvent?e.attachEvent("on"+t,n):e["on"+t]=n},t.addUserSelectStyles=function addUserSelectStyles(e){if(!e)return;let t=e.getElementById("react-draggable-style-el");t||(t=e.createElement("style"),t.type="text/css",t.id="react-draggable-style-el",t.innerHTML=".react-draggable-transparent-selection *::-moz-selection {all: inherit;}\n",t.innerHTML+=".react-draggable-transparent-selection *::selection {all: inherit;}\n",e.getElementsByTagName("head")[0].appendChild(t));e.body&&addClassName(e.body,"react-draggable-transparent-selection")},t.createCSSTransform=function createCSSTransform(e,t){const n=getTranslation(e,t,"px");return{[(0,a.browserPrefixToKey)("transform",a.default)]:n}},t.createSVGTransform=function createSVGTransform(e,t){return getTranslation(e,t,"")},t.getTouch=function getTouch(e,t){return e.targetTouches&&(0,o.findInArray)(e.targetTouches,(e=>t===e.identifier))||e.changedTouches&&(0,o.findInArray)(e.changedTouches,(e=>t===e.identifier))},t.getTouchIdentifier=function getTouchIdentifier(e){if(e.targetTouches&&e.targetTouches[0])return e.targetTouches[0].identifier;if(e.changedTouches&&e.changedTouches[0])return e.changedTouches[0].identifier},t.getTranslation=getTranslation,t.innerHeight=function innerHeight(e){let t=e.clientHeight;const n=e.ownerDocument.defaultView.getComputedStyle(e);return t-=(0,o.int)(n.paddingTop),t-=(0,o.int)(n.paddingBottom),t},t.innerWidth=function innerWidth(e){let t=e.clientWidth;const n=e.ownerDocument.defaultView.getComputedStyle(e);return t-=(0,o.int)(n.paddingLeft),t-=(0,o.int)(n.paddingRight),t},t.matchesSelector=matchesSelector,t.matchesSelectorAndParentsTo=function matchesSelectorAndParentsTo(e,t,n){let o=e;do{if(matchesSelector(o,t))return!0;if(o===n)return!1;o=o.parentNode}while(o);return!1},t.offsetXYFromParent=function offsetXYFromParent(e,t,n){const o=t===t.ownerDocument.body?{left:0,top:0}:t.getBoundingClientRect(),a=(e.clientX+t.scrollLeft-o.left)/n,i=(e.clientY+t.scrollTop-o.top)/n;return{x:a,y:i}},t.outerHeight=function outerHeight(e){let t=e.clientHeight;const n=e.ownerDocument.defaultView.getComputedStyle(e);return t+=(0,o.int)(n.borderTopWidth),t+=(0,o.int)(n.borderBottomWidth),t},t.outerWidth=function outerWidth(e){let t=e.clientWidth;const n=e.ownerDocument.defaultView.getComputedStyle(e);return t+=(0,o.int)(n.borderLeftWidth),t+=(0,o.int)(n.borderRightWidth),t},t.removeClassName=removeClassName,t.removeEvent=function removeEvent(e,t,n,o){if(!e)return;const a={capture:!0,...o};e.removeEventListener?e.removeEventListener(t,n,a):e.detachEvent?e.detachEvent("on"+t,n):e["on"+t]=null},t.removeUserSelectStyles=function removeUserSelectStyles(e){if(!e)return;try{if(e.body&&removeClassName(e.body,"react-draggable-transparent-selection"),e.selection)e.selection.empty();else{const t=(e.defaultView||window).getSelection();t&&"Caret"!==t.type&&t.removeAllRanges()}}catch(e){}};var o=n(26732),a=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var n=_getRequireWildcardCache(t);if(n&&n.has(e))return n.get(e);var o={},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&Object.prototype.hasOwnProperty.call(e,i)){var l=a?Object.getOwnPropertyDescriptor(e,i):null;l&&(l.get||l.set)?Object.defineProperty(o,i,l):o[i]=e[i]}o.default=e,n&&n.set(e,o);return o}(n(47350));function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(_getRequireWildcardCache=function(e){return e?n:t})(e)}let i="";function matchesSelector(e,t){return i||(i=(0,o.findInArray)(["matches","webkitMatchesSelector","mozMatchesSelector","msMatchesSelector","oMatchesSelector"],(function(t){return(0,o.isFunction)(e[t])}))),!!(0,o.isFunction)(e[i])&&e[i](t)}function getTranslation(e,t,n){let{x:o,y:a}=e,i="translate(".concat(o).concat(n,",").concat(a).concat(n,")");if(t){const e="".concat("string"==typeof t.x?t.x:t.x+n),o="".concat("string"==typeof t.y?t.y:t.y+n);i="translate(".concat(e,", ").concat(o,")")+i}return i}function addClassName(e,t){e.classList?e.classList.add(t):e.className.match(new RegExp("(?:^|\\s)".concat(t,"(?!\\S)")))||(e.className+=" ".concat(t))}function removeClassName(e,t){e.classList?e.classList.remove(t):e.className=e.className.replace(new RegExp("(?:^|\\s)".concat(t,"(?!\\S)"),"g"),"")}},47350:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.browserPrefixToKey=browserPrefixToKey,t.browserPrefixToStyle=function browserPrefixToStyle(e,t){return t?"-".concat(t.toLowerCase(),"-").concat(e):e},t.default=void 0,t.getPrefix=getPrefix;const n=["Moz","Webkit","O","ms"];function getPrefix(){var e;let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"transform";if("undefined"==typeof window)return"";const o=null===(e=window.document)||void 0===e||null===(e=e.documentElement)||void 0===e?void 0:e.style;if(!o)return"";if(t in o)return"";for(let e=0;e<n.length;e++)if(browserPrefixToKey(t,n[e])in o)return n[e];return""}function browserPrefixToKey(e,t){return t?"".concat(t).concat(function kebabToTitleCase(e){let t="",n=!0;for(let o=0;o<e.length;o++)n?(t+=e[o].toUpperCase(),n=!1):"-"===e[o]?n=!0:t+=e[o];return t}(e)):e}t.default=getPrefix()},57988:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function log(){0}},10402:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.canDragX=function canDragX(e){return"both"===e.props.axis||"x"===e.props.axis},t.canDragY=function canDragY(e){return"both"===e.props.axis||"y"===e.props.axis},t.createCoreData=function createCoreData(e,t,n){const a=!(0,o.isNum)(e.lastX),i=findDOMNode(e);return a?{node:i,deltaX:0,deltaY:0,lastX:t,lastY:n,x:t,y:n}:{node:i,deltaX:t-e.lastX,deltaY:n-e.lastY,lastX:e.lastX,lastY:e.lastY,x:t,y:n}},t.createDraggableData=function createDraggableData(e,t){const n=e.props.scale;return{node:t.node,x:e.state.x+t.deltaX/n,y:e.state.y+t.deltaY/n,deltaX:t.deltaX/n,deltaY:t.deltaY/n,lastX:e.state.x,lastY:e.state.y}},t.getBoundPosition=function getBoundPosition(e,t,n){if(!e.props.bounds)return[t,n];let{bounds:i}=e.props;i="string"==typeof i?i:function cloneBounds(e){return{left:e.left,top:e.top,right:e.right,bottom:e.bottom}}(i);const l=findDOMNode(e);if("string"==typeof i){const{ownerDocument:e}=l,t=e.defaultView;let n;if(n="parent"===i?l.parentNode:e.querySelector(i),!(n instanceof t.HTMLElement))throw new Error('Bounds selector "'+i+'" could not find an element.');const u=n,c=t.getComputedStyle(l),s=t.getComputedStyle(u);i={left:-l.offsetLeft+(0,o.int)(s.paddingLeft)+(0,o.int)(c.marginLeft),top:-l.offsetTop+(0,o.int)(s.paddingTop)+(0,o.int)(c.marginTop),right:(0,a.innerWidth)(u)-(0,a.outerWidth)(l)-l.offsetLeft+(0,o.int)(s.paddingRight)-(0,o.int)(c.marginRight),bottom:(0,a.innerHeight)(u)-(0,a.outerHeight)(l)-l.offsetTop+(0,o.int)(s.paddingBottom)-(0,o.int)(c.marginBottom)}}(0,o.isNum)(i.right)&&(t=Math.min(t,i.right));(0,o.isNum)(i.bottom)&&(n=Math.min(n,i.bottom));(0,o.isNum)(i.left)&&(t=Math.max(t,i.left));(0,o.isNum)(i.top)&&(n=Math.max(n,i.top));return[t,n]},t.getControlPosition=function getControlPosition(e,t,n){const o="number"==typeof t?(0,a.getTouch)(e,t):null;if("number"==typeof t&&!o)return null;const i=findDOMNode(n),l=n.props.offsetParent||i.offsetParent||i.ownerDocument.body;return(0,a.offsetXYFromParent)(o||e,l,n.props.scale)},t.snapToGrid=function snapToGrid(e,t,n){const o=Math.round(t/e[0])*e[0],a=Math.round(n/e[1])*e[1];return[o,a]};var o=n(26732),a=n(32837);function findDOMNode(e){const t=e.findDOMNode();if(!t)throw new Error("<DraggableCore>: Unmounted during event!");return t}},26732:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.dontSetMe=function dontSetMe(e,t,n){if(e[t])return new Error("Invalid prop ".concat(t," passed to ").concat(n," - do not set this, set it on the child."))},t.findInArray=function findInArray(e,t){for(let n=0,o=e.length;n<o;n++)if(t.apply(t,[e[n],n,e]))return e[n]},t.int=function int(e){return parseInt(e,10)},t.isFunction=function isFunction(e){return"function"==typeof e||"[object Function]"===Object.prototype.toString.call(e)},t.isNum=function isNum(e){return"number"==typeof e&&!isNaN(e)}},38262:(e,t,n)=>{"use strict";function r(e){var t,n,o="";if("string"==typeof e||"number"==typeof e)o+=e;else if("object"==typeof e)if(Array.isArray(e))for(t=0;t<e.length;t++)e[t]&&(n=r(e[t]))&&(o&&(o+=" "),o+=n);else for(t in e)e[t]&&(o&&(o+=" "),o+=t);return o}function clsx(){for(var e,t,n=0,o="";n<arguments.length;)(e=arguments[n++])&&(t=r(e))&&(o&&(o+=" "),o+=t);return o}n.r(t),n.d(t,{clsx:()=>clsx,default:()=>o});const o=clsx},21806:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function isFQDN(e,t){(0,o.default)(e),(t=(0,a.default)(t,i)).allow_trailing_dot&&"."===e[e.length-1]&&(e=e.substring(0,e.length-1));!0===t.allow_wildcard&&0===e.indexOf("*.")&&(e=e.substring(2));var n=e.split("."),l=n[n.length-1];if(t.require_tld){if(n.length<2)return!1;if(!t.allow_numeric_tld&&!/^([a-z\u00A1-\u00A8\u00AA-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]{2,}|xn[a-z0-9-]{2,})$/i.test(l))return!1;if(/\s/.test(l))return!1}if(!t.allow_numeric_tld&&/^\d+$/.test(l))return!1;return n.every((function(e){return!(e.length>63&&!t.ignore_max_length)&&(!!/^[a-z_\u00a1-\uffff0-9-]+$/i.test(e)&&(!/[\uff01-\uff5e]/.test(e)&&(!/^-|-$/.test(e)&&!(!t.allow_underscores&&/_/.test(e)))))}))};var o=_interopRequireDefault(n(93443)),a=_interopRequireDefault(n(41398));function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}var i={require_tld:!0,allow_underscores:!1,allow_trailing_dot:!1,allow_numeric_tld:!1,allow_wildcard:!1,ignore_max_length:!1};e.exports=t.default,e.exports.default=t.default},14744:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function isIP(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";if((0,o.default)(e),!(t=String(t)))return isIP(e,4)||isIP(e,6);if("4"===t)return l.test(e);if("6"===t)return c.test(e);return!1};var o=function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}(n(93443));var a="(?:[0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])",i="(".concat(a,"[.]){3}").concat(a),l=new RegExp("^".concat(i,"$")),u="(?:[0-9a-fA-F]{1,4})",c=new RegExp("^("+"(?:".concat(u,":){7}(?:").concat(u,"|:)|")+"(?:".concat(u,":){6}(?:").concat(i,"|:").concat(u,"|:)|")+"(?:".concat(u,":){5}(?::").concat(i,"|(:").concat(u,"){1,2}|:)|")+"(?:".concat(u,":){4}(?:(:").concat(u,"){0,1}:").concat(i,"|(:").concat(u,"){1,3}|:)|")+"(?:".concat(u,":){3}(?:(:").concat(u,"){0,2}:").concat(i,"|(:").concat(u,"){1,4}|:)|")+"(?:".concat(u,":){2}(?:(:").concat(u,"){0,3}:").concat(i,"|(:").concat(u,"){1,5}|:)|")+"(?:".concat(u,":){1}(?:(:").concat(u,"){0,4}:").concat(i,"|(:").concat(u,"){1,6}|:)|")+"(?::((?::".concat(u,"){0,5}:").concat(i,"|(?::").concat(u,"){1,7}|:))")+")(%[0-9a-zA-Z-.:]{1,})?$");e.exports=t.default,e.exports.default=t.default},94760:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function isURL(e,t){if((0,o.default)(e),!e||/[\s<>]/.test(e))return!1;if(0===e.indexOf("mailto:"))return!1;if((t=(0,l.default)(t,u)).validate_length&&e.length>=2083)return!1;if(!t.allow_fragments&&e.includes("#"))return!1;if(!t.allow_query_components&&(e.includes("?")||e.includes("&")))return!1;var n,s,d,f,p,m,g,h;if(g=e.split("#"),e=g.shift(),g=e.split("?"),e=g.shift(),(g=e.split("://")).length>1){if(n=g.shift().toLowerCase(),t.require_valid_protocol&&-1===t.protocols.indexOf(n))return!1}else{if(t.require_protocol)return!1;if("//"===e.slice(0,2)){if(!t.allow_protocol_relative_urls)return!1;g[0]=e.slice(2)}}if(""===(e=g.join("://")))return!1;if(g=e.split("/"),""===(e=g.shift())&&!t.require_host)return!0;if((g=e.split("@")).length>1){if(t.disallow_auth)return!1;if(""===g[0])return!1;if((s=g.shift()).indexOf(":")>=0&&s.split(":").length>2)return!1;var y=function _slicedToArray(e,t){return function _arrayWithHoles(e){if(Array.isArray(e))return e}(e)||function _iterableToArrayLimit(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,a,i,l,u=[],c=!0,s=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=i.call(n)).done)&&(u.push(o.value),u.length!==t);c=!0);}catch(e){s=!0,a=e}finally{try{if(!c&&null!=n.return&&(l=n.return(),Object(l)!==l))return}finally{if(s)throw a}}return u}}(e,t)||function _unsupportedIterableToArray(e,t){if(!e)return;if("string"==typeof e)return _arrayLikeToArray(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return _arrayLikeToArray(e,t)}(e,t)||function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}(s.split(":"),2),v=y[0],_=y[1];if(""===v&&""===_)return!1}f=g.join("@"),m=null,h=null;var b=f.match(c);b?(d="",h=b[1],m=b[2]||null):(d=(g=f.split(":")).shift(),g.length&&(m=g.join(":")));if(null!==m&&m.length>0){if(p=parseInt(m,10),!/^[0-9]+$/.test(m)||p<=0||p>65535)return!1}else if(t.require_port)return!1;if(t.host_whitelist)return checkHost(d,t.host_whitelist);if(""===d&&!t.require_host)return!0;if(!((0,i.default)(d)||(0,a.default)(d,t)||h&&(0,i.default)(h,6)))return!1;if(d=d||h,t.host_blacklist&&checkHost(d,t.host_blacklist))return!1;return!0};var o=_interopRequireDefault(n(93443)),a=_interopRequireDefault(n(21806)),i=_interopRequireDefault(n(14744)),l=_interopRequireDefault(n(41398));function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=new Array(t);n<t;n++)o[n]=e[n];return o}var u={protocols:["http","https","ftp"],require_tld:!0,require_protocol:!1,require_host:!0,require_port:!1,require_valid_protocol:!0,allow_underscores:!1,allow_trailing_dot:!1,allow_protocol_relative_urls:!1,allow_fragments:!0,allow_query_components:!0,validate_length:!0},c=/^\[([^\]]+)\](?::([0-9]+))?$/;function checkHost(e,t){for(var n=0;n<t.length;n++){var o=t[n];if(e===o||(a=o,"[object RegExp]"===Object.prototype.toString.call(a)&&o.test(e)))return!0}var a;return!1}e.exports=t.default,e.exports.default=t.default},93443:(e,t)=>{"use strict";function _typeof(e){return _typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},_typeof(e)}Object.defineProperty(t,"__esModule",{value:!0}),t.default=function assertString(e){if(!("string"==typeof e||e instanceof String)){var t=_typeof(e);throw null===e?t="null":"object"===t&&(t=e.constructor.name),new TypeError("Expected a string but received a ".concat(t))}},e.exports=t.default,e.exports.default=t.default},41398:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function merge(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;for(var n in t)void 0===e[n]&&(e[n]=t[n]);return e},e.exports=t.default,e.exports.default=t.default},41594:e=>{"use strict";e.exports=React},75206:e=>{"use strict";e.exports=ReactDOM},44048:e=>{"use strict";e.exports=elementorV2.icons},86956:e=>{"use strict";e.exports=elementorV2.ui},12470:e=>{"use strict";e.exports=wp.i18n},78113:e=>{e.exports=function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=Array(t);n<t;n++)o[n]=e[n];return o},e.exports.__esModule=!0,e.exports.default=e.exports},70569:e=>{e.exports=function _arrayWithHoles(e){if(Array.isArray(e))return e},e.exports.__esModule=!0,e.exports.default=e.exports},91819:(e,t,n)=>{var o=n(78113);e.exports=function _arrayWithoutHoles(e){if(Array.isArray(e))return o(e)},e.exports.__esModule=!0,e.exports.default=e.exports},36417:e=>{e.exports=function _assertThisInitialized(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e},e.exports.__esModule=!0,e.exports.default=e.exports},58155:e=>{function asyncGeneratorStep(e,t,n,o,a,i,l){try{var u=e[i](l),c=u.value}catch(e){return void n(e)}u.done?t(c):Promise.resolve(c).then(o,a)}e.exports=function _asyncToGenerator(e){return function(){var t=this,n=arguments;return new Promise((function(o,a){var i=e.apply(t,n);function _next(e){asyncGeneratorStep(i,o,a,_next,_throw,"next",e)}function _throw(e){asyncGeneratorStep(i,o,a,_next,_throw,"throw",e)}_next(void 0)}))}},e.exports.__esModule=!0,e.exports.default=e.exports},39805:e=>{e.exports=function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")},e.exports.__esModule=!0,e.exports.default=e.exports},40989:(e,t,n)=>{var o=n(45498);function _defineProperties(e,t){for(var n=0;n<t.length;n++){var a=t[n];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(e,o(a.key),a)}}e.exports=function _createClass(e,t,n){return t&&_defineProperties(e.prototype,t),n&&_defineProperties(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e},e.exports.__esModule=!0,e.exports.default=e.exports},85707:(e,t,n)=>{var o=n(45498);e.exports=function _defineProperty(e,t,n){return(t=o(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e},e.exports.__esModule=!0,e.exports.default=e.exports},78304:e=>{function _extends(){return e.exports=_extends=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)({}).hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},e.exports.__esModule=!0,e.exports.default=e.exports,_extends.apply(null,arguments)}e.exports=_extends,e.exports.__esModule=!0,e.exports.default=e.exports},29402:e=>{function _getPrototypeOf(t){return e.exports=_getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},e.exports.__esModule=!0,e.exports.default=e.exports,_getPrototypeOf(t)}e.exports=_getPrototypeOf,e.exports.__esModule=!0,e.exports.default=e.exports},87861:(e,t,n)=>{var o=n(91270);e.exports=function _inherits(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&o(e,t)},e.exports.__esModule=!0,e.exports.default=e.exports},96784:e=>{e.exports=function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}},e.exports.__esModule=!0,e.exports.default=e.exports},20365:e=>{e.exports=function _iterableToArray(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)},e.exports.__esModule=!0,e.exports.default=e.exports},65474:e=>{e.exports=function _iterableToArrayLimit(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,a,i,l,u=[],c=!0,s=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=i.call(n)).done)&&(u.push(o.value),u.length!==t);c=!0);}catch(e){s=!0,a=e}finally{try{if(!c&&null!=n.return&&(l=n.return(),Object(l)!==l))return}finally{if(s)throw a}}return u}},e.exports.__esModule=!0,e.exports.default=e.exports},11018:e=>{e.exports=function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},e.exports.__esModule=!0,e.exports.default=e.exports},78687:e=>{e.exports=function _nonIterableSpread(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},e.exports.__esModule=!0,e.exports.default=e.exports},40453:(e,t,n)=>{var o=n(10739);e.exports=function _objectWithoutProperties(e,t){if(null==e)return{};var n,a,i=o(e,t);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);for(a=0;a<l.length;a++)n=l[a],t.includes(n)||{}.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i},e.exports.__esModule=!0,e.exports.default=e.exports},10739:e=>{e.exports=function _objectWithoutPropertiesLoose(e,t){if(null==e)return{};var n={};for(var o in e)if({}.hasOwnProperty.call(e,o)){if(t.includes(o))continue;n[o]=e[o]}return n},e.exports.__esModule=!0,e.exports.default=e.exports},15118:(e,t,n)=>{var o=n(10564).default,a=n(36417);e.exports=function _possibleConstructorReturn(e,t){if(t&&("object"==o(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return a(e)},e.exports.__esModule=!0,e.exports.default=e.exports},53051:(e,t,n)=>{var o=n(10564).default;function _regeneratorRuntime(){"use strict";e.exports=_regeneratorRuntime=function _regeneratorRuntime(){return n},e.exports.__esModule=!0,e.exports.default=e.exports;var t,n={},a=Object.prototype,i=a.hasOwnProperty,l=Object.defineProperty||function(e,t,n){e[t]=n.value},u="function"==typeof Symbol?Symbol:{},c=u.iterator||"@@iterator",s=u.asyncIterator||"@@asyncIterator",d=u.toStringTag||"@@toStringTag";function define(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{define({},"")}catch(t){define=function define(e,t,n){return e[t]=n}}function wrap(e,t,n,o){var a=t&&t.prototype instanceof Generator?t:Generator,i=Object.create(a.prototype),u=new Context(o||[]);return l(i,"_invoke",{value:makeInvokeMethod(e,n,u)}),i}function tryCatch(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}n.wrap=wrap;var f="suspendedStart",p="suspendedYield",m="executing",g="completed",h={};function Generator(){}function GeneratorFunction(){}function GeneratorFunctionPrototype(){}var y={};define(y,c,(function(){return this}));var v=Object.getPrototypeOf,_=v&&v(v(values([])));_&&_!==a&&i.call(_,c)&&(y=_);var b=GeneratorFunctionPrototype.prototype=Generator.prototype=Object.create(y);function defineIteratorMethods(e){["next","throw","return"].forEach((function(t){define(e,t,(function(e){return this._invoke(t,e)}))}))}function AsyncIterator(e,t){function invoke(n,a,l,u){var c=tryCatch(e[n],e,a);if("throw"!==c.type){var s=c.arg,d=s.value;return d&&"object"==o(d)&&i.call(d,"__await")?t.resolve(d.__await).then((function(e){invoke("next",e,l,u)}),(function(e){invoke("throw",e,l,u)})):t.resolve(d).then((function(e){s.value=e,l(s)}),(function(e){return invoke("throw",e,l,u)}))}u(c.arg)}var n;l(this,"_invoke",{value:function value(e,o){function callInvokeWithMethodAndArg(){return new t((function(t,n){invoke(e,o,t,n)}))}return n=n?n.then(callInvokeWithMethodAndArg,callInvokeWithMethodAndArg):callInvokeWithMethodAndArg()}})}function makeInvokeMethod(e,n,o){var a=f;return function(i,l){if(a===m)throw Error("Generator is already running");if(a===g){if("throw"===i)throw l;return{value:t,done:!0}}for(o.method=i,o.arg=l;;){var u=o.delegate;if(u){var c=maybeInvokeDelegate(u,o);if(c){if(c===h)continue;return c}}if("next"===o.method)o.sent=o._sent=o.arg;else if("throw"===o.method){if(a===f)throw a=g,o.arg;o.dispatchException(o.arg)}else"return"===o.method&&o.abrupt("return",o.arg);a=m;var s=tryCatch(e,n,o);if("normal"===s.type){if(a=o.done?g:p,s.arg===h)continue;return{value:s.arg,done:o.done}}"throw"===s.type&&(a=g,o.method="throw",o.arg=s.arg)}}}function maybeInvokeDelegate(e,n){var o=n.method,a=e.iterator[o];if(a===t)return n.delegate=null,"throw"===o&&e.iterator.return&&(n.method="return",n.arg=t,maybeInvokeDelegate(e,n),"throw"===n.method)||"return"!==o&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+o+"' method")),h;var i=tryCatch(a,e.iterator,n.arg);if("throw"===i.type)return n.method="throw",n.arg=i.arg,n.delegate=null,h;var l=i.arg;return l?l.done?(n[e.resultName]=l.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,h):l:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,h)}function pushTryEntry(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function resetTryEntry(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function Context(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(pushTryEntry,this),this.reset(!0)}function values(e){if(e||""===e){var n=e[c];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var a=-1,l=function next(){for(;++a<e.length;)if(i.call(e,a))return next.value=e[a],next.done=!1,next;return next.value=t,next.done=!0,next};return l.next=l}}throw new TypeError(o(e)+" is not iterable")}return GeneratorFunction.prototype=GeneratorFunctionPrototype,l(b,"constructor",{value:GeneratorFunctionPrototype,configurable:!0}),l(GeneratorFunctionPrototype,"constructor",{value:GeneratorFunction,configurable:!0}),GeneratorFunction.displayName=define(GeneratorFunctionPrototype,d,"GeneratorFunction"),n.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===GeneratorFunction||"GeneratorFunction"===(t.displayName||t.name))},n.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,GeneratorFunctionPrototype):(e.__proto__=GeneratorFunctionPrototype,define(e,d,"GeneratorFunction")),e.prototype=Object.create(b),e},n.awrap=function(e){return{__await:e}},defineIteratorMethods(AsyncIterator.prototype),define(AsyncIterator.prototype,s,(function(){return this})),n.AsyncIterator=AsyncIterator,n.async=function(e,t,o,a,i){void 0===i&&(i=Promise);var l=new AsyncIterator(wrap(e,t,o,a),i);return n.isGeneratorFunction(t)?l:l.next().then((function(e){return e.done?e.value:l.next()}))},defineIteratorMethods(b),define(b,d,"Generator"),define(b,c,(function(){return this})),define(b,"toString",(function(){return"[object Generator]"})),n.keys=function(e){var t=Object(e),n=[];for(var o in t)n.push(o);return n.reverse(),function next(){for(;n.length;){var e=n.pop();if(e in t)return next.value=e,next.done=!1,next}return next.done=!0,next}},n.values=values,Context.prototype={constructor:Context,reset:function reset(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(resetTryEntry),!e)for(var n in this)"t"===n.charAt(0)&&i.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function stop(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function dispatchException(e){if(this.done)throw e;var n=this;function handle(o,a){return l.type="throw",l.arg=e,n.next=o,a&&(n.method="next",n.arg=t),!!a}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],l=a.completion;if("root"===a.tryLoc)return handle("end");if(a.tryLoc<=this.prev){var u=i.call(a,"catchLoc"),c=i.call(a,"finallyLoc");if(u&&c){if(this.prev<a.catchLoc)return handle(a.catchLoc,!0);if(this.prev<a.finallyLoc)return handle(a.finallyLoc)}else if(u){if(this.prev<a.catchLoc)return handle(a.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return handle(a.finallyLoc)}}}},abrupt:function abrupt(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&i.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var a=o;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var l=a?a.completion:{};return l.type=e,l.arg=t,a?(this.method="next",this.next=a.finallyLoc,h):this.complete(l)},complete:function complete(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),h},finish:function finish(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),resetTryEntry(n),h}},catch:function _catch(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var o=n.completion;if("throw"===o.type){var a=o.arg;resetTryEntry(n)}return a}}throw Error("illegal catch attempt")},delegateYield:function delegateYield(e,n,o){return this.delegate={iterator:values(e),resultName:n,nextLoc:o},"next"===this.method&&(this.arg=t),h}},n}e.exports=_regeneratorRuntime,e.exports.__esModule=!0,e.exports.default=e.exports},91270:e=>{function _setPrototypeOf(t,n){return e.exports=_setPrototypeOf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},e.exports.__esModule=!0,e.exports.default=e.exports,_setPrototypeOf(t,n)}e.exports=_setPrototypeOf,e.exports.__esModule=!0,e.exports.default=e.exports},18821:(e,t,n)=>{var o=n(70569),a=n(65474),i=n(37744),l=n(11018);e.exports=function _slicedToArray(e,t){return o(e)||a(e,t)||i(e,t)||l()},e.exports.__esModule=!0,e.exports.default=e.exports},98832:e=>{e.exports=function _taggedTemplateLiteral(e,t){return t||(t=e.slice(0)),Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(t)}}))},e.exports.__esModule=!0,e.exports.default=e.exports},10906:(e,t,n)=>{var o=n(91819),a=n(20365),i=n(37744),l=n(78687);e.exports=function _toConsumableArray(e){return o(e)||a(e)||i(e)||l()},e.exports.__esModule=!0,e.exports.default=e.exports},11327:(e,t,n)=>{var o=n(10564).default;e.exports=function toPrimitive(e,t){if("object"!=o(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var a=n.call(e,t||"default");if("object"!=o(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)},e.exports.__esModule=!0,e.exports.default=e.exports},45498:(e,t,n)=>{var o=n(10564).default,a=n(11327);e.exports=function toPropertyKey(e){var t=a(e,"string");return"symbol"==o(t)?t:t+""},e.exports.__esModule=!0,e.exports.default=e.exports},10564:e=>{function _typeof(t){return e.exports=_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports.default=e.exports,_typeof(t)}e.exports=_typeof,e.exports.__esModule=!0,e.exports.default=e.exports},37744:(e,t,n)=>{var o=n(78113);e.exports=function _unsupportedIterableToArray(e,t){if(e){if("string"==typeof e)return o(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?o(e,t):void 0}},e.exports.__esModule=!0,e.exports.default=e.exports},61790:(e,t,n)=>{var o=n(53051)();e.exports=o;try{regeneratorRuntime=o}catch(e){"object"==typeof globalThis?globalThis.regeneratorRuntime=o:Function("r","regeneratorRuntime = r")(o)}}},t={};function __webpack_require__(n){var o=t[n];if(void 0!==o)return o.exports;var a=t[n]={exports:{}};return e[n](a,a.exports,__webpack_require__),a.exports}__webpack_require__.d=(e,t)=>{for(var n in t)__webpack_require__.o(t,n)&&!__webpack_require__.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},__webpack_require__.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),__webpack_require__.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};(()=>{"use strict";var e=__webpack_require__(96784);var t=e(__webpack_require__(61790)),n=e(__webpack_require__(58155)),o=e(__webpack_require__(39805)),a=e(__webpack_require__(40989)),i=e(__webpack_require__(15118)),l=e(__webpack_require__(29402)),u=e(__webpack_require__(87861)),c=e(__webpack_require__(85707)),s=e(__webpack_require__(47389)),d=__webpack_require__(36833),f=__webpack_require__(12470),p=__webpack_require__(91258),m=e(__webpack_require__(92263)),g=__webpack_require__(4353);function _isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!e})()}var h="ai-attachment";new(function(e){function Module(){var e;(0,o.default)(this,Module);for(var a=arguments.length,u=new Array(a),s=0;s<a;s++)u[s]=arguments[s];return e=function _callSuper(e,t,n){return t=(0,l.default)(t),(0,i.default)(e,_isNativeReflectConstruct()?Reflect.construct(t,n||[],(0,l.default)(e).constructor):t.apply(e,n))}(this,Module,[].concat(u)),(0,c.default)(e,"registerVariationsContextMenu",(function(e,o){var a=e.find((function(e){return"save"===e.name}));if(!a)return e;var i,l={name:"ai",icon:"eicon-ai",isEnabled:function isEnabled(){return 0!==o.getContainer().children.length},title:(0,f.__)("Generate variations with AI","elementor"),callback:(i=(0,n.default)(t.default.mark((function _callee(){var e,n,a;return t.default.wrap((function _callee$(t){for(;;)switch(t.prev=t.next){case 0:e=o.getContainer(),n=e.model.toJSON({remove:["default"]}),a=[{type:"json",previewHTML:"",content:n,label:e.model.get("title"),source:g.USER_VARIATION_SOURCE}],(0,d.renderLayoutApp)({parentContainer:e.parent,mode:p.MODE_VARIATION,at:e.view._index,attachments:a,onSelect:function onSelect(){e.view.$el.hide()},onClose:function onClose(){e.view.$el.show()},onInsert:function onInsert(t){(0,d.importToEditor)({parentContainer:e.parent,at:e.view._index,template:t,historyTitle:(0,f.__)("AI Variation","elementor"),replace:!0})}});case 4:case"end":return t.stop()}}),_callee)}))),function callback(){return i.apply(this,arguments)})};return a.actions.unshift(l),e})),e}return(0,u.default)(Module,e),(0,a.default)(Module,[{key:"onElementorInit",value:function onElementorInit(){var e=this;elementor.hooks.addFilter("views/add-section/behaviors",this.registerAiLayoutBehavior),elementor.hooks.addFilter("elements/container/contextMenuGroups",this.registerVariationsContextMenu),elementor.hooks.addFilter("elementor/editor/template-library/template/behaviors",this.registerLibraryActionButtonBehavior),elementor.hooks.addFilter("elementor/editor/template-library/template/action-button",this.filterLibraryActionButtonTemplate,11),$e.commands.register("library","generate-ai-variation",(function(t){return e.applyTemplate(t)}))}},{key:"applyTemplate",value:function applyTemplate(e){window.postMessage({type:"library/attach:start"}),$e.components.get("library").downloadTemplate(e,(function(t){var n=e.model;window.postMessage({type:"library/attach",json:t.content[0],html:'<img src="'.concat(n.get("thumbnail"),'" />'),label:"".concat(n.get("template_id")," - ").concat(n.get("title")),source:g.ELEMENTOR_LIBRARY_SOURCE},window.location.origin)}))}},{key:"registerLibraryActionButtonBehavior",value:function registerLibraryActionButtonBehavior(e){return e.applyAiTemplate={behaviorClass:m.default},e}},{key:"registerAiLayoutBehavior",value:function registerAiLayoutBehavior(e){return e.ai={behaviorClass:s.default,context:{documentType:window.elementor.documents.getCurrent().config.type}},e}},{key:"filterLibraryActionButtonTemplate",value:function filterLibraryActionButtonTemplate(e){var t=$e.components.get("library").manager.modalConfig;return"#tmpl-elementor-template-library-insert-button"!==e||"library/templates/blocks"!==$e.routes.current.library?e:e=h===t.mode?"#tmpl-elementor-template-library-apply-ai-button":"#tmpl-elementor-template-library-insert-and-ai-variations-buttons"}}])}(elementorModules.editor.utils.Module))})()})();