/*! elementor - v3.29.0 - 04-06-2025 */
(()=>{var e={18791:(e,t,r)=>{"use strict";var o=r(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;_interopRequireWildcard(r(41594));var n=_interopRequireWildcard(r(75206)),a=r(7470);function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?r:t})(e)}function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=o(e)&&"function"!=typeof e)return{default:e};var r=_getRequireWildcardCache(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&{}.hasOwnProperty.call(e,i)){var u=a?Object.getOwnPropertyDescriptor(e,i):null;u&&(u.get||u.set)?Object.defineProperty(n,i,u):n[i]=e[i]}return n.default=e,r&&r.set(e,n),n}t.default={render:function render(e,t){var r;try{var o=(0,a.createRoot)(t);o.render(e),r=function unmountFunction(){o.unmount()}}catch(o){n.render(e,t),r=function unmountFunction(){n.unmountComponentAtNode(t)}}return{unmount:r}}}},6903:(e,t,r)=>{"use strict";var o=r(62688),n=r(96784),a=r(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.WelcomeDialog=void 0;var i=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=a(e)&&"function"!=typeof e)return{default:e};var r=_getRequireWildcardCache(t);if(r&&r.has(e))return r.get(e);var o={__proto__:null},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&{}.hasOwnProperty.call(e,i)){var u=n?Object.getOwnPropertyDescriptor(e,i):null;u&&(u.get||u.set)?Object.defineProperty(o,i,u):o[i]=e[i]}return o.default=e,r&&r.set(e,o),o}(r(41594)),u=n(r(18821)),l=r(12470),c=r(86956);function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?r:t})(e)}var s={heading:(0,l.__)("Say hello to a new experience!","elementor"),introduction:(0,l.__)("You're now using Editor V4, a new generation of web creation.","elementor"),listItems:[(0,l.__)("Try out Editor V4 elements such as Div, SVG and Paragraph.","elementor"),(0,l.__)("Set up a new Class and apply it site-wide for perfect consistency.","elementor"),(0,l.__)("Customize any style element per screen size by switching between responsive views.","elementor")],footerText:(0,l.__)("Need help getting started?","elementor"),helpCenter:(0,l.__)("Learn more","elementor"),closeButton:(0,l.__)("Let's Go","elementor")},p="https://go.elementor.com/wp-dash-opt-in-v4-help-center/";(t.WelcomeDialog=function WelcomeDialog(e){var t=e.doClose,r=(0,i.useRef)(null),o=(0,i.useState)(!1),n=(0,u.default)(o,2),a=n[0],l=n[1];return(0,i.useEffect)((function(){r.current=document.body,l(!0)}),[]),a&&r.current?i.default.createElement(c.Dialog,{open:Boolean(r.current),onClose:t,maxWidth:"sm"},i.default.createElement(c.Box,{sx:{aspectRatio:"2",backgroundImage:"url(https://assets.elementor.com/v4-promotion/v1/images/v4_opt_in.png)",backgroundSize:"cover",backgroundPosition:"center"}}),i.default.createElement(c.Stack,{pt:3,pb:1.5,px:3,gap:3},i.default.createElement(c.Typography,{variant:"h6",color:"text.primary"},s.heading),i.default.createElement(c.Box,null,i.default.createElement(c.Typography,{variant:"body1",color:"text.secondary"},s.introduction),i.default.createElement(c.List,{sx:{pl:2}},s.listItems.map((function(e,t){return i.default.createElement(c.ListItem,{key:t,sx:{listStyle:"disc",display:"list-item",color:"text.secondary",p:0}},i.default.createElement(c.Typography,{variant:"body1"},e))})))),i.default.createElement(c.Stack,{direction:"row",alignItems:"center",gap:1.5},i.default.createElement(c.Typography,{variant:"body1",color:"text.secondary"},s.footerText),i.default.createElement(c.Link,{href:p,target:"_blank",variant:"body1",color:"info.main",sx:{textDecoration:"none"}},s.helpCenter))),i.default.createElement(c.Divider,null),i.default.createElement(c.Stack,{py:2,px:3},i.default.createElement(c.Button,{variant:"contained",color:"accent",onClick:t,sx:{ml:"auto"}},s.closeButton))):null}).propTypes={doClose:o.func}},40362:(e,t,r)=>{"use strict";var o=r(56441);function emptyFunction(){}function emptyFunctionWithReset(){}emptyFunctionWithReset.resetWarningCache=emptyFunction,e.exports=function(){function shim(e,t,r,n,a,i){if(i!==o){var u=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw u.name="Invariant Violation",u}}function getShim(){return shim}shim.isRequired=shim;var e={array:shim,bigint:shim,bool:shim,func:shim,number:shim,object:shim,string:shim,symbol:shim,any:shim,arrayOf:getShim,element:shim,elementType:shim,instanceOf:getShim,node:shim,objectOf:getShim,oneOf:getShim,oneOfType:getShim,shape:getShim,exact:getShim,checkPropTypes:emptyFunctionWithReset,resetWarningCache:emptyFunction};return e.PropTypes=e,e}},62688:(e,t,r)=>{e.exports=r(40362)()},56441:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},7470:(e,t,r)=>{"use strict";var o=r(75206);t.createRoot=o.createRoot,t.hydrateRoot=o.hydrateRoot},41594:e=>{"use strict";e.exports=React},75206:e=>{"use strict";e.exports=ReactDOM},86956:e=>{"use strict";e.exports=elementorV2.ui},12470:e=>{"use strict";e.exports=wp.i18n},78113:e=>{e.exports=function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,o=Array(t);r<t;r++)o[r]=e[r];return o},e.exports.__esModule=!0,e.exports.default=e.exports},70569:e=>{e.exports=function _arrayWithHoles(e){if(Array.isArray(e))return e},e.exports.__esModule=!0,e.exports.default=e.exports},96784:e=>{e.exports=function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}},e.exports.__esModule=!0,e.exports.default=e.exports},65474:e=>{e.exports=function _iterableToArrayLimit(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var o,n,a,i,u=[],l=!0,c=!1;try{if(a=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;l=!1}else for(;!(l=(o=a.call(r)).done)&&(u.push(o.value),u.length!==t);l=!0);}catch(e){c=!0,n=e}finally{try{if(!l&&null!=r.return&&(i=r.return(),Object(i)!==i))return}finally{if(c)throw n}}return u}},e.exports.__esModule=!0,e.exports.default=e.exports},11018:e=>{e.exports=function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},e.exports.__esModule=!0,e.exports.default=e.exports},18821:(e,t,r)=>{var o=r(70569),n=r(65474),a=r(37744),i=r(11018);e.exports=function _slicedToArray(e,t){return o(e)||n(e,t)||a(e,t)||i()},e.exports.__esModule=!0,e.exports.default=e.exports},10564:e=>{function _typeof(t){return e.exports=_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports.default=e.exports,_typeof(t)}e.exports=_typeof,e.exports.__esModule=!0,e.exports.default=e.exports},37744:(e,t,r)=>{var o=r(78113);e.exports=function _unsupportedIterableToArray(e,t){if(e){if("string"==typeof e)return o(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?o(e,t):void 0}},e.exports.__esModule=!0,e.exports.default=e.exports}},t={};function __webpack_require__(r){var o=t[r];if(void 0!==o)return o.exports;var n=t[r]={exports:{}};return e[r](n,n.exports,__webpack_require__),n.exports}(()=>{"use strict";var e=__webpack_require__(62688),t=__webpack_require__(96784),r=__webpack_require__(10564),o=t(__webpack_require__(18821)),n=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=r(e)&&"function"!=typeof e)return{default:e};var o=_getRequireWildcardCache(t);if(o&&o.has(e))return o.get(e);var n={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&{}.hasOwnProperty.call(e,i)){var u=a?Object.getOwnPropertyDescriptor(e,i):null;u&&(u.get||u.set)?Object.defineProperty(n,i,u):n[i]=e[i]}return n.default=e,o&&o.set(e,n),n}(__webpack_require__(41594)),a=t(__webpack_require__(18791)),i=__webpack_require__(86956),u=__webpack_require__(6903);function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?r:t})(e)}var l=function App(e){var t=(0,n.useState)(!0),r=(0,o.default)(t,2),a=r[0],l=r[1];return n.default.createElement(i.DirectionProvider,{rtl:e.isRTL},n.default.createElement(i.LocalizationProvider,null,n.default.createElement(i.ThemeProvider,{colorScheme:"light",palette:"unstable"},a&&n.default.createElement(u.WelcomeDialog,{doClose:function handleClose(){l(!1)}}))))};l.propTypes={isRTL:e.bool.isRequired};!function init(){if(!document.body.classList.contains("elementor-editor-active"))return null;a.default.render(n.default.createElement(l,{isRTL:!!elementorCommon.config.isRTL}),function getRootElement(){var e=document.querySelector("#e-v4-opt-in-welcome");return e||((e=document.createElement("div")).id="e-v4-opt-in-welcome",document.body.appendChild(e)),e}())}()})()})();