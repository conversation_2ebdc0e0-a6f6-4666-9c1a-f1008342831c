/*! elementor - v3.29.0 - 04-06-2025 */
(()=>{var e={28551:(e,t,o)=>{"use strict";var r=o(96784),n=o(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var u=r(o(39805)),s=r(o(40989)),i=r(o(15118)),a=r(o(29402)),l=r(o(87861)),c=_interopRequireWildcard(o(72876)),f=_interopRequireWildcard(o(23477));function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,o=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?o:t})(e)}function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=n(e)&&"function"!=typeof e)return{default:e};var o=_getRequireWildcardCache(t);if(o&&o.has(e))return o.get(e);var r={__proto__:null},u=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var s in e)if("default"!==s&&{}.hasOwnProperty.call(e,s)){var i=u?Object.getOwnPropertyDescriptor(e,s):null;i&&(i.get||i.set)?Object.defineProperty(r,s,i):r[s]=e[s]}return r.default=e,o&&o.set(e,r),r}function _isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!e})()}t.default=function(e){function EComponent(e){var t;return(0,u.default)(this,EComponent),(t=function _callSuper(e,t,o){return t=(0,a.default)(t),(0,i.default)(e,_isNativeReflectConstruct()?Reflect.construct(t,o||[],(0,a.default)(e).constructor):t.apply(e,o))}(this,EComponent,[e])).loadModules(),t}return(0,l.default)(EComponent,e),(0,s.default)(EComponent,[{key:"getNamespace",value:function getNamespace(){return"notes"}},{key:"defaultHooks",value:function defaultHooks(){return this.importHooks(f)}},{key:"loadModules",value:function loadModules(){for(var e in c)new c[e]}}])}($e.modules.ComponentBase)},23477:(e,t,o)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"NotesAddPanelMenuItem",{enumerable:!0,get:function get(){return r.NotesAddPanelMenuItem}});var r=o(79413)},79413:(e,t,o)=>{"use strict";var r=o(12470).__,n=o(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=t.NotesAddPanelMenuItem=void 0;var u=n(o(39805)),s=n(o(40989)),i=n(o(15118)),a=n(o(29402)),l=n(o(87861));function _isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!e})()}var c=t.NotesAddPanelMenuItem=function(e){function NotesAddPanelMenuItem(){return(0,u.default)(this,NotesAddPanelMenuItem),function _callSuper(e,t,o){return t=(0,a.default)(t),(0,i.default)(e,_isNativeReflectConstruct()?Reflect.construct(t,o||[],(0,a.default)(e).constructor):t.apply(e,o))}(this,NotesAddPanelMenuItem,arguments)}return(0,l.default)(NotesAddPanelMenuItem,e),(0,s.default)(NotesAddPanelMenuItem,[{key:"getCommand",value:function getCommand(){return"panel/state-ready"}},{key:"getId",value:function getId(){return"notes-add-panel-menu-item"}},{key:"apply",value:function apply(){elementor.modules.layouts.panel.pages.menu.Menu.addItem({name:"notes",icon:"eicon-commenting-o",title:r("Notes","elementor")+'<i class="elementor-panel-menu-item-title-badge eicon-pro-icon"></i>',callback:function callback(){var e=elementor.helpers.hasProAndNotConnected();elementor.promotion.showDialog({title:r("Notes","elementor"),content:r("With Notes, teamwork gets even better. Stay in sync with comments, feedback & more on your website.","elementor"),position:{blockStart:"-3",inlineStart:"+10"},targetElement:this.$el,actionButton:{url:e?elementorProEditorConfig.urls.connect:elementor.config.promotions.notes.upgrade_url||"https://go.elementor.com/go-pro-notes/",text:r(e?"Connect & Activate":"Upgrade","elementor")}})}},"navigate_from_page","finder")}}])}($e.modules.hookUI.After);t.default=c},31891:(e,t,o)=>{"use strict";var r=o(12470).__,n=o(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=t.NotesContextMenu=void 0;var u=n(o(39805)),s=n(o(40989)),i=n(o(15118)),a=n(o(29402)),l=n(o(87861));function _isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!e})()}var c=t.NotesContextMenu=function(e){function NotesContextMenu(){return(0,u.default)(this,NotesContextMenu),function _callSuper(e,t,o){return t=(0,a.default)(t),(0,i.default)(e,_isNativeReflectConstruct()?Reflect.construct(t,o||[],(0,a.default)(e).constructor):t.apply(e,o))}(this,NotesContextMenu,arguments)}return(0,l.default)(NotesContextMenu,e),(0,s.default)(NotesContextMenu,[{key:"onInit",value:function onInit(){this.contextMenuNotesGroup()}},{key:"contextMenuNotesGroup",value:function contextMenuNotesGroup(){var e=this;["widget","section","column","container"].forEach((function(t){elementor.hooks.addFilter("elements/".concat(t,"/contextMenuGroups"),e.contextMenuAddGroup)}))}},{key:"contextMenuAddGroup",value:function contextMenuAddGroup(e){var t=_.findWhere(e,{name:"delete"}),o=e.indexOf(t);return-1===o&&(o=e.length),e.splice(o,0,{name:"notes",actions:[{name:"open_notes",title:r("Notes","elementor"),shortcut:'<i class="eicon-pro-icon"></i>',promotionURL:"https://go.elementor.com/go-pro-notes-context-menu/",isEnabled:function isEnabled(){return!1},callback:function callback(){}}]}),e}}])}(elementorModules.editor.utils.Module);t.default=c},72876:(e,t,o)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"NotesContextMenu",{enumerable:!0,get:function get(){return r.NotesContextMenu}});var r=o(31891)},12470:e=>{"use strict";e.exports=wp.i18n},36417:e=>{e.exports=function _assertThisInitialized(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e},e.exports.__esModule=!0,e.exports.default=e.exports},39805:e=>{e.exports=function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")},e.exports.__esModule=!0,e.exports.default=e.exports},40989:(e,t,o)=>{var r=o(45498);function _defineProperties(e,t){for(var o=0;o<t.length;o++){var n=t[o];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,r(n.key),n)}}e.exports=function _createClass(e,t,o){return t&&_defineProperties(e.prototype,t),o&&_defineProperties(e,o),Object.defineProperty(e,"prototype",{writable:!1}),e},e.exports.__esModule=!0,e.exports.default=e.exports},29402:e=>{function _getPrototypeOf(t){return e.exports=_getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},e.exports.__esModule=!0,e.exports.default=e.exports,_getPrototypeOf(t)}e.exports=_getPrototypeOf,e.exports.__esModule=!0,e.exports.default=e.exports},87861:(e,t,o)=>{var r=o(91270);e.exports=function _inherits(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&r(e,t)},e.exports.__esModule=!0,e.exports.default=e.exports},96784:e=>{e.exports=function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}},e.exports.__esModule=!0,e.exports.default=e.exports},15118:(e,t,o)=>{var r=o(10564).default,n=o(36417);e.exports=function _possibleConstructorReturn(e,t){if(t&&("object"==r(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return n(e)},e.exports.__esModule=!0,e.exports.default=e.exports},91270:e=>{function _setPrototypeOf(t,o){return e.exports=_setPrototypeOf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},e.exports.__esModule=!0,e.exports.default=e.exports,_setPrototypeOf(t,o)}e.exports=_setPrototypeOf,e.exports.__esModule=!0,e.exports.default=e.exports},11327:(e,t,o)=>{var r=o(10564).default;e.exports=function toPrimitive(e,t){if("object"!=r(e)||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var n=o.call(e,t||"default");if("object"!=r(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)},e.exports.__esModule=!0,e.exports.default=e.exports},45498:(e,t,o)=>{var r=o(10564).default,n=o(11327);e.exports=function toPropertyKey(e){var t=n(e,"string");return"symbol"==r(t)?t:t+""},e.exports.__esModule=!0,e.exports.default=e.exports},10564:e=>{function _typeof(t){return e.exports=_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports.default=e.exports,_typeof(t)}e.exports=_typeof,e.exports.__esModule=!0,e.exports.default=e.exports}},t={};function __webpack_require__(o){var r=t[o];if(void 0!==r)return r.exports;var n=t[o]={exports:{}};return e[o](n,n.exports,__webpack_require__),n.exports}(()=>{"use strict";var e=__webpack_require__(96784)(__webpack_require__(28551));window.top.$e.components.register(new e.default)})()})();