/*! elementor - v3.29.0 - 04-06-2025 */
/*! For license information please see admin-notifications.min.js.LICENSE.txt */
(()=>{var e={92584:(e,t,r)=>{"use strict";var s,n=Object.defineProperty,i=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,o=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)n(e,r,{get:t[r],enumerable:!0})})(u,{QueryClient:()=>l.QueryClient,QueryClientProvider:()=>l.QueryClientProvider,createQueryClient:()=>createQueryClient,useInfiniteQuery:()=>l.useInfiniteQuery,useMutation:()=>l.useMutation,useQuery:()=>l.useQuery,useQueryClient:()=>l.useQueryClient}),e.exports=(s=u,((e,t,r,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of a(t))o.call(e,u)||u===r||n(e,u,{get:()=>t[u],enumerable:!(s=i(t,u))||s.enumerable});return e})(n({},"__esModule",{value:!0}),s));var c=r(51688),l=r(51688);function createQueryClient(){return new c.QueryClient({defaultOptions:{queries:{refetchOnWindowFocus:!1,refetchOnReconnect:!1}}})}},46120:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getNotifications=void 0;t.getNotifications=function getNotifications(){return function request(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return new Promise((function(r,s){elementorCommon.ajax.addRequest(e,{success:r,error:s,data:t})}))}("notifications_get")}},83876:(e,t,r)=>{"use strict";var s=r(62688),n=r(96784),i=r(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.BarButtonNotification=void 0;var a=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=i(e)&&"function"!=typeof e)return{default:e};var r=_getRequireWildcardCache(t);if(r&&r.has(e))return r.get(e);var s={__proto__:null},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&{}.hasOwnProperty.call(e,a)){var o=n?Object.getOwnPropertyDescriptor(e,a):null;o&&(o.get||o.set)?Object.defineProperty(s,a,o):s[a]=e[a]}return s.default=e,r&&r.set(e,s),s}(r(41594)),o=n(r(18821)),u=r(74324),c=r(86956);function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?r:t})(e)}(t.BarButtonNotification=function BarButtonNotification(e){var t=e.defaultIsRead,r=(0,a.useState)(!1),s=(0,o.default)(r,2),n=s[0],i=s[1],l=(0,a.useState)(t),h=(0,o.default)(l,2),p=h[0],f=h[1];return a.default.createElement(a.default.Fragment,null,a.default.createElement("button",{className:"e-admin-top-bar__bar-button",style:{backgroundColor:"transparent",border:"none"},onClick:function onClick(e){e.preventDefault(),i(!0)}},a.default.createElement(c.Badge,{color:"primary",variant:"dot",invisible:p,sx:{mx:.5}},a.default.createElement("i",{className:"e-admin-top-bar__bar-button-icon eicon-speakerphone"})),a.default.createElement("span",{className:"e-admin-top-bar__bar-button-title"},e.children)),a.default.createElement(u.WhatsNew,{isOpen:n,setIsOpen:i,setIsRead:f}))}).propTypes={defaultIsRead:s.bool,children:s.any.isRequired}},58644:(e,t,r)=>{"use strict";var s=r(62688),n=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.WhatsNewDrawerContent=void 0;var i=n(r(41594)),a=r(92584),o=r(46120),u=r(86956),c=r(25206);(t.WhatsNewDrawerContent=function WhatsNewDrawerContent(e){var t=e.setIsOpen,r=(0,a.useQuery)({queryKey:["e-notifications"],queryFn:o.getNotifications}),s=r.isPending,n=r.error,l=r.data;return s?i.default.createElement(u.Box,null,i.default.createElement(u.LinearProgress,{color:"secondary"})):n?i.default.createElement(u.Box,null,"An error has occurred: ",n):l.map((function(e,r){return i.default.createElement(c.WhatsNewItem,{key:r,item:e,itemIndex:r,itemsLength:l.length,setIsOpen:t})}))}).propTypes={setIsOpen:s.func.isRequired}},24752:(e,t,r)=>{"use strict";var s=r(62688),n=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.WhatsNewItemChips=void 0;var i=n(r(41594)),a=n(r(78304)),o=r(86956);(t.WhatsNewItemChips=function WhatsNewItemChips(e){var t=e.chipPlan,r=e.chipTags,s=e.itemIndex,n=[];return t&&n.push({color:"promotion",size:"small",label:t}),r&&r.forEach((function(e){n.push({variant:"outlined",size:"small",label:e})})),n.length?i.default.createElement(o.Stack,{direction:"row",flexWrap:"wrap",gap:1,sx:{pb:1}},n.map((function(e,t){return i.default.createElement(o.Chip,(0,a.default)({key:"chip-".concat(s).concat(t)},e))}))):null}).propTypes={chipPlan:s.string,chipTags:s.array,itemIndex:s.number.isRequired}},46555:(e,t,r)=>{"use strict";var s=r(62688),n=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.WhatsNewItemThumbnail=void 0;var i=n(r(41594)),a=r(86956),o=r(94841);(t.WhatsNewItemThumbnail=function WhatsNewItemThumbnail(e){var t=e.imageSrc,r=e.title,s=e.link;return i.default.createElement(a.Box,{sx:{pb:2}},i.default.createElement(o.WrapperWithLink,{link:s},i.default.createElement("img",{src:t,alt:r,style:{maxWidth:"100%"}})))}).propTypes={imageSrc:s.string.isRequired,title:s.string.isRequired,link:s.string}},56971:(e,t,r)=>{"use strict";var s=r(62688),n=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.WhatsNewItemTopicLine=void 0;var i=n(r(41594)),a=r(86956);(t.WhatsNewItemTopicLine=function WhatsNewItemTopicLine(e){var t=e.topic,r=e.date;return i.default.createElement(a.Stack,{direction:"row",divider:i.default.createElement(a.Divider,{orientation:"vertical",flexItem:!0}),spacing:1,color:"text.tertiary",sx:{pb:1}},t&&i.default.createElement(a.Box,null,t),r&&i.default.createElement(a.Box,null,r))}).propTypes={topic:s.string,date:s.string}},25206:(e,t,r)=>{"use strict";var s=r(62688),n=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.WhatsNewItem=void 0;var i=n(r(41594)),a=r(86956),o=r(56971),u=r(94841),c=r(46555),l=r(24752);(t.WhatsNewItem=function WhatsNewItem(e){var t=e.item,r=e.itemIndex,s=e.itemsLength,n=e.setIsOpen;return i.default.createElement(a.Box,{key:r,display:"flex",flexDirection:"column",sx:{pt:2}},(t.topic||t.date)&&i.default.createElement(o.WhatsNewItemTopicLine,{topic:t.topic,date:t.date}),i.default.createElement(u.WrapperWithLink,{link:t.link},i.default.createElement(a.Typography,{variant:"subtitle1",sx:{pb:2}},t.title)),t.imageSrc&&i.default.createElement(c.WhatsNewItemThumbnail,{imageSrc:t.imageSrc,link:t.link,title:t.title}),i.default.createElement(l.WhatsNewItemChips,{chipPlan:t.chipPlan,chipTags:t.chipTags,itemIndex:r}),t.description&&i.default.createElement(a.Typography,{variant:"body2",color:"text.secondary",sx:{pb:2}},t.description,t.readMoreText&&i.default.createElement(i.default.Fragment,null," ",i.default.createElement(a.Link,{href:t.link,color:"info.main",target:"_blank"},t.readMoreText))),t.cta&&t.ctaLink&&i.default.createElement(a.Box,{sx:{pb:2}},i.default.createElement(a.Button,{href:t.ctaLink,target:t.ctaLink.startsWith("#")?"_self":"_blank",variant:"contained",size:"small",color:"promotion",onClick:t.ctaLink.startsWith("#")?function(){return n(!1)}:function(){}},t.cta)),r!==s-1&&i.default.createElement(a.Divider,{sx:{my:1}}))}).propTypes={item:s.object.isRequired,itemIndex:s.number.isRequired,itemsLength:s.number.isRequired,setIsOpen:s.func.isRequired}},30482:(e,t,r)=>{"use strict";var s=r(62688),n=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.WhatsNewTopBar=void 0;var i=n(r(41594)),a=r(86956),o=r(12470),u=r(59190);(t.WhatsNewTopBar=function WhatsNewTopBar(e){var t=e.setIsOpen;return i.default.createElement(i.default.Fragment,null,i.default.createElement(a.AppBar,{elevation:0,position:"sticky",sx:{backgroundColor:"background.default"}},i.default.createElement(a.Toolbar,{variant:"dense"},i.default.createElement(a.Typography,{variant:"overline",sx:{flexGrow:1}},(0,o.__)("What's New","elementor")),i.default.createElement(a.IconButton,{"aria-label":"close",size:"small",onClick:function onClick(){return t(!1)}},i.default.createElement(u.XIcon,null)))),i.default.createElement(a.Divider,null))}).propTypes={setIsOpen:s.func.isRequired}},74324:(e,t,r)=>{"use strict";var s=r(62688),n=r(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.WhatsNew=void 0;var i=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=n(e)&&"function"!=typeof e)return{default:e};var r=_getRequireWildcardCache(t);if(r&&r.has(e))return r.get(e);var s={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&{}.hasOwnProperty.call(e,a)){var o=i?Object.getOwnPropertyDescriptor(e,a):null;o&&(o.get||o.set)?Object.defineProperty(s,a,o):s[a]=e[a]}return s.default=e,r&&r.set(e,s),s}(r(41594)),a=r(86956),o=r(92584),u=r(30482),c=r(58644);function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?r:t})(e)}var l=new o.QueryClient({defaultOptions:{queries:{refetchOnWindowFocus:!1,retry:!1,staleTime:18e5}}});(t.WhatsNew=function WhatsNew(e){var t,r,s=e.isOpen,n=e.setIsOpen,h=e.setIsRead,p=e.anchorPosition,f=void 0===p?"right":p;return(0,i.useEffect)((function(){s&&h(!0)}),[s,h]),i.default.createElement(i.default.Fragment,null,i.default.createElement(o.QueryClientProvider,{client:l},i.default.createElement(a.DirectionProvider,{rtl:elementorCommon.config.isRTL},i.default.createElement(a.ThemeProvider,{colorScheme:(null===(t=window.elementor)||void 0===t||null===(r=t.getPreferences)||void 0===r?void 0:r.call(t,"ui_theme"))||"auto"},i.default.createElement(a.Drawer,{anchor:f,open:s,onClose:function onClose(){return n(!1)},ModalProps:{style:{zIndex:999999}}},i.default.createElement(a.Box,{sx:{width:320,backgroundColor:"background.default"},role:"presentation"},i.default.createElement(u.WhatsNewTopBar,{setIsOpen:n}),i.default.createElement(a.Box,{sx:{padding:"16px"}},i.default.createElement(c.WhatsNewDrawerContent,{setIsOpen:n}))))))))}).propTypes={isOpen:s.bool.isRequired,setIsOpen:s.func.isRequired,setIsRead:s.func.isRequired,anchorPosition:s.oneOf(["left","top","right","bottom"])}},94841:(e,t,r)=>{"use strict";var s=r(62688),n=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.WrapperWithLink=void 0;var i=n(r(41594)),a=r(86956);(t.WrapperWithLink=function WrapperWithLink(e){var t=e.link,r=e.children;return t?i.default.createElement(a.Link,{href:t,target:"_blank",underline:"none",color:"inherit",sx:{"&:hover":{color:"inherit"}}},r):r}).propTypes={link:s.string,children:s.any.isRequired}},59190:(e,t,r)=>{"use strict";var s=r(96784),n=r(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.XIcon=void 0;var i=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=n(e)&&"function"!=typeof e)return{default:e};var r=_getRequireWildcardCache(t);if(r&&r.has(e))return r.get(e);var s={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&{}.hasOwnProperty.call(e,a)){var o=i?Object.getOwnPropertyDescriptor(e,a):null;o&&(o.get||o.set)?Object.defineProperty(s,a,o):s[a]=e[a]}return s.default=e,r&&r.set(e,s),s}(r(41594)),a=s(r(78304)),o=r(86956);function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?r:t})(e)}t.XIcon=(0,i.forwardRef)((function(e,t){return i.default.createElement(o.SvgIcon,(0,a.default)({viewBox:"0 0 24 24"},e,{ref:t}),i.default.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M18.5303 5.46967C18.8232 5.76256 18.8232 6.23744 18.5303 6.53033L6.53033 18.5303C6.23744 18.8232 5.76256 18.8232 5.46967 18.5303C5.17678 18.2374 5.17678 17.7626 5.46967 17.4697L17.4697 5.46967C17.7626 5.17678 18.2374 5.17678 18.5303 5.46967Z"}),i.default.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M5.46967 5.46967C5.76256 5.17678 6.23744 5.17678 6.53033 5.46967L18.5303 17.4697C18.8232 17.7626 18.8232 18.2374 18.5303 18.5303C18.2374 18.8232 17.7626 18.8232 17.4697 18.5303L5.46967 6.53033C5.17678 6.23744 5.17678 5.76256 5.46967 5.46967Z"}))}))},40362:(e,t,r)=>{"use strict";var s=r(56441);function emptyFunction(){}function emptyFunctionWithReset(){}emptyFunctionWithReset.resetWarningCache=emptyFunction,e.exports=function(){function shim(e,t,r,n,i,a){if(a!==s){var o=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw o.name="Invariant Violation",o}}function getShim(){return shim}shim.isRequired=shim;var e={array:shim,bigint:shim,bool:shim,func:shim,number:shim,object:shim,string:shim,symbol:shim,any:shim,arrayOf:getShim,element:shim,elementType:shim,instanceOf:getShim,node:shim,objectOf:getShim,oneOf:getShim,oneOfType:getShim,shape:getShim,exact:getShim,checkPropTypes:emptyFunctionWithReset,resetWarningCache:emptyFunction};return e.PropTypes=e,e}},62688:(e,t,r)=>{e.exports=r(40362)()},56441:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},2192:(e,t,r)=>{"use strict";var s=r(41594),n=Symbol.for("react.element"),i=Symbol.for("react.fragment"),a=Object.prototype.hasOwnProperty,o=s.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,u={key:!0,ref:!0,__self:!0,__source:!0};function q(e,t,r){var s,i={},c=null,l=null;for(s in void 0!==r&&(c=""+r),void 0!==t.key&&(c=""+t.key),void 0!==t.ref&&(l=t.ref),t)a.call(t,s)&&!u.hasOwnProperty(s)&&(i[s]=t[s]);if(e&&e.defaultProps)for(s in t=e.defaultProps)void 0===i[s]&&(i[s]=t[s]);return{$$typeof:n,type:e,key:c,ref:l,props:i,_owner:o.current}}t.Fragment=i,t.jsx=q,t.jsxs=q},62540:(e,t,r)=>{"use strict";e.exports=r(2192)},41594:e=>{"use strict";e.exports=React},86956:e=>{"use strict";e.exports=elementorV2.ui},12470:e=>{"use strict";e.exports=wp.i18n},78113:e=>{e.exports=function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,s=Array(t);r<t;r++)s[r]=e[r];return s},e.exports.__esModule=!0,e.exports.default=e.exports},70569:e=>{e.exports=function _arrayWithHoles(e){if(Array.isArray(e))return e},e.exports.__esModule=!0,e.exports.default=e.exports},78304:e=>{function _extends(){return e.exports=_extends=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var s in r)({}).hasOwnProperty.call(r,s)&&(e[s]=r[s])}return e},e.exports.__esModule=!0,e.exports.default=e.exports,_extends.apply(null,arguments)}e.exports=_extends,e.exports.__esModule=!0,e.exports.default=e.exports},96784:e=>{e.exports=function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}},e.exports.__esModule=!0,e.exports.default=e.exports},65474:e=>{e.exports=function _iterableToArrayLimit(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var s,n,i,a,o=[],u=!0,c=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;u=!1}else for(;!(u=(s=i.call(r)).done)&&(o.push(s.value),o.length!==t);u=!0);}catch(e){c=!0,n=e}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(c)throw n}}return o}},e.exports.__esModule=!0,e.exports.default=e.exports},11018:e=>{e.exports=function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},e.exports.__esModule=!0,e.exports.default=e.exports},18821:(e,t,r)=>{var s=r(70569),n=r(65474),i=r(37744),a=r(11018);e.exports=function _slicedToArray(e,t){return s(e)||n(e,t)||i(e,t)||a()},e.exports.__esModule=!0,e.exports.default=e.exports},10564:e=>{function _typeof(t){return e.exports=_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports.default=e.exports,_typeof(t)}e.exports=_typeof,e.exports.__esModule=!0,e.exports.default=e.exports},37744:(e,t,r)=>{var s=r(78113);e.exports=function _unsupportedIterableToArray(e,t){if(e){if("string"==typeof e)return s(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?s(e,t):void 0}},e.exports.__esModule=!0,e.exports.default=e.exports},52803:(e,t,r)=>{"use strict";var s,n=Object.defineProperty,i=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,o=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)n(e,r,{get:t[r],enumerable:!0})})(u,{FocusManager:()=>h,focusManager:()=>p}),e.exports=(s=u,((e,t,r,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of a(t))o.call(e,u)||u===r||n(e,u,{get:()=>t[u],enumerable:!(s=i(t,u))||s.enumerable});return e})(n({},"__esModule",{value:!0}),s));var c=r(66133),l=r(73025),h=class extends c.Subscribable{#e;#t;#r;constructor(){super(),this.#r=e=>{if(!l.isServer&&window.addEventListener){const listener=()=>e();return window.addEventListener("visibilitychange",listener,!1),()=>{window.removeEventListener("visibilitychange",listener)}}}}onSubscribe(){this.#t||this.setEventListener(this.#r)}onUnsubscribe(){this.hasListeners()||(this.#t?.(),this.#t=void 0)}setEventListener(e){this.#r=e,this.#t?.(),this.#t=e((e=>{"boolean"==typeof e?this.setFocused(e):this.onFocus()}))}setFocused(e){this.#e!==e&&(this.#e=e,this.onFocus())}onFocus(){const e=this.isFocused();this.listeners.forEach((t=>{t(e)}))}isFocused(){return"boolean"==typeof this.#e?this.#e:"hidden"!==globalThis.document?.visibilityState}},p=new h},28620:e=>{"use strict";var t,r=Object.defineProperty,s=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,a={};function defaultTransformerFn(e){return e}function dehydrateMutation(e){return{mutationKey:e.options.mutationKey,state:e.state,...e.options.scope&&{scope:e.options.scope},...e.meta&&{meta:e.meta}}}function dehydrateQuery(e,t){return{state:{...e.state,...void 0!==e.state.data&&{data:t(e.state.data)}},queryKey:e.queryKey,queryHash:e.queryHash,..."pending"===e.state.status&&{promise:e.promise?.then(t).catch((e=>Promise.reject(new Error("redacted"))))},...e.meta&&{meta:e.meta}}}function defaultShouldDehydrateMutation(e){return e.state.isPaused}function defaultShouldDehydrateQuery(e){return"success"===e.state.status}function dehydrate(e,t={}){const r=t.shouldDehydrateMutation??e.getDefaultOptions().dehydrate?.shouldDehydrateMutation??defaultShouldDehydrateMutation,s=e.getMutationCache().getAll().flatMap((e=>r(e)?[dehydrateMutation(e)]:[])),n=t.shouldDehydrateQuery??e.getDefaultOptions().dehydrate?.shouldDehydrateQuery??defaultShouldDehydrateQuery,i=t.serializeData??e.getDefaultOptions().dehydrate?.serializeData??defaultTransformerFn;return{mutations:s,queries:e.getQueryCache().getAll().flatMap((e=>n(e)?[dehydrateQuery(e,i)]:[]))}}function hydrate(e,t,r){if("object"!=typeof t||null===t)return;const s=e.getMutationCache(),n=e.getQueryCache(),i=r?.defaultOptions?.deserializeData??e.getDefaultOptions().hydrate?.deserializeData??defaultTransformerFn,a=t.mutations||[],o=t.queries||[];a.forEach((({state:t,...n})=>{s.build(e,{...e.getDefaultOptions().hydrate?.mutations,...r?.defaultOptions?.mutations,...n},t)})),o.forEach((({queryKey:t,state:s,queryHash:a,meta:o,promise:u})=>{let c=n.get(a);const l=void 0===s.data?s.data:i(s.data);if(c){if(c.state.dataUpdatedAt<s.dataUpdatedAt){const{fetchStatus:e,...t}=s;c.setState({...t,data:l})}}else c=n.build(e,{...e.getDefaultOptions().hydrate?.queries,...r?.defaultOptions?.queries,queryKey:t,queryHash:a,meta:o},{...s,data:l,fetchStatus:"idle"});if(u){const e=Promise.resolve(u).then(i);c.fetch(void 0,{initialPromise:e})}}))}((e,t)=>{for(var s in t)r(e,s,{get:t[s],enumerable:!0})})(a,{defaultShouldDehydrateMutation:()=>defaultShouldDehydrateMutation,defaultShouldDehydrateQuery:()=>defaultShouldDehydrateQuery,dehydrate:()=>dehydrate,hydrate:()=>hydrate}),e.exports=(t=a,((e,t,a,o)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of n(t))i.call(e,u)||u===a||r(e,u,{get:()=>t[u],enumerable:!(o=s(t,u))||o.enumerable});return e})(r({},"__esModule",{value:!0}),t))},51934:(e,t,r)=>{"use strict";var s,n=Object.defineProperty,i=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,o=Object.prototype.hasOwnProperty,__copyProps=(e,t,r,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of a(t))o.call(e,u)||u===r||n(e,u,{get:()=>t[u],enumerable:!(s=i(t,u))||s.enumerable});return e},u={};((e,t)=>{for(var r in t)n(e,r,{get:t[r],enumerable:!0})})(u,{CancelledError:()=>c.CancelledError,InfiniteQueryObserver:()=>d.InfiniteQueryObserver,Mutation:()=>M.Mutation,MutationCache:()=>y.MutationCache,MutationObserver:()=>b.MutationObserver,QueriesObserver:()=>f.QueriesObserver,Query:()=>j.Query,QueryCache:()=>l.QueryCache,QueryClient:()=>h.QueryClient,QueryObserver:()=>p.QueryObserver,defaultShouldDehydrateMutation:()=>w.defaultShouldDehydrateMutation,defaultShouldDehydrateQuery:()=>w.defaultShouldDehydrateQuery,dehydrate:()=>w.dehydrate,focusManager:()=>v.focusManager,hashKey:()=>g.hashKey,hydrate:()=>w.hydrate,isCancelledError:()=>P.isCancelledError,isServer:()=>g.isServer,keepPreviousData:()=>g.keepPreviousData,matchMutation:()=>g.matchMutation,matchQuery:()=>g.matchQuery,notifyManager:()=>m.notifyManager,onlineManager:()=>O.onlineManager,replaceEqualDeep:()=>g.replaceEqualDeep,skipToken:()=>g.skipToken}),e.exports=(s=u,__copyProps(n({},"__esModule",{value:!0}),s));var c=r(7785),l=r(18976),h=r(56587),p=r(63524),f=r(55828),d=r(75360),y=r(69239),b=r(3877),m=r(7734),v=r(52803),O=r(97692),g=r(73025),P=r(7785),w=r(28620);((e,t,r)=>{__copyProps(e,t,"default"),r&&__copyProps(r,t,"default")})(u,r(98813),e.exports);var j=r(44478),M=r(46071)},52200:(e,t,r)=>{"use strict";var s,n=Object.defineProperty,i=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,o=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)n(e,r,{get:t[r],enumerable:!0})})(u,{hasNextPage:()=>hasNextPage,hasPreviousPage:()=>hasPreviousPage,infiniteQueryBehavior:()=>infiniteQueryBehavior}),e.exports=(s=u,((e,t,r,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of a(t))o.call(e,u)||u===r||n(e,u,{get:()=>t[u],enumerable:!(s=i(t,u))||s.enumerable});return e})(n({},"__esModule",{value:!0}),s));var c=r(73025);function infiniteQueryBehavior(e){return{onFetch:(t,r)=>{const s=t.options,n=t.fetchOptions?.meta?.fetchMore?.direction,i=t.state.data?.pages||[],a=t.state.data?.pageParams||[];let o={pages:[],pageParams:[]},u=0;const fetchFn=async()=>{let r=!1;const l=(0,c.ensureQueryFn)(t.options,t.fetchOptions),fetchPage=async(e,s,n)=>{if(r)return Promise.reject();if(null==s&&e.pages.length)return Promise.resolve(e);const i={queryKey:t.queryKey,pageParam:s,direction:n?"backward":"forward",meta:t.options.meta};var a;a=i,Object.defineProperty(a,"signal",{enumerable:!0,get:()=>(t.signal.aborted?r=!0:t.signal.addEventListener("abort",(()=>{r=!0})),t.signal)});const o=await l(i),{maxPages:u}=t.options,h=n?c.addToStart:c.addToEnd;return{pages:h(e.pages,o,u),pageParams:h(e.pageParams,s,u)}};if(n&&i.length){const e="backward"===n,t={pages:i,pageParams:a},r=(e?getPreviousPageParam:getNextPageParam)(s,t);o=await fetchPage(t,r,e)}else{const t=e??i.length;do{const e=0===u?a[0]??s.initialPageParam:getNextPageParam(s,o);if(u>0&&null==e)break;o=await fetchPage(o,e),u++}while(u<t)}return o};t.options.persister?t.fetchFn=()=>t.options.persister?.(fetchFn,{queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},r):t.fetchFn=fetchFn}}}function getNextPageParam(e,{pages:t,pageParams:r}){const s=t.length-1;return t.length>0?e.getNextPageParam(t[s],t,r[s],r):void 0}function getPreviousPageParam(e,{pages:t,pageParams:r}){return t.length>0?e.getPreviousPageParam?.(t[0],t,r[0],r):void 0}function hasNextPage(e,t){return!!t&&null!=getNextPageParam(e,t)}function hasPreviousPage(e,t){return!(!t||!e.getPreviousPageParam)&&null!=getPreviousPageParam(e,t)}},75360:(e,t,r)=>{"use strict";var s,n=Object.defineProperty,i=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,o=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)n(e,r,{get:t[r],enumerable:!0})})(u,{InfiniteQueryObserver:()=>h}),e.exports=(s=u,((e,t,r,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of a(t))o.call(e,u)||u===r||n(e,u,{get:()=>t[u],enumerable:!(s=i(t,u))||s.enumerable});return e})(n({},"__esModule",{value:!0}),s));var c=r(63524),l=r(52200),h=class extends c.QueryObserver{constructor(e,t){super(e,t)}bindMethods(){super.bindMethods(),this.fetchNextPage=this.fetchNextPage.bind(this),this.fetchPreviousPage=this.fetchPreviousPage.bind(this)}setOptions(e,t){super.setOptions({...e,behavior:(0,l.infiniteQueryBehavior)()},t)}getOptimisticResult(e){return e.behavior=(0,l.infiniteQueryBehavior)(),super.getOptimisticResult(e)}fetchNextPage(e){return this.fetch({...e,meta:{fetchMore:{direction:"forward"}}})}fetchPreviousPage(e){return this.fetch({...e,meta:{fetchMore:{direction:"backward"}}})}createResult(e,t){const{state:r}=e,s=super.createResult(e,t),{isFetching:n,isRefetching:i,isError:a,isRefetchError:o}=s,u=r.fetchMeta?.fetchMore?.direction,c=a&&"forward"===u,h=n&&"forward"===u,p=a&&"backward"===u,f=n&&"backward"===u;return{...s,fetchNextPage:this.fetchNextPage,fetchPreviousPage:this.fetchPreviousPage,hasNextPage:(0,l.hasNextPage)(t,r.data),hasPreviousPage:(0,l.hasPreviousPage)(t,r.data),isFetchNextPageError:c,isFetchingNextPage:h,isFetchPreviousPageError:p,isFetchingPreviousPage:f,isRefetchError:o&&!c&&!p,isRefetching:i&&!h&&!f}}}},46071:(e,t,r)=>{"use strict";var s,n=Object.defineProperty,i=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,o=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)n(e,r,{get:t[r],enumerable:!0})})(u,{Mutation:()=>p,getDefaultState:()=>getDefaultState}),e.exports=(s=u,((e,t,r,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of a(t))o.call(e,u)||u===r||n(e,u,{get:()=>t[u],enumerable:!(s=i(t,u))||s.enumerable});return e})(n({},"__esModule",{value:!0}),s));var c=r(7734),l=r(95181),h=r(7785),p=class extends l.Removable{#s;#n;#i;constructor(e){super(),this.mutationId=e.mutationId,this.#n=e.mutationCache,this.#s=[],this.state=e.state||{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0},this.setOptions(e.options),this.scheduleGc()}setOptions(e){this.options=e,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(e){this.#s.includes(e)||(this.#s.push(e),this.clearGcTimeout(),this.#n.notify({type:"observerAdded",mutation:this,observer:e}))}removeObserver(e){this.#s=this.#s.filter((t=>t!==e)),this.scheduleGc(),this.#n.notify({type:"observerRemoved",mutation:this,observer:e})}optionalRemove(){this.#s.length||("pending"===this.state.status?this.scheduleGc():this.#n.remove(this))}continue(){return this.#i?.continue()??this.execute(this.state.variables)}async execute(e){this.#i=(0,h.createRetryer)({fn:()=>this.options.mutationFn?this.options.mutationFn(e):Promise.reject(new Error("No mutationFn found")),onFail:(e,t)=>{this.#a({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#a({type:"pause"})},onContinue:()=>{this.#a({type:"continue"})},retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#n.canRun(this)});const t="pending"===this.state.status,r=!this.#i.canStart();try{if(!t){this.#a({type:"pending",variables:e,isPaused:r}),await(this.#n.config.onMutate?.(e,this));const t=await(this.options.onMutate?.(e));t!==this.state.context&&this.#a({type:"pending",context:t,variables:e,isPaused:r})}const s=await this.#i.start();return await(this.#n.config.onSuccess?.(s,e,this.state.context,this)),await(this.options.onSuccess?.(s,e,this.state.context)),await(this.#n.config.onSettled?.(s,null,this.state.variables,this.state.context,this)),await(this.options.onSettled?.(s,null,e,this.state.context)),this.#a({type:"success",data:s}),s}catch(t){try{throw await(this.#n.config.onError?.(t,e,this.state.context,this)),await(this.options.onError?.(t,e,this.state.context)),await(this.#n.config.onSettled?.(void 0,t,this.state.variables,this.state.context,this)),await(this.options.onSettled?.(void 0,t,e,this.state.context)),t}finally{this.#a({type:"error",error:t})}}finally{this.#n.runNext(this)}}#a(e){this.state=(t=>{switch(e.type){case"failed":return{...t,failureCount:e.failureCount,failureReason:e.error};case"pause":return{...t,isPaused:!0};case"continue":return{...t,isPaused:!1};case"pending":return{...t,context:e.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:e.isPaused,status:"pending",variables:e.variables,submittedAt:Date.now()};case"success":return{...t,data:e.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...t,data:void 0,error:e.error,failureCount:t.failureCount+1,failureReason:e.error,isPaused:!1,status:"error"}}})(this.state),c.notifyManager.batch((()=>{this.#s.forEach((t=>{t.onMutationUpdate(e)})),this.#n.notify({mutation:this,type:"updated",action:e})}))}};function getDefaultState(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}},69239:(e,t,r)=>{"use strict";var s,n=Object.defineProperty,i=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,o=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)n(e,r,{get:t[r],enumerable:!0})})(u,{MutationCache:()=>f}),e.exports=(s=u,((e,t,r,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of a(t))o.call(e,u)||u===r||n(e,u,{get:()=>t[u],enumerable:!(s=i(t,u))||s.enumerable});return e})(n({},"__esModule",{value:!0}),s));var c=r(7734),l=r(46071),h=r(73025),p=r(66133),f=class extends p.Subscribable{constructor(e={}){super(),this.config=e,this.#o=new Map,this.#u=Date.now()}#o;#u;build(e,t,r){const s=new l.Mutation({mutationCache:this,mutationId:++this.#u,options:e.defaultMutationOptions(t),state:r});return this.add(s),s}add(e){const t=scopeFor(e),r=this.#o.get(t)??[];r.push(e),this.#o.set(t,r),this.notify({type:"added",mutation:e})}remove(e){const t=scopeFor(e);if(this.#o.has(t)){const r=this.#o.get(t)?.filter((t=>t!==e));r&&(0===r.length?this.#o.delete(t):this.#o.set(t,r))}this.notify({type:"removed",mutation:e})}canRun(e){const t=this.#o.get(scopeFor(e))?.find((e=>"pending"===e.state.status));return!t||t===e}runNext(e){const t=this.#o.get(scopeFor(e))?.find((t=>t!==e&&t.state.isPaused));return t?.continue()??Promise.resolve()}clear(){c.notifyManager.batch((()=>{this.getAll().forEach((e=>{this.remove(e)}))}))}getAll(){return[...this.#o.values()].flat()}find(e){const t={exact:!0,...e};return this.getAll().find((e=>(0,h.matchMutation)(t,e)))}findAll(e={}){return this.getAll().filter((t=>(0,h.matchMutation)(e,t)))}notify(e){c.notifyManager.batch((()=>{this.listeners.forEach((t=>{t(e)}))}))}resumePausedMutations(){const e=this.getAll().filter((e=>e.state.isPaused));return c.notifyManager.batch((()=>Promise.all(e.map((e=>e.continue().catch(h.noop))))))}};function scopeFor(e){return e.options.scope?.id??String(e.mutationId)}},3877:(e,t,r)=>{"use strict";var s,n=Object.defineProperty,i=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,o=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)n(e,r,{get:t[r],enumerable:!0})})(u,{MutationObserver:()=>f}),e.exports=(s=u,((e,t,r,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of a(t))o.call(e,u)||u===r||n(e,u,{get:()=>t[u],enumerable:!(s=i(t,u))||s.enumerable});return e})(n({},"__esModule",{value:!0}),s));var c=r(46071),l=r(7734),h=r(66133),p=r(73025),f=class extends h.Subscribable{#c;#l=void 0;#h;#p;constructor(e,t){super(),this.#c=e,this.setOptions(t),this.bindMethods(),this.#f()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(e){const t=this.options;this.options=this.#c.defaultMutationOptions(e),(0,p.shallowEqualObjects)(this.options,t)||this.#c.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.#h,observer:this}),t?.mutationKey&&this.options.mutationKey&&(0,p.hashKey)(t.mutationKey)!==(0,p.hashKey)(this.options.mutationKey)?this.reset():"pending"===this.#h?.state.status&&this.#h.setOptions(this.options)}onUnsubscribe(){this.hasListeners()||this.#h?.removeObserver(this)}onMutationUpdate(e){this.#f(),this.#d(e)}getCurrentResult(){return this.#l}reset(){this.#h?.removeObserver(this),this.#h=void 0,this.#f(),this.#d()}mutate(e,t){return this.#p=t,this.#h?.removeObserver(this),this.#h=this.#c.getMutationCache().build(this.#c,this.options),this.#h.addObserver(this),this.#h.execute(e)}#f(){const e=this.#h?.state??(0,c.getDefaultState)();this.#l={...e,isPending:"pending"===e.status,isSuccess:"success"===e.status,isError:"error"===e.status,isIdle:"idle"===e.status,mutate:this.mutate,reset:this.reset}}#d(e){l.notifyManager.batch((()=>{if(this.#p&&this.hasListeners()){const t=this.#l.variables,r=this.#l.context;"success"===e?.type?(this.#p.onSuccess?.(e.data,t,r),this.#p.onSettled?.(e.data,null,t,r)):"error"===e?.type&&(this.#p.onError?.(e.error,t,r),this.#p.onSettled?.(void 0,e.error,t,r))}this.listeners.forEach((e=>{e(this.#l)}))}))}}},7734:e=>{"use strict";var t,r=Object.defineProperty,s=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,a={};function createNotifyManager(){let e=[],t=0,notifyFn=e=>{e()},batchNotifyFn=e=>{e()},scheduleFn=e=>setTimeout(e,0);const schedule=r=>{t?e.push(r):scheduleFn((()=>{notifyFn(r)}))};return{batch:r=>{let s;t++;try{s=r()}finally{t--,t||(()=>{const t=e;e=[],t.length&&scheduleFn((()=>{batchNotifyFn((()=>{t.forEach((e=>{notifyFn(e)}))}))}))})()}return s},batchCalls:e=>(...t)=>{schedule((()=>{e(...t)}))},schedule,setNotifyFunction:e=>{notifyFn=e},setBatchNotifyFunction:e=>{batchNotifyFn=e},setScheduler:e=>{scheduleFn=e}}}((e,t)=>{for(var s in t)r(e,s,{get:t[s],enumerable:!0})})(a,{createNotifyManager:()=>createNotifyManager,notifyManager:()=>o}),e.exports=(t=a,((e,t,a,o)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of n(t))i.call(e,u)||u===a||r(e,u,{get:()=>t[u],enumerable:!(o=s(t,u))||o.enumerable});return e})(r({},"__esModule",{value:!0}),t));var o=createNotifyManager()},97692:(e,t,r)=>{"use strict";var s,n=Object.defineProperty,i=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,o=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)n(e,r,{get:t[r],enumerable:!0})})(u,{OnlineManager:()=>h,onlineManager:()=>p}),e.exports=(s=u,((e,t,r,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of a(t))o.call(e,u)||u===r||n(e,u,{get:()=>t[u],enumerable:!(s=i(t,u))||s.enumerable});return e})(n({},"__esModule",{value:!0}),s));var c=r(66133),l=r(73025),h=class extends c.Subscribable{#y=!0;#t;#r;constructor(){super(),this.#r=e=>{if(!l.isServer&&window.addEventListener){const onlineListener=()=>e(!0),offlineListener=()=>e(!1);return window.addEventListener("online",onlineListener,!1),window.addEventListener("offline",offlineListener,!1),()=>{window.removeEventListener("online",onlineListener),window.removeEventListener("offline",offlineListener)}}}}onSubscribe(){this.#t||this.setEventListener(this.#r)}onUnsubscribe(){this.hasListeners()||(this.#t?.(),this.#t=void 0)}setEventListener(e){this.#r=e,this.#t?.(),this.#t=e(this.setOnline.bind(this))}setOnline(e){this.#y!==e&&(this.#y=e,this.listeners.forEach((t=>{t(e)})))}isOnline(){return this.#y}},p=new h},55828:(e,t,r)=>{"use strict";var s,n=Object.defineProperty,i=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,o=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)n(e,r,{get:t[r],enumerable:!0})})(u,{QueriesObserver:()=>f}),e.exports=(s=u,((e,t,r,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of a(t))o.call(e,u)||u===r||n(e,u,{get:()=>t[u],enumerable:!(s=i(t,u))||s.enumerable});return e})(n({},"__esModule",{value:!0}),s));var c=r(7734),l=r(63524),h=r(66133),p=r(73025);function difference(e,t){return e.filter((e=>!t.includes(e)))}var f=class extends h.Subscribable{#c;#b;#m;#v;#s;#O;#g;#P;constructor(e,t,r){super(),this.#c=e,this.#v=r,this.#m=[],this.#s=[],this.#b=[],this.setQueries(t)}onSubscribe(){1===this.listeners.size&&this.#s.forEach((e=>{e.subscribe((t=>{this.#w(e,t)}))}))}onUnsubscribe(){this.listeners.size||this.destroy()}destroy(){this.listeners=new Set,this.#s.forEach((e=>{e.destroy()}))}setQueries(e,t,r){this.#m=e,this.#v=t,c.notifyManager.batch((()=>{const e=this.#s,t=this.#j(this.#m);t.forEach((e=>e.observer.setOptions(e.defaultedQueryOptions,r)));const s=t.map((e=>e.observer)),n=s.map((e=>e.getCurrentResult())),i=s.some(((t,r)=>t!==e[r]));(e.length!==s.length||i)&&(this.#s=s,this.#b=n,this.hasListeners()&&(difference(e,s).forEach((e=>{e.destroy()})),difference(s,e).forEach((e=>{e.subscribe((t=>{this.#w(e,t)}))})),this.#d()))}))}getCurrentResult(){return this.#b}getQueries(){return this.#s.map((e=>e.getCurrentQuery()))}getObservers(){return this.#s}getOptimisticResult(e,t){const r=this.#j(e).map((e=>e.observer.getOptimisticResult(e.defaultedQueryOptions)));return[r,e=>this.#M(e??r,t),()=>this.#Q(r,e)]}#Q(e,t){const r=this.#j(t);return r.map(((t,s)=>{const n=e[s];return t.defaultedQueryOptions.notifyOnChangeProps?n:t.observer.trackResult(n,(e=>{r.forEach((t=>{t.observer.trackProp(e)}))}))}))}#M(e,t){return t?(this.#O&&this.#b===this.#P&&t===this.#g||(this.#g=t,this.#P=this.#b,this.#O=(0,p.replaceEqualDeep)(this.#O,t(e))),this.#O):e}#j(e){const t=new Map(this.#s.map((e=>[e.options.queryHash,e]))),r=[];return e.forEach((e=>{const s=this.#c.defaultQueryOptions(e),n=t.get(s.queryHash);n?r.push({defaultedQueryOptions:s,observer:n}):r.push({defaultedQueryOptions:s,observer:new l.QueryObserver(this.#c,s)})})),r}#w(e,t){const r=this.#s.indexOf(e);-1!==r&&(this.#b=function replaceAt(e,t,r){const s=e.slice(0);return s[t]=r,s}(this.#b,r,t),this.#d())}#d(){if(this.hasListeners()){this.#O!==this.#M(this.#Q(this.#b,this.#m),this.#v?.combine)&&c.notifyManager.batch((()=>{this.listeners.forEach((e=>{e(this.#b)}))}))}}}},44478:(e,t,r)=>{"use strict";var s,n=Object.defineProperty,i=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,o=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)n(e,r,{get:t[r],enumerable:!0})})(u,{Query:()=>f,fetchState:()=>fetchState}),e.exports=(s=u,((e,t,r,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of a(t))o.call(e,u)||u===r||n(e,u,{get:()=>t[u],enumerable:!(s=i(t,u))||s.enumerable});return e})(n({},"__esModule",{value:!0}),s));var c=r(73025),l=r(7734),h=r(7785),p=r(95181),f=class extends p.Removable{#_;#R;#C;#i;#S;#x;constructor(e){super(),this.#x=!1,this.#S=e.defaultOptions,this.setOptions(e.options),this.observers=[],this.#C=e.cache,this.queryKey=e.queryKey,this.queryHash=e.queryHash,this.#_=function getDefaultState(e){const t="function"==typeof e.initialData?e.initialData():e.initialData,r=void 0!==t,s=r?"function"==typeof e.initialDataUpdatedAt?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:r?s??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:r?"success":"pending",fetchStatus:"idle"}}(this.options),this.state=e.state??this.#_,this.scheduleGc()}get meta(){return this.options.meta}get promise(){return this.#i?.promise}setOptions(e){this.options={...this.#S,...e},this.updateGcTime(this.options.gcTime)}optionalRemove(){this.observers.length||"idle"!==this.state.fetchStatus||this.#C.remove(this)}setData(e,t){const r=(0,c.replaceData)(this.state.data,e,this.options);return this.#a({data:r,type:"success",dataUpdatedAt:t?.updatedAt,manual:t?.manual}),r}setState(e,t){this.#a({type:"setState",state:e,setStateOptions:t})}cancel(e){const t=this.#i?.promise;return this.#i?.cancel(e),t?t.then(c.noop).catch(c.noop):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(this.#_)}isActive(){return this.observers.some((e=>!1!==(0,c.resolveEnabled)(e.options.enabled,this)))}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===c.skipToken||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStale(){return!!this.state.isInvalidated||(this.getObserversCount()>0?this.observers.some((e=>e.getCurrentResult().isStale)):void 0===this.state.data)}isStaleByTime(e=0){return this.state.isInvalidated||void 0===this.state.data||!(0,c.timeUntilStale)(this.state.dataUpdatedAt,e)}onFocus(){const e=this.observers.find((e=>e.shouldFetchOnWindowFocus()));e?.refetch({cancelRefetch:!1}),this.#i?.continue()}onOnline(){const e=this.observers.find((e=>e.shouldFetchOnReconnect()));e?.refetch({cancelRefetch:!1}),this.#i?.continue()}addObserver(e){this.observers.includes(e)||(this.observers.push(e),this.clearGcTimeout(),this.#C.notify({type:"observerAdded",query:this,observer:e}))}removeObserver(e){this.observers.includes(e)&&(this.observers=this.observers.filter((t=>t!==e)),this.observers.length||(this.#i&&(this.#x?this.#i.cancel({revert:!0}):this.#i.cancelRetry()),this.scheduleGc()),this.#C.notify({type:"observerRemoved",query:this,observer:e}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||this.#a({type:"invalidate"})}fetch(e,t){if("idle"!==this.state.fetchStatus)if(void 0!==this.state.data&&t?.cancelRefetch)this.cancel({silent:!0});else if(this.#i)return this.#i.continueRetry(),this.#i.promise;if(e&&this.setOptions(e),!this.options.queryFn){const e=this.observers.find((e=>e.options.queryFn));e&&this.setOptions(e.options)}const r=new AbortController,addSignalProperty=e=>{Object.defineProperty(e,"signal",{enumerable:!0,get:()=>(this.#x=!0,r.signal)})},s={fetchOptions:t,options:this.options,queryKey:this.queryKey,state:this.state,fetchFn:()=>{const e=(0,c.ensureQueryFn)(this.options,t),r={queryKey:this.queryKey,meta:this.meta};return addSignalProperty(r),this.#x=!1,this.options.persister?this.options.persister(e,r,this):e(r)}};addSignalProperty(s),this.options.behavior?.onFetch(s,this),this.#R=this.state,"idle"!==this.state.fetchStatus&&this.state.fetchMeta===s.fetchOptions?.meta||this.#a({type:"fetch",meta:s.fetchOptions?.meta});const onError=e=>{(0,h.isCancelledError)(e)&&e.silent||this.#a({type:"error",error:e}),(0,h.isCancelledError)(e)||(this.#C.config.onError?.(e,this),this.#C.config.onSettled?.(this.state.data,e,this)),this.scheduleGc()};return this.#i=(0,h.createRetryer)({initialPromise:t?.initialPromise,fn:s.fetchFn,abort:r.abort.bind(r),onSuccess:e=>{if(void 0!==e){try{this.setData(e)}catch(e){return void onError(e)}this.#C.config.onSuccess?.(e,this),this.#C.config.onSettled?.(e,this.state.error,this),this.scheduleGc()}else onError(new Error(`${this.queryHash} data is undefined`))},onError,onFail:(e,t)=>{this.#a({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#a({type:"pause"})},onContinue:()=>{this.#a({type:"continue"})},retry:s.options.retry,retryDelay:s.options.retryDelay,networkMode:s.options.networkMode,canRun:()=>!0}),this.#i.start()}#a(e){this.state=(t=>{switch(e.type){case"failed":return{...t,fetchFailureCount:e.failureCount,fetchFailureReason:e.error};case"pause":return{...t,fetchStatus:"paused"};case"continue":return{...t,fetchStatus:"fetching"};case"fetch":return{...t,...fetchState(t.data,this.options),fetchMeta:e.meta??null};case"success":return{...t,data:e.data,dataUpdateCount:t.dataUpdateCount+1,dataUpdatedAt:e.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!e.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const r=e.error;return(0,h.isCancelledError)(r)&&r.revert&&this.#R?{...this.#R,fetchStatus:"idle"}:{...t,error:r,errorUpdateCount:t.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:t.fetchFailureCount+1,fetchFailureReason:r,fetchStatus:"idle",status:"error"};case"invalidate":return{...t,isInvalidated:!0};case"setState":return{...t,...e.state}}})(this.state),l.notifyManager.batch((()=>{this.observers.forEach((e=>{e.onQueryUpdate()})),this.#C.notify({query:this,type:"updated",action:e})}))}};function fetchState(e,t){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:(0,h.canFetch)(t.networkMode)?"fetching":"paused",...void 0===e&&{error:null,status:"pending"}}}},18976:(e,t,r)=>{"use strict";var s,n=Object.defineProperty,i=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,o=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)n(e,r,{get:t[r],enumerable:!0})})(u,{QueryCache:()=>f}),e.exports=(s=u,((e,t,r,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of a(t))o.call(e,u)||u===r||n(e,u,{get:()=>t[u],enumerable:!(s=i(t,u))||s.enumerable});return e})(n({},"__esModule",{value:!0}),s));var c=r(73025),l=r(44478),h=r(7734),p=r(66133),f=class extends p.Subscribable{constructor(e={}){super(),this.config=e,this.#m=new Map}#m;build(e,t,r){const s=t.queryKey,n=t.queryHash??(0,c.hashQueryKeyByOptions)(s,t);let i=this.get(n);return i||(i=new l.Query({cache:this,queryKey:s,queryHash:n,options:e.defaultQueryOptions(t),state:r,defaultOptions:e.getQueryDefaults(s)}),this.add(i)),i}add(e){this.#m.has(e.queryHash)||(this.#m.set(e.queryHash,e),this.notify({type:"added",query:e}))}remove(e){const t=this.#m.get(e.queryHash);t&&(e.destroy(),t===e&&this.#m.delete(e.queryHash),this.notify({type:"removed",query:e}))}clear(){h.notifyManager.batch((()=>{this.getAll().forEach((e=>{this.remove(e)}))}))}get(e){return this.#m.get(e)}getAll(){return[...this.#m.values()]}find(e){const t={exact:!0,...e};return this.getAll().find((e=>(0,c.matchQuery)(t,e)))}findAll(e={}){const t=this.getAll();return Object.keys(e).length>0?t.filter((t=>(0,c.matchQuery)(e,t))):t}notify(e){h.notifyManager.batch((()=>{this.listeners.forEach((t=>{t(e)}))}))}onFocus(){h.notifyManager.batch((()=>{this.getAll().forEach((e=>{e.onFocus()}))}))}onOnline(){h.notifyManager.batch((()=>{this.getAll().forEach((e=>{e.onOnline()}))}))}}},56587:(e,t,r)=>{"use strict";var s,n=Object.defineProperty,i=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,o=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)n(e,r,{get:t[r],enumerable:!0})})(u,{QueryClient:()=>b}),e.exports=(s=u,((e,t,r,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of a(t))o.call(e,u)||u===r||n(e,u,{get:()=>t[u],enumerable:!(s=i(t,u))||s.enumerable});return e})(n({},"__esModule",{value:!0}),s));var c=r(73025),l=r(18976),h=r(69239),p=r(52803),f=r(97692),d=r(7734),y=r(52200),b=class{#E;#n;#S;#q;#D;#T;#I;#F;constructor(e={}){this.#E=e.queryCache||new l.QueryCache,this.#n=e.mutationCache||new h.MutationCache,this.#S=e.defaultOptions||{},this.#q=new Map,this.#D=new Map,this.#T=0}mount(){this.#T++,1===this.#T&&(this.#I=p.focusManager.subscribe((async e=>{e&&(await this.resumePausedMutations(),this.#E.onFocus())})),this.#F=f.onlineManager.subscribe((async e=>{e&&(await this.resumePausedMutations(),this.#E.onOnline())})))}unmount(){this.#T--,0===this.#T&&(this.#I?.(),this.#I=void 0,this.#F?.(),this.#F=void 0)}isFetching(e){return this.#E.findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return this.#n.findAll({...e,status:"pending"}).length}getQueryData(e){const t=this.defaultQueryOptions({queryKey:e});return this.#E.get(t.queryHash)?.state.data}ensureQueryData(e){const t=this.defaultQueryOptions(e),r=this.#E.build(this,t),s=r.state.data;return void 0===s?this.fetchQuery(e):(e.revalidateIfStale&&r.isStaleByTime((0,c.resolveStaleTime)(t.staleTime,r))&&this.prefetchQuery(t),Promise.resolve(s))}getQueriesData(e){return this.#E.findAll(e).map((({queryKey:e,state:t})=>[e,t.data]))}setQueryData(e,t,r){const s=this.defaultQueryOptions({queryKey:e}),n=this.#E.get(s.queryHash),i=n?.state.data,a=(0,c.functionalUpdate)(t,i);if(void 0!==a)return this.#E.build(this,s).setData(a,{...r,manual:!0})}setQueriesData(e,t,r){return d.notifyManager.batch((()=>this.#E.findAll(e).map((({queryKey:e})=>[e,this.setQueryData(e,t,r)]))))}getQueryState(e){const t=this.defaultQueryOptions({queryKey:e});return this.#E.get(t.queryHash)?.state}removeQueries(e){const t=this.#E;d.notifyManager.batch((()=>{t.findAll(e).forEach((e=>{t.remove(e)}))}))}resetQueries(e,t){const r=this.#E,s={type:"active",...e};return d.notifyManager.batch((()=>(r.findAll(e).forEach((e=>{e.reset()})),this.refetchQueries(s,t))))}cancelQueries(e,t={}){const r={revert:!0,...t},s=d.notifyManager.batch((()=>this.#E.findAll(e).map((e=>e.cancel(r)))));return Promise.all(s).then(c.noop).catch(c.noop)}invalidateQueries(e,t={}){return d.notifyManager.batch((()=>{if(this.#E.findAll(e).forEach((e=>{e.invalidate()})),"none"===e?.refetchType)return Promise.resolve();const r={...e,type:e?.refetchType??e?.type??"active"};return this.refetchQueries(r,t)}))}refetchQueries(e,t={}){const r={...t,cancelRefetch:t.cancelRefetch??!0},s=d.notifyManager.batch((()=>this.#E.findAll(e).filter((e=>!e.isDisabled())).map((e=>{let t=e.fetch(void 0,r);return r.throwOnError||(t=t.catch(c.noop)),"paused"===e.state.fetchStatus?Promise.resolve():t}))));return Promise.all(s).then(c.noop)}fetchQuery(e){const t=this.defaultQueryOptions(e);void 0===t.retry&&(t.retry=!1);const r=this.#E.build(this,t);return r.isStaleByTime((0,c.resolveStaleTime)(t.staleTime,r))?r.fetch(t):Promise.resolve(r.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(c.noop).catch(c.noop)}fetchInfiniteQuery(e){return e.behavior=(0,y.infiniteQueryBehavior)(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(c.noop).catch(c.noop)}ensureInfiniteQueryData(e){return e.behavior=(0,y.infiniteQueryBehavior)(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return f.onlineManager.isOnline()?this.#n.resumePausedMutations():Promise.resolve()}getQueryCache(){return this.#E}getMutationCache(){return this.#n}getDefaultOptions(){return this.#S}setDefaultOptions(e){this.#S=e}setQueryDefaults(e,t){this.#q.set((0,c.hashKey)(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){const t=[...this.#q.values()],r={};return t.forEach((t=>{(0,c.partialMatchKey)(e,t.queryKey)&&Object.assign(r,t.defaultOptions)})),r}setMutationDefaults(e,t){this.#D.set((0,c.hashKey)(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){const t=[...this.#D.values()];let r={};return t.forEach((t=>{(0,c.partialMatchKey)(e,t.mutationKey)&&(r={...r,...t.defaultOptions})})),r}defaultQueryOptions(e){if(e._defaulted)return e;const t={...this.#S.queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=(0,c.hashQueryKeyByOptions)(t.queryKey,t)),void 0===t.refetchOnReconnect&&(t.refetchOnReconnect="always"!==t.networkMode),void 0===t.throwOnError&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t.queryFn===c.skipToken&&(t.enabled=!1),t}defaultMutationOptions(e){return e?._defaulted?e:{...this.#S.mutations,...e?.mutationKey&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){this.#E.clear(),this.#n.clear()}}},63524:(e,t,r)=>{"use strict";var s,n=Object.defineProperty,i=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,o=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)n(e,r,{get:t[r],enumerable:!0})})(u,{QueryObserver:()=>y}),e.exports=(s=u,((e,t,r,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of a(t))o.call(e,u)||u===r||n(e,u,{get:()=>t[u],enumerable:!(s=i(t,u))||s.enumerable});return e})(n({},"__esModule",{value:!0}),s));var c=r(52803),l=r(7734),h=r(44478),p=r(66133),f=r(89675),d=r(73025),y=class extends p.Subscribable{constructor(e,t){super(),this.options=t,this.#c=e,this.#k=null,this.#N=(0,f.pendingThenable)(),this.options.experimental_prefetchInRender||this.#N.reject(new Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(t)}#c;#W=void 0;#A=void 0;#l=void 0;#B;#K;#N;#k;#U;#L;#H;#z;#G;#V;#$=new Set;bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){1===this.listeners.size&&(this.#W.addObserver(this),shouldFetchOnMount(this.#W,this.options)?this.#X():this.updateResult(),this.#Y())}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return shouldFetchOn(this.#W,this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return shouldFetchOn(this.#W,this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,this.#Z(),this.#J(),this.#W.removeObserver(this)}setOptions(e,t){const r=this.options,s=this.#W;if(this.options=this.#c.defaultQueryOptions(e),void 0!==this.options.enabled&&"boolean"!=typeof this.options.enabled&&"function"!=typeof this.options.enabled&&"boolean"!=typeof(0,d.resolveEnabled)(this.options.enabled,this.#W))throw new Error("Expected enabled to be a boolean or a callback that returns a boolean");this.#ee(),this.#W.setOptions(this.options),r._defaulted&&!(0,d.shallowEqualObjects)(this.options,r)&&this.#c.getQueryCache().notify({type:"observerOptionsUpdated",query:this.#W,observer:this});const n=this.hasListeners();n&&shouldFetchOptionally(this.#W,s,this.options,r)&&this.#X(),this.updateResult(t),!n||this.#W===s&&(0,d.resolveEnabled)(this.options.enabled,this.#W)===(0,d.resolveEnabled)(r.enabled,this.#W)&&(0,d.resolveStaleTime)(this.options.staleTime,this.#W)===(0,d.resolveStaleTime)(r.staleTime,this.#W)||this.#te();const i=this.#re();!n||this.#W===s&&(0,d.resolveEnabled)(this.options.enabled,this.#W)===(0,d.resolveEnabled)(r.enabled,this.#W)&&i===this.#V||this.#se(i)}getOptimisticResult(e){const t=this.#c.getQueryCache().build(this.#c,e),r=this.createResult(t,e);return function shouldAssignObserverCurrentProperties(e,t){if(!(0,d.shallowEqualObjects)(e.getCurrentResult(),t))return!0;return!1}(this,r)&&(this.#l=r,this.#K=this.options,this.#B=this.#W.state),r}getCurrentResult(){return this.#l}trackResult(e,t){const r={};return Object.keys(e).forEach((s=>{Object.defineProperty(r,s,{configurable:!1,enumerable:!0,get:()=>(this.trackProp(s),t?.(s),e[s])})})),r}trackProp(e){this.#$.add(e)}getCurrentQuery(){return this.#W}refetch({...e}={}){return this.fetch({...e})}fetchOptimistic(e){const t=this.#c.defaultQueryOptions(e),r=this.#c.getQueryCache().build(this.#c,t);return r.fetch().then((()=>this.createResult(r,t)))}fetch(e){return this.#X({...e,cancelRefetch:e.cancelRefetch??!0}).then((()=>(this.updateResult(),this.#l)))}#X(e){this.#ee();let t=this.#W.fetch(this.options,e);return e?.throwOnError||(t=t.catch(d.noop)),t}#te(){this.#Z();const e=(0,d.resolveStaleTime)(this.options.staleTime,this.#W);if(d.isServer||this.#l.isStale||!(0,d.isValidTimeout)(e))return;const t=(0,d.timeUntilStale)(this.#l.dataUpdatedAt,e)+1;this.#z=setTimeout((()=>{this.#l.isStale||this.updateResult()}),t)}#re(){return("function"==typeof this.options.refetchInterval?this.options.refetchInterval(this.#W):this.options.refetchInterval)??!1}#se(e){this.#J(),this.#V=e,!d.isServer&&!1!==(0,d.resolveEnabled)(this.options.enabled,this.#W)&&(0,d.isValidTimeout)(this.#V)&&0!==this.#V&&(this.#G=setInterval((()=>{(this.options.refetchIntervalInBackground||c.focusManager.isFocused())&&this.#X()}),this.#V))}#Y(){this.#te(),this.#se(this.#re())}#Z(){this.#z&&(clearTimeout(this.#z),this.#z=void 0)}#J(){this.#G&&(clearInterval(this.#G),this.#G=void 0)}createResult(e,t){const r=this.#W,s=this.options,n=this.#l,i=this.#B,a=this.#K,o=e!==r?e.state:this.#A,{state:u}=e;let c,l={...u},p=!1;if(t._optimisticResults){const n=this.hasListeners(),i=!n&&shouldFetchOnMount(e,t),a=n&&shouldFetchOptionally(e,r,t,s);(i||a)&&(l={...l,...(0,h.fetchState)(u.data,e.options)}),"isRestoring"===t._optimisticResults&&(l.fetchStatus="idle")}let{error:y,errorUpdatedAt:b,status:m}=l;if(t.select&&void 0!==l.data)if(n&&l.data===i?.data&&t.select===this.#U)c=this.#L;else try{this.#U=t.select,c=t.select(l.data),c=(0,d.replaceData)(n?.data,c,t),this.#L=c,this.#k=null}catch(e){this.#k=e}else c=l.data;if(void 0!==t.placeholderData&&void 0===c&&"pending"===m){let e;if(n?.isPlaceholderData&&t.placeholderData===a?.placeholderData)e=n.data;else if(e="function"==typeof t.placeholderData?t.placeholderData(this.#H?.state.data,this.#H):t.placeholderData,t.select&&void 0!==e)try{e=t.select(e),this.#k=null}catch(e){this.#k=e}void 0!==e&&(m="success",c=(0,d.replaceData)(n?.data,e,t),p=!0)}this.#k&&(y=this.#k,c=this.#L,b=Date.now(),m="error");const v="fetching"===l.fetchStatus,O="pending"===m,g="error"===m,P=O&&v,w=void 0!==c,j={status:m,fetchStatus:l.fetchStatus,isPending:O,isSuccess:"success"===m,isError:g,isInitialLoading:P,isLoading:P,data:c,dataUpdatedAt:l.dataUpdatedAt,error:y,errorUpdatedAt:b,failureCount:l.fetchFailureCount,failureReason:l.fetchFailureReason,errorUpdateCount:l.errorUpdateCount,isFetched:l.dataUpdateCount>0||l.errorUpdateCount>0,isFetchedAfterMount:l.dataUpdateCount>o.dataUpdateCount||l.errorUpdateCount>o.errorUpdateCount,isFetching:v,isRefetching:v&&!O,isLoadingError:g&&!w,isPaused:"paused"===l.fetchStatus,isPlaceholderData:p,isRefetchError:g&&w,isStale:isStale(e,t),refetch:this.refetch,promise:this.#N};if(this.options.experimental_prefetchInRender){const finalizeThenableIfPossible=e=>{"error"===j.status?e.reject(j.error):void 0!==j.data&&e.resolve(j.data)},recreateThenable=()=>{const e=this.#N=j.promise=(0,f.pendingThenable)();finalizeThenableIfPossible(e)},t=this.#N;switch(t.status){case"pending":e.queryHash===r.queryHash&&finalizeThenableIfPossible(t);break;case"fulfilled":"error"!==j.status&&j.data===t.value||recreateThenable();break;case"rejected":"error"===j.status&&j.error===t.reason||recreateThenable()}}return j}updateResult(e){const t=this.#l,r=this.createResult(this.#W,this.options);if(this.#B=this.#W.state,this.#K=this.options,void 0!==this.#B.data&&(this.#H=this.#W),(0,d.shallowEqualObjects)(r,t))return;this.#l=r;const s={};!1!==e?.listeners&&(()=>{if(!t)return!0;const{notifyOnChangeProps:e}=this.options,r="function"==typeof e?e():e;if("all"===r||!r&&!this.#$.size)return!0;const s=new Set(r??this.#$);return this.options.throwOnError&&s.add("error"),Object.keys(this.#l).some((e=>{const r=e;return this.#l[r]!==t[r]&&s.has(r)}))})()&&(s.listeners=!0),this.#d({...s,...e})}#ee(){const e=this.#c.getQueryCache().build(this.#c,this.options);if(e===this.#W)return;const t=this.#W;this.#W=e,this.#A=e.state,this.hasListeners()&&(t?.removeObserver(this),e.addObserver(this))}onQueryUpdate(){this.updateResult(),this.hasListeners()&&this.#Y()}#d(e){l.notifyManager.batch((()=>{e.listeners&&this.listeners.forEach((e=>{e(this.#l)})),this.#c.getQueryCache().notify({query:this.#W,type:"observerResultsUpdated"})}))}};function shouldFetchOnMount(e,t){return function shouldLoadOnMount(e,t){return!1!==(0,d.resolveEnabled)(t.enabled,e)&&void 0===e.state.data&&!("error"===e.state.status&&!1===t.retryOnMount)}(e,t)||void 0!==e.state.data&&shouldFetchOn(e,t,t.refetchOnMount)}function shouldFetchOn(e,t,r){if(!1!==(0,d.resolveEnabled)(t.enabled,e)){const s="function"==typeof r?r(e):r;return"always"===s||!1!==s&&isStale(e,t)}return!1}function shouldFetchOptionally(e,t,r,s){return(e!==t||!1===(0,d.resolveEnabled)(s.enabled,e))&&(!r.suspense||"error"!==e.state.status)&&isStale(e,r)}function isStale(e,t){return!1!==(0,d.resolveEnabled)(t.enabled,e)&&e.isStaleByTime((0,d.resolveStaleTime)(t.staleTime,e))}},95181:(e,t,r)=>{"use strict";var s,n=Object.defineProperty,i=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,o=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)n(e,r,{get:t[r],enumerable:!0})})(u,{Removable:()=>l}),e.exports=(s=u,((e,t,r,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of a(t))o.call(e,u)||u===r||n(e,u,{get:()=>t[u],enumerable:!(s=i(t,u))||s.enumerable});return e})(n({},"__esModule",{value:!0}),s));var c=r(73025),l=class{#ne;destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),(0,c.isValidTimeout)(this.gcTime)&&(this.#ne=setTimeout((()=>{this.optionalRemove()}),this.gcTime))}updateGcTime(e){this.gcTime=Math.max(this.gcTime||0,e??(c.isServer?1/0:3e5))}clearGcTimeout(){this.#ne&&(clearTimeout(this.#ne),this.#ne=void 0)}}},7785:(e,t,r)=>{"use strict";var s,n=Object.defineProperty,i=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,o=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)n(e,r,{get:t[r],enumerable:!0})})(u,{CancelledError:()=>f,canFetch:()=>canFetch,createRetryer:()=>createRetryer,isCancelledError:()=>isCancelledError}),e.exports=(s=u,((e,t,r,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of a(t))o.call(e,u)||u===r||n(e,u,{get:()=>t[u],enumerable:!(s=i(t,u))||s.enumerable});return e})(n({},"__esModule",{value:!0}),s));var c=r(52803),l=r(97692),h=r(89675),p=r(73025);function defaultRetryDelay(e){return Math.min(1e3*2**e,3e4)}function canFetch(e){return"online"!==(e??"online")||l.onlineManager.isOnline()}var f=class extends Error{constructor(e){super("CancelledError"),this.revert=e?.revert,this.silent=e?.silent}};function isCancelledError(e){return e instanceof f}function createRetryer(e){let t,r=!1,s=0,n=!1;const i=(0,h.pendingThenable)(),canContinue=()=>c.focusManager.isFocused()&&("always"===e.networkMode||l.onlineManager.isOnline())&&e.canRun(),canStart=()=>canFetch(e.networkMode)&&e.canRun(),resolve=r=>{n||(n=!0,e.onSuccess?.(r),t?.(),i.resolve(r))},reject=r=>{n||(n=!0,e.onError?.(r),t?.(),i.reject(r))},pause=()=>new Promise((r=>{t=e=>{(n||canContinue())&&r(e)},e.onPause?.()})).then((()=>{t=void 0,n||e.onContinue?.()})),run=()=>{if(n)return;let t;const i=0===s?e.initialPromise:void 0;try{t=i??e.fn()}catch(e){t=Promise.reject(e)}Promise.resolve(t).then(resolve).catch((t=>{if(n)return;const i=e.retry??(p.isServer?0:3),a=e.retryDelay??defaultRetryDelay,o="function"==typeof a?a(s,t):a,u=!0===i||"number"==typeof i&&s<i||"function"==typeof i&&i(s,t);!r&&u?(s++,e.onFail?.(s,t),(0,p.sleep)(o).then((()=>canContinue()?void 0:pause())).then((()=>{r?reject(t):run()}))):reject(t)}))};return{promise:i,cancel:t=>{n||(reject(new f(t)),e.abort?.())},continue:()=>(t?.(),i),cancelRetry:()=>{r=!0},continueRetry:()=>{r=!1},canStart,start:()=>(canStart()?run():pause().then(run),i)}}},66133:e=>{"use strict";var t,r=Object.defineProperty,s=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,a={};((e,t)=>{for(var s in t)r(e,s,{get:t[s],enumerable:!0})})(a,{Subscribable:()=>o}),e.exports=(t=a,((e,t,a,o)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of n(t))i.call(e,u)||u===a||r(e,u,{get:()=>t[u],enumerable:!(o=s(t,u))||o.enumerable});return e})(r({},"__esModule",{value:!0}),t));var o=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}}},89675:e=>{"use strict";var t,r=Object.defineProperty,s=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,a={};function pendingThenable(){let e,t;const r=new Promise(((r,s)=>{e=r,t=s}));function finalize(e){Object.assign(r,e),delete r.resolve,delete r.reject}return r.status="pending",r.catch((()=>{})),r.resolve=t=>{finalize({status:"fulfilled",value:t}),e(t)},r.reject=e=>{finalize({status:"rejected",reason:e}),t(e)},r}((e,t)=>{for(var s in t)r(e,s,{get:t[s],enumerable:!0})})(a,{pendingThenable:()=>pendingThenable}),e.exports=(t=a,((e,t,a,o)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of n(t))i.call(e,u)||u===a||r(e,u,{get:()=>t[u],enumerable:!(o=s(t,u))||o.enumerable});return e})(r({},"__esModule",{value:!0}),t))},98813:e=>{"use strict";var t,r=Object.defineProperty,s=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty;e.exports=(t={},((e,t,a,o)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of n(t))i.call(e,u)||u===a||r(e,u,{get:()=>t[u],enumerable:!(o=s(t,u))||o.enumerable});return e})(r({},"__esModule",{value:!0}),t))},73025:e=>{"use strict";var t,r=Object.defineProperty,s=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,a={};((e,t)=>{for(var s in t)r(e,s,{get:t[s],enumerable:!0})})(a,{addToEnd:()=>addToEnd,addToStart:()=>addToStart,ensureQueryFn:()=>ensureQueryFn,functionalUpdate:()=>functionalUpdate,hashKey:()=>hashKey,hashQueryKeyByOptions:()=>hashQueryKeyByOptions,isPlainArray:()=>isPlainArray,isPlainObject:()=>isPlainObject,isServer:()=>o,isValidTimeout:()=>isValidTimeout,keepPreviousData:()=>keepPreviousData,matchMutation:()=>matchMutation,matchQuery:()=>matchQuery,noop:()=>noop,partialMatchKey:()=>partialMatchKey,replaceData:()=>replaceData,replaceEqualDeep:()=>replaceEqualDeep,resolveEnabled:()=>resolveEnabled,resolveStaleTime:()=>resolveStaleTime,shallowEqualObjects:()=>shallowEqualObjects,skipToken:()=>u,sleep:()=>sleep,timeUntilStale:()=>timeUntilStale}),e.exports=(t=a,((e,t,a,o)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of n(t))i.call(e,u)||u===a||r(e,u,{get:()=>t[u],enumerable:!(o=s(t,u))||o.enumerable});return e})(r({},"__esModule",{value:!0}),t));var o="undefined"==typeof window||"Deno"in globalThis;function noop(){}function functionalUpdate(e,t){return"function"==typeof e?e(t):e}function isValidTimeout(e){return"number"==typeof e&&e>=0&&e!==1/0}function timeUntilStale(e,t){return Math.max(e+(t||0)-Date.now(),0)}function resolveStaleTime(e,t){return"function"==typeof e?e(t):e}function resolveEnabled(e,t){return"function"==typeof e?e(t):e}function matchQuery(e,t){const{type:r="all",exact:s,fetchStatus:n,predicate:i,queryKey:a,stale:o}=e;if(a)if(s){if(t.queryHash!==hashQueryKeyByOptions(a,t.options))return!1}else if(!partialMatchKey(t.queryKey,a))return!1;if("all"!==r){const e=t.isActive();if("active"===r&&!e)return!1;if("inactive"===r&&e)return!1}return("boolean"!=typeof o||t.isStale()===o)&&((!n||n===t.state.fetchStatus)&&!(i&&!i(t)))}function matchMutation(e,t){const{exact:r,status:s,predicate:n,mutationKey:i}=e;if(i){if(!t.options.mutationKey)return!1;if(r){if(hashKey(t.options.mutationKey)!==hashKey(i))return!1}else if(!partialMatchKey(t.options.mutationKey,i))return!1}return(!s||t.state.status===s)&&!(n&&!n(t))}function hashQueryKeyByOptions(e,t){return(t?.queryKeyHashFn||hashKey)(e)}function hashKey(e){return JSON.stringify(e,((e,t)=>isPlainObject(t)?Object.keys(t).sort().reduce(((e,r)=>(e[r]=t[r],e)),{}):t))}function partialMatchKey(e,t){return e===t||typeof e==typeof t&&(!(!e||!t||"object"!=typeof e||"object"!=typeof t)&&!Object.keys(t).some((r=>!partialMatchKey(e[r],t[r]))))}function replaceEqualDeep(e,t){if(e===t)return e;const r=isPlainArray(e)&&isPlainArray(t);if(r||isPlainObject(e)&&isPlainObject(t)){const s=r?e:Object.keys(e),n=s.length,i=r?t:Object.keys(t),a=i.length,o=r?[]:{};let u=0;for(let n=0;n<a;n++){const a=r?n:i[n];(!r&&s.includes(a)||r)&&void 0===e[a]&&void 0===t[a]?(o[a]=void 0,u++):(o[a]=replaceEqualDeep(e[a],t[a]),o[a]===e[a]&&void 0!==e[a]&&u++)}return n===a&&u===n?e:o}return t}function shallowEqualObjects(e,t){if(!t||Object.keys(e).length!==Object.keys(t).length)return!1;for(const r in e)if(e[r]!==t[r])return!1;return!0}function isPlainArray(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function isPlainObject(e){if(!hasObjectPrototype(e))return!1;const t=e.constructor;if(void 0===t)return!0;const r=t.prototype;return!!hasObjectPrototype(r)&&(!!r.hasOwnProperty("isPrototypeOf")&&Object.getPrototypeOf(e)===Object.prototype)}function hasObjectPrototype(e){return"[object Object]"===Object.prototype.toString.call(e)}function sleep(e){return new Promise((t=>{setTimeout(t,e)}))}function replaceData(e,t,r){return"function"==typeof r.structuralSharing?r.structuralSharing(e,t):!1!==r.structuralSharing?replaceEqualDeep(e,t):t}function keepPreviousData(e){return e}function addToEnd(e,t,r=0){const s=[...e,t];return r&&s.length>r?s.slice(1):s}function addToStart(e,t,r=0){const s=[t,...e];return r&&s.length>r?s.slice(0,-1):s}var u=Symbol();function ensureQueryFn(e,t){return!e.queryFn&&t?.initialPromise?()=>t.initialPromise:e.queryFn&&e.queryFn!==u?e.queryFn:()=>Promise.reject(new Error(`Missing queryFn: '${e.queryHash}'`))}},84776:(e,t,r)=>{"use strict";var s,n=Object.create,i=Object.defineProperty,a=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,u=Object.getPrototypeOf,c=Object.prototype.hasOwnProperty,__copyProps=(e,t,r,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let n of o(t))c.call(e,n)||n===r||i(e,n,{get:()=>t[n],enumerable:!(s=a(t,n))||s.enumerable});return e},l={};((e,t)=>{for(var r in t)i(e,r,{get:t[r],enumerable:!0})})(l,{HydrationBoundary:()=>HydrationBoundary}),e.exports=(s=l,__copyProps(i({},"__esModule",{value:!0}),s));var h=((e,t,r)=>(r=null!=e?n(u(e)):{},__copyProps(!t&&e&&e.__esModule?r:i(r,"default",{value:e,enumerable:!0}),e)))(r(41594),1),p=r(51934),f=r(59762),HydrationBoundary=({children:e,options:t={},state:r,queryClient:s})=>{const n=(0,f.useQueryClient)(s),[i,a]=h.useState(),o=h.useRef(t);return o.current=t,h.useMemo((()=>{if(r){if("object"!=typeof r)return;const e=n.getQueryCache(),t=r.queries||[],s=[],u=[];for(const r of t){const t=e.get(r.queryHash);if(t){const e=r.state.dataUpdatedAt>t.state.dataUpdatedAt,s=i?.find((e=>e.queryHash===r.queryHash));e&&(!s||r.state.dataUpdatedAt>s.state.dataUpdatedAt)&&u.push(r)}else s.push(r)}s.length>0&&(0,p.hydrate)(n,{queries:s},o.current),u.length>0&&a((e=>e?[...e,...u]:u))}}),[n,i,r]),h.useEffect((()=>{i&&((0,p.hydrate)(n,{queries:i},o.current),a(void 0))}),[n,i]),e}},59762:(e,t,r)=>{"use strict";var s,n=Object.create,i=Object.defineProperty,a=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,u=Object.getPrototypeOf,c=Object.prototype.hasOwnProperty,__copyProps=(e,t,r,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let n of o(t))c.call(e,n)||n===r||i(e,n,{get:()=>t[n],enumerable:!(s=a(t,n))||s.enumerable});return e},l={};((e,t)=>{for(var r in t)i(e,r,{get:t[r],enumerable:!0})})(l,{QueryClientContext:()=>f,QueryClientProvider:()=>QueryClientProvider,useQueryClient:()=>useQueryClient}),e.exports=(s=l,__copyProps(i({},"__esModule",{value:!0}),s));var h=((e,t,r)=>(r=null!=e?n(u(e)):{},__copyProps(!t&&e&&e.__esModule?r:i(r,"default",{value:e,enumerable:!0}),e)))(r(41594),1),p=r(62540),f=h.createContext(void 0),useQueryClient=e=>{const t=h.useContext(f);if(e)return e;if(!t)throw new Error("No QueryClient set, use QueryClientProvider to set one");return t},QueryClientProvider=({client:e,children:t})=>(h.useEffect((()=>(e.mount(),()=>{e.unmount()})),[e]),(0,p.jsx)(f.Provider,{value:e,children:t}))},16729:(e,t,r)=>{"use strict";var s,n=Object.create,i=Object.defineProperty,a=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,u=Object.getPrototypeOf,c=Object.prototype.hasOwnProperty,__copyProps=(e,t,r,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let n of o(t))c.call(e,n)||n===r||i(e,n,{get:()=>t[n],enumerable:!(s=a(t,n))||s.enumerable});return e},l={};((e,t)=>{for(var r in t)i(e,r,{get:t[r],enumerable:!0})})(l,{QueryErrorResetBoundary:()=>QueryErrorResetBoundary,useQueryErrorResetBoundary:()=>useQueryErrorResetBoundary}),e.exports=(s=l,__copyProps(i({},"__esModule",{value:!0}),s));var h=((e,t,r)=>(r=null!=e?n(u(e)):{},__copyProps(!t&&e&&e.__esModule?r:i(r,"default",{value:e,enumerable:!0}),e)))(r(41594),1),p=r(62540);function createValue(){let e=!1;return{clearReset:()=>{e=!1},reset:()=>{e=!0},isReset:()=>e}}var f=h.createContext(createValue()),useQueryErrorResetBoundary=()=>h.useContext(f),QueryErrorResetBoundary=({children:e})=>{const[t]=h.useState((()=>createValue()));return(0,p.jsx)(f.Provider,{value:t,children:"function"==typeof e?e(t):e})}},74151:(e,t,r)=>{"use strict";var s,n=Object.create,i=Object.defineProperty,a=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,u=Object.getPrototypeOf,c=Object.prototype.hasOwnProperty,__copyProps=(e,t,r,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let n of o(t))c.call(e,n)||n===r||i(e,n,{get:()=>t[n],enumerable:!(s=a(t,n))||s.enumerable});return e},l={};((e,t)=>{for(var r in t)i(e,r,{get:t[r],enumerable:!0})})(l,{ensurePreventErrorBoundaryRetry:()=>ensurePreventErrorBoundaryRetry,getHasError:()=>getHasError,useClearResetErrorBoundary:()=>useClearResetErrorBoundary}),e.exports=(s=l,__copyProps(i({},"__esModule",{value:!0}),s));var h=((e,t,r)=>(r=null!=e?n(u(e)):{},__copyProps(!t&&e&&e.__esModule?r:i(r,"default",{value:e,enumerable:!0}),e)))(r(41594),1),p=r(63315),ensurePreventErrorBoundaryRetry=(e,t)=>{(e.suspense||e.throwOnError||e.experimental_prefetchInRender)&&(t.isReset()||(e.retryOnMount=!1))},useClearResetErrorBoundary=e=>{h.useEffect((()=>{e.clearReset()}),[e])},getHasError=({result:e,errorResetBoundary:t,throwOnError:r,query:s})=>e.isError&&!t.isReset()&&!e.isFetching&&s&&(0,p.shouldThrowError)(r,[e.error,s])},51688:(e,t,r)=>{"use strict";var s,n=Object.defineProperty,i=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,o=Object.prototype.hasOwnProperty,__copyProps=(e,t,r,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of a(t))o.call(e,u)||u===r||n(e,u,{get:()=>t[u],enumerable:!(s=i(t,u))||s.enumerable});return e},__reExport=(e,t,r)=>(__copyProps(e,t,"default"),r&&__copyProps(r,t,"default")),u={};((e,t)=>{for(var r in t)n(e,r,{get:t[r],enumerable:!0})})(u,{HydrationBoundary:()=>O.HydrationBoundary,IsRestoringProvider:()=>Q.IsRestoringProvider,QueryClientContext:()=>v.QueryClientContext,QueryClientProvider:()=>v.QueryClientProvider,QueryErrorResetBoundary:()=>g.QueryErrorResetBoundary,infiniteQueryOptions:()=>m.infiniteQueryOptions,queryOptions:()=>b.queryOptions,useInfiniteQuery:()=>M.useInfiniteQuery,useIsFetching:()=>P.useIsFetching,useIsMutating:()=>w.useIsMutating,useIsRestoring:()=>Q.useIsRestoring,useMutation:()=>j.useMutation,useMutationState:()=>w.useMutationState,usePrefetchInfiniteQuery:()=>y.usePrefetchInfiniteQuery,usePrefetchQuery:()=>d.usePrefetchQuery,useQueries:()=>c.useQueries,useQuery:()=>l.useQuery,useQueryClient:()=>v.useQueryClient,useQueryErrorResetBoundary:()=>g.useQueryErrorResetBoundary,useSuspenseInfiniteQuery:()=>p.useSuspenseInfiniteQuery,useSuspenseQueries:()=>f.useSuspenseQueries,useSuspenseQuery:()=>h.useSuspenseQuery}),e.exports=(s=u,__copyProps(n({},"__esModule",{value:!0}),s)),__reExport(u,r(51934),e.exports),__reExport(u,r(62143),e.exports);var c=r(37963),l=r(16743),h=r(37063),p=r(28619),f=r(44459),d=r(94860),y=r(48380),b=r(5938),m=r(28218),v=r(59762),O=r(84776),g=r(16729),P=r(81809),w=r(26803),j=r(3188),M=r(81355),Q=r(21683)},28218:e=>{"use strict";var t,r=Object.defineProperty,s=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,a={};function infiniteQueryOptions(e){return e}((e,t)=>{for(var s in t)r(e,s,{get:t[s],enumerable:!0})})(a,{infiniteQueryOptions:()=>infiniteQueryOptions}),e.exports=(t=a,((e,t,a,o)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of n(t))i.call(e,u)||u===a||r(e,u,{get:()=>t[u],enumerable:!(o=s(t,u))||o.enumerable});return e})(r({},"__esModule",{value:!0}),t))},21683:(e,t,r)=>{"use strict";var s,n=Object.create,i=Object.defineProperty,a=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,u=Object.getPrototypeOf,c=Object.prototype.hasOwnProperty,__copyProps=(e,t,r,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let n of o(t))c.call(e,n)||n===r||i(e,n,{get:()=>t[n],enumerable:!(s=a(t,n))||s.enumerable});return e},l={};((e,t)=>{for(var r in t)i(e,r,{get:t[r],enumerable:!0})})(l,{IsRestoringProvider:()=>f,useIsRestoring:()=>useIsRestoring}),e.exports=(s=l,__copyProps(i({},"__esModule",{value:!0}),s));var h=((e,t,r)=>(r=null!=e?n(u(e)):{},__copyProps(!t&&e&&e.__esModule?r:i(r,"default",{value:e,enumerable:!0}),e)))(r(41594),1),p=h.createContext(!1),useIsRestoring=()=>h.useContext(p),f=p.Provider},5938:e=>{"use strict";var t,r=Object.defineProperty,s=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,a={};function queryOptions(e){return e}((e,t)=>{for(var s in t)r(e,s,{get:t[s],enumerable:!0})})(a,{queryOptions:()=>queryOptions}),e.exports=(t=a,((e,t,a,o)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of n(t))i.call(e,u)||u===a||r(e,u,{get:()=>t[u],enumerable:!(o=s(t,u))||o.enumerable});return e})(r({},"__esModule",{value:!0}),t))},92816:e=>{"use strict";var t,r=Object.defineProperty,s=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,a={};((e,t)=>{for(var s in t)r(e,s,{get:t[s],enumerable:!0})})(a,{defaultThrowOnError:()=>defaultThrowOnError,ensureSuspenseTimers:()=>ensureSuspenseTimers,fetchOptimistic:()=>fetchOptimistic,shouldSuspend:()=>shouldSuspend,willFetch:()=>willFetch}),e.exports=(t=a,((e,t,a,o)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of n(t))i.call(e,u)||u===a||r(e,u,{get:()=>t[u],enumerable:!(o=s(t,u))||o.enumerable});return e})(r({},"__esModule",{value:!0}),t));var defaultThrowOnError=(e,t)=>void 0===t.state.data,ensureSuspenseTimers=e=>{e.suspense&&(void 0===e.staleTime&&(e.staleTime=1e3),"number"==typeof e.gcTime&&(e.gcTime=Math.max(e.gcTime,1e3)))},willFetch=(e,t)=>e.isLoading&&e.isFetching&&!t,shouldSuspend=(e,t)=>e?.suspense&&t.isPending,fetchOptimistic=(e,t,r)=>t.fetchOptimistic(e).catch((()=>{r.clearReset()}))},62143:e=>{"use strict";var t,r=Object.defineProperty,s=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty;e.exports=(t={},((e,t,a,o)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of n(t))i.call(e,u)||u===a||r(e,u,{get:()=>t[u],enumerable:!(o=s(t,u))||o.enumerable});return e})(r({},"__esModule",{value:!0}),t))},35442:(e,t,r)=>{"use strict";var s,n=Object.create,i=Object.defineProperty,a=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,u=Object.getPrototypeOf,c=Object.prototype.hasOwnProperty,__copyProps=(e,t,r,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let n of o(t))c.call(e,n)||n===r||i(e,n,{get:()=>t[n],enumerable:!(s=a(t,n))||s.enumerable});return e},l={};((e,t)=>{for(var r in t)i(e,r,{get:t[r],enumerable:!0})})(l,{useBaseQuery:()=>useBaseQuery}),e.exports=(s=l,__copyProps(i({},"__esModule",{value:!0}),s));var h=((e,t,r)=>(r=null!=e?n(u(e)):{},__copyProps(!t&&e&&e.__esModule?r:i(r,"default",{value:e,enumerable:!0}),e)))(r(41594),1),p=r(51934),f=r(59762),d=r(16729),y=r(74151),b=r(21683),m=r(92816),v=r(63315);function useBaseQuery(e,t,r){const s=(0,f.useQueryClient)(r),n=(0,b.useIsRestoring)(),i=(0,d.useQueryErrorResetBoundary)(),a=s.defaultQueryOptions(e);s.getDefaultOptions().queries?._experimental_beforeQuery?.(a),a._optimisticResults=n?"isRestoring":"optimistic",(0,m.ensureSuspenseTimers)(a),(0,y.ensurePreventErrorBoundaryRetry)(a,i),(0,y.useClearResetErrorBoundary)(i);const o=!s.getQueryCache().get(a.queryHash),[u]=h.useState((()=>new t(s,a))),c=u.getOptimisticResult(a);if(h.useSyncExternalStore(h.useCallback((e=>{const t=n?v.noop:u.subscribe(p.notifyManager.batchCalls(e));return u.updateResult(),t}),[u,n]),(()=>u.getCurrentResult()),(()=>u.getCurrentResult())),h.useEffect((()=>{u.setOptions(a,{listeners:!1})}),[a,u]),(0,m.shouldSuspend)(a,c))throw(0,m.fetchOptimistic)(a,u,i);if((0,y.getHasError)({result:c,errorResetBoundary:i,throwOnError:a.throwOnError,query:s.getQueryCache().get(a.queryHash)}))throw c.error;if(s.getDefaultOptions().queries?._experimental_afterQuery?.(a,c),a.experimental_prefetchInRender&&!p.isServer&&(0,m.willFetch)(c,n)){const e=o?(0,m.fetchOptimistic)(a,u,i):s.getQueryCache().get(a.queryHash)?.promise;e?.catch(v.noop).finally((()=>{u.updateResult()}))}return a.notifyOnChangeProps?c:u.trackResult(c)}},81355:(e,t,r)=>{"use strict";var s,n=Object.defineProperty,i=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,o=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)n(e,r,{get:t[r],enumerable:!0})})(u,{useInfiniteQuery:()=>useInfiniteQuery}),e.exports=(s=u,((e,t,r,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of a(t))o.call(e,u)||u===r||n(e,u,{get:()=>t[u],enumerable:!(s=i(t,u))||s.enumerable});return e})(n({},"__esModule",{value:!0}),s));var c=r(51934),l=r(35442);function useInfiniteQuery(e,t){return(0,l.useBaseQuery)(e,c.InfiniteQueryObserver,t)}},81809:(e,t,r)=>{"use strict";var s,n=Object.create,i=Object.defineProperty,a=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,u=Object.getPrototypeOf,c=Object.prototype.hasOwnProperty,__copyProps=(e,t,r,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let n of o(t))c.call(e,n)||n===r||i(e,n,{get:()=>t[n],enumerable:!(s=a(t,n))||s.enumerable});return e},l={};((e,t)=>{for(var r in t)i(e,r,{get:t[r],enumerable:!0})})(l,{useIsFetching:()=>useIsFetching}),e.exports=(s=l,__copyProps(i({},"__esModule",{value:!0}),s));var h=((e,t,r)=>(r=null!=e?n(u(e)):{},__copyProps(!t&&e&&e.__esModule?r:i(r,"default",{value:e,enumerable:!0}),e)))(r(41594),1),p=r(51934),f=r(59762);function useIsFetching(e,t){const r=(0,f.useQueryClient)(t),s=r.getQueryCache();return h.useSyncExternalStore(h.useCallback((e=>s.subscribe(p.notifyManager.batchCalls(e))),[s]),(()=>r.isFetching(e)),(()=>r.isFetching(e)))}},3188:(e,t,r)=>{"use strict";var s,n=Object.create,i=Object.defineProperty,a=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,u=Object.getPrototypeOf,c=Object.prototype.hasOwnProperty,__copyProps=(e,t,r,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let n of o(t))c.call(e,n)||n===r||i(e,n,{get:()=>t[n],enumerable:!(s=a(t,n))||s.enumerable});return e},l={};((e,t)=>{for(var r in t)i(e,r,{get:t[r],enumerable:!0})})(l,{useMutation:()=>useMutation}),e.exports=(s=l,__copyProps(i({},"__esModule",{value:!0}),s));var h=((e,t,r)=>(r=null!=e?n(u(e)):{},__copyProps(!t&&e&&e.__esModule?r:i(r,"default",{value:e,enumerable:!0}),e)))(r(41594),1),p=r(51934),f=r(59762),d=r(63315);function useMutation(e,t){const r=(0,f.useQueryClient)(t),[s]=h.useState((()=>new p.MutationObserver(r,e)));h.useEffect((()=>{s.setOptions(e)}),[s,e]);const n=h.useSyncExternalStore(h.useCallback((e=>s.subscribe(p.notifyManager.batchCalls(e))),[s]),(()=>s.getCurrentResult()),(()=>s.getCurrentResult())),i=h.useCallback(((e,t)=>{s.mutate(e,t).catch(d.noop)}),[s]);if(n.error&&(0,d.shouldThrowError)(s.options.throwOnError,[n.error]))throw n.error;return{...n,mutate:i,mutateAsync:n.mutate}}},26803:(e,t,r)=>{"use strict";var s,n=Object.create,i=Object.defineProperty,a=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,u=Object.getPrototypeOf,c=Object.prototype.hasOwnProperty,__copyProps=(e,t,r,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let n of o(t))c.call(e,n)||n===r||i(e,n,{get:()=>t[n],enumerable:!(s=a(t,n))||s.enumerable});return e},l={};((e,t)=>{for(var r in t)i(e,r,{get:t[r],enumerable:!0})})(l,{useIsMutating:()=>useIsMutating,useMutationState:()=>useMutationState}),e.exports=(s=l,__copyProps(i({},"__esModule",{value:!0}),s));var h=((e,t,r)=>(r=null!=e?n(u(e)):{},__copyProps(!t&&e&&e.__esModule?r:i(r,"default",{value:e,enumerable:!0}),e)))(r(41594),1),p=r(51934),f=r(59762);function useIsMutating(e,t){return useMutationState({filters:{...e,status:"pending"}},(0,f.useQueryClient)(t)).length}function getResult(e,t){return e.findAll(t.filters).map((e=>t.select?t.select(e):e.state))}function useMutationState(e={},t){const r=(0,f.useQueryClient)(t).getMutationCache(),s=h.useRef(e),n=h.useRef(null);return n.current||(n.current=getResult(r,e)),h.useEffect((()=>{s.current=e})),h.useSyncExternalStore(h.useCallback((e=>r.subscribe((()=>{const t=(0,p.replaceEqualDeep)(n.current,getResult(r,s.current));n.current!==t&&(n.current=t,p.notifyManager.schedule(e))}))),[r]),(()=>n.current),(()=>n.current))}},48380:(e,t,r)=>{"use strict";var s,n=Object.defineProperty,i=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,o=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)n(e,r,{get:t[r],enumerable:!0})})(u,{usePrefetchInfiniteQuery:()=>usePrefetchInfiniteQuery}),e.exports=(s=u,((e,t,r,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of a(t))o.call(e,u)||u===r||n(e,u,{get:()=>t[u],enumerable:!(s=i(t,u))||s.enumerable});return e})(n({},"__esModule",{value:!0}),s));var c=r(59762);function usePrefetchInfiniteQuery(e,t){const r=(0,c.useQueryClient)(t);r.getQueryState(e.queryKey)||r.prefetchInfiniteQuery(e)}},94860:(e,t,r)=>{"use strict";var s,n=Object.defineProperty,i=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,o=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)n(e,r,{get:t[r],enumerable:!0})})(u,{usePrefetchQuery:()=>usePrefetchQuery}),e.exports=(s=u,((e,t,r,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of a(t))o.call(e,u)||u===r||n(e,u,{get:()=>t[u],enumerable:!(s=i(t,u))||s.enumerable});return e})(n({},"__esModule",{value:!0}),s));var c=r(59762);function usePrefetchQuery(e,t){const r=(0,c.useQueryClient)(t);r.getQueryState(e.queryKey)||r.prefetchQuery(e)}},37963:(e,t,r)=>{"use strict";var s,n=Object.create,i=Object.defineProperty,a=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,u=Object.getPrototypeOf,c=Object.prototype.hasOwnProperty,__copyProps=(e,t,r,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let n of o(t))c.call(e,n)||n===r||i(e,n,{get:()=>t[n],enumerable:!(s=a(t,n))||s.enumerable});return e},l={};((e,t)=>{for(var r in t)i(e,r,{get:t[r],enumerable:!0})})(l,{useQueries:()=>useQueries}),e.exports=(s=l,__copyProps(i({},"__esModule",{value:!0}),s));var h=((e,t,r)=>(r=null!=e?n(u(e)):{},__copyProps(!t&&e&&e.__esModule?r:i(r,"default",{value:e,enumerable:!0}),e)))(r(41594),1),p=r(51934),f=r(59762),d=r(21683),y=r(16729),b=r(74151),m=r(92816),v=r(63315);function useQueries({queries:e,...t},r){const s=(0,f.useQueryClient)(r),n=(0,d.useIsRestoring)(),i=(0,y.useQueryErrorResetBoundary)(),a=h.useMemo((()=>e.map((e=>{const t=s.defaultQueryOptions(e);return t._optimisticResults=n?"isRestoring":"optimistic",t}))),[e,s,n]);a.forEach((e=>{(0,m.ensureSuspenseTimers)(e),(0,b.ensurePreventErrorBoundaryRetry)(e,i)})),(0,b.useClearResetErrorBoundary)(i);const[o]=h.useState((()=>new p.QueriesObserver(s,a,t))),[u,c,l]=o.getOptimisticResult(a,t.combine);h.useSyncExternalStore(h.useCallback((e=>n?v.noop:o.subscribe(p.notifyManager.batchCalls(e))),[o,n]),(()=>o.getCurrentResult()),(()=>o.getCurrentResult())),h.useEffect((()=>{o.setQueries(a,t,{listeners:!1})}),[a,t,o]);const O=u.some(((e,t)=>(0,m.shouldSuspend)(a[t],e)))?u.flatMap(((e,t)=>{const r=a[t];if(r){const t=new p.QueryObserver(s,r);if((0,m.shouldSuspend)(r,e))return(0,m.fetchOptimistic)(r,t,i);(0,m.willFetch)(e,n)&&(0,m.fetchOptimistic)(r,t,i)}return[]})):[];if(O.length>0)throw Promise.all(O);const g=u.find(((e,t)=>{const r=a[t];return r&&(0,b.getHasError)({result:e,errorResetBoundary:i,throwOnError:r.throwOnError,query:s.getQueryCache().get(r.queryHash)})}));if(g?.error)throw g.error;return c(l())}},16743:(e,t,r)=>{"use strict";var s,n=Object.defineProperty,i=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,o=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)n(e,r,{get:t[r],enumerable:!0})})(u,{useQuery:()=>useQuery}),e.exports=(s=u,((e,t,r,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of a(t))o.call(e,u)||u===r||n(e,u,{get:()=>t[u],enumerable:!(s=i(t,u))||s.enumerable});return e})(n({},"__esModule",{value:!0}),s));var c=r(51934),l=r(35442);function useQuery(e,t){return(0,l.useBaseQuery)(e,c.QueryObserver,t)}},28619:(e,t,r)=>{"use strict";var s,n=Object.defineProperty,i=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,o=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)n(e,r,{get:t[r],enumerable:!0})})(u,{useSuspenseInfiniteQuery:()=>useSuspenseInfiniteQuery}),e.exports=(s=u,((e,t,r,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of a(t))o.call(e,u)||u===r||n(e,u,{get:()=>t[u],enumerable:!(s=i(t,u))||s.enumerable});return e})(n({},"__esModule",{value:!0}),s));var c=r(51934),l=r(35442),h=r(92816);function useSuspenseInfiniteQuery(e,t){return(0,l.useBaseQuery)({...e,enabled:!0,suspense:!0,throwOnError:h.defaultThrowOnError},c.InfiniteQueryObserver,t)}},44459:(e,t,r)=>{"use strict";var s,n=Object.defineProperty,i=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,o=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)n(e,r,{get:t[r],enumerable:!0})})(u,{useSuspenseQueries:()=>useSuspenseQueries}),e.exports=(s=u,((e,t,r,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of a(t))o.call(e,u)||u===r||n(e,u,{get:()=>t[u],enumerable:!(s=i(t,u))||s.enumerable});return e})(n({},"__esModule",{value:!0}),s));r(51934);var c=r(37963),l=r(92816);function useSuspenseQueries(e,t){return(0,c.useQueries)({...e,queries:e.queries.map((e=>({...e,suspense:!0,throwOnError:l.defaultThrowOnError,enabled:!0,placeholderData:void 0})))},t)}},37063:(e,t,r)=>{"use strict";var s,n=Object.defineProperty,i=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,o=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)n(e,r,{get:t[r],enumerable:!0})})(u,{useSuspenseQuery:()=>useSuspenseQuery}),e.exports=(s=u,((e,t,r,s)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of a(t))o.call(e,u)||u===r||n(e,u,{get:()=>t[u],enumerable:!(s=i(t,u))||s.enumerable});return e})(n({},"__esModule",{value:!0}),s));var c=r(51934),l=r(35442),h=r(92816);function useSuspenseQuery(e,t){return(0,l.useBaseQuery)({...e,enabled:!0,suspense:!0,throwOnError:h.defaultThrowOnError,placeholderData:void 0},c.QueryObserver,t)}},63315:e=>{"use strict";var t,r=Object.defineProperty,s=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,a={};function shouldThrowError(e,t){return"function"==typeof e?e(...t):!!e}function noop(){}((e,t)=>{for(var s in t)r(e,s,{get:t[s],enumerable:!0})})(a,{noop:()=>noop,shouldThrowError:()=>shouldThrowError}),e.exports=(t=a,((e,t,a,o)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of n(t))i.call(e,u)||u===a||r(e,u,{get:()=>t[u],enumerable:!(o=s(t,u))||o.enumerable});return e})(r({},"__esModule",{value:!0}),t))}},t={};function __webpack_require__(r){var s=t[r];if(void 0!==s)return s.exports;var n=t[r]={exports:{}};return e[r](n,n.exports,__webpack_require__),n.exports}(()=>{"use strict";var e=__webpack_require__(83876);window.elementorNotificationCenter={BarButtonNotification:e.BarButtonNotification}})()})();