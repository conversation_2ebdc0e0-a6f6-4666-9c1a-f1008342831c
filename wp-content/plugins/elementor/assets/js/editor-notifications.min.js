/*! elementor - v3.29.0 - 04-06-2025 */
/*! For license information please see editor-notifications.min.js.LICENSE.txt */
(()=>{var e={92584:(e,t,r)=>{"use strict";var n,s=Object.defineProperty,i=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,a=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)s(e,r,{get:t[r],enumerable:!0})})(u,{QueryClient:()=>l.QueryClient,QueryClientProvider:()=>l.QueryClientProvider,createQueryClient:()=>createQueryClient,useInfiniteQuery:()=>l.useInfiniteQuery,useMutation:()=>l.useMutation,useQuery:()=>l.useQuery,useQueryClient:()=>l.useQueryClient}),e.exports=(n=u,((e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of o(t))a.call(e,u)||u===r||s(e,u,{get:()=>t[u],enumerable:!(n=i(t,u))||n.enumerable});return e})(s({},"__esModule",{value:!0}),n));var c=r(51688),l=r(51688);function createQueryClient(){return new c.QueryClient({defaultOptions:{queries:{refetchOnWindowFocus:!1,refetchOnReconnect:!1}}})}},18791:(e,t,r)=>{"use strict";var n=r(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;_interopRequireWildcard(r(41594));var s=_interopRequireWildcard(r(75206)),i=r(7470);function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?r:t})(e)}function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=n(e)&&"function"!=typeof e)return{default:e};var r=_getRequireWildcardCache(t);if(r&&r.has(e))return r.get(e);var s={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&{}.hasOwnProperty.call(e,o)){var a=i?Object.getOwnPropertyDescriptor(e,o):null;a&&(a.get||a.set)?Object.defineProperty(s,o,a):s[o]=e[o]}return s.default=e,r&&r.set(e,s),s}t.default={render:function render(e,t){var r;try{var n=(0,i.createRoot)(t);n.render(e),r=function unmountFunction(){n.unmount()}}catch(n){s.render(e,t),r=function unmountFunction(){s.unmountComponentAtNode(t)}}return{unmount:r}}}},46120:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getNotifications=void 0;t.getNotifications=function getNotifications(){return function request(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return new Promise((function(r,n){elementorCommon.ajax.addRequest(e,{success:r,error:n,data:t})}))}("notifications_get")}},38205:(e,t,r)=>{"use strict";var n=r(62688),s=r(96784),i=r(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.EditorDrawer=void 0;var o=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=i(e)&&"function"!=typeof e)return{default:e};var r=_getRequireWildcardCache(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},s=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&{}.hasOwnProperty.call(e,o)){var a=s?Object.getOwnPropertyDescriptor(e,o):null;a&&(a.get||a.set)?Object.defineProperty(n,o,a):n[o]=e[o]}return n.default=e,r&&r.set(e,n),n}(r(41594)),a=s(r(18821)),u=r(74324);function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?r:t})(e)}(t.EditorDrawer=function EditorDrawer(e){var t=e.anchorPosition,r=void 0===t?"left":t,n=(0,o.useState)(!0),s=(0,a.default)(n,2),i=s[0],c=s[1];return(0,o.useEffect)((function(){elementor.on("elementor/editor/panel/whats-new/clicked",(function(){return c(!0)}))}),[]),o.default.createElement(u.WhatsNew,{isOpen:i,setIsOpen:c,setIsRead:function setIsRead(){return document.body.classList.remove("e-has-notification")},anchorPosition:r})}).propTypes={anchorPosition:n.oneOf(["left","top","right","bottom"])}},86034:(e,t,r)=>{"use strict";var n=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.editorOnButtonClicked=void 0;var s=n(r(41594)),i=n(r(18791)),o=r(38205),a=!1;t.editorOnButtonClicked=function editorOnButtonClicked(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"left";if(!a){a=!0;var t=document.createElement("div");return document.body.append(t),void i.default.render(s.default.createElement(o.EditorDrawer,{anchorPosition:e}),t)}elementor.trigger("elementor/editor/panel/whats-new/clicked")}},55549:(e,t,r)=>{"use strict";var n=r(12470).__;Object.defineProperty(t,"__esModule",{value:!0}),t.editorV1=void 0;var s=r(86034);t.editorV1=function editorV1(){elementor.on("panel:init",(function(){elementorNotifications.is_unread&&document.body.classList.add("e-has-notification"),elementor.getPanelView().getPages("menu").view.addItem({name:"notification-center",icon:"eicon-speakerphone",title:n("What's New","elementor"),callback:s.editorOnButtonClicked},"navigate_from_page","view-page")}))}},27348:(e,t,r)=>{"use strict";var n=r(62688),s=r(96784),i=r(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.editorV2=void 0;var o=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=i(e)&&"function"!=typeof e)return{default:e};var r=_getRequireWildcardCache(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},s=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&{}.hasOwnProperty.call(e,o)){var a=s?Object.getOwnPropertyDescriptor(e,o):null;a&&(a.get||a.set)?Object.defineProperty(n,o,a):n[o]=e[o]}return n.default=e,r&&r.set(e,n),n}(r(41594)),a=s(r(18821)),u=r(86034),c=r(86956),l=r(12470),h=s(r(8640));function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?r:t})(e)}var p=function IconWithBadge(e){var t=e.invisible;return o.default.createElement(c.Badge,{color:"primary",variant:"dot",invisible:t},o.default.createElement(h.default,null))};p.propTypes={invisible:n.bool};t.editorV2=function editorV2(){window.elementorV2.editorAppBar.utilitiesMenu.registerLink({id:"app-bar-menu-item-whats-new",priority:10,useProps:function useProps(){var e=(0,o.useState)(!elementorNotifications.is_unread),t=(0,a.default)(e,2),r=t[0],n=t[1];return{title:(0,l.__)("What's New","elementor"),icon:function icon(){return o.default.createElement(p,{invisible:r})},onClick:function onClick(){elementor.editorEvents.dispatchEvent(elementor.editorEvents.config.names.topBar.whatsNew,{location:elementor.editorEvents.config.locations.topBar,secondaryLocation:elementor.editorEvents.config.secondaryLocations["whats-new"],trigger:elementor.editorEvents.config.triggers.click,element:elementor.editorEvents.config.elements.buttonIcon}),n(!0),elementorNotifications.is_unread=!1,(0,u.editorOnButtonClicked)("right")}}}})}},58644:(e,t,r)=>{"use strict";var n=r(62688),s=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.WhatsNewDrawerContent=void 0;var i=s(r(41594)),o=r(92584),a=r(46120),u=r(86956),c=r(25206);(t.WhatsNewDrawerContent=function WhatsNewDrawerContent(e){var t=e.setIsOpen,r=(0,o.useQuery)({queryKey:["e-notifications"],queryFn:a.getNotifications}),n=r.isPending,s=r.error,l=r.data;return n?i.default.createElement(u.Box,null,i.default.createElement(u.LinearProgress,{color:"secondary"})):s?i.default.createElement(u.Box,null,"An error has occurred: ",s):l.map((function(e,r){return i.default.createElement(c.WhatsNewItem,{key:r,item:e,itemIndex:r,itemsLength:l.length,setIsOpen:t})}))}).propTypes={setIsOpen:n.func.isRequired}},24752:(e,t,r)=>{"use strict";var n=r(62688),s=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.WhatsNewItemChips=void 0;var i=s(r(41594)),o=s(r(78304)),a=r(86956);(t.WhatsNewItemChips=function WhatsNewItemChips(e){var t=e.chipPlan,r=e.chipTags,n=e.itemIndex,s=[];return t&&s.push({color:"promotion",size:"small",label:t}),r&&r.forEach((function(e){s.push({variant:"outlined",size:"small",label:e})})),s.length?i.default.createElement(a.Stack,{direction:"row",flexWrap:"wrap",gap:1,sx:{pb:1}},s.map((function(e,t){return i.default.createElement(a.Chip,(0,o.default)({key:"chip-".concat(n).concat(t)},e))}))):null}).propTypes={chipPlan:n.string,chipTags:n.array,itemIndex:n.number.isRequired}},46555:(e,t,r)=>{"use strict";var n=r(62688),s=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.WhatsNewItemThumbnail=void 0;var i=s(r(41594)),o=r(86956),a=r(94841);(t.WhatsNewItemThumbnail=function WhatsNewItemThumbnail(e){var t=e.imageSrc,r=e.title,n=e.link;return i.default.createElement(o.Box,{sx:{pb:2}},i.default.createElement(a.WrapperWithLink,{link:n},i.default.createElement("img",{src:t,alt:r,style:{maxWidth:"100%"}})))}).propTypes={imageSrc:n.string.isRequired,title:n.string.isRequired,link:n.string}},56971:(e,t,r)=>{"use strict";var n=r(62688),s=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.WhatsNewItemTopicLine=void 0;var i=s(r(41594)),o=r(86956);(t.WhatsNewItemTopicLine=function WhatsNewItemTopicLine(e){var t=e.topic,r=e.date;return i.default.createElement(o.Stack,{direction:"row",divider:i.default.createElement(o.Divider,{orientation:"vertical",flexItem:!0}),spacing:1,color:"text.tertiary",sx:{pb:1}},t&&i.default.createElement(o.Box,null,t),r&&i.default.createElement(o.Box,null,r))}).propTypes={topic:n.string,date:n.string}},25206:(e,t,r)=>{"use strict";var n=r(62688),s=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.WhatsNewItem=void 0;var i=s(r(41594)),o=r(86956),a=r(56971),u=r(94841),c=r(46555),l=r(24752);(t.WhatsNewItem=function WhatsNewItem(e){var t=e.item,r=e.itemIndex,n=e.itemsLength,s=e.setIsOpen;return i.default.createElement(o.Box,{key:r,display:"flex",flexDirection:"column",sx:{pt:2}},(t.topic||t.date)&&i.default.createElement(a.WhatsNewItemTopicLine,{topic:t.topic,date:t.date}),i.default.createElement(u.WrapperWithLink,{link:t.link},i.default.createElement(o.Typography,{variant:"subtitle1",sx:{pb:2}},t.title)),t.imageSrc&&i.default.createElement(c.WhatsNewItemThumbnail,{imageSrc:t.imageSrc,link:t.link,title:t.title}),i.default.createElement(l.WhatsNewItemChips,{chipPlan:t.chipPlan,chipTags:t.chipTags,itemIndex:r}),t.description&&i.default.createElement(o.Typography,{variant:"body2",color:"text.secondary",sx:{pb:2}},t.description,t.readMoreText&&i.default.createElement(i.default.Fragment,null," ",i.default.createElement(o.Link,{href:t.link,color:"info.main",target:"_blank"},t.readMoreText))),t.cta&&t.ctaLink&&i.default.createElement(o.Box,{sx:{pb:2}},i.default.createElement(o.Button,{href:t.ctaLink,target:t.ctaLink.startsWith("#")?"_self":"_blank",variant:"contained",size:"small",color:"promotion",onClick:t.ctaLink.startsWith("#")?function(){return s(!1)}:function(){}},t.cta)),r!==n-1&&i.default.createElement(o.Divider,{sx:{my:1}}))}).propTypes={item:n.object.isRequired,itemIndex:n.number.isRequired,itemsLength:n.number.isRequired,setIsOpen:n.func.isRequired}},30482:(e,t,r)=>{"use strict";var n=r(62688),s=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.WhatsNewTopBar=void 0;var i=s(r(41594)),o=r(86956),a=r(12470),u=r(59190);(t.WhatsNewTopBar=function WhatsNewTopBar(e){var t=e.setIsOpen;return i.default.createElement(i.default.Fragment,null,i.default.createElement(o.AppBar,{elevation:0,position:"sticky",sx:{backgroundColor:"background.default"}},i.default.createElement(o.Toolbar,{variant:"dense"},i.default.createElement(o.Typography,{variant:"overline",sx:{flexGrow:1}},(0,a.__)("What's New","elementor")),i.default.createElement(o.IconButton,{"aria-label":"close",size:"small",onClick:function onClick(){return t(!1)}},i.default.createElement(u.XIcon,null)))),i.default.createElement(o.Divider,null))}).propTypes={setIsOpen:n.func.isRequired}},74324:(e,t,r)=>{"use strict";var n=r(62688),s=r(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.WhatsNew=void 0;var i=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=s(e)&&"function"!=typeof e)return{default:e};var r=_getRequireWildcardCache(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&{}.hasOwnProperty.call(e,o)){var a=i?Object.getOwnPropertyDescriptor(e,o):null;a&&(a.get||a.set)?Object.defineProperty(n,o,a):n[o]=e[o]}return n.default=e,r&&r.set(e,n),n}(r(41594)),o=r(86956),a=r(92584),u=r(30482),c=r(58644);function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?r:t})(e)}var l=new a.QueryClient({defaultOptions:{queries:{refetchOnWindowFocus:!1,retry:!1,staleTime:18e5}}});(t.WhatsNew=function WhatsNew(e){var t,r,n=e.isOpen,s=e.setIsOpen,h=e.setIsRead,p=e.anchorPosition,f=void 0===p?"right":p;return(0,i.useEffect)((function(){n&&h(!0)}),[n,h]),i.default.createElement(i.default.Fragment,null,i.default.createElement(a.QueryClientProvider,{client:l},i.default.createElement(o.DirectionProvider,{rtl:elementorCommon.config.isRTL},i.default.createElement(o.ThemeProvider,{colorScheme:(null===(t=window.elementor)||void 0===t||null===(r=t.getPreferences)||void 0===r?void 0:r.call(t,"ui_theme"))||"auto"},i.default.createElement(o.Drawer,{anchor:f,open:n,onClose:function onClose(){return s(!1)},ModalProps:{style:{zIndex:999999}}},i.default.createElement(o.Box,{sx:{width:320,backgroundColor:"background.default"},role:"presentation"},i.default.createElement(u.WhatsNewTopBar,{setIsOpen:s}),i.default.createElement(o.Box,{sx:{padding:"16px"}},i.default.createElement(c.WhatsNewDrawerContent,{setIsOpen:s}))))))))}).propTypes={isOpen:n.bool.isRequired,setIsOpen:n.func.isRequired,setIsRead:n.func.isRequired,anchorPosition:n.oneOf(["left","top","right","bottom"])}},94841:(e,t,r)=>{"use strict";var n=r(62688),s=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.WrapperWithLink=void 0;var i=s(r(41594)),o=r(86956);(t.WrapperWithLink=function WrapperWithLink(e){var t=e.link,r=e.children;return t?i.default.createElement(o.Link,{href:t,target:"_blank",underline:"none",color:"inherit",sx:{"&:hover":{color:"inherit"}}},r):r}).propTypes={link:n.string,children:n.any.isRequired}},59190:(e,t,r)=>{"use strict";var n=r(96784),s=r(10564);Object.defineProperty(t,"__esModule",{value:!0}),t.XIcon=void 0;var i=function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=s(e)&&"function"!=typeof e)return{default:e};var r=_getRequireWildcardCache(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&{}.hasOwnProperty.call(e,o)){var a=i?Object.getOwnPropertyDescriptor(e,o):null;a&&(a.get||a.set)?Object.defineProperty(n,o,a):n[o]=e[o]}return n.default=e,r&&r.set(e,n),n}(r(41594)),o=n(r(78304)),a=r(86956);function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?r:t})(e)}t.XIcon=(0,i.forwardRef)((function(e,t){return i.default.createElement(a.SvgIcon,(0,o.default)({viewBox:"0 0 24 24"},e,{ref:t}),i.default.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M18.5303 5.46967C18.8232 5.76256 18.8232 6.23744 18.5303 6.53033L6.53033 18.5303C6.23744 18.8232 5.76256 18.8232 5.46967 18.5303C5.17678 18.2374 5.17678 17.7626 5.46967 17.4697L17.4697 5.46967C17.7626 5.17678 18.2374 5.17678 18.5303 5.46967Z"}),i.default.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M5.46967 5.46967C5.76256 5.17678 6.23744 5.17678 6.53033 5.46967L18.5303 17.4697C18.8232 17.7626 18.8232 18.2374 18.5303 18.5303C18.2374 18.8232 17.7626 18.8232 17.4697 18.5303L5.46967 6.53033C5.17678 6.23744 5.17678 5.76256 5.46967 5.46967Z"}))}))},40362:(e,t,r)=>{"use strict";var n=r(56441);function emptyFunction(){}function emptyFunctionWithReset(){}emptyFunctionWithReset.resetWarningCache=emptyFunction,e.exports=function(){function shim(e,t,r,s,i,o){if(o!==n){var a=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw a.name="Invariant Violation",a}}function getShim(){return shim}shim.isRequired=shim;var e={array:shim,bigint:shim,bool:shim,func:shim,number:shim,object:shim,string:shim,symbol:shim,any:shim,arrayOf:getShim,element:shim,elementType:shim,instanceOf:getShim,node:shim,objectOf:getShim,oneOf:getShim,oneOfType:getShim,shape:getShim,exact:getShim,checkPropTypes:emptyFunctionWithReset,resetWarningCache:emptyFunction};return e.PropTypes=e,e}},62688:(e,t,r)=>{e.exports=r(40362)()},56441:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},7470:(e,t,r)=>{"use strict";var n=r(75206);t.createRoot=n.createRoot,t.hydrateRoot=n.hydrateRoot},2192:(e,t,r)=>{"use strict";var n=r(41594),s=Symbol.for("react.element"),i=Symbol.for("react.fragment"),o=Object.prototype.hasOwnProperty,a=n.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,u={key:!0,ref:!0,__self:!0,__source:!0};function q(e,t,r){var n,i={},c=null,l=null;for(n in void 0!==r&&(c=""+r),void 0!==t.key&&(c=""+t.key),void 0!==t.ref&&(l=t.ref),t)o.call(t,n)&&!u.hasOwnProperty(n)&&(i[n]=t[n]);if(e&&e.defaultProps)for(n in t=e.defaultProps)void 0===i[n]&&(i[n]=t[n]);return{$$typeof:s,type:e,key:c,ref:l,props:i,_owner:a.current}}t.Fragment=i,t.jsx=q,t.jsxs=q},62540:(e,t,r)=>{"use strict";e.exports=r(2192)},41594:e=>{"use strict";e.exports=React},75206:e=>{"use strict";e.exports=ReactDOM},8640:e=>{"use strict";e.exports=elementorV2.icons.SpeakerphoneIcon},86956:e=>{"use strict";e.exports=elementorV2.ui},12470:e=>{"use strict";e.exports=wp.i18n},78113:e=>{e.exports=function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n},e.exports.__esModule=!0,e.exports.default=e.exports},70569:e=>{e.exports=function _arrayWithHoles(e){if(Array.isArray(e))return e},e.exports.__esModule=!0,e.exports.default=e.exports},78304:e=>{function _extends(){return e.exports=_extends=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},e.exports.__esModule=!0,e.exports.default=e.exports,_extends.apply(null,arguments)}e.exports=_extends,e.exports.__esModule=!0,e.exports.default=e.exports},96784:e=>{e.exports=function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}},e.exports.__esModule=!0,e.exports.default=e.exports},65474:e=>{e.exports=function _iterableToArrayLimit(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,s,i,o,a=[],u=!0,c=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(a.push(n.value),a.length!==t);u=!0);}catch(e){c=!0,s=e}finally{try{if(!u&&null!=r.return&&(o=r.return(),Object(o)!==o))return}finally{if(c)throw s}}return a}},e.exports.__esModule=!0,e.exports.default=e.exports},11018:e=>{e.exports=function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},e.exports.__esModule=!0,e.exports.default=e.exports},18821:(e,t,r)=>{var n=r(70569),s=r(65474),i=r(37744),o=r(11018);e.exports=function _slicedToArray(e,t){return n(e)||s(e,t)||i(e,t)||o()},e.exports.__esModule=!0,e.exports.default=e.exports},10564:e=>{function _typeof(t){return e.exports=_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports.default=e.exports,_typeof(t)}e.exports=_typeof,e.exports.__esModule=!0,e.exports.default=e.exports},37744:(e,t,r)=>{var n=r(78113);e.exports=function _unsupportedIterableToArray(e,t){if(e){if("string"==typeof e)return n(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?n(e,t):void 0}},e.exports.__esModule=!0,e.exports.default=e.exports},52803:(e,t,r)=>{"use strict";var n,s=Object.defineProperty,i=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,a=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)s(e,r,{get:t[r],enumerable:!0})})(u,{FocusManager:()=>h,focusManager:()=>p}),e.exports=(n=u,((e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of o(t))a.call(e,u)||u===r||s(e,u,{get:()=>t[u],enumerable:!(n=i(t,u))||n.enumerable});return e})(s({},"__esModule",{value:!0}),n));var c=r(66133),l=r(73025),h=class extends c.Subscribable{#e;#t;#r;constructor(){super(),this.#r=e=>{if(!l.isServer&&window.addEventListener){const listener=()=>e();return window.addEventListener("visibilitychange",listener,!1),()=>{window.removeEventListener("visibilitychange",listener)}}}}onSubscribe(){this.#t||this.setEventListener(this.#r)}onUnsubscribe(){this.hasListeners()||(this.#t?.(),this.#t=void 0)}setEventListener(e){this.#r=e,this.#t?.(),this.#t=e((e=>{"boolean"==typeof e?this.setFocused(e):this.onFocus()}))}setFocused(e){this.#e!==e&&(this.#e=e,this.onFocus())}onFocus(){const e=this.isFocused();this.listeners.forEach((t=>{t(e)}))}isFocused(){return"boolean"==typeof this.#e?this.#e:"hidden"!==globalThis.document?.visibilityState}},p=new h},28620:e=>{"use strict";var t,r=Object.defineProperty,n=Object.getOwnPropertyDescriptor,s=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,o={};function defaultTransformerFn(e){return e}function dehydrateMutation(e){return{mutationKey:e.options.mutationKey,state:e.state,...e.options.scope&&{scope:e.options.scope},...e.meta&&{meta:e.meta}}}function dehydrateQuery(e,t){return{state:{...e.state,...void 0!==e.state.data&&{data:t(e.state.data)}},queryKey:e.queryKey,queryHash:e.queryHash,..."pending"===e.state.status&&{promise:e.promise?.then(t).catch((e=>Promise.reject(new Error("redacted"))))},...e.meta&&{meta:e.meta}}}function defaultShouldDehydrateMutation(e){return e.state.isPaused}function defaultShouldDehydrateQuery(e){return"success"===e.state.status}function dehydrate(e,t={}){const r=t.shouldDehydrateMutation??e.getDefaultOptions().dehydrate?.shouldDehydrateMutation??defaultShouldDehydrateMutation,n=e.getMutationCache().getAll().flatMap((e=>r(e)?[dehydrateMutation(e)]:[])),s=t.shouldDehydrateQuery??e.getDefaultOptions().dehydrate?.shouldDehydrateQuery??defaultShouldDehydrateQuery,i=t.serializeData??e.getDefaultOptions().dehydrate?.serializeData??defaultTransformerFn;return{mutations:n,queries:e.getQueryCache().getAll().flatMap((e=>s(e)?[dehydrateQuery(e,i)]:[]))}}function hydrate(e,t,r){if("object"!=typeof t||null===t)return;const n=e.getMutationCache(),s=e.getQueryCache(),i=r?.defaultOptions?.deserializeData??e.getDefaultOptions().hydrate?.deserializeData??defaultTransformerFn,o=t.mutations||[],a=t.queries||[];o.forEach((({state:t,...s})=>{n.build(e,{...e.getDefaultOptions().hydrate?.mutations,...r?.defaultOptions?.mutations,...s},t)})),a.forEach((({queryKey:t,state:n,queryHash:o,meta:a,promise:u})=>{let c=s.get(o);const l=void 0===n.data?n.data:i(n.data);if(c){if(c.state.dataUpdatedAt<n.dataUpdatedAt){const{fetchStatus:e,...t}=n;c.setState({...t,data:l})}}else c=s.build(e,{...e.getDefaultOptions().hydrate?.queries,...r?.defaultOptions?.queries,queryKey:t,queryHash:o,meta:a},{...n,data:l,fetchStatus:"idle"});if(u){const e=Promise.resolve(u).then(i);c.fetch(void 0,{initialPromise:e})}}))}((e,t)=>{for(var n in t)r(e,n,{get:t[n],enumerable:!0})})(o,{defaultShouldDehydrateMutation:()=>defaultShouldDehydrateMutation,defaultShouldDehydrateQuery:()=>defaultShouldDehydrateQuery,dehydrate:()=>dehydrate,hydrate:()=>hydrate}),e.exports=(t=o,((e,t,o,a)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of s(t))i.call(e,u)||u===o||r(e,u,{get:()=>t[u],enumerable:!(a=n(t,u))||a.enumerable});return e})(r({},"__esModule",{value:!0}),t))},51934:(e,t,r)=>{"use strict";var n,s=Object.defineProperty,i=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,a=Object.prototype.hasOwnProperty,__copyProps=(e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of o(t))a.call(e,u)||u===r||s(e,u,{get:()=>t[u],enumerable:!(n=i(t,u))||n.enumerable});return e},u={};((e,t)=>{for(var r in t)s(e,r,{get:t[r],enumerable:!0})})(u,{CancelledError:()=>c.CancelledError,InfiniteQueryObserver:()=>d.InfiniteQueryObserver,Mutation:()=>_.Mutation,MutationCache:()=>y.MutationCache,MutationObserver:()=>b.MutationObserver,QueriesObserver:()=>f.QueriesObserver,Query:()=>j.Query,QueryCache:()=>l.QueryCache,QueryClient:()=>h.QueryClient,QueryObserver:()=>p.QueryObserver,defaultShouldDehydrateMutation:()=>w.defaultShouldDehydrateMutation,defaultShouldDehydrateQuery:()=>w.defaultShouldDehydrateQuery,dehydrate:()=>w.dehydrate,focusManager:()=>v.focusManager,hashKey:()=>O.hashKey,hydrate:()=>w.hydrate,isCancelledError:()=>P.isCancelledError,isServer:()=>O.isServer,keepPreviousData:()=>O.keepPreviousData,matchMutation:()=>O.matchMutation,matchQuery:()=>O.matchQuery,notifyManager:()=>m.notifyManager,onlineManager:()=>g.onlineManager,replaceEqualDeep:()=>O.replaceEqualDeep,skipToken:()=>O.skipToken}),e.exports=(n=u,__copyProps(s({},"__esModule",{value:!0}),n));var c=r(7785),l=r(18976),h=r(56587),p=r(63524),f=r(55828),d=r(75360),y=r(69239),b=r(3877),m=r(7734),v=r(52803),g=r(97692),O=r(73025),P=r(7785),w=r(28620);((e,t,r)=>{__copyProps(e,t,"default"),r&&__copyProps(r,t,"default")})(u,r(98813),e.exports);var j=r(44478),_=r(46071)},52200:(e,t,r)=>{"use strict";var n,s=Object.defineProperty,i=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,a=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)s(e,r,{get:t[r],enumerable:!0})})(u,{hasNextPage:()=>hasNextPage,hasPreviousPage:()=>hasPreviousPage,infiniteQueryBehavior:()=>infiniteQueryBehavior}),e.exports=(n=u,((e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of o(t))a.call(e,u)||u===r||s(e,u,{get:()=>t[u],enumerable:!(n=i(t,u))||n.enumerable});return e})(s({},"__esModule",{value:!0}),n));var c=r(73025);function infiniteQueryBehavior(e){return{onFetch:(t,r)=>{const n=t.options,s=t.fetchOptions?.meta?.fetchMore?.direction,i=t.state.data?.pages||[],o=t.state.data?.pageParams||[];let a={pages:[],pageParams:[]},u=0;const fetchFn=async()=>{let r=!1;const l=(0,c.ensureQueryFn)(t.options,t.fetchOptions),fetchPage=async(e,n,s)=>{if(r)return Promise.reject();if(null==n&&e.pages.length)return Promise.resolve(e);const i={queryKey:t.queryKey,pageParam:n,direction:s?"backward":"forward",meta:t.options.meta};var o;o=i,Object.defineProperty(o,"signal",{enumerable:!0,get:()=>(t.signal.aborted?r=!0:t.signal.addEventListener("abort",(()=>{r=!0})),t.signal)});const a=await l(i),{maxPages:u}=t.options,h=s?c.addToStart:c.addToEnd;return{pages:h(e.pages,a,u),pageParams:h(e.pageParams,n,u)}};if(s&&i.length){const e="backward"===s,t={pages:i,pageParams:o},r=(e?getPreviousPageParam:getNextPageParam)(n,t);a=await fetchPage(t,r,e)}else{const t=e??i.length;do{const e=0===u?o[0]??n.initialPageParam:getNextPageParam(n,a);if(u>0&&null==e)break;a=await fetchPage(a,e),u++}while(u<t)}return a};t.options.persister?t.fetchFn=()=>t.options.persister?.(fetchFn,{queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},r):t.fetchFn=fetchFn}}}function getNextPageParam(e,{pages:t,pageParams:r}){const n=t.length-1;return t.length>0?e.getNextPageParam(t[n],t,r[n],r):void 0}function getPreviousPageParam(e,{pages:t,pageParams:r}){return t.length>0?e.getPreviousPageParam?.(t[0],t,r[0],r):void 0}function hasNextPage(e,t){return!!t&&null!=getNextPageParam(e,t)}function hasPreviousPage(e,t){return!(!t||!e.getPreviousPageParam)&&null!=getPreviousPageParam(e,t)}},75360:(e,t,r)=>{"use strict";var n,s=Object.defineProperty,i=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,a=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)s(e,r,{get:t[r],enumerable:!0})})(u,{InfiniteQueryObserver:()=>h}),e.exports=(n=u,((e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of o(t))a.call(e,u)||u===r||s(e,u,{get:()=>t[u],enumerable:!(n=i(t,u))||n.enumerable});return e})(s({},"__esModule",{value:!0}),n));var c=r(63524),l=r(52200),h=class extends c.QueryObserver{constructor(e,t){super(e,t)}bindMethods(){super.bindMethods(),this.fetchNextPage=this.fetchNextPage.bind(this),this.fetchPreviousPage=this.fetchPreviousPage.bind(this)}setOptions(e,t){super.setOptions({...e,behavior:(0,l.infiniteQueryBehavior)()},t)}getOptimisticResult(e){return e.behavior=(0,l.infiniteQueryBehavior)(),super.getOptimisticResult(e)}fetchNextPage(e){return this.fetch({...e,meta:{fetchMore:{direction:"forward"}}})}fetchPreviousPage(e){return this.fetch({...e,meta:{fetchMore:{direction:"backward"}}})}createResult(e,t){const{state:r}=e,n=super.createResult(e,t),{isFetching:s,isRefetching:i,isError:o,isRefetchError:a}=n,u=r.fetchMeta?.fetchMore?.direction,c=o&&"forward"===u,h=s&&"forward"===u,p=o&&"backward"===u,f=s&&"backward"===u;return{...n,fetchNextPage:this.fetchNextPage,fetchPreviousPage:this.fetchPreviousPage,hasNextPage:(0,l.hasNextPage)(t,r.data),hasPreviousPage:(0,l.hasPreviousPage)(t,r.data),isFetchNextPageError:c,isFetchingNextPage:h,isFetchPreviousPageError:p,isFetchingPreviousPage:f,isRefetchError:a&&!c&&!p,isRefetching:i&&!h&&!f}}}},46071:(e,t,r)=>{"use strict";var n,s=Object.defineProperty,i=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,a=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)s(e,r,{get:t[r],enumerable:!0})})(u,{Mutation:()=>p,getDefaultState:()=>getDefaultState}),e.exports=(n=u,((e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of o(t))a.call(e,u)||u===r||s(e,u,{get:()=>t[u],enumerable:!(n=i(t,u))||n.enumerable});return e})(s({},"__esModule",{value:!0}),n));var c=r(7734),l=r(95181),h=r(7785),p=class extends l.Removable{#n;#s;#i;constructor(e){super(),this.mutationId=e.mutationId,this.#s=e.mutationCache,this.#n=[],this.state=e.state||{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0},this.setOptions(e.options),this.scheduleGc()}setOptions(e){this.options=e,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(e){this.#n.includes(e)||(this.#n.push(e),this.clearGcTimeout(),this.#s.notify({type:"observerAdded",mutation:this,observer:e}))}removeObserver(e){this.#n=this.#n.filter((t=>t!==e)),this.scheduleGc(),this.#s.notify({type:"observerRemoved",mutation:this,observer:e})}optionalRemove(){this.#n.length||("pending"===this.state.status?this.scheduleGc():this.#s.remove(this))}continue(){return this.#i?.continue()??this.execute(this.state.variables)}async execute(e){this.#i=(0,h.createRetryer)({fn:()=>this.options.mutationFn?this.options.mutationFn(e):Promise.reject(new Error("No mutationFn found")),onFail:(e,t)=>{this.#o({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#o({type:"pause"})},onContinue:()=>{this.#o({type:"continue"})},retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#s.canRun(this)});const t="pending"===this.state.status,r=!this.#i.canStart();try{if(!t){this.#o({type:"pending",variables:e,isPaused:r}),await(this.#s.config.onMutate?.(e,this));const t=await(this.options.onMutate?.(e));t!==this.state.context&&this.#o({type:"pending",context:t,variables:e,isPaused:r})}const n=await this.#i.start();return await(this.#s.config.onSuccess?.(n,e,this.state.context,this)),await(this.options.onSuccess?.(n,e,this.state.context)),await(this.#s.config.onSettled?.(n,null,this.state.variables,this.state.context,this)),await(this.options.onSettled?.(n,null,e,this.state.context)),this.#o({type:"success",data:n}),n}catch(t){try{throw await(this.#s.config.onError?.(t,e,this.state.context,this)),await(this.options.onError?.(t,e,this.state.context)),await(this.#s.config.onSettled?.(void 0,t,this.state.variables,this.state.context,this)),await(this.options.onSettled?.(void 0,t,e,this.state.context)),t}finally{this.#o({type:"error",error:t})}}finally{this.#s.runNext(this)}}#o(e){this.state=(t=>{switch(e.type){case"failed":return{...t,failureCount:e.failureCount,failureReason:e.error};case"pause":return{...t,isPaused:!0};case"continue":return{...t,isPaused:!1};case"pending":return{...t,context:e.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:e.isPaused,status:"pending",variables:e.variables,submittedAt:Date.now()};case"success":return{...t,data:e.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...t,data:void 0,error:e.error,failureCount:t.failureCount+1,failureReason:e.error,isPaused:!1,status:"error"}}})(this.state),c.notifyManager.batch((()=>{this.#n.forEach((t=>{t.onMutationUpdate(e)})),this.#s.notify({mutation:this,type:"updated",action:e})}))}};function getDefaultState(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}},69239:(e,t,r)=>{"use strict";var n,s=Object.defineProperty,i=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,a=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)s(e,r,{get:t[r],enumerable:!0})})(u,{MutationCache:()=>f}),e.exports=(n=u,((e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of o(t))a.call(e,u)||u===r||s(e,u,{get:()=>t[u],enumerable:!(n=i(t,u))||n.enumerable});return e})(s({},"__esModule",{value:!0}),n));var c=r(7734),l=r(46071),h=r(73025),p=r(66133),f=class extends p.Subscribable{constructor(e={}){super(),this.config=e,this.#a=new Map,this.#u=Date.now()}#a;#u;build(e,t,r){const n=new l.Mutation({mutationCache:this,mutationId:++this.#u,options:e.defaultMutationOptions(t),state:r});return this.add(n),n}add(e){const t=scopeFor(e),r=this.#a.get(t)??[];r.push(e),this.#a.set(t,r),this.notify({type:"added",mutation:e})}remove(e){const t=scopeFor(e);if(this.#a.has(t)){const r=this.#a.get(t)?.filter((t=>t!==e));r&&(0===r.length?this.#a.delete(t):this.#a.set(t,r))}this.notify({type:"removed",mutation:e})}canRun(e){const t=this.#a.get(scopeFor(e))?.find((e=>"pending"===e.state.status));return!t||t===e}runNext(e){const t=this.#a.get(scopeFor(e))?.find((t=>t!==e&&t.state.isPaused));return t?.continue()??Promise.resolve()}clear(){c.notifyManager.batch((()=>{this.getAll().forEach((e=>{this.remove(e)}))}))}getAll(){return[...this.#a.values()].flat()}find(e){const t={exact:!0,...e};return this.getAll().find((e=>(0,h.matchMutation)(t,e)))}findAll(e={}){return this.getAll().filter((t=>(0,h.matchMutation)(e,t)))}notify(e){c.notifyManager.batch((()=>{this.listeners.forEach((t=>{t(e)}))}))}resumePausedMutations(){const e=this.getAll().filter((e=>e.state.isPaused));return c.notifyManager.batch((()=>Promise.all(e.map((e=>e.continue().catch(h.noop))))))}};function scopeFor(e){return e.options.scope?.id??String(e.mutationId)}},3877:(e,t,r)=>{"use strict";var n,s=Object.defineProperty,i=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,a=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)s(e,r,{get:t[r],enumerable:!0})})(u,{MutationObserver:()=>f}),e.exports=(n=u,((e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of o(t))a.call(e,u)||u===r||s(e,u,{get:()=>t[u],enumerable:!(n=i(t,u))||n.enumerable});return e})(s({},"__esModule",{value:!0}),n));var c=r(46071),l=r(7734),h=r(66133),p=r(73025),f=class extends h.Subscribable{#c;#l=void 0;#h;#p;constructor(e,t){super(),this.#c=e,this.setOptions(t),this.bindMethods(),this.#f()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(e){const t=this.options;this.options=this.#c.defaultMutationOptions(e),(0,p.shallowEqualObjects)(this.options,t)||this.#c.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.#h,observer:this}),t?.mutationKey&&this.options.mutationKey&&(0,p.hashKey)(t.mutationKey)!==(0,p.hashKey)(this.options.mutationKey)?this.reset():"pending"===this.#h?.state.status&&this.#h.setOptions(this.options)}onUnsubscribe(){this.hasListeners()||this.#h?.removeObserver(this)}onMutationUpdate(e){this.#f(),this.#d(e)}getCurrentResult(){return this.#l}reset(){this.#h?.removeObserver(this),this.#h=void 0,this.#f(),this.#d()}mutate(e,t){return this.#p=t,this.#h?.removeObserver(this),this.#h=this.#c.getMutationCache().build(this.#c,this.options),this.#h.addObserver(this),this.#h.execute(e)}#f(){const e=this.#h?.state??(0,c.getDefaultState)();this.#l={...e,isPending:"pending"===e.status,isSuccess:"success"===e.status,isError:"error"===e.status,isIdle:"idle"===e.status,mutate:this.mutate,reset:this.reset}}#d(e){l.notifyManager.batch((()=>{if(this.#p&&this.hasListeners()){const t=this.#l.variables,r=this.#l.context;"success"===e?.type?(this.#p.onSuccess?.(e.data,t,r),this.#p.onSettled?.(e.data,null,t,r)):"error"===e?.type&&(this.#p.onError?.(e.error,t,r),this.#p.onSettled?.(void 0,e.error,t,r))}this.listeners.forEach((e=>{e(this.#l)}))}))}}},7734:e=>{"use strict";var t,r=Object.defineProperty,n=Object.getOwnPropertyDescriptor,s=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,o={};function createNotifyManager(){let e=[],t=0,notifyFn=e=>{e()},batchNotifyFn=e=>{e()},scheduleFn=e=>setTimeout(e,0);const schedule=r=>{t?e.push(r):scheduleFn((()=>{notifyFn(r)}))};return{batch:r=>{let n;t++;try{n=r()}finally{t--,t||(()=>{const t=e;e=[],t.length&&scheduleFn((()=>{batchNotifyFn((()=>{t.forEach((e=>{notifyFn(e)}))}))}))})()}return n},batchCalls:e=>(...t)=>{schedule((()=>{e(...t)}))},schedule,setNotifyFunction:e=>{notifyFn=e},setBatchNotifyFunction:e=>{batchNotifyFn=e},setScheduler:e=>{scheduleFn=e}}}((e,t)=>{for(var n in t)r(e,n,{get:t[n],enumerable:!0})})(o,{createNotifyManager:()=>createNotifyManager,notifyManager:()=>a}),e.exports=(t=o,((e,t,o,a)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of s(t))i.call(e,u)||u===o||r(e,u,{get:()=>t[u],enumerable:!(a=n(t,u))||a.enumerable});return e})(r({},"__esModule",{value:!0}),t));var a=createNotifyManager()},97692:(e,t,r)=>{"use strict";var n,s=Object.defineProperty,i=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,a=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)s(e,r,{get:t[r],enumerable:!0})})(u,{OnlineManager:()=>h,onlineManager:()=>p}),e.exports=(n=u,((e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of o(t))a.call(e,u)||u===r||s(e,u,{get:()=>t[u],enumerable:!(n=i(t,u))||n.enumerable});return e})(s({},"__esModule",{value:!0}),n));var c=r(66133),l=r(73025),h=class extends c.Subscribable{#y=!0;#t;#r;constructor(){super(),this.#r=e=>{if(!l.isServer&&window.addEventListener){const onlineListener=()=>e(!0),offlineListener=()=>e(!1);return window.addEventListener("online",onlineListener,!1),window.addEventListener("offline",offlineListener,!1),()=>{window.removeEventListener("online",onlineListener),window.removeEventListener("offline",offlineListener)}}}}onSubscribe(){this.#t||this.setEventListener(this.#r)}onUnsubscribe(){this.hasListeners()||(this.#t?.(),this.#t=void 0)}setEventListener(e){this.#r=e,this.#t?.(),this.#t=e(this.setOnline.bind(this))}setOnline(e){this.#y!==e&&(this.#y=e,this.listeners.forEach((t=>{t(e)})))}isOnline(){return this.#y}},p=new h},55828:(e,t,r)=>{"use strict";var n,s=Object.defineProperty,i=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,a=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)s(e,r,{get:t[r],enumerable:!0})})(u,{QueriesObserver:()=>f}),e.exports=(n=u,((e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of o(t))a.call(e,u)||u===r||s(e,u,{get:()=>t[u],enumerable:!(n=i(t,u))||n.enumerable});return e})(s({},"__esModule",{value:!0}),n));var c=r(7734),l=r(63524),h=r(66133),p=r(73025);function difference(e,t){return e.filter((e=>!t.includes(e)))}var f=class extends h.Subscribable{#c;#b;#m;#v;#n;#g;#O;#P;constructor(e,t,r){super(),this.#c=e,this.#v=r,this.#m=[],this.#n=[],this.#b=[],this.setQueries(t)}onSubscribe(){1===this.listeners.size&&this.#n.forEach((e=>{e.subscribe((t=>{this.#w(e,t)}))}))}onUnsubscribe(){this.listeners.size||this.destroy()}destroy(){this.listeners=new Set,this.#n.forEach((e=>{e.destroy()}))}setQueries(e,t,r){this.#m=e,this.#v=t,c.notifyManager.batch((()=>{const e=this.#n,t=this.#j(this.#m);t.forEach((e=>e.observer.setOptions(e.defaultedQueryOptions,r)));const n=t.map((e=>e.observer)),s=n.map((e=>e.getCurrentResult())),i=n.some(((t,r)=>t!==e[r]));(e.length!==n.length||i)&&(this.#n=n,this.#b=s,this.hasListeners()&&(difference(e,n).forEach((e=>{e.destroy()})),difference(n,e).forEach((e=>{e.subscribe((t=>{this.#w(e,t)}))})),this.#d()))}))}getCurrentResult(){return this.#b}getQueries(){return this.#n.map((e=>e.getCurrentQuery()))}getObservers(){return this.#n}getOptimisticResult(e,t){const r=this.#j(e).map((e=>e.observer.getOptimisticResult(e.defaultedQueryOptions)));return[r,e=>this.#_(e??r,t),()=>this.#M(r,e)]}#M(e,t){const r=this.#j(t);return r.map(((t,n)=>{const s=e[n];return t.defaultedQueryOptions.notifyOnChangeProps?s:t.observer.trackResult(s,(e=>{r.forEach((t=>{t.observer.trackProp(e)}))}))}))}#_(e,t){return t?(this.#g&&this.#b===this.#P&&t===this.#O||(this.#O=t,this.#P=this.#b,this.#g=(0,p.replaceEqualDeep)(this.#g,t(e))),this.#g):e}#j(e){const t=new Map(this.#n.map((e=>[e.options.queryHash,e]))),r=[];return e.forEach((e=>{const n=this.#c.defaultQueryOptions(e),s=t.get(n.queryHash);s?r.push({defaultedQueryOptions:n,observer:s}):r.push({defaultedQueryOptions:n,observer:new l.QueryObserver(this.#c,n)})})),r}#w(e,t){const r=this.#n.indexOf(e);-1!==r&&(this.#b=function replaceAt(e,t,r){const n=e.slice(0);return n[t]=r,n}(this.#b,r,t),this.#d())}#d(){if(this.hasListeners()){this.#g!==this.#_(this.#M(this.#b,this.#m),this.#v?.combine)&&c.notifyManager.batch((()=>{this.listeners.forEach((e=>{e(this.#b)}))}))}}}},44478:(e,t,r)=>{"use strict";var n,s=Object.defineProperty,i=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,a=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)s(e,r,{get:t[r],enumerable:!0})})(u,{Query:()=>f,fetchState:()=>fetchState}),e.exports=(n=u,((e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of o(t))a.call(e,u)||u===r||s(e,u,{get:()=>t[u],enumerable:!(n=i(t,u))||n.enumerable});return e})(s({},"__esModule",{value:!0}),n));var c=r(73025),l=r(7734),h=r(7785),p=r(95181),f=class extends p.Removable{#R;#Q;#C;#i;#E;#S;constructor(e){super(),this.#S=!1,this.#E=e.defaultOptions,this.setOptions(e.options),this.observers=[],this.#C=e.cache,this.queryKey=e.queryKey,this.queryHash=e.queryHash,this.#R=function getDefaultState(e){const t="function"==typeof e.initialData?e.initialData():e.initialData,r=void 0!==t,n=r?"function"==typeof e.initialDataUpdatedAt?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:r?n??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:r?"success":"pending",fetchStatus:"idle"}}(this.options),this.state=e.state??this.#R,this.scheduleGc()}get meta(){return this.options.meta}get promise(){return this.#i?.promise}setOptions(e){this.options={...this.#E,...e},this.updateGcTime(this.options.gcTime)}optionalRemove(){this.observers.length||"idle"!==this.state.fetchStatus||this.#C.remove(this)}setData(e,t){const r=(0,c.replaceData)(this.state.data,e,this.options);return this.#o({data:r,type:"success",dataUpdatedAt:t?.updatedAt,manual:t?.manual}),r}setState(e,t){this.#o({type:"setState",state:e,setStateOptions:t})}cancel(e){const t=this.#i?.promise;return this.#i?.cancel(e),t?t.then(c.noop).catch(c.noop):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(this.#R)}isActive(){return this.observers.some((e=>!1!==(0,c.resolveEnabled)(e.options.enabled,this)))}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===c.skipToken||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStale(){return!!this.state.isInvalidated||(this.getObserversCount()>0?this.observers.some((e=>e.getCurrentResult().isStale)):void 0===this.state.data)}isStaleByTime(e=0){return this.state.isInvalidated||void 0===this.state.data||!(0,c.timeUntilStale)(this.state.dataUpdatedAt,e)}onFocus(){const e=this.observers.find((e=>e.shouldFetchOnWindowFocus()));e?.refetch({cancelRefetch:!1}),this.#i?.continue()}onOnline(){const e=this.observers.find((e=>e.shouldFetchOnReconnect()));e?.refetch({cancelRefetch:!1}),this.#i?.continue()}addObserver(e){this.observers.includes(e)||(this.observers.push(e),this.clearGcTimeout(),this.#C.notify({type:"observerAdded",query:this,observer:e}))}removeObserver(e){this.observers.includes(e)&&(this.observers=this.observers.filter((t=>t!==e)),this.observers.length||(this.#i&&(this.#S?this.#i.cancel({revert:!0}):this.#i.cancelRetry()),this.scheduleGc()),this.#C.notify({type:"observerRemoved",query:this,observer:e}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||this.#o({type:"invalidate"})}fetch(e,t){if("idle"!==this.state.fetchStatus)if(void 0!==this.state.data&&t?.cancelRefetch)this.cancel({silent:!0});else if(this.#i)return this.#i.continueRetry(),this.#i.promise;if(e&&this.setOptions(e),!this.options.queryFn){const e=this.observers.find((e=>e.options.queryFn));e&&this.setOptions(e.options)}const r=new AbortController,addSignalProperty=e=>{Object.defineProperty(e,"signal",{enumerable:!0,get:()=>(this.#S=!0,r.signal)})},n={fetchOptions:t,options:this.options,queryKey:this.queryKey,state:this.state,fetchFn:()=>{const e=(0,c.ensureQueryFn)(this.options,t),r={queryKey:this.queryKey,meta:this.meta};return addSignalProperty(r),this.#S=!1,this.options.persister?this.options.persister(e,r,this):e(r)}};addSignalProperty(n),this.options.behavior?.onFetch(n,this),this.#Q=this.state,"idle"!==this.state.fetchStatus&&this.state.fetchMeta===n.fetchOptions?.meta||this.#o({type:"fetch",meta:n.fetchOptions?.meta});const onError=e=>{(0,h.isCancelledError)(e)&&e.silent||this.#o({type:"error",error:e}),(0,h.isCancelledError)(e)||(this.#C.config.onError?.(e,this),this.#C.config.onSettled?.(this.state.data,e,this)),this.scheduleGc()};return this.#i=(0,h.createRetryer)({initialPromise:t?.initialPromise,fn:n.fetchFn,abort:r.abort.bind(r),onSuccess:e=>{if(void 0!==e){try{this.setData(e)}catch(e){return void onError(e)}this.#C.config.onSuccess?.(e,this),this.#C.config.onSettled?.(e,this.state.error,this),this.scheduleGc()}else onError(new Error(`${this.queryHash} data is undefined`))},onError,onFail:(e,t)=>{this.#o({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#o({type:"pause"})},onContinue:()=>{this.#o({type:"continue"})},retry:n.options.retry,retryDelay:n.options.retryDelay,networkMode:n.options.networkMode,canRun:()=>!0}),this.#i.start()}#o(e){this.state=(t=>{switch(e.type){case"failed":return{...t,fetchFailureCount:e.failureCount,fetchFailureReason:e.error};case"pause":return{...t,fetchStatus:"paused"};case"continue":return{...t,fetchStatus:"fetching"};case"fetch":return{...t,...fetchState(t.data,this.options),fetchMeta:e.meta??null};case"success":return{...t,data:e.data,dataUpdateCount:t.dataUpdateCount+1,dataUpdatedAt:e.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!e.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const r=e.error;return(0,h.isCancelledError)(r)&&r.revert&&this.#Q?{...this.#Q,fetchStatus:"idle"}:{...t,error:r,errorUpdateCount:t.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:t.fetchFailureCount+1,fetchFailureReason:r,fetchStatus:"idle",status:"error"};case"invalidate":return{...t,isInvalidated:!0};case"setState":return{...t,...e.state}}})(this.state),l.notifyManager.batch((()=>{this.observers.forEach((e=>{e.onQueryUpdate()})),this.#C.notify({query:this,type:"updated",action:e})}))}};function fetchState(e,t){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:(0,h.canFetch)(t.networkMode)?"fetching":"paused",...void 0===e&&{error:null,status:"pending"}}}},18976:(e,t,r)=>{"use strict";var n,s=Object.defineProperty,i=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,a=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)s(e,r,{get:t[r],enumerable:!0})})(u,{QueryCache:()=>f}),e.exports=(n=u,((e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of o(t))a.call(e,u)||u===r||s(e,u,{get:()=>t[u],enumerable:!(n=i(t,u))||n.enumerable});return e})(s({},"__esModule",{value:!0}),n));var c=r(73025),l=r(44478),h=r(7734),p=r(66133),f=class extends p.Subscribable{constructor(e={}){super(),this.config=e,this.#m=new Map}#m;build(e,t,r){const n=t.queryKey,s=t.queryHash??(0,c.hashQueryKeyByOptions)(n,t);let i=this.get(s);return i||(i=new l.Query({cache:this,queryKey:n,queryHash:s,options:e.defaultQueryOptions(t),state:r,defaultOptions:e.getQueryDefaults(n)}),this.add(i)),i}add(e){this.#m.has(e.queryHash)||(this.#m.set(e.queryHash,e),this.notify({type:"added",query:e}))}remove(e){const t=this.#m.get(e.queryHash);t&&(e.destroy(),t===e&&this.#m.delete(e.queryHash),this.notify({type:"removed",query:e}))}clear(){h.notifyManager.batch((()=>{this.getAll().forEach((e=>{this.remove(e)}))}))}get(e){return this.#m.get(e)}getAll(){return[...this.#m.values()]}find(e){const t={exact:!0,...e};return this.getAll().find((e=>(0,c.matchQuery)(t,e)))}findAll(e={}){const t=this.getAll();return Object.keys(e).length>0?t.filter((t=>(0,c.matchQuery)(e,t))):t}notify(e){h.notifyManager.batch((()=>{this.listeners.forEach((t=>{t(e)}))}))}onFocus(){h.notifyManager.batch((()=>{this.getAll().forEach((e=>{e.onFocus()}))}))}onOnline(){h.notifyManager.batch((()=>{this.getAll().forEach((e=>{e.onOnline()}))}))}}},56587:(e,t,r)=>{"use strict";var n,s=Object.defineProperty,i=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,a=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)s(e,r,{get:t[r],enumerable:!0})})(u,{QueryClient:()=>b}),e.exports=(n=u,((e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of o(t))a.call(e,u)||u===r||s(e,u,{get:()=>t[u],enumerable:!(n=i(t,u))||n.enumerable});return e})(s({},"__esModule",{value:!0}),n));var c=r(73025),l=r(18976),h=r(69239),p=r(52803),f=r(97692),d=r(7734),y=r(52200),b=class{#x;#s;#E;#q;#D;#I;#T;#k;constructor(e={}){this.#x=e.queryCache||new l.QueryCache,this.#s=e.mutationCache||new h.MutationCache,this.#E=e.defaultOptions||{},this.#q=new Map,this.#D=new Map,this.#I=0}mount(){this.#I++,1===this.#I&&(this.#T=p.focusManager.subscribe((async e=>{e&&(await this.resumePausedMutations(),this.#x.onFocus())})),this.#k=f.onlineManager.subscribe((async e=>{e&&(await this.resumePausedMutations(),this.#x.onOnline())})))}unmount(){this.#I--,0===this.#I&&(this.#T?.(),this.#T=void 0,this.#k?.(),this.#k=void 0)}isFetching(e){return this.#x.findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return this.#s.findAll({...e,status:"pending"}).length}getQueryData(e){const t=this.defaultQueryOptions({queryKey:e});return this.#x.get(t.queryHash)?.state.data}ensureQueryData(e){const t=this.defaultQueryOptions(e),r=this.#x.build(this,t),n=r.state.data;return void 0===n?this.fetchQuery(e):(e.revalidateIfStale&&r.isStaleByTime((0,c.resolveStaleTime)(t.staleTime,r))&&this.prefetchQuery(t),Promise.resolve(n))}getQueriesData(e){return this.#x.findAll(e).map((({queryKey:e,state:t})=>[e,t.data]))}setQueryData(e,t,r){const n=this.defaultQueryOptions({queryKey:e}),s=this.#x.get(n.queryHash),i=s?.state.data,o=(0,c.functionalUpdate)(t,i);if(void 0!==o)return this.#x.build(this,n).setData(o,{...r,manual:!0})}setQueriesData(e,t,r){return d.notifyManager.batch((()=>this.#x.findAll(e).map((({queryKey:e})=>[e,this.setQueryData(e,t,r)]))))}getQueryState(e){const t=this.defaultQueryOptions({queryKey:e});return this.#x.get(t.queryHash)?.state}removeQueries(e){const t=this.#x;d.notifyManager.batch((()=>{t.findAll(e).forEach((e=>{t.remove(e)}))}))}resetQueries(e,t){const r=this.#x,n={type:"active",...e};return d.notifyManager.batch((()=>(r.findAll(e).forEach((e=>{e.reset()})),this.refetchQueries(n,t))))}cancelQueries(e,t={}){const r={revert:!0,...t},n=d.notifyManager.batch((()=>this.#x.findAll(e).map((e=>e.cancel(r)))));return Promise.all(n).then(c.noop).catch(c.noop)}invalidateQueries(e,t={}){return d.notifyManager.batch((()=>{if(this.#x.findAll(e).forEach((e=>{e.invalidate()})),"none"===e?.refetchType)return Promise.resolve();const r={...e,type:e?.refetchType??e?.type??"active"};return this.refetchQueries(r,t)}))}refetchQueries(e,t={}){const r={...t,cancelRefetch:t.cancelRefetch??!0},n=d.notifyManager.batch((()=>this.#x.findAll(e).filter((e=>!e.isDisabled())).map((e=>{let t=e.fetch(void 0,r);return r.throwOnError||(t=t.catch(c.noop)),"paused"===e.state.fetchStatus?Promise.resolve():t}))));return Promise.all(n).then(c.noop)}fetchQuery(e){const t=this.defaultQueryOptions(e);void 0===t.retry&&(t.retry=!1);const r=this.#x.build(this,t);return r.isStaleByTime((0,c.resolveStaleTime)(t.staleTime,r))?r.fetch(t):Promise.resolve(r.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(c.noop).catch(c.noop)}fetchInfiniteQuery(e){return e.behavior=(0,y.infiniteQueryBehavior)(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(c.noop).catch(c.noop)}ensureInfiniteQueryData(e){return e.behavior=(0,y.infiniteQueryBehavior)(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return f.onlineManager.isOnline()?this.#s.resumePausedMutations():Promise.resolve()}getQueryCache(){return this.#x}getMutationCache(){return this.#s}getDefaultOptions(){return this.#E}setDefaultOptions(e){this.#E=e}setQueryDefaults(e,t){this.#q.set((0,c.hashKey)(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){const t=[...this.#q.values()],r={};return t.forEach((t=>{(0,c.partialMatchKey)(e,t.queryKey)&&Object.assign(r,t.defaultOptions)})),r}setMutationDefaults(e,t){this.#D.set((0,c.hashKey)(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){const t=[...this.#D.values()];let r={};return t.forEach((t=>{(0,c.partialMatchKey)(e,t.mutationKey)&&(r={...r,...t.defaultOptions})})),r}defaultQueryOptions(e){if(e._defaulted)return e;const t={...this.#E.queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=(0,c.hashQueryKeyByOptions)(t.queryKey,t)),void 0===t.refetchOnReconnect&&(t.refetchOnReconnect="always"!==t.networkMode),void 0===t.throwOnError&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t.queryFn===c.skipToken&&(t.enabled=!1),t}defaultMutationOptions(e){return e?._defaulted?e:{...this.#E.mutations,...e?.mutationKey&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){this.#x.clear(),this.#s.clear()}}},63524:(e,t,r)=>{"use strict";var n,s=Object.defineProperty,i=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,a=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)s(e,r,{get:t[r],enumerable:!0})})(u,{QueryObserver:()=>y}),e.exports=(n=u,((e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of o(t))a.call(e,u)||u===r||s(e,u,{get:()=>t[u],enumerable:!(n=i(t,u))||n.enumerable});return e})(s({},"__esModule",{value:!0}),n));var c=r(52803),l=r(7734),h=r(44478),p=r(66133),f=r(89675),d=r(73025),y=class extends p.Subscribable{constructor(e,t){super(),this.options=t,this.#c=e,this.#F=null,this.#N=(0,f.pendingThenable)(),this.options.experimental_prefetchInRender||this.#N.reject(new Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(t)}#c;#W=void 0;#A=void 0;#l=void 0;#B;#K;#N;#F;#U;#L;#H;#V;#z;#G;#$=new Set;bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){1===this.listeners.size&&(this.#W.addObserver(this),shouldFetchOnMount(this.#W,this.options)?this.#X():this.updateResult(),this.#Y())}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return shouldFetchOn(this.#W,this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return shouldFetchOn(this.#W,this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,this.#Z(),this.#J(),this.#W.removeObserver(this)}setOptions(e,t){const r=this.options,n=this.#W;if(this.options=this.#c.defaultQueryOptions(e),void 0!==this.options.enabled&&"boolean"!=typeof this.options.enabled&&"function"!=typeof this.options.enabled&&"boolean"!=typeof(0,d.resolveEnabled)(this.options.enabled,this.#W))throw new Error("Expected enabled to be a boolean or a callback that returns a boolean");this.#ee(),this.#W.setOptions(this.options),r._defaulted&&!(0,d.shallowEqualObjects)(this.options,r)&&this.#c.getQueryCache().notify({type:"observerOptionsUpdated",query:this.#W,observer:this});const s=this.hasListeners();s&&shouldFetchOptionally(this.#W,n,this.options,r)&&this.#X(),this.updateResult(t),!s||this.#W===n&&(0,d.resolveEnabled)(this.options.enabled,this.#W)===(0,d.resolveEnabled)(r.enabled,this.#W)&&(0,d.resolveStaleTime)(this.options.staleTime,this.#W)===(0,d.resolveStaleTime)(r.staleTime,this.#W)||this.#te();const i=this.#re();!s||this.#W===n&&(0,d.resolveEnabled)(this.options.enabled,this.#W)===(0,d.resolveEnabled)(r.enabled,this.#W)&&i===this.#G||this.#ne(i)}getOptimisticResult(e){const t=this.#c.getQueryCache().build(this.#c,e),r=this.createResult(t,e);return function shouldAssignObserverCurrentProperties(e,t){if(!(0,d.shallowEqualObjects)(e.getCurrentResult(),t))return!0;return!1}(this,r)&&(this.#l=r,this.#K=this.options,this.#B=this.#W.state),r}getCurrentResult(){return this.#l}trackResult(e,t){const r={};return Object.keys(e).forEach((n=>{Object.defineProperty(r,n,{configurable:!1,enumerable:!0,get:()=>(this.trackProp(n),t?.(n),e[n])})})),r}trackProp(e){this.#$.add(e)}getCurrentQuery(){return this.#W}refetch({...e}={}){return this.fetch({...e})}fetchOptimistic(e){const t=this.#c.defaultQueryOptions(e),r=this.#c.getQueryCache().build(this.#c,t);return r.fetch().then((()=>this.createResult(r,t)))}fetch(e){return this.#X({...e,cancelRefetch:e.cancelRefetch??!0}).then((()=>(this.updateResult(),this.#l)))}#X(e){this.#ee();let t=this.#W.fetch(this.options,e);return e?.throwOnError||(t=t.catch(d.noop)),t}#te(){this.#Z();const e=(0,d.resolveStaleTime)(this.options.staleTime,this.#W);if(d.isServer||this.#l.isStale||!(0,d.isValidTimeout)(e))return;const t=(0,d.timeUntilStale)(this.#l.dataUpdatedAt,e)+1;this.#V=setTimeout((()=>{this.#l.isStale||this.updateResult()}),t)}#re(){return("function"==typeof this.options.refetchInterval?this.options.refetchInterval(this.#W):this.options.refetchInterval)??!1}#ne(e){this.#J(),this.#G=e,!d.isServer&&!1!==(0,d.resolveEnabled)(this.options.enabled,this.#W)&&(0,d.isValidTimeout)(this.#G)&&0!==this.#G&&(this.#z=setInterval((()=>{(this.options.refetchIntervalInBackground||c.focusManager.isFocused())&&this.#X()}),this.#G))}#Y(){this.#te(),this.#ne(this.#re())}#Z(){this.#V&&(clearTimeout(this.#V),this.#V=void 0)}#J(){this.#z&&(clearInterval(this.#z),this.#z=void 0)}createResult(e,t){const r=this.#W,n=this.options,s=this.#l,i=this.#B,o=this.#K,a=e!==r?e.state:this.#A,{state:u}=e;let c,l={...u},p=!1;if(t._optimisticResults){const s=this.hasListeners(),i=!s&&shouldFetchOnMount(e,t),o=s&&shouldFetchOptionally(e,r,t,n);(i||o)&&(l={...l,...(0,h.fetchState)(u.data,e.options)}),"isRestoring"===t._optimisticResults&&(l.fetchStatus="idle")}let{error:y,errorUpdatedAt:b,status:m}=l;if(t.select&&void 0!==l.data)if(s&&l.data===i?.data&&t.select===this.#U)c=this.#L;else try{this.#U=t.select,c=t.select(l.data),c=(0,d.replaceData)(s?.data,c,t),this.#L=c,this.#F=null}catch(e){this.#F=e}else c=l.data;if(void 0!==t.placeholderData&&void 0===c&&"pending"===m){let e;if(s?.isPlaceholderData&&t.placeholderData===o?.placeholderData)e=s.data;else if(e="function"==typeof t.placeholderData?t.placeholderData(this.#H?.state.data,this.#H):t.placeholderData,t.select&&void 0!==e)try{e=t.select(e),this.#F=null}catch(e){this.#F=e}void 0!==e&&(m="success",c=(0,d.replaceData)(s?.data,e,t),p=!0)}this.#F&&(y=this.#F,c=this.#L,b=Date.now(),m="error");const v="fetching"===l.fetchStatus,g="pending"===m,O="error"===m,P=g&&v,w=void 0!==c,j={status:m,fetchStatus:l.fetchStatus,isPending:g,isSuccess:"success"===m,isError:O,isInitialLoading:P,isLoading:P,data:c,dataUpdatedAt:l.dataUpdatedAt,error:y,errorUpdatedAt:b,failureCount:l.fetchFailureCount,failureReason:l.fetchFailureReason,errorUpdateCount:l.errorUpdateCount,isFetched:l.dataUpdateCount>0||l.errorUpdateCount>0,isFetchedAfterMount:l.dataUpdateCount>a.dataUpdateCount||l.errorUpdateCount>a.errorUpdateCount,isFetching:v,isRefetching:v&&!g,isLoadingError:O&&!w,isPaused:"paused"===l.fetchStatus,isPlaceholderData:p,isRefetchError:O&&w,isStale:isStale(e,t),refetch:this.refetch,promise:this.#N};if(this.options.experimental_prefetchInRender){const finalizeThenableIfPossible=e=>{"error"===j.status?e.reject(j.error):void 0!==j.data&&e.resolve(j.data)},recreateThenable=()=>{const e=this.#N=j.promise=(0,f.pendingThenable)();finalizeThenableIfPossible(e)},t=this.#N;switch(t.status){case"pending":e.queryHash===r.queryHash&&finalizeThenableIfPossible(t);break;case"fulfilled":"error"!==j.status&&j.data===t.value||recreateThenable();break;case"rejected":"error"===j.status&&j.error===t.reason||recreateThenable()}}return j}updateResult(e){const t=this.#l,r=this.createResult(this.#W,this.options);if(this.#B=this.#W.state,this.#K=this.options,void 0!==this.#B.data&&(this.#H=this.#W),(0,d.shallowEqualObjects)(r,t))return;this.#l=r;const n={};!1!==e?.listeners&&(()=>{if(!t)return!0;const{notifyOnChangeProps:e}=this.options,r="function"==typeof e?e():e;if("all"===r||!r&&!this.#$.size)return!0;const n=new Set(r??this.#$);return this.options.throwOnError&&n.add("error"),Object.keys(this.#l).some((e=>{const r=e;return this.#l[r]!==t[r]&&n.has(r)}))})()&&(n.listeners=!0),this.#d({...n,...e})}#ee(){const e=this.#c.getQueryCache().build(this.#c,this.options);if(e===this.#W)return;const t=this.#W;this.#W=e,this.#A=e.state,this.hasListeners()&&(t?.removeObserver(this),e.addObserver(this))}onQueryUpdate(){this.updateResult(),this.hasListeners()&&this.#Y()}#d(e){l.notifyManager.batch((()=>{e.listeners&&this.listeners.forEach((e=>{e(this.#l)})),this.#c.getQueryCache().notify({query:this.#W,type:"observerResultsUpdated"})}))}};function shouldFetchOnMount(e,t){return function shouldLoadOnMount(e,t){return!1!==(0,d.resolveEnabled)(t.enabled,e)&&void 0===e.state.data&&!("error"===e.state.status&&!1===t.retryOnMount)}(e,t)||void 0!==e.state.data&&shouldFetchOn(e,t,t.refetchOnMount)}function shouldFetchOn(e,t,r){if(!1!==(0,d.resolveEnabled)(t.enabled,e)){const n="function"==typeof r?r(e):r;return"always"===n||!1!==n&&isStale(e,t)}return!1}function shouldFetchOptionally(e,t,r,n){return(e!==t||!1===(0,d.resolveEnabled)(n.enabled,e))&&(!r.suspense||"error"!==e.state.status)&&isStale(e,r)}function isStale(e,t){return!1!==(0,d.resolveEnabled)(t.enabled,e)&&e.isStaleByTime((0,d.resolveStaleTime)(t.staleTime,e))}},95181:(e,t,r)=>{"use strict";var n,s=Object.defineProperty,i=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,a=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)s(e,r,{get:t[r],enumerable:!0})})(u,{Removable:()=>l}),e.exports=(n=u,((e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of o(t))a.call(e,u)||u===r||s(e,u,{get:()=>t[u],enumerable:!(n=i(t,u))||n.enumerable});return e})(s({},"__esModule",{value:!0}),n));var c=r(73025),l=class{#se;destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),(0,c.isValidTimeout)(this.gcTime)&&(this.#se=setTimeout((()=>{this.optionalRemove()}),this.gcTime))}updateGcTime(e){this.gcTime=Math.max(this.gcTime||0,e??(c.isServer?1/0:3e5))}clearGcTimeout(){this.#se&&(clearTimeout(this.#se),this.#se=void 0)}}},7785:(e,t,r)=>{"use strict";var n,s=Object.defineProperty,i=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,a=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)s(e,r,{get:t[r],enumerable:!0})})(u,{CancelledError:()=>f,canFetch:()=>canFetch,createRetryer:()=>createRetryer,isCancelledError:()=>isCancelledError}),e.exports=(n=u,((e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of o(t))a.call(e,u)||u===r||s(e,u,{get:()=>t[u],enumerable:!(n=i(t,u))||n.enumerable});return e})(s({},"__esModule",{value:!0}),n));var c=r(52803),l=r(97692),h=r(89675),p=r(73025);function defaultRetryDelay(e){return Math.min(1e3*2**e,3e4)}function canFetch(e){return"online"!==(e??"online")||l.onlineManager.isOnline()}var f=class extends Error{constructor(e){super("CancelledError"),this.revert=e?.revert,this.silent=e?.silent}};function isCancelledError(e){return e instanceof f}function createRetryer(e){let t,r=!1,n=0,s=!1;const i=(0,h.pendingThenable)(),canContinue=()=>c.focusManager.isFocused()&&("always"===e.networkMode||l.onlineManager.isOnline())&&e.canRun(),canStart=()=>canFetch(e.networkMode)&&e.canRun(),resolve=r=>{s||(s=!0,e.onSuccess?.(r),t?.(),i.resolve(r))},reject=r=>{s||(s=!0,e.onError?.(r),t?.(),i.reject(r))},pause=()=>new Promise((r=>{t=e=>{(s||canContinue())&&r(e)},e.onPause?.()})).then((()=>{t=void 0,s||e.onContinue?.()})),run=()=>{if(s)return;let t;const i=0===n?e.initialPromise:void 0;try{t=i??e.fn()}catch(e){t=Promise.reject(e)}Promise.resolve(t).then(resolve).catch((t=>{if(s)return;const i=e.retry??(p.isServer?0:3),o=e.retryDelay??defaultRetryDelay,a="function"==typeof o?o(n,t):o,u=!0===i||"number"==typeof i&&n<i||"function"==typeof i&&i(n,t);!r&&u?(n++,e.onFail?.(n,t),(0,p.sleep)(a).then((()=>canContinue()?void 0:pause())).then((()=>{r?reject(t):run()}))):reject(t)}))};return{promise:i,cancel:t=>{s||(reject(new f(t)),e.abort?.())},continue:()=>(t?.(),i),cancelRetry:()=>{r=!0},continueRetry:()=>{r=!1},canStart,start:()=>(canStart()?run():pause().then(run),i)}}},66133:e=>{"use strict";var t,r=Object.defineProperty,n=Object.getOwnPropertyDescriptor,s=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,o={};((e,t)=>{for(var n in t)r(e,n,{get:t[n],enumerable:!0})})(o,{Subscribable:()=>a}),e.exports=(t=o,((e,t,o,a)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of s(t))i.call(e,u)||u===o||r(e,u,{get:()=>t[u],enumerable:!(a=n(t,u))||a.enumerable});return e})(r({},"__esModule",{value:!0}),t));var a=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}}},89675:e=>{"use strict";var t,r=Object.defineProperty,n=Object.getOwnPropertyDescriptor,s=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,o={};function pendingThenable(){let e,t;const r=new Promise(((r,n)=>{e=r,t=n}));function finalize(e){Object.assign(r,e),delete r.resolve,delete r.reject}return r.status="pending",r.catch((()=>{})),r.resolve=t=>{finalize({status:"fulfilled",value:t}),e(t)},r.reject=e=>{finalize({status:"rejected",reason:e}),t(e)},r}((e,t)=>{for(var n in t)r(e,n,{get:t[n],enumerable:!0})})(o,{pendingThenable:()=>pendingThenable}),e.exports=(t=o,((e,t,o,a)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of s(t))i.call(e,u)||u===o||r(e,u,{get:()=>t[u],enumerable:!(a=n(t,u))||a.enumerable});return e})(r({},"__esModule",{value:!0}),t))},98813:e=>{"use strict";var t,r=Object.defineProperty,n=Object.getOwnPropertyDescriptor,s=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty;e.exports=(t={},((e,t,o,a)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of s(t))i.call(e,u)||u===o||r(e,u,{get:()=>t[u],enumerable:!(a=n(t,u))||a.enumerable});return e})(r({},"__esModule",{value:!0}),t))},73025:e=>{"use strict";var t,r=Object.defineProperty,n=Object.getOwnPropertyDescriptor,s=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,o={};((e,t)=>{for(var n in t)r(e,n,{get:t[n],enumerable:!0})})(o,{addToEnd:()=>addToEnd,addToStart:()=>addToStart,ensureQueryFn:()=>ensureQueryFn,functionalUpdate:()=>functionalUpdate,hashKey:()=>hashKey,hashQueryKeyByOptions:()=>hashQueryKeyByOptions,isPlainArray:()=>isPlainArray,isPlainObject:()=>isPlainObject,isServer:()=>a,isValidTimeout:()=>isValidTimeout,keepPreviousData:()=>keepPreviousData,matchMutation:()=>matchMutation,matchQuery:()=>matchQuery,noop:()=>noop,partialMatchKey:()=>partialMatchKey,replaceData:()=>replaceData,replaceEqualDeep:()=>replaceEqualDeep,resolveEnabled:()=>resolveEnabled,resolveStaleTime:()=>resolveStaleTime,shallowEqualObjects:()=>shallowEqualObjects,skipToken:()=>u,sleep:()=>sleep,timeUntilStale:()=>timeUntilStale}),e.exports=(t=o,((e,t,o,a)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of s(t))i.call(e,u)||u===o||r(e,u,{get:()=>t[u],enumerable:!(a=n(t,u))||a.enumerable});return e})(r({},"__esModule",{value:!0}),t));var a="undefined"==typeof window||"Deno"in globalThis;function noop(){}function functionalUpdate(e,t){return"function"==typeof e?e(t):e}function isValidTimeout(e){return"number"==typeof e&&e>=0&&e!==1/0}function timeUntilStale(e,t){return Math.max(e+(t||0)-Date.now(),0)}function resolveStaleTime(e,t){return"function"==typeof e?e(t):e}function resolveEnabled(e,t){return"function"==typeof e?e(t):e}function matchQuery(e,t){const{type:r="all",exact:n,fetchStatus:s,predicate:i,queryKey:o,stale:a}=e;if(o)if(n){if(t.queryHash!==hashQueryKeyByOptions(o,t.options))return!1}else if(!partialMatchKey(t.queryKey,o))return!1;if("all"!==r){const e=t.isActive();if("active"===r&&!e)return!1;if("inactive"===r&&e)return!1}return("boolean"!=typeof a||t.isStale()===a)&&((!s||s===t.state.fetchStatus)&&!(i&&!i(t)))}function matchMutation(e,t){const{exact:r,status:n,predicate:s,mutationKey:i}=e;if(i){if(!t.options.mutationKey)return!1;if(r){if(hashKey(t.options.mutationKey)!==hashKey(i))return!1}else if(!partialMatchKey(t.options.mutationKey,i))return!1}return(!n||t.state.status===n)&&!(s&&!s(t))}function hashQueryKeyByOptions(e,t){return(t?.queryKeyHashFn||hashKey)(e)}function hashKey(e){return JSON.stringify(e,((e,t)=>isPlainObject(t)?Object.keys(t).sort().reduce(((e,r)=>(e[r]=t[r],e)),{}):t))}function partialMatchKey(e,t){return e===t||typeof e==typeof t&&(!(!e||!t||"object"!=typeof e||"object"!=typeof t)&&!Object.keys(t).some((r=>!partialMatchKey(e[r],t[r]))))}function replaceEqualDeep(e,t){if(e===t)return e;const r=isPlainArray(e)&&isPlainArray(t);if(r||isPlainObject(e)&&isPlainObject(t)){const n=r?e:Object.keys(e),s=n.length,i=r?t:Object.keys(t),o=i.length,a=r?[]:{};let u=0;for(let s=0;s<o;s++){const o=r?s:i[s];(!r&&n.includes(o)||r)&&void 0===e[o]&&void 0===t[o]?(a[o]=void 0,u++):(a[o]=replaceEqualDeep(e[o],t[o]),a[o]===e[o]&&void 0!==e[o]&&u++)}return s===o&&u===s?e:a}return t}function shallowEqualObjects(e,t){if(!t||Object.keys(e).length!==Object.keys(t).length)return!1;for(const r in e)if(e[r]!==t[r])return!1;return!0}function isPlainArray(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function isPlainObject(e){if(!hasObjectPrototype(e))return!1;const t=e.constructor;if(void 0===t)return!0;const r=t.prototype;return!!hasObjectPrototype(r)&&(!!r.hasOwnProperty("isPrototypeOf")&&Object.getPrototypeOf(e)===Object.prototype)}function hasObjectPrototype(e){return"[object Object]"===Object.prototype.toString.call(e)}function sleep(e){return new Promise((t=>{setTimeout(t,e)}))}function replaceData(e,t,r){return"function"==typeof r.structuralSharing?r.structuralSharing(e,t):!1!==r.structuralSharing?replaceEqualDeep(e,t):t}function keepPreviousData(e){return e}function addToEnd(e,t,r=0){const n=[...e,t];return r&&n.length>r?n.slice(1):n}function addToStart(e,t,r=0){const n=[t,...e];return r&&n.length>r?n.slice(0,-1):n}var u=Symbol();function ensureQueryFn(e,t){return!e.queryFn&&t?.initialPromise?()=>t.initialPromise:e.queryFn&&e.queryFn!==u?e.queryFn:()=>Promise.reject(new Error(`Missing queryFn: '${e.queryHash}'`))}},84776:(e,t,r)=>{"use strict";var n,s=Object.create,i=Object.defineProperty,o=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,u=Object.getPrototypeOf,c=Object.prototype.hasOwnProperty,__copyProps=(e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let s of a(t))c.call(e,s)||s===r||i(e,s,{get:()=>t[s],enumerable:!(n=o(t,s))||n.enumerable});return e},l={};((e,t)=>{for(var r in t)i(e,r,{get:t[r],enumerable:!0})})(l,{HydrationBoundary:()=>HydrationBoundary}),e.exports=(n=l,__copyProps(i({},"__esModule",{value:!0}),n));var h=((e,t,r)=>(r=null!=e?s(u(e)):{},__copyProps(!t&&e&&e.__esModule?r:i(r,"default",{value:e,enumerable:!0}),e)))(r(41594),1),p=r(51934),f=r(59762),HydrationBoundary=({children:e,options:t={},state:r,queryClient:n})=>{const s=(0,f.useQueryClient)(n),[i,o]=h.useState(),a=h.useRef(t);return a.current=t,h.useMemo((()=>{if(r){if("object"!=typeof r)return;const e=s.getQueryCache(),t=r.queries||[],n=[],u=[];for(const r of t){const t=e.get(r.queryHash);if(t){const e=r.state.dataUpdatedAt>t.state.dataUpdatedAt,n=i?.find((e=>e.queryHash===r.queryHash));e&&(!n||r.state.dataUpdatedAt>n.state.dataUpdatedAt)&&u.push(r)}else n.push(r)}n.length>0&&(0,p.hydrate)(s,{queries:n},a.current),u.length>0&&o((e=>e?[...e,...u]:u))}}),[s,i,r]),h.useEffect((()=>{i&&((0,p.hydrate)(s,{queries:i},a.current),o(void 0))}),[s,i]),e}},59762:(e,t,r)=>{"use strict";var n,s=Object.create,i=Object.defineProperty,o=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,u=Object.getPrototypeOf,c=Object.prototype.hasOwnProperty,__copyProps=(e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let s of a(t))c.call(e,s)||s===r||i(e,s,{get:()=>t[s],enumerable:!(n=o(t,s))||n.enumerable});return e},l={};((e,t)=>{for(var r in t)i(e,r,{get:t[r],enumerable:!0})})(l,{QueryClientContext:()=>f,QueryClientProvider:()=>QueryClientProvider,useQueryClient:()=>useQueryClient}),e.exports=(n=l,__copyProps(i({},"__esModule",{value:!0}),n));var h=((e,t,r)=>(r=null!=e?s(u(e)):{},__copyProps(!t&&e&&e.__esModule?r:i(r,"default",{value:e,enumerable:!0}),e)))(r(41594),1),p=r(62540),f=h.createContext(void 0),useQueryClient=e=>{const t=h.useContext(f);if(e)return e;if(!t)throw new Error("No QueryClient set, use QueryClientProvider to set one");return t},QueryClientProvider=({client:e,children:t})=>(h.useEffect((()=>(e.mount(),()=>{e.unmount()})),[e]),(0,p.jsx)(f.Provider,{value:e,children:t}))},16729:(e,t,r)=>{"use strict";var n,s=Object.create,i=Object.defineProperty,o=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,u=Object.getPrototypeOf,c=Object.prototype.hasOwnProperty,__copyProps=(e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let s of a(t))c.call(e,s)||s===r||i(e,s,{get:()=>t[s],enumerable:!(n=o(t,s))||n.enumerable});return e},l={};((e,t)=>{for(var r in t)i(e,r,{get:t[r],enumerable:!0})})(l,{QueryErrorResetBoundary:()=>QueryErrorResetBoundary,useQueryErrorResetBoundary:()=>useQueryErrorResetBoundary}),e.exports=(n=l,__copyProps(i({},"__esModule",{value:!0}),n));var h=((e,t,r)=>(r=null!=e?s(u(e)):{},__copyProps(!t&&e&&e.__esModule?r:i(r,"default",{value:e,enumerable:!0}),e)))(r(41594),1),p=r(62540);function createValue(){let e=!1;return{clearReset:()=>{e=!1},reset:()=>{e=!0},isReset:()=>e}}var f=h.createContext(createValue()),useQueryErrorResetBoundary=()=>h.useContext(f),QueryErrorResetBoundary=({children:e})=>{const[t]=h.useState((()=>createValue()));return(0,p.jsx)(f.Provider,{value:t,children:"function"==typeof e?e(t):e})}},74151:(e,t,r)=>{"use strict";var n,s=Object.create,i=Object.defineProperty,o=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,u=Object.getPrototypeOf,c=Object.prototype.hasOwnProperty,__copyProps=(e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let s of a(t))c.call(e,s)||s===r||i(e,s,{get:()=>t[s],enumerable:!(n=o(t,s))||n.enumerable});return e},l={};((e,t)=>{for(var r in t)i(e,r,{get:t[r],enumerable:!0})})(l,{ensurePreventErrorBoundaryRetry:()=>ensurePreventErrorBoundaryRetry,getHasError:()=>getHasError,useClearResetErrorBoundary:()=>useClearResetErrorBoundary}),e.exports=(n=l,__copyProps(i({},"__esModule",{value:!0}),n));var h=((e,t,r)=>(r=null!=e?s(u(e)):{},__copyProps(!t&&e&&e.__esModule?r:i(r,"default",{value:e,enumerable:!0}),e)))(r(41594),1),p=r(63315),ensurePreventErrorBoundaryRetry=(e,t)=>{(e.suspense||e.throwOnError||e.experimental_prefetchInRender)&&(t.isReset()||(e.retryOnMount=!1))},useClearResetErrorBoundary=e=>{h.useEffect((()=>{e.clearReset()}),[e])},getHasError=({result:e,errorResetBoundary:t,throwOnError:r,query:n})=>e.isError&&!t.isReset()&&!e.isFetching&&n&&(0,p.shouldThrowError)(r,[e.error,n])},51688:(e,t,r)=>{"use strict";var n,s=Object.defineProperty,i=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,a=Object.prototype.hasOwnProperty,__copyProps=(e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of o(t))a.call(e,u)||u===r||s(e,u,{get:()=>t[u],enumerable:!(n=i(t,u))||n.enumerable});return e},__reExport=(e,t,r)=>(__copyProps(e,t,"default"),r&&__copyProps(r,t,"default")),u={};((e,t)=>{for(var r in t)s(e,r,{get:t[r],enumerable:!0})})(u,{HydrationBoundary:()=>g.HydrationBoundary,IsRestoringProvider:()=>M.IsRestoringProvider,QueryClientContext:()=>v.QueryClientContext,QueryClientProvider:()=>v.QueryClientProvider,QueryErrorResetBoundary:()=>O.QueryErrorResetBoundary,infiniteQueryOptions:()=>m.infiniteQueryOptions,queryOptions:()=>b.queryOptions,useInfiniteQuery:()=>_.useInfiniteQuery,useIsFetching:()=>P.useIsFetching,useIsMutating:()=>w.useIsMutating,useIsRestoring:()=>M.useIsRestoring,useMutation:()=>j.useMutation,useMutationState:()=>w.useMutationState,usePrefetchInfiniteQuery:()=>y.usePrefetchInfiniteQuery,usePrefetchQuery:()=>d.usePrefetchQuery,useQueries:()=>c.useQueries,useQuery:()=>l.useQuery,useQueryClient:()=>v.useQueryClient,useQueryErrorResetBoundary:()=>O.useQueryErrorResetBoundary,useSuspenseInfiniteQuery:()=>p.useSuspenseInfiniteQuery,useSuspenseQueries:()=>f.useSuspenseQueries,useSuspenseQuery:()=>h.useSuspenseQuery}),e.exports=(n=u,__copyProps(s({},"__esModule",{value:!0}),n)),__reExport(u,r(51934),e.exports),__reExport(u,r(62143),e.exports);var c=r(37963),l=r(16743),h=r(37063),p=r(28619),f=r(44459),d=r(94860),y=r(48380),b=r(5938),m=r(28218),v=r(59762),g=r(84776),O=r(16729),P=r(81809),w=r(26803),j=r(3188),_=r(81355),M=r(21683)},28218:e=>{"use strict";var t,r=Object.defineProperty,n=Object.getOwnPropertyDescriptor,s=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,o={};function infiniteQueryOptions(e){return e}((e,t)=>{for(var n in t)r(e,n,{get:t[n],enumerable:!0})})(o,{infiniteQueryOptions:()=>infiniteQueryOptions}),e.exports=(t=o,((e,t,o,a)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of s(t))i.call(e,u)||u===o||r(e,u,{get:()=>t[u],enumerable:!(a=n(t,u))||a.enumerable});return e})(r({},"__esModule",{value:!0}),t))},21683:(e,t,r)=>{"use strict";var n,s=Object.create,i=Object.defineProperty,o=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,u=Object.getPrototypeOf,c=Object.prototype.hasOwnProperty,__copyProps=(e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let s of a(t))c.call(e,s)||s===r||i(e,s,{get:()=>t[s],enumerable:!(n=o(t,s))||n.enumerable});return e},l={};((e,t)=>{for(var r in t)i(e,r,{get:t[r],enumerable:!0})})(l,{IsRestoringProvider:()=>f,useIsRestoring:()=>useIsRestoring}),e.exports=(n=l,__copyProps(i({},"__esModule",{value:!0}),n));var h=((e,t,r)=>(r=null!=e?s(u(e)):{},__copyProps(!t&&e&&e.__esModule?r:i(r,"default",{value:e,enumerable:!0}),e)))(r(41594),1),p=h.createContext(!1),useIsRestoring=()=>h.useContext(p),f=p.Provider},5938:e=>{"use strict";var t,r=Object.defineProperty,n=Object.getOwnPropertyDescriptor,s=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,o={};function queryOptions(e){return e}((e,t)=>{for(var n in t)r(e,n,{get:t[n],enumerable:!0})})(o,{queryOptions:()=>queryOptions}),e.exports=(t=o,((e,t,o,a)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of s(t))i.call(e,u)||u===o||r(e,u,{get:()=>t[u],enumerable:!(a=n(t,u))||a.enumerable});return e})(r({},"__esModule",{value:!0}),t))},92816:e=>{"use strict";var t,r=Object.defineProperty,n=Object.getOwnPropertyDescriptor,s=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,o={};((e,t)=>{for(var n in t)r(e,n,{get:t[n],enumerable:!0})})(o,{defaultThrowOnError:()=>defaultThrowOnError,ensureSuspenseTimers:()=>ensureSuspenseTimers,fetchOptimistic:()=>fetchOptimistic,shouldSuspend:()=>shouldSuspend,willFetch:()=>willFetch}),e.exports=(t=o,((e,t,o,a)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of s(t))i.call(e,u)||u===o||r(e,u,{get:()=>t[u],enumerable:!(a=n(t,u))||a.enumerable});return e})(r({},"__esModule",{value:!0}),t));var defaultThrowOnError=(e,t)=>void 0===t.state.data,ensureSuspenseTimers=e=>{e.suspense&&(void 0===e.staleTime&&(e.staleTime=1e3),"number"==typeof e.gcTime&&(e.gcTime=Math.max(e.gcTime,1e3)))},willFetch=(e,t)=>e.isLoading&&e.isFetching&&!t,shouldSuspend=(e,t)=>e?.suspense&&t.isPending,fetchOptimistic=(e,t,r)=>t.fetchOptimistic(e).catch((()=>{r.clearReset()}))},62143:e=>{"use strict";var t,r=Object.defineProperty,n=Object.getOwnPropertyDescriptor,s=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty;e.exports=(t={},((e,t,o,a)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of s(t))i.call(e,u)||u===o||r(e,u,{get:()=>t[u],enumerable:!(a=n(t,u))||a.enumerable});return e})(r({},"__esModule",{value:!0}),t))},35442:(e,t,r)=>{"use strict";var n,s=Object.create,i=Object.defineProperty,o=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,u=Object.getPrototypeOf,c=Object.prototype.hasOwnProperty,__copyProps=(e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let s of a(t))c.call(e,s)||s===r||i(e,s,{get:()=>t[s],enumerable:!(n=o(t,s))||n.enumerable});return e},l={};((e,t)=>{for(var r in t)i(e,r,{get:t[r],enumerable:!0})})(l,{useBaseQuery:()=>useBaseQuery}),e.exports=(n=l,__copyProps(i({},"__esModule",{value:!0}),n));var h=((e,t,r)=>(r=null!=e?s(u(e)):{},__copyProps(!t&&e&&e.__esModule?r:i(r,"default",{value:e,enumerable:!0}),e)))(r(41594),1),p=r(51934),f=r(59762),d=r(16729),y=r(74151),b=r(21683),m=r(92816),v=r(63315);function useBaseQuery(e,t,r){const n=(0,f.useQueryClient)(r),s=(0,b.useIsRestoring)(),i=(0,d.useQueryErrorResetBoundary)(),o=n.defaultQueryOptions(e);n.getDefaultOptions().queries?._experimental_beforeQuery?.(o),o._optimisticResults=s?"isRestoring":"optimistic",(0,m.ensureSuspenseTimers)(o),(0,y.ensurePreventErrorBoundaryRetry)(o,i),(0,y.useClearResetErrorBoundary)(i);const a=!n.getQueryCache().get(o.queryHash),[u]=h.useState((()=>new t(n,o))),c=u.getOptimisticResult(o);if(h.useSyncExternalStore(h.useCallback((e=>{const t=s?v.noop:u.subscribe(p.notifyManager.batchCalls(e));return u.updateResult(),t}),[u,s]),(()=>u.getCurrentResult()),(()=>u.getCurrentResult())),h.useEffect((()=>{u.setOptions(o,{listeners:!1})}),[o,u]),(0,m.shouldSuspend)(o,c))throw(0,m.fetchOptimistic)(o,u,i);if((0,y.getHasError)({result:c,errorResetBoundary:i,throwOnError:o.throwOnError,query:n.getQueryCache().get(o.queryHash)}))throw c.error;if(n.getDefaultOptions().queries?._experimental_afterQuery?.(o,c),o.experimental_prefetchInRender&&!p.isServer&&(0,m.willFetch)(c,s)){const e=a?(0,m.fetchOptimistic)(o,u,i):n.getQueryCache().get(o.queryHash)?.promise;e?.catch(v.noop).finally((()=>{u.updateResult()}))}return o.notifyOnChangeProps?c:u.trackResult(c)}},81355:(e,t,r)=>{"use strict";var n,s=Object.defineProperty,i=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,a=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)s(e,r,{get:t[r],enumerable:!0})})(u,{useInfiniteQuery:()=>useInfiniteQuery}),e.exports=(n=u,((e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of o(t))a.call(e,u)||u===r||s(e,u,{get:()=>t[u],enumerable:!(n=i(t,u))||n.enumerable});return e})(s({},"__esModule",{value:!0}),n));var c=r(51934),l=r(35442);function useInfiniteQuery(e,t){return(0,l.useBaseQuery)(e,c.InfiniteQueryObserver,t)}},81809:(e,t,r)=>{"use strict";var n,s=Object.create,i=Object.defineProperty,o=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,u=Object.getPrototypeOf,c=Object.prototype.hasOwnProperty,__copyProps=(e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let s of a(t))c.call(e,s)||s===r||i(e,s,{get:()=>t[s],enumerable:!(n=o(t,s))||n.enumerable});return e},l={};((e,t)=>{for(var r in t)i(e,r,{get:t[r],enumerable:!0})})(l,{useIsFetching:()=>useIsFetching}),e.exports=(n=l,__copyProps(i({},"__esModule",{value:!0}),n));var h=((e,t,r)=>(r=null!=e?s(u(e)):{},__copyProps(!t&&e&&e.__esModule?r:i(r,"default",{value:e,enumerable:!0}),e)))(r(41594),1),p=r(51934),f=r(59762);function useIsFetching(e,t){const r=(0,f.useQueryClient)(t),n=r.getQueryCache();return h.useSyncExternalStore(h.useCallback((e=>n.subscribe(p.notifyManager.batchCalls(e))),[n]),(()=>r.isFetching(e)),(()=>r.isFetching(e)))}},3188:(e,t,r)=>{"use strict";var n,s=Object.create,i=Object.defineProperty,o=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,u=Object.getPrototypeOf,c=Object.prototype.hasOwnProperty,__copyProps=(e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let s of a(t))c.call(e,s)||s===r||i(e,s,{get:()=>t[s],enumerable:!(n=o(t,s))||n.enumerable});return e},l={};((e,t)=>{for(var r in t)i(e,r,{get:t[r],enumerable:!0})})(l,{useMutation:()=>useMutation}),e.exports=(n=l,__copyProps(i({},"__esModule",{value:!0}),n));var h=((e,t,r)=>(r=null!=e?s(u(e)):{},__copyProps(!t&&e&&e.__esModule?r:i(r,"default",{value:e,enumerable:!0}),e)))(r(41594),1),p=r(51934),f=r(59762),d=r(63315);function useMutation(e,t){const r=(0,f.useQueryClient)(t),[n]=h.useState((()=>new p.MutationObserver(r,e)));h.useEffect((()=>{n.setOptions(e)}),[n,e]);const s=h.useSyncExternalStore(h.useCallback((e=>n.subscribe(p.notifyManager.batchCalls(e))),[n]),(()=>n.getCurrentResult()),(()=>n.getCurrentResult())),i=h.useCallback(((e,t)=>{n.mutate(e,t).catch(d.noop)}),[n]);if(s.error&&(0,d.shouldThrowError)(n.options.throwOnError,[s.error]))throw s.error;return{...s,mutate:i,mutateAsync:s.mutate}}},26803:(e,t,r)=>{"use strict";var n,s=Object.create,i=Object.defineProperty,o=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,u=Object.getPrototypeOf,c=Object.prototype.hasOwnProperty,__copyProps=(e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let s of a(t))c.call(e,s)||s===r||i(e,s,{get:()=>t[s],enumerable:!(n=o(t,s))||n.enumerable});return e},l={};((e,t)=>{for(var r in t)i(e,r,{get:t[r],enumerable:!0})})(l,{useIsMutating:()=>useIsMutating,useMutationState:()=>useMutationState}),e.exports=(n=l,__copyProps(i({},"__esModule",{value:!0}),n));var h=((e,t,r)=>(r=null!=e?s(u(e)):{},__copyProps(!t&&e&&e.__esModule?r:i(r,"default",{value:e,enumerable:!0}),e)))(r(41594),1),p=r(51934),f=r(59762);function useIsMutating(e,t){return useMutationState({filters:{...e,status:"pending"}},(0,f.useQueryClient)(t)).length}function getResult(e,t){return e.findAll(t.filters).map((e=>t.select?t.select(e):e.state))}function useMutationState(e={},t){const r=(0,f.useQueryClient)(t).getMutationCache(),n=h.useRef(e),s=h.useRef(null);return s.current||(s.current=getResult(r,e)),h.useEffect((()=>{n.current=e})),h.useSyncExternalStore(h.useCallback((e=>r.subscribe((()=>{const t=(0,p.replaceEqualDeep)(s.current,getResult(r,n.current));s.current!==t&&(s.current=t,p.notifyManager.schedule(e))}))),[r]),(()=>s.current),(()=>s.current))}},48380:(e,t,r)=>{"use strict";var n,s=Object.defineProperty,i=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,a=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)s(e,r,{get:t[r],enumerable:!0})})(u,{usePrefetchInfiniteQuery:()=>usePrefetchInfiniteQuery}),e.exports=(n=u,((e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of o(t))a.call(e,u)||u===r||s(e,u,{get:()=>t[u],enumerable:!(n=i(t,u))||n.enumerable});return e})(s({},"__esModule",{value:!0}),n));var c=r(59762);function usePrefetchInfiniteQuery(e,t){const r=(0,c.useQueryClient)(t);r.getQueryState(e.queryKey)||r.prefetchInfiniteQuery(e)}},94860:(e,t,r)=>{"use strict";var n,s=Object.defineProperty,i=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,a=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)s(e,r,{get:t[r],enumerable:!0})})(u,{usePrefetchQuery:()=>usePrefetchQuery}),e.exports=(n=u,((e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of o(t))a.call(e,u)||u===r||s(e,u,{get:()=>t[u],enumerable:!(n=i(t,u))||n.enumerable});return e})(s({},"__esModule",{value:!0}),n));var c=r(59762);function usePrefetchQuery(e,t){const r=(0,c.useQueryClient)(t);r.getQueryState(e.queryKey)||r.prefetchQuery(e)}},37963:(e,t,r)=>{"use strict";var n,s=Object.create,i=Object.defineProperty,o=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,u=Object.getPrototypeOf,c=Object.prototype.hasOwnProperty,__copyProps=(e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let s of a(t))c.call(e,s)||s===r||i(e,s,{get:()=>t[s],enumerable:!(n=o(t,s))||n.enumerable});return e},l={};((e,t)=>{for(var r in t)i(e,r,{get:t[r],enumerable:!0})})(l,{useQueries:()=>useQueries}),e.exports=(n=l,__copyProps(i({},"__esModule",{value:!0}),n));var h=((e,t,r)=>(r=null!=e?s(u(e)):{},__copyProps(!t&&e&&e.__esModule?r:i(r,"default",{value:e,enumerable:!0}),e)))(r(41594),1),p=r(51934),f=r(59762),d=r(21683),y=r(16729),b=r(74151),m=r(92816),v=r(63315);function useQueries({queries:e,...t},r){const n=(0,f.useQueryClient)(r),s=(0,d.useIsRestoring)(),i=(0,y.useQueryErrorResetBoundary)(),o=h.useMemo((()=>e.map((e=>{const t=n.defaultQueryOptions(e);return t._optimisticResults=s?"isRestoring":"optimistic",t}))),[e,n,s]);o.forEach((e=>{(0,m.ensureSuspenseTimers)(e),(0,b.ensurePreventErrorBoundaryRetry)(e,i)})),(0,b.useClearResetErrorBoundary)(i);const[a]=h.useState((()=>new p.QueriesObserver(n,o,t))),[u,c,l]=a.getOptimisticResult(o,t.combine);h.useSyncExternalStore(h.useCallback((e=>s?v.noop:a.subscribe(p.notifyManager.batchCalls(e))),[a,s]),(()=>a.getCurrentResult()),(()=>a.getCurrentResult())),h.useEffect((()=>{a.setQueries(o,t,{listeners:!1})}),[o,t,a]);const g=u.some(((e,t)=>(0,m.shouldSuspend)(o[t],e)))?u.flatMap(((e,t)=>{const r=o[t];if(r){const t=new p.QueryObserver(n,r);if((0,m.shouldSuspend)(r,e))return(0,m.fetchOptimistic)(r,t,i);(0,m.willFetch)(e,s)&&(0,m.fetchOptimistic)(r,t,i)}return[]})):[];if(g.length>0)throw Promise.all(g);const O=u.find(((e,t)=>{const r=o[t];return r&&(0,b.getHasError)({result:e,errorResetBoundary:i,throwOnError:r.throwOnError,query:n.getQueryCache().get(r.queryHash)})}));if(O?.error)throw O.error;return c(l())}},16743:(e,t,r)=>{"use strict";var n,s=Object.defineProperty,i=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,a=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)s(e,r,{get:t[r],enumerable:!0})})(u,{useQuery:()=>useQuery}),e.exports=(n=u,((e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of o(t))a.call(e,u)||u===r||s(e,u,{get:()=>t[u],enumerable:!(n=i(t,u))||n.enumerable});return e})(s({},"__esModule",{value:!0}),n));var c=r(51934),l=r(35442);function useQuery(e,t){return(0,l.useBaseQuery)(e,c.QueryObserver,t)}},28619:(e,t,r)=>{"use strict";var n,s=Object.defineProperty,i=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,a=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)s(e,r,{get:t[r],enumerable:!0})})(u,{useSuspenseInfiniteQuery:()=>useSuspenseInfiniteQuery}),e.exports=(n=u,((e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of o(t))a.call(e,u)||u===r||s(e,u,{get:()=>t[u],enumerable:!(n=i(t,u))||n.enumerable});return e})(s({},"__esModule",{value:!0}),n));var c=r(51934),l=r(35442),h=r(92816);function useSuspenseInfiniteQuery(e,t){return(0,l.useBaseQuery)({...e,enabled:!0,suspense:!0,throwOnError:h.defaultThrowOnError},c.InfiniteQueryObserver,t)}},44459:(e,t,r)=>{"use strict";var n,s=Object.defineProperty,i=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,a=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)s(e,r,{get:t[r],enumerable:!0})})(u,{useSuspenseQueries:()=>useSuspenseQueries}),e.exports=(n=u,((e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of o(t))a.call(e,u)||u===r||s(e,u,{get:()=>t[u],enumerable:!(n=i(t,u))||n.enumerable});return e})(s({},"__esModule",{value:!0}),n));r(51934);var c=r(37963),l=r(92816);function useSuspenseQueries(e,t){return(0,c.useQueries)({...e,queries:e.queries.map((e=>({...e,suspense:!0,throwOnError:l.defaultThrowOnError,enabled:!0,placeholderData:void 0})))},t)}},37063:(e,t,r)=>{"use strict";var n,s=Object.defineProperty,i=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,a=Object.prototype.hasOwnProperty,u={};((e,t)=>{for(var r in t)s(e,r,{get:t[r],enumerable:!0})})(u,{useSuspenseQuery:()=>useSuspenseQuery}),e.exports=(n=u,((e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of o(t))a.call(e,u)||u===r||s(e,u,{get:()=>t[u],enumerable:!(n=i(t,u))||n.enumerable});return e})(s({},"__esModule",{value:!0}),n));var c=r(51934),l=r(35442),h=r(92816);function useSuspenseQuery(e,t){return(0,l.useBaseQuery)({...e,enabled:!0,suspense:!0,throwOnError:h.defaultThrowOnError,placeholderData:void 0},c.QueryObserver,t)}},63315:e=>{"use strict";var t,r=Object.defineProperty,n=Object.getOwnPropertyDescriptor,s=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,o={};function shouldThrowError(e,t){return"function"==typeof e?e(...t):!!e}function noop(){}((e,t)=>{for(var n in t)r(e,n,{get:t[n],enumerable:!0})})(o,{noop:()=>noop,shouldThrowError:()=>shouldThrowError}),e.exports=(t=o,((e,t,o,a)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let u of s(t))i.call(e,u)||u===o||r(e,u,{get:()=>t[u],enumerable:!(a=n(t,u))||a.enumerable});return e})(r({},"__esModule",{value:!0}),t))}},t={};function __webpack_require__(r){var n=t[r];if(void 0!==n)return n.exports;var s=t[r]={exports:{}};return e[r](s,s.exports,__webpack_require__),s.exports}(()=>{"use strict";var e,t=__webpack_require__(55549),r=__webpack_require__(27348);null!==(e=window)&&void 0!==e&&null!==(e=e.elementorV2)&&void 0!==e&&e.editorAppBar?(0,r.editorV2)():(0,t.editorV1)()})()})();