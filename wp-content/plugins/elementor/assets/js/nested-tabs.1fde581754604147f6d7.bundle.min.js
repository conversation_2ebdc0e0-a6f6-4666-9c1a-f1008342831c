/*! elementor - v3.29.0 - 04-06-2025 */
"use strict";(self.webpackChunkelementorFrontend=self.webpackChunkelementorFrontend||[]).push([[215],{7557:(t,e)=>{function getChildrenWidth(t){let e=0;const i=t[0].parentNode,n=getComputedStyle(i),s=parseFloat(n.gap)||0;for(let i=0;i<t.length;i++)e+=t[i].offsetWidth+s;return e}Object.defineProperty(e,"__esModule",{value:!0}),e.changeScrollStatus=function changeScrollStatus(t,e){"mousedown"===e.type?(t.classList.add("e-scroll"),t.dataset.pageX=e.pageX):(t.classList.remove("e-scroll","e-scroll-active"),t.dataset.pageX="")},e.setHorizontalScrollAlignment=function setHorizontalScrollAlignment(t){let{element:e,direction:i,justifyCSSVariable:n,horizontalScrollStatus:s}=t;if(!e)return;!function isHorizontalScroll(t,e){return t.clientWidth<getChildrenWidth(t.children)&&"enable"===e}(e,s)?e.style.setProperty(n,""):function initialScrollPosition(t,e,i){const n=elementorFrontend.config.is_rtl;if("end"===e)t.style.setProperty(i,"start"),t.scrollLeft=n?-1*getChildrenWidth(t.children):getChildrenWidth(t.children);else t.style.setProperty(i,"start"),t.scrollLeft=0}(e,i,n)},e.setHorizontalTitleScrollValues=function setHorizontalTitleScrollValues(t,e,i){const n=t.classList.contains("e-scroll"),s="enable"===e,a=t.scrollWidth>t.clientWidth;if(!n||!s||!a)return;i.preventDefault();const o=parseFloat(t.dataset.pageX),r=i.pageX-o;let l=0;l=20<r?5:-20>r?-5:r;t.scrollLeft=t.scrollLeft-l,t.classList.add("e-scroll-active")}},4328:(t,e,i)=>{var n=i(6784);Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i(4846),i(7458),i(6211);var s=n(i(7224)),a=i(7557);class NestedTabs extends s.default{getTabTitleFilterSelector(t){return`[${this.getSettings("dataAttributes").tabIndex}="${t}"]`}getTabContentFilterSelector(t){return`*:nth-child(${t})`}getTabIndex(t){return t.getAttribute(this.getSettings("dataAttributes").tabIndex)}getActiveTabIndex(){const t=this.getSettings(),e=t.ariaAttributes.activeTitleSelector,i=t.dataAttributes.tabIndex;return this.elements.$tabTitles.filter(e).attr(i)||null}getWidgetNumber(){return this.$element.find("> .elementor-widget-container > .e-n-tabs, > .e-n-tabs").attr("data-widget-number")}getDefaultSettings(){const t=this.getWidgetNumber();return{selectors:{widgetContainer:`[data-widget-number="${t}"]`,tabTitle:`[aria-controls*="e-n-tab-content-${t}"]`,tabTitleIcon:`[id*="e-n-tab-title-${t}"] > .e-n-tab-icon`,tabTitleText:`[id*="e-n-tab-title-${t}"] > .e-n-tab-title-text`,tabContent:`[data-widget-number="${t}"] > .e-n-tabs-content > .e-con`,headingContainer:`[data-widget-number="${t}"] > .e-n-tabs-heading`,activeTabContentContainers:`[id*="e-n-tab-content-${t}"].e-active`},classes:{active:"e-active"},dataAttributes:{tabIndex:"data-tab-index"},ariaAttributes:{titleStateAttribute:"aria-selected",activeTitleSelector:'[aria-selected="true"]'},showTabFn:"show",hideTabFn:"hide",toggleSelf:!1,hidePrevious:!0,autoExpand:!0}}getDefaultElements(){const t=this.getSettings("selectors");return{$widgetContainer:this.findElement(t.widgetContainer),$tabTitles:this.findElement(t.tabTitle),$tabContents:this.findElement(t.tabContent),$headingContainer:this.findElement(t.headingContainer)}}getKeyboardNavigationSettings(){return this.getSettings()}activateDefaultTab(){const t=this.getSettings(),e=this.getEditSettings("activeItemIndex")||1,i={showTabFn:t.showTabFn,hideTabFn:t.hideTabFn};this.setSettings({showTabFn:"show",hideTabFn:"hide"}),this.changeActiveTab(e),this.setSettings(i),this.elements.$widgetContainer.addClass("e-activated")}deactivateActiveTab(t){const e=this.getSettings(),i=e.classes.active,n=e.ariaAttributes.activeTitleSelector,s="."+i,a=this.elements.$tabTitles.filter(n),o=this.elements.$tabContents.filter(s);return this.setTabDeactivationAttributes(a,t),o.removeClass(i),o[e.hideTabFn](0,(()=>this.onHideTabContent(o))),o}getTitleActivationAttributes(){const t=this.getSettings("ariaAttributes").titleStateAttribute;return{tabindex:"0",[t]:"true"}}setTabDeactivationAttributes(t){const e=this.getSettings("ariaAttributes").titleStateAttribute;t.attr({tabindex:"-1",[e]:"false"})}onHideTabContent(){}activateTab(t){const e=this.getSettings(),i=e.classes.active,n="show"===e.showTabFn?0:400;let s=this.elements.$tabTitles.filter(this.getTabTitleFilterSelector(t)),a=this.elements.$tabContents.filter(this.getTabContentFilterSelector(t));if(!s.length){const e=Math.max(t-1,1);s=this.elements.$tabTitles.filter(this.getTabTitleFilterSelector(e)),a=this.elements.$tabContents.filter(this.getTabContentFilterSelector(e))}s.attr(this.getTitleActivationAttributes()),a.addClass(i),a[e.showTabFn](n,(()=>this.onShowTabContent(a)))}onShowTabContent(t){elementorFrontend.elements.$window.trigger("elementor-pro/motion-fx/recalc"),elementorFrontend.elements.$window.trigger("elementor/nested-tabs/activate",t),elementorFrontend.elements.$window.trigger("elementor/bg-video/recalc")}isActiveTab(t){const e=this.getSettings(),i="true"===this.elements.$tabTitles.filter(`[${e.dataAttributes.tabIndex}="${t}"]`).attr(e.ariaAttributes.titleStateAttribute),n=this.elements.$tabContents.filter(this.getTabContentFilterSelector(t)).hasClass(this.getActiveClass());return i&&n}onTabClick(t){t.preventDefault(),this.changeActiveTab(t.currentTarget?.getAttribute(this.getSettings("dataAttributes").tabIndex),!0)}getTabEvents(){return{click:this.onTabClick.bind(this)}}getHeadingEvents(){const t=this.elements.$headingContainer[0];return{mousedown:a.changeScrollStatus.bind(this,t),mouseup:a.changeScrollStatus.bind(this,t),mouseleave:a.changeScrollStatus.bind(this,t),mousemove:a.setHorizontalTitleScrollValues.bind(this,t,this.getHorizontalScrollSetting())}}bindEvents(){this.elements.$tabTitles.on(this.getTabEvents()),this.elements.$headingContainer.on(this.getHeadingEvents()),elementorFrontend.elements.$window.on("resize",this.onResizeUpdateHorizontalScrolling.bind(this)),elementorFrontend.elements.$window.on("resize",this.setTouchMode.bind(this)),elementorFrontend.elements.$window.on("elementor/nested-tabs/activate",this.reInitSwipers),elementorFrontend.elements.$window.on("elementor/nested-elements/activate-by-keyboard",this.changeActiveTabByKeyboard.bind(this)),elementorFrontend.elements.$window.on("elementor/nested-container/atomic-repeater",this.linkContainer.bind(this))}unbindEvents(){this.elements.$tabTitles.off(),this.elements.$headingContainer.off(),this.elements.$tabContents.children().off(),elementorFrontend.elements.$window.off("resize",this.onResizeUpdateHorizontalScrolling.bind(this)),elementorFrontend.elements.$window.off("resize",this.setTouchMode.bind(this)),elementorFrontend.elements.$window.off("elementor/nested-tabs/activate",this.reInitSwipers),elementorFrontend.elements.$window.off("elementor/nested-elements/activate-by-keyboard",this.changeActiveTabByKeyboard.bind(this)),elementorFrontend.elements.$window.off("elementor/nested-container/atomic-repeater",this.linkContainer.bind(this))}reInitSwipers(t,e){const i=e.querySelectorAll(".swiper");for(const t of i){if(!t.swiper)return;t.swiper.initialized=!1,t.swiper.init()}}onInit(){super.onInit(...arguments),this.getSettings("autoExpand")&&this.activateDefaultTab(),(0,a.setHorizontalScrollAlignment)(this.getHorizontalScrollingSettings()),this.setTouchMode(),"nested-tabs.default"===this.getSettings("elementName")&&i.e(304).then(i.bind(i,7469)).then((t=>{let{default:e}=t;new e(this.getKeyboardNavigationSettings())})).catch((t=>{console.error("Error importing module:",t)}))}onEditSettingsChange(t,e){"activeItemIndex"===t&&this.changeActiveTab(e,!1)}onElementChange(t){this.checkSliderPropsToWatch(t)&&(0,a.setHorizontalScrollAlignment)(this.getHorizontalScrollingSettings())}checkSliderPropsToWatch(t){return 0===t.indexOf("horizontal_scroll")||"breakpoint_selector"===t||0===t.indexOf("tabs_justify_horizontal")||0===t.indexOf("tabs_title_space_between")}changeActiveTab(t){if(arguments.length>1&&void 0!==arguments[1]&&arguments[1]&&this.isEdit&&this.isElementInTheCurrentDocument())return window.top.$e.run("document/repeater/select",{container:elementor.getContainer(this.$element.attr("data-id")),index:parseInt(t)});const e=this.isActiveTab(t),i=this.getSettings();if(!i.toggleSelf&&e||!i.hidePrevious||this.deactivateActiveTab(t),!i.hidePrevious&&e&&this.deactivateActiveTab(t),!e){if(this.isAccordionVersion())return void this.activateMobileTab(t);this.activateTab(t)}}changeActiveTabByKeyboard(t,e){e.widgetId.toString()===this.getID().toString()&&this.changeActiveTab(e.titleIndex,!0)}activateMobileTab(t){setTimeout((()=>{this.activateTab(t),this.forceActiveTabToBeInViewport(t)}),10)}forceActiveTabToBeInViewport(t){if(!elementorFrontend.isEditMode())return;const e=this.elements.$tabTitles.filter(this.getTabTitleFilterSelector(t));elementor.helpers.isInViewport(e[0])||e[0].scrollIntoView({block:"center"})}getActiveClass(){return this.getSettings().classes.active}getTabsDirection(){const t=elementorFrontend.getCurrentDeviceMode();return elementorFrontend.utils.controls.getResponsiveControlValue(this.getElementSettings(),"tabs_justify_horizontal","",t)}getHorizontalScrollSetting(){const t=elementorFrontend.getCurrentDeviceMode();return elementorFrontend.utils.controls.getResponsiveControlValue(this.getElementSettings(),"horizontal_scroll","",t)}isAccordionVersion(){return"contents"===this.elements.$headingContainer.css("display")}setTouchMode(){const t=this.getSettings("selectors").widgetContainer;if(elementorFrontend.isEditMode()||"resize"===event?.type){const e=["mobile","mobile_extra","tablet","tablet_extra"],i=elementorFrontend.getCurrentDeviceMode();if(-1!==e.indexOf(i))return void this.$element.find(t).attr("data-touch-mode","true")}else if("ontouchstart"in window)return void this.$element.find(t).attr("data-touch-mode","true");this.$element.find(t).attr("data-touch-mode","false")}linkContainer(t){const{container:e}=t.detail,i=e.model.get("id"),n=this.$element.data("id"),s=e.view.$el;if(i===n&&(this.updateIndexValues(),this.updateListeners(s),elementor.$preview[0].contentWindow.dispatchEvent(new CustomEvent("elementor/elements/link-data-bindings"))),!this.getActiveTabIndex()){const e=t.detail.index+1||1;this.changeActiveTab(e)}}updateListeners(t){this.elements.$tabContents=t.find(this.getSettings("selectors.tabContent")),this.elements.$tabTitles=t.find(this.getSettings("selectors.tabTitle")),this.elements.$tabTitles.on(this.getTabEvents())}updateIndexValues(){const{$widgetContainer:t,$tabContents:e,$tabTitles:i}=this.getDefaultElements(),n=this.getSettings(),s=n.dataAttributes.tabIndex,a=t.data("widgetNumber");i.each(((t,i)=>{const o=t+1,r=`e-n-tab-title-${a}${o}`,l=`e-n-tab-content-${a}${o}`;i.setAttribute("id",r),i.setAttribute("style",`--n-tabs-title-order: ${o}`),i.setAttribute(s,o),i.setAttribute("aria-controls",l),i.querySelector(n.selectors.tabTitleIcon)?.setAttribute("data-binding-index",o),i.querySelector(n.selectors.tabTitleText).setAttribute("data-binding-index",o),e[t].setAttribute("aria-labelledby",r),e[t].setAttribute(s,o),e[t].setAttribute("id",l),e[t].setAttribute("style",`--n-tabs-title-order: ${o}`)}))}onResizeUpdateHorizontalScrolling(){(0,a.setHorizontalScrollAlignment)(this.getHorizontalScrollingSettings())}getHorizontalScrollingSettings(){return{element:this.elements.$headingContainer[0],direction:this.getTabsDirection(),justifyCSSVariable:"--n-tabs-heading-justify-content",horizontalScrollStatus:this.getHorizontalScrollSetting()}}}e.default=NestedTabs}}]);